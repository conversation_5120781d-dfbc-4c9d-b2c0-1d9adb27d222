import { computed, effect, inject, Injectable, Signal, signal } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { ConversationMessage, FirestoreService } from '@galaxy/conversation/core';
import { convertMessageIntoConversationMessage } from '@galaxy/conversation/core/lib/message-utils';
import {
  Conversation,
  ConversationApiService,
  ConversationChannel,
  CreateWidgetConversationResponse,
  MessageStatus,
  MessageType,
  Metadata,
  Participant,
  PlatformLocation,
} from '@vendasta/conversation';
import {
  asyncScheduler,
  BehaviorSubject,
  catchError,
  combineLatest,
  filter,
  firstValueFrom,
  map,
  Observable,
  observeOn,
  shareReplay,
  switchMap,
  throwError,
} from 'rxjs';
import { getDocId } from '../util/conversation-id';
import { groupMessages } from '../util/message-group';
import { AuthService } from './auth.service';
import { WebchatConfigService } from './webchat-config.service';
import { AnalyticsService } from './analytics/analytics.service';
import {
  TEMP_AI_ASSISTANT_PARTICIPANT_ID,
  TEMP_CONVERSATION_ID,
  TEMP_PARTICIPANT_ID,
} from '@galaxy/conversation/webchat-widget';
import { isAnonymous } from '../util/visitor';
import { CHAT_START_TAG } from './analytics/analytics.constants';
import { TranslateService } from '@ngx-translate/core';
import { KeyValuePair } from '@vendasta/conversation';

const DATA_QUERY_PARAMS_KEY = 'data_query_params_key';
const TIMEZONE_KEY = 'timezone_key';
const SNAPSHOT_ID_KEY = 'snapshot_id';

@Injectable({
  providedIn: 'root',
})
export class ChatService {
  private readonly conversationApiService = inject(ConversationApiService);
  private readonly authService = inject(AuthService);
  private readonly webchatConfigService = inject(WebchatConfigService);
  private readonly conversationFirestore = inject(FirestoreService);
  readonly conversation = signal<Conversation | null>(null);
  private readonly unsavedMessages$$ = new BehaviorSubject<{ [docId: string]: ConversationMessage[] }>({});
  private readonly analyticsService = inject(AnalyticsService);
  private readonly translateService = inject(TranslateService);

  private readonly currentFirestoreConversationId$ = toObservable(this.conversation).pipe(
    map((conversation) =>
      conversation && conversation.conversationId !== undefined ? getDocId(conversation.conversationId) : '',
    ),
  );
  public readonly currentParticipantId = toSignal(this.authService.participantId$, { initialValue: null });
  public readonly isCreatingConversation = signal(false);
  private readonly greetingMessage = computed(
    () =>
      this.webchatConfigService.config()?.customGreetingMessage ||
      this.translateService.instant('INBOX.WEBCHAT.SETTINGS.WEBCHAT_CUSTOM_GREETING_PLACEHOLDER'),
  );

  private readonly greetingMessagePlaceholder: Signal<ConversationMessage> = computed(() => {
    return {
      type: MessageType.MESSAGE_TYPE_MESSAGE,
      sender: {
        participantId: TEMP_AI_ASSISTANT_PARTICIPANT_ID,
      } as Participant,
      body: this.greetingMessage(),
      created: new Date(),
      channel: ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT,
    };
  });

  constructor() {
    effect(() => {
      this.analyticsService.setConversationId(this.conversation()?.conversationId || '');
    });
  }

  async sendMessage(body: string, metadata?: Metadata[]): Promise<void> {
    const created = new Date();
    const newMessage: ConversationMessage = {
      sender: new Participant({
        participantId: TEMP_PARTICIPANT_ID,
        channel: ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT,
      }),
      body,
      type: MessageType.MESSAGE_TYPE_MESSAGE,
      channel: ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT,
      created,
      sendStatus: {
        created,
        status: MessageStatus.MESSAGE_STATUS_SENDING,
      },
    };

    if (!this.currentParticipantId() || !this.conversation()) {
      this.addUnsavedMessage(TEMP_CONVERSATION_ID, newMessage);

      if (this.webchatConfigService.widgetId() === null) {
        return;
      }
      try {
        this.isCreatingConversation.set(true);
        const createConversationResult = await this.createConversation(this.webchatConfigService.widgetId());
        if (
          createConversationResult.greetingMessageId !== '' &&
          this.webchatConfigService.config().enableGreetingMessage
        ) {
          const greetingMessage = this.greetingMessagePlaceholder();
          greetingMessage.messageId = createConversationResult.greetingMessageId;
          greetingMessage.conversationId = this.conversation().conversationId;
          this.addUnsavedMessage(getDocId(createConversationResult.conversation.conversationId), greetingMessage);
        }
        this.isCreatingConversation.set(false);
      } catch (e) {
        console.error('Error creating conversation', e);
        this.updateMessageStatus(getDocId(TEMP_CONVERSATION_ID), newMessage, MessageStatus.MESSAGE_STATUS_UNDELIVERED);
        this.isCreatingConversation.set(false);
        return;
      }
    }
    newMessage.conversationId = this.conversation().conversationId;
    newMessage.sender = new Participant({
      participantId: this.currentParticipantId(),
      channel: ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT,
    });
    newMessage.created = new Date();
    this.addUnsavedMessage(getDocId(this.conversation().conversationId), newMessage);

    // Conversation with Web Chat channel will always start as origin location of partner centre or business app locations
    const loc = this.conversation().originLocation || PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP;
    const result = await firstValueFrom(
      this.conversationApiService
        .sendWidgetMessage({
          type: MessageType.MESSAGE_TYPE_MESSAGE,
          body,
          location: loc,
          metadata: metadata,
        })
        .pipe(
          catchError((error) => {
            console.error('Error sending message', error);
            this.updateMessageStatus(
              getDocId(this.conversation()?.conversationId),
              newMessage,
              MessageStatus.MESSAGE_STATUS_UNDELIVERED,
            );
            return throwError(() => error);
          }),
        ),
    );

    this.updateMessageStatus(
      getDocId(this.conversation().conversationId),
      newMessage,
      MessageStatus.MESSAGE_STATUS_SENT,
      result.messageId,
    );
  }

  async openConversation(widgetId: string): Promise<void> {
    const token = await this.authService.token;
    try {
      if (token && this.jwtMatchesWidgetId(token, widgetId)) {
        await this.loadConversation();
      } else {
        this.authService.token = '';
      }
    } catch (e) {
      if (this.webchatConfigService.widgetId() !== null && e.status === 400) {
        this.authService.token = '';
      }
    }
  }

  private async createConversation(widgetId: string): Promise<CreateWidgetConversationResponse> {
    const metadata = [];
    if (this.webchatConfigService.dataQueryParams()) {
      metadata.push(
        new KeyValuePair({
          key: DATA_QUERY_PARAMS_KEY,
          value: this.webchatConfigService.dataQueryParams(),
        }),
      );
    }
    if (this.webchatConfigService.snapshotId()) {
      metadata.push(
        new KeyValuePair({
          key: SNAPSHOT_ID_KEY,
          value: this.webchatConfigService.snapshotId(),
        }),
      );
    }

    metadata.push(
      new KeyValuePair({
        key: TIMEZONE_KEY,
        value: Intl.DateTimeFormat().resolvedOptions().timeZone,
      }),
    );

    const conversationResp = await firstValueFrom(
      this.conversationApiService.createWidgetConversation({
        widgetId: widgetId,
        chatSourceUrl: window.location.href,
        greetingMessage: this.webchatConfigService.config().enableGreetingMessage ? this.greetingMessage() : '',
        metadata: metadata,
      }),
    );
    this.conversation.set(conversationResp.conversation);
    this.authService.token = conversationResp.widgetParticipantToken;
    if (isAnonymous(conversationResp)) {
      setTimeout(() => this.analyticsService.startPollingForLeadCapturedEvent(), 0);
    }
    // WARNING: this has to be after the polling otherwise we might poll when GTM isn't installed
    setTimeout(() => this.analyticsService.pushEvent(CHAT_START_TAG), 0);
    return conversationResp;
  }

  private async loadConversation(): Promise<void> {
    const conversationResp = await firstValueFrom(this.conversationApiService.getWidgetConversation());
    if (isAnonymous(conversationResp)) {
      this.analyticsService.startPollingForLeadCapturedEvent();
    }
    this.conversation.set(conversationResp.conversation);
  }

  messagesScrollable$: Observable<{
    messages: ConversationMessage[];
    loading: boolean;
  }> = this.currentFirestoreConversationId$.pipe(
    observeOn(asyncScheduler),
    switchMap((docId) => {
      if (!docId) {
        return this.unsavedMessages$$.pipe(
          map((unsavedMessages) => ({
            messages: unsavedMessages[TEMP_CONVERSATION_ID] || [],
            loading: false,
          })),
        );
      }

      return combineLatest([
        this.conversationFirestore.webchatMessages$(docId).pipe(switchMap((msgs) => this.getMultiWidgetMessage(msgs))),
        this.unsavedMessages$$,
      ]).pipe(
        map(([messagesResponse, unsavedMessages]) => {
          const savedMessages = messagesResponse || [];
          const unsavedMessagesForDocId = unsavedMessages[docId] || [];
          const filteredSavedMessages = savedMessages.filter(
            (savedMsg) => !unsavedMessagesForDocId.find((unsavedMsg) => unsavedMsg.messageId === savedMsg.messageId),
          );
          return {
            messages: [...unsavedMessagesForDocId, ...filteredSavedMessages].sort(
              (a, b) => b.created.getTime() - a.created.getTime(),
            ),
            loading: false,
          };
        }),
      );
    }),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  public readonly messages$ = this.messagesScrollable$.pipe(map((res) => res.messages));

  public readonly messageGroups$ = this.messages$.pipe(
    map((messages) => groupMessages(messages).slice().reverse()),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  private addUnsavedMessage(docId: string, message: ConversationMessage) {
    const unsavedMessages = this.unsavedMessages$$.getValue();
    const unsavedMessagesForDocId = unsavedMessages[docId] || [];
    this.unsavedMessages$$.next({
      ...unsavedMessages,
      [docId]: [message, ...unsavedMessagesForDocId],
    });
  }

  public addGreetingMessage(conversationID: string): void {
    const greetingMessage = this.greetingMessagePlaceholder();
    this.addUnsavedMessage(conversationID, greetingMessage);
  }

  private updateMessageStatus(
    docId: string,
    message: ConversationMessage,
    newStatus: MessageStatus,
    messageID?: string,
  ): void {
    const unsavedMessages = this.unsavedMessages$$.getValue();
    const unsavedMessagesForDocId = unsavedMessages[docId] || [];

    const updatedMessages = unsavedMessagesForDocId.map((msg) =>
      msg === message ? { ...msg, sendStatus: { ...msg.sendStatus, status: newStatus }, messageId: messageID } : msg,
    );

    this.unsavedMessages$$.next({
      ...unsavedMessages,
      [docId]: updatedMessages,
    });
  }

  private getMultiWidgetMessage(msgs: ConversationMessage[]): Observable<ConversationMessage[]> {
    const msgIds = msgs.map((message) => message.messageId) as string[];

    return this.conversationApiService.getMultiWidgetMessages({ messageIds: msgIds }).pipe(
      filter((resp) => !!resp),
      map((resp) =>
        resp.messages?.map((msg) => {
          const participant = new Participant({
            participantId: msg.participantId,
            channel: msg.channel,
          });
          return convertMessageIntoConversationMessage(msg, participant);
        }),
      ),
    );
  }

  // This method is used to check that the widget id matches.
  // it does not verify the token is valid, or any security related checks.
  private jwtMatchesWidgetId(token: string, widgetId: string): boolean {
    const payload = JSON.parse(window.atob(token.split('.')[1]));
    return payload?.['widget_id'] === widgetId;
  }

  setDataQueryParams(data: string): void {
    this.webchatConfigService.setDataQueryParams(data);
  }

  setSnapshotId(snapshotId: string): void {
    this.webchatConfigService.setSnapshotId(snapshotId);
  }
}
