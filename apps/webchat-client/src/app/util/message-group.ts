import { ConversationMessage } from '@galaxy/conversation/core';
import { MessageGroup, DEFAULT_CHAT_MESSAGE_GROUPING_DURATION } from '@galaxy/conversation/webchat-widget';

export function groupMessages(messages: ConversationMessage[]): MessageGroup[] {
  const allMessages = (messages || []).filter(Boolean).sort((a, b) => {
    return a?.created?.getTime() - b?.created?.getTime();
  });

  return allMessages.reduce((groups, message) => {
    // if possible, add the message to the previous message group
    const previousGroup = groups[groups.length - 1];
    if (canAddMessageToPreviousGroup(message, previousGroup)) {
      previousGroup.addMessage(message);
      return groups;
    }

    // othwerwise, create a new message group and add the item to it
    const newGroup = createNewGroupWithMessage(message, previousGroup?.lastMessage);
    return [...groups, newGroup];
  }, []);
}

function canAddMessageToPreviousGroup(message: ConversationMessage, previousGroup: MessageGroup | undefined): boolean {
  if (!previousGroup) return false;
  return previousGroup.participantId === message.sender?.participantId;
}

function createNewGroupWithMessage(
  message: ConversationMessage,
  previousMessage: ConversationMessage | undefined,
): MessageGroup {
  // if enough time has passed since the previous message, set the createdAt
  // date of the new message group so a date divider will be shown in the
  // conversation stream
  const previousDate = previousMessage?.created;
  const messageDate = message.created;
  const enoughTimeElapsed = messageDate.getTime() - previousDate?.getTime() > DEFAULT_CHAT_MESSAGE_GROUPING_DURATION;
  const addTimeDivider = !previousDate || enoughTimeElapsed;
  const createdAt = addTimeDivider ? messageDate : null;

  const newGroup = new MessageGroup(message.messageId, message.sender.participantId, createdAt);
  newGroup.addMessage(message);
  return newGroup;
}
