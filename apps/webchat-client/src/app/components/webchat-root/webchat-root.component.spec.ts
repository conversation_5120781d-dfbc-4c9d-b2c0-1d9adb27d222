import { signal } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { WebchatRootComponent } from './webchat-root.component';
import { ChatService } from '../../services/chat.service';
import { WebchatConfigService } from '../../services/webchat-config.service';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { AnalyticsService } from '../../services/analytics/analytics.service';
import { WebchatConfig } from '@galaxy/conversation/webchat-widget';
import { of } from 'rxjs';

describe('WebchatRootComponent', () => {
  let component: WebchatRootComponent;
  let fixture: ComponentFixture<WebchatRootComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        WebchatRootComponent,
        TranslateTestingModule.withTranslations({
          en: {
            'INBOX.WEBCHAT.WIDGET.POWERED_BY': 'This is powered by',
          },
        }),
      ],
      providers: [
        {
          provide: ChatService,
          useValue: {
            openConversation: jest.fn(() => Promise.resolve()),
            messages$: of([]),
            messages: signal([]),
            messageGroups$: of([]),
            conversation: signal(null),
            isCreatingConversation: signal(false),
          },
        },
        { provide: WebchatConfigService, useValue: { config: signal({ color: 'red', isEnabled: true }) } },
        {
          provide: AnalyticsService,
          useValue: {
            pushEvent: jest.fn(),
            startPollingForLeadCapturedEvent: jest.fn(),
            stopPollingForLeadCapturedEvent: jest.fn(),
          },
        },
      ],
    }).compileComponents();

    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation((query) => ({
        matches: true,
        onchange: null,
        media: query,
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    fixture = TestBed.createComponent(WebchatRootComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('isChatOpen', () => {
    it('should be false by default', () => {
      expect(component.isChatOpen()).toBe(null);
    });
  });

  describe('config.isEnabled', () => {
    it('should hide button if webchat is disabled', () => {
      component['configSvc'].config.set({ color: 'red', isEnabled: false } as WebchatConfig);
      fixture.detectChanges();
      expect(component.isEnabled()).toBe(false);
      expect(fixture.nativeElement.shadowRoot.querySelector('button')).toBeFalsy();
    });

    it('should show button if webchat is enabled', () => {
      component['configSvc'].config.set({ color: 'red', isEnabled: true } as WebchatConfig);
      fixture.detectChanges();
      expect(component.isEnabled()).toBe(true);
      expect(fixture.nativeElement.shadowRoot.querySelector('button')).toBeTruthy();
    });
  });

  describe('config.webchatColor', () => {
    it('should read from webchat config', () => {
      expect(component.webchatColor()).toBe('red');
    });
  });

  describe('#toggleChat()-Mobile', () => {
    beforeEach(() => {
      component.isMobile.set(true);
      component.isChatOpen.set(null);
    });
    it('should toggle value of isChatOpen', () => {
      expect(component.isChatOpen()).toBe(null);
      component.toggleChat();
      fixture.detectChanges();
      expect(component.isChatOpen()).toBe(true);
    });

    it('should open chat if closed', () => {
      component.toggleChat();
      expect(component.isChatOpen()).toBe(true);
      fixture.detectChanges();
      expect(fixture.nativeElement.shadowRoot.querySelector('.chat-box')).toBeTruthy();
    });

    it('should close chat if open', () => {
      component.toggleChat();
      component.toggleChat();
      fixture.detectChanges();
      expect(fixture.nativeElement.shadowRoot.querySelector('.chat-box')).toBeFalsy();
    });
  });

  describe('#toggleChat()-Desktop', () => {
    beforeEach(() => {
      component.isMobile.set(false);
      component.isChatOpen.set(null);
    });
    it('should start opened and closed after toggling', () => {
      expect(component.isChatOpen()).toBe(null);
      component.toggleChat();
      expect(component.isChatOpen()).toBe(false);
    });

    it('should open chat if closed', () => {
      component.toggleChat();
      expect(component.isChatOpen()).toBe(false);
      fixture.detectChanges();
      expect(fixture.nativeElement.shadowRoot.querySelector('.chat-box')).toBeFalsy();
    });

    it('should close chat if open', () => {
      component.toggleChat();
      component.toggleChat();
      fixture.detectChanges();
      expect(fixture.nativeElement.shadowRoot.querySelector('.chat-box')).toBeTruthy();
    });

    it('should hide miniView when the show mobile cta is false and the browers is mobile', () => {
      component['configSvc'].config.set({ showMobileCta: false } as WebchatConfig);
      component.isMobile.set(true);

      fixture.detectChanges();
      expect(component.isMiniViewOpen()).toBe(false);
    });

    it('should show miniView when the show mobile cta is true and the browers is mobile', () => {
      component['configSvc'].config.set({ showMobileCta: true } as WebchatConfig);
      component.isMobile.set(true);

      fixture.detectChanges();
      expect(component.isMiniViewOpen()).toBe(true);
    });

    it('should show miniView when the show mobile cta is false and the browers is not mobile', () => {
      component.isMobile.set(false);
      component['configSvc'].config.set({ showMobileCta: false } as WebchatConfig);

      fixture.detectChanges();
      expect(component.isMiniViewOpen()).toBe(true);
    });

    it('should show miniView when the show mobile cta is true and the browers is not mobile', () => {
      component.isMobile.set(false);
      component['configSvc'].config.set({ showMobileCta: true } as WebchatConfig);

      fixture.detectChanges();
      expect(component.isMiniViewOpen()).toBe(true);
    });

    it('should show poweredBy if poweredByName and poweredByUrl are provided', () => {
      component['configSvc'].config.set({
        color: 'red',
        isEnabled: true,
        showFooterContent: true,
        poweredByName: 'Vendasta',
        poweredByUrl: 'https://www.vendasta.com/',
      } as WebchatConfig);

      Object.defineProperty(window, 'location', {
        value: { href: 'http://test_host.com' },
        writable: true,
      });

      fixture.detectChanges();

      const chatBox = fixture.nativeElement.shadowRoot.querySelector('.chat-box');
      const poweredBy = fixture.nativeElement.shadowRoot.querySelector('.powered-by');

      expect(chatBox).toBeTruthy();
      expect(poweredBy).toBeTruthy();

      const poweredByLink = poweredBy.querySelector('a');

      expect(poweredByLink).toBeTruthy();
      expect(poweredByLink.textContent.trim()).toBe('This is powered by Vendasta');
      expect(poweredByLink.getAttribute('href')).toBe(
        'https://www.vendasta.com/?utm_source=webchat&utm_medium=referral&utm_referrer=http%3A%2F%2Ftest_host.com',
      );
    });

    it('should not show poweredBy if poweredByName and poweredByUrl are provided but hide', () => {
      component['configSvc'].config.set({
        color: 'red',
        isEnabled: true,
        showFooterContent: false,
        poweredByName: 'Vendasta',
        poweredByUrl: 'https://www.vendasta.com/',
      } as WebchatConfig);

      fixture.detectChanges();

      const chatBox = fixture.nativeElement.shadowRoot.querySelector('.chat-box');
      const poweredBy = fixture.nativeElement.shadowRoot.querySelector('.powered-by');

      expect(chatBox).toBeTruthy();
      expect(poweredBy).toBeFalsy();
    });

    it('should not show poweredBy if poweredByName and poweredByUrl are not provided', () => {
      component['configSvc'].config.set({
        color: 'red',
        isEnabled: true,
        showFooterContent: true,
      } as WebchatConfig);

      fixture.detectChanges();

      const chatBox = fixture.nativeElement.shadowRoot.querySelector('.chat-box');
      const poweredBy = fixture.nativeElement.shadowRoot.querySelector('.powered-by');

      expect(chatBox).toBeTruthy();
      expect(poweredBy).toBeFalsy();
    });
  });
});
