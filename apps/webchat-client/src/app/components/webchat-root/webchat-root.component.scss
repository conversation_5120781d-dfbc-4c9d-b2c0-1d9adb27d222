@use '@angular/material' as mat;
@use 'design-tokens' as dt;
@use 'utilities' as ut;

// global styles for our shadow DOM
@include mat.elevation-classes();
@include mat.app-background();
@import 'base-themes/glxy-light-theme';

*,
*:before,
*:after {
  box-sizing: border-box;
}

:host {
  // we don't inherit CSS rules from the host site, e.g. button { color: red; }
  // but we do inherit *styles* from any ancestor node, e.g. body { font-size: 24px }
  // so we have to do a little reset here on things we might inherit from the body
  font-size: 16px;
  font-family: dt.$default-font-family;
  display: flex;
  flex-direction: row;
  position: fixed;
  bottom: 0;
  z-index: 1000000;
  margin: 0 !important;
  padding: 1rem 0.5rem 1rem 0.5rem !important;

  .chat-box-container-mini {
    display: block;
    position: relative;

    @include ut.tablet {
      margin-bottom: 1rem;
    }
  }

  .chat-box-container-full {
    display: contents;

    @include ut.tablet {
      display: block;
      position: relative;
      margin-bottom: 1rem;
    }
  }

  // not including icon font in webchat
  mat-icon {
    display: none;
  }
}
