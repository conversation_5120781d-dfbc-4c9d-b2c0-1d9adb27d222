import {
  Component,
  computed,
  effect,
  HostBinding,
  inject,
  Input,
  OnD<PERSON>roy,
  OnInit,
  signal,
  Signal,
  ViewEncapsulation,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  WebchatBoxComponent,
  WebchatButtonComponent,
  DefaultPosition,
  TEMP_CONVERSATION_ID,
} from '@galaxy/conversation/webchat-widget';
import { ConversationApiService, MetadataIdentifier, Metadata } from '@vendasta/conversation';
import { WebchatConfigService } from '../../services/webchat-config.service';
import { ChatService } from '../../services/chat.service';
import { getCookie, setCookie, VIEW_COOKIE_NAME } from '../../util/cookie';
import { AnalyticsService } from '../../services/analytics/analytics.service';
import {
  CHAT_BUTTON_TOGGLE_TAG,
  SEND_MESSAGE_TAG,
  BOOK_APPOINTMENT_TAG,
} from '../../services/analytics/analytics.constants';
import { escapeHtml } from '@vendasta/galaxy/pipes/src/escape-html/escape-html.pipe';
import { SentMessage } from '@vendasta/galaxy/chat-composer/src/chat-composer.component';
import { BookingAvailabilityOption } from '@galaxy/conversation/core';
import { BOOKING_PREFERRED_KEY } from '@galaxy/conversation/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service/src/snackbar.service';
import { toSignal } from '@angular/core/rxjs-interop';
import { AuthService } from '../../services/auth.service';

function addUTMTrackingToURL(path: string): string {
  if (!path) return path;
  try {
    const url = new URL(path);
    url.searchParams.set('utm_source', 'webchat');
    url.searchParams.set('utm_medium', 'referral');
    url.searchParams.set('utm_referrer', window.location.href);
    return url.toString();
  } catch {
    return path;
  }
}

@Component({
  selector: 'app-webchat-root',
  imports: [CommonModule, TranslateModule, WebchatBoxComponent, WebchatButtonComponent],
  templateUrl: './webchat-root.component.html',
  styleUrls: ['./webchat-root.component.scss'],
  providers: [ConversationApiService],
  encapsulation: ViewEncapsulation.ShadowDom,
})
export class WebchatRootComponent implements OnInit, OnDestroy {
  private readonly chatService = inject(ChatService);
  private readonly configSvc = inject(WebchatConfigService);
  private readonly analyticsService = inject(AnalyticsService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly translateService = inject(TranslateService);
  private readonly authService = inject(AuthService);

  readonly conversation = this.chatService.conversation;
  readonly webchatColor: Signal<string | undefined> = computed(() => this.configSvc.config()?.color);
  readonly welcomeMessage: Signal<string | undefined> = computed(() => this.configSvc.config()?.welcomeMessage);
  readonly isEnabled: Signal<boolean> = computed(() => this.configSvc.config()?.isEnabled);
  readonly webchatTextColor: Signal<string | undefined> = computed(() => this.configSvc.config()?.textColor);
  readonly webchatAccentColor: Signal<string | undefined> = computed(() => this.configSvc.config()?.accentColor);
  readonly widgetTopSpacing: Signal<number | undefined> = computed(() => this.configSvc.config()?.widgetTopSpace);
  readonly webchatAccentTextColor: Signal<string | undefined> = computed(
    () => this.configSvc.config()?.accentTextColor,
  );
  readonly isDefaultAssistantName = computed(() => this.configSvc.config()?.assistantName === 'Chat Receptionist');
  readonly assistantAvatarUrl: Signal<string | undefined> = computed(() => this.configSvc.config()?.assistantAvatarUrl);
  protected readonly position: Signal<'right' | 'left'> = computed(
    () => this.configSvc.config()?.position || DefaultPosition,
  );
  protected readonly showFooterContent = computed(() => this.configSvc.config()?.showFooterContent);
  protected readonly poweredBy = computed(() => ({
    name: this.configSvc.config()?.poweredByName,
    url: addUTMTrackingToURL(this.configSvc.config()?.poweredByUrl),
  }));
  protected readonly messageGroups = toSignal(this.chatService.messageGroups$, { initialValue: [] });
  protected readonly messages = toSignal(this.chatService.messages$);
  protected readonly isReplying = computed(
    () => this.messages()?.[0]?.sender?.participantId === this.currentParticipantId(),
  );
  protected readonly currentParticipantId = toSignal(this.authService.participantId$, { initialValue: null });
  private readonly enableGreetingMessage: Signal<boolean> = computed(
    () => this.configSvc.config()?.enableGreetingMessage,
  );

  readonly isChatOpen = signal<boolean | null>(this.getViewCookie());
  readonly isMiniViewOpen = computed(
    () =>
      this.isChatOpen() === null &&
      !this.conversation() &&
      (this.configSvc.config()?.showMobileCta || !this.isMobile()),
  );
  readonly isMobile = signal(false);
  readonly showWebchat = computed(
    () => this.isChatOpen() || this.isMiniViewOpen() || (this.isChatOpen() === null && this.conversation()),
  );

  protected readonly isCreatingConversation = computed(() => this.chatService.isCreatingConversation());

  private viewportWidth = 0;
  private viewportHeight = 0;
  private readonly mediaMatcher = window.matchMedia('(max-width:600px)');

  @HostBinding('id') id = 'webchat-root';

  @HostBinding('style.--viewport-width') get styleWidth() {
    if (this.viewportWidth) return this.viewportWidth + 'px';
    return '100vw';
  }
  @HostBinding('style.--viewport-height') get styleHeight() {
    if (this.viewportHeight) return this.viewportHeight + 'px';
    return '100vh';
  }

  @HostBinding('style.--widget-top-spacing') get styleTopSpacing() {
    if (this.widgetTopSpacing()) return this.widgetTopSpacing().toString() + 'px';
    return '0px';
  }

  @HostBinding('style.right') get rightStyles() {
    return this.position() === 'right' ? '0' : null;
  }

  @HostBinding('style.left') get leftStyles() {
    return this.position() === 'left' ? '0' : null;
  }

  @HostBinding('style.display') get displayStyles() {
    return this.isMobile() ? 'flex' : 'block';
  }

  @HostBinding('style.flex-direction') get flexDirectionStyles() {
    return this.isMobile() ? (this.position() === 'right' ? 'row' : 'row-reverse') : null;
  }

  @Input() set widgetId(widgetId: string) {
    this.configSvc.loadWidget(widgetId);
    this.chatService.openConversation(widgetId);
  }

  @Input() set dataQueryParams(dataQueryParams: string) {
    this.chatService.setDataQueryParams(dataQueryParams);
  }

  @Input() set snapshotId(snapshotId: string) {
    this.chatService.setSnapshotId(snapshotId);
  }

  constructor() {
    effect(() => this.setViewCookie(this.isChatOpen()));
    effect(() => {
      if (
        (!this.currentParticipantId() || !this.conversation()) &&
        this.messages()?.length === 0 &&
        (this.isChatOpen() || !this.isMobile()) &&
        this.enableGreetingMessage()
      ) {
        setTimeout(
          () => {
            this.chatService.addGreetingMessage(TEMP_CONVERSATION_ID);
          },
          this.isMobile() ? 1000 : 3000,
        );
      }
    });
  }

  ngOnInit(): void {
    this.setViewportSize = this.setViewportSize.bind(this);
    window.visualViewport?.addEventListener('resize', this.setViewportSize, { passive: true });
    this.setViewportSize();
    this.mediaMatcher.addEventListener('change', this.setIsMobile, { passive: true });
    this.isMobile.set(this.mediaMatcher.matches);
  }

  ngOnDestroy(): void {
    window.visualViewport?.removeEventListener('resize', this.setViewportSize);
    this.mediaMatcher.removeEventListener('change', this.setIsMobile);
  }

  expandChat(): void {
    if (!this.isMobile() || this.isChatOpen()) return;
    this.toggleChat();
  }

  toggleChat(): void {
    this.isChatOpen.update((value) => {
      if (this.isMiniViewOpen()) {
        return this.isMobile();
      }
      return !value;
    });
    setTimeout(() => {
      this.analyticsService.pushEvent(CHAT_BUTTON_TOGGLE_TAG);
    }, 0);
  }

  closeChat(): void {
    this.isChatOpen.set(false);
    setTimeout(() => {
      this.analyticsService.pushEvent(CHAT_BUTTON_TOGGLE_TAG);
    }, 0);
  }

  protected async sendMessage(msg: SentMessage): Promise<void> {
    if (!msg?.messageText) return;

    const messageText = escapeHtml(msg.messageText);

    await this.chatService.sendMessage(messageText);
    setTimeout(() => {
      this.analyticsService.pushEvent(SEND_MESSAGE_TAG);
    }, 0);

    return;
  }

  async setPreferredBooking(booking: BookingAvailabilityOption) {
    try {
      await this.chatService.sendMessage(booking.displayText, [
        new Metadata({
          identifier: MetadataIdentifier.METADATA_IDENTIFIER_UNDEFINED,
          data: {
            [BOOKING_PREFERRED_KEY]: JSON.stringify(booking),
          },
        }),
      ]);
      setTimeout(() => {
        this.analyticsService.pushEvent(BOOK_APPOINTMENT_TAG);
      }, 0);
    } catch (e) {
      this.snackbarService.openErrorSnack(this.translateService.instant('WEBCHAT.ERROR_SENDING_MESSAGE'));
    }
  }

  private setIsMobile = (matches: MediaQueryListEvent) => {
    this.isMobile.set(matches.matches);
  };

  private setViewportSize(): void {
    this.viewportWidth = window.visualViewport?.width;
    this.viewportHeight = window.visualViewport?.height;
  }

  private setViewCookie(isOpen: boolean | null): void {
    if (isOpen == null) return;
    setCookie(VIEW_COOKIE_NAME, this.isChatOpen() ? 'true' : 'false');
  }

  private getViewCookie(): boolean | null {
    const cookieValue = getCookie(VIEW_COOKIE_NAME);
    if (cookieValue == null || cookieValue === '') {
      return null;
    }
    return cookieValue === 'true';
  }
}
