{"name": "webchat-client", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/webchat-client/src", "prefix": "app", "targets": {"build": {"executor": "@nx/angular:browser-esbuild", "options": {"outputPath": "dist/apps/webchat-client", "index": "apps/webchat-client/src/index.html", "main": "apps/webchat-client/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/webchat-client/tsconfig.app.json", "assets": ["apps/webchat-client/src/favicon.ico", "apps/webchat-client/src/assets", "apps/webchat-client/src/sdk.js"], "styles": [], "stylePreprocessorOptions": {"includePaths": ["libs/galaxy/styles/"]}, "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}, "fonts": false}}, "configurations": {"production": {"deployUrl": "https://cdn.apigateway.co/webchat-client/", "budgets": [{"type": "initial", "maximumWarning": "3.5mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb"}], "outputHashing": "none"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}, "devServer": {}}, "defaultConfiguration": "production", "outputs": ["{options.outputPath}"]}, "serve": {"defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "webchat-client:build"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/webchat-client/jest.config.ts"}}, "vstatic-publish": {"executor": "./tools/builders/vstatic:publish", "options": {"buildPath": "dist/apps/webchat-client", "appId": "webchat-client"}}, "weblate-upload": {"executor": "./tools/builders/weblate-upload:upload", "options": {"filePath": "apps/webchat-client/src/assets/i18n/en_devel.json", "weblateProject": "webchat", "weblateComponent": "webchat-client"}}, "weblate-commit": {"executor": "./tools/builders/weblate-commit:commit", "options": {"weblateProject": "webchat", "weblateComponent": "webchat-client"}}}, "tags": ["scope:client"]}