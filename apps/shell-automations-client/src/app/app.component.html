<div class="top-nav-bar">
  <button mat-icon-button (click)="nav.toggle()">
    <mat-icon>menu</mat-icon>
  </button>
</div>

<glxy-nav #nav [fixedTopGap]="40" appName="reports" class="glxy-nav--light-theme">
  <glxy-nav-panel style="width: 276px">
    <glxy-nav-header>
      <a routerLink="/">
        <div class="sidenav-header">
          <mat-icon inline style="font-size: 54px; opacity: 0.75">offline_bolt</mat-icon>
          <div class="sidenav-header-text">Automations</div>
        </div>
      </a>
    </glxy-nav-header>

    <glxy-nav-item route="/automations">My Automations</glxy-nav-item>
    <glxy-nav-item route="/automations/system-automations">System Automations</glxy-nav-item>
    <glxy-nav-item *ngIf="automationContext === PARTNER_CONTEXT" route="/automations/automation-activities">
      Automation activities
    </glxy-nav-item>
    <glxy-nav-item route="/automations/create-from-template">Templates</glxy-nav-item>
    <glxy-nav-item *ngIf="automationContext === PARTNER_CONTEXT" route="/automations/config/smb/templates">
      SMB Templates
    </glxy-nav-item>

    <glxy-nav-footer class="footer-actions">
      <button type="button" mat-stroked-button (click)="changeNamespace()">
        Change Namespace ({{ namespaceService.namespace$ | async }})
      </button>
      <button type="button" mat-stroked-button (click)="changeEnv()">Change Env ({{ currentEnv }})</button>
      <button type="button" mat-stroked-button (click)="changeContext()">
        Change Context ({{ automationContext }})
      </button>
      <button type="button" mat-stroked-button (click)="changeLocalSession()">Change Session Token</button>
      <div>
        <a href="https://iam-demo.apigateway.co/" target="_blank">IAM Demo</a>
        |
        <a href="https://iam-prod.apigateway.co/" target="_blank">IAM Prod</a>
      </div>
    </glxy-nav-footer>
  </glxy-nav-panel>

  <router-outlet></router-outlet>
</glxy-nav>
