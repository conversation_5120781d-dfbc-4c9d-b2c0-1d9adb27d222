import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatSelectChange } from '@angular/material/select';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { EmailContentData } from '@galaxy/email-ui/email-builder';
import { DefaultCardAction, Template } from '@galaxy/email-ui/email-library/src/email-library-list/card/card.component';
import { PageComponent } from '@galaxy/email-ui/email-library/src/email-library-list/page/page.component';
import { PreviewDialogComponent } from '@galaxy/email-ui/email-library/src/email-library-list/preview-dialog/preview-dialog.component';
import { TranslateService } from '@ngx-translate/core';
import {
  CampaignStatsService,
  CampaignStepInterface,
  CampaignStepStats,
  CampaignStepType,
  EmailTemplateService,
  GetCampaignDetailsStatsResponse,
  GetterCampaignData,
  SenderInterface,
  SenderType,
  Statuses,
  TemplateReferenceInterface,
} from '@vendasta/campaigns';
import { OwnerType } from '@vendasta/email-builder';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { DateRangeFilter, DateRangeSelectorService } from '@vendasta/uikit';
import { BehaviorSubject, combineLatest, EMPTY, firstValueFrom, Observable, of, Subscription } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  map,
  publishReplay,
  refCount,
  skipWhile,
  switchMap,
  take,
} from 'rxjs/operators';
import { ActionListsStoreService } from '../../../action-lists';
import { AddToCampaignDialogComponent } from '../../../action-lists/actions/add-to-campaign-dialog.component';
import { ConfirmationDialogComponent } from '../../../common/confirmation-dialog.component';
import { AccessService, Feature, StubAccessService } from '../../../core/access';
import { Market } from '../../../core/markets/market';
import { MarketsService } from '../../../core/markets/markets.service';
import { PartnerService } from '../../../core/partner.service';
import { DynamicOpenCloseTemplateRefService } from '../../../side-drawer/dynamic-open-close-template-ref.service';
import { Context } from '../../base-template-editing/context';
import { EmailTemplateSaver } from '../../email-template-saver';
import { CampaignsService, SNAPSHOT_STEP_NAME } from '../campaigns.service';
import { CampaignStats, CampaignStepData, TemplateData } from '../interface';
import {
  CampaignDuplicateSuccessDialogComponent,
  Data,
} from './campaign-duplicate-success-dialog/campaign-duplicate-success-dialog.component';
import { CampaignID } from './campaign-options/campaign-options.component';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { TrialBannerService } from '../../../trial-banner/trial-banner.service';
import { AccessRestrictedDialogComponent } from '../../../access';
import { SubscriptionTiersService } from '@galaxy/partner';

interface State {
  selectedMarketId?: string;
  campaignSteps?: CampaignStep[];
  campaignStatus?: Statuses;
  campaignFocus?: string;
  campaignTitle?: string;
  emailCategory?: string;
  campaignState?: string;
  locale?: string;
}

const InitialState: State = {
  selectedMarketId: '',
  campaignSteps: [],
  campaignStatus: Statuses.STATUSES_UNSPECIFIED,
  campaignFocus: '',
  campaignTitle: '',
  emailCategory: '',
  campaignState: '',
  locale: '',
};

const EMAIL_LIBRARY_NAMESPACE = 'email_library';

export interface CampaignStep {
  campaignStepId: string;
  stepType: CampaignStepType;
  templateId: string;
  secondsAfterLastEmail: number;
  name: string;
}

@Component({
  selector: 'app-campaign-details',
  templateUrl: './campaign-details.component.html',
  styleUrls: ['./campaign-details.component.scss'],
  providers: [DynamicOpenCloseTemplateRefService],
  standalone: false,
})
export class CampaignDetailsComponent implements OnInit, OnDestroy {
  private store: BehaviorSubject<State> = new BehaviorSubject<State>(InitialState);
  private oldTitle: string;

  selectedMarketId$ = this.store.pipe(
    map((state) => state.selectedMarketId),
    distinctUntilChanged(),
  );

  campaignSteps$ = this.store.pipe(
    map((state) => state.campaignSteps),
    distinctUntilChanged(),
  );
  campaignStatus$ = this.store.pipe(
    map((state) => state.campaignStatus),
    distinctUntilChanged(),
  );

  campaignState$ = this.store.pipe(
    map((state) => state.campaignState),
    distinctUntilChanged(),
  );

  locale$ = this.store.pipe(
    map((state) => state.locale),
    distinctUntilChanged(),
  );

  campaignDetails$: Observable<GetterCampaignData>;
  templateDetails$: Observable<TemplateData[]>;
  campaignStepStats$: Observable<{ [key: string]: CampaignStepData }>;
  campaignStats$: Observable<CampaignStats>;

  markets$: Observable<Market[]> = this.marketsService.userAccessibleMarkets$;
  hasAccessToAllMarkets$: Observable<boolean> = this.marketsService.hasAccessToAllMarkets$;

  campaignScheduleVersion: number;
  campaignId: string;
  draftMode: boolean;
  campaignTitle$: Observable<string> = this.store.pipe(
    map((state) => state.campaignTitle),
    distinctUntilChanged(),
  );
  campaignFocus$: Observable<string> = this.store.pipe(
    map((state) => state.campaignFocus),
    distinctUntilChanged(),
  );

  emailCategoriesEnabled = false;
  emailCategory$: Observable<string> = this.store.pipe(
    map((state) => state.emailCategory),
    distinctUntilChanged(),
  );

  disablePublishing$: Observable<boolean> = this.store.pipe(
    map(
      (state) =>
        this.updatingStatus || (!this.draftMode && state.campaignSteps.length === 0 && state.emailCategory === ''),
    ),
  );

  readonly reload$$ = new BehaviorSubject(null);

  isOnTrial$: Observable<boolean>;

  loadingStep = false;
  dayList$$: BehaviorSubject<number[]> = new BehaviorSubject<number[]>([]);
  shouldShowSnack = false;
  subscriptions: Subscription[] = [];
  editingTitle = false;

  updatingStatus = false;
  readonly hasAccessToMarkets$: Observable<boolean>;
  totalRecipients$: Observable<number>;

  selectedLibraryTemplate$$: BehaviorSubject<Template>;

  nanDateFilter = true;

  get state(): State {
    return this.store.value;
  }

  updateState(newState: State): void {
    this.store.next({ ...this.state, ...newState });
  }

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private emailTemplateService: EmailTemplateService,
    private translate: TranslateService,
    private snackbarService: SnackbarService,
    private actionListsStoreService: ActionListsStoreService,
    private dialog: MatDialog,
    private dateRangeService: DateRangeSelectorService,
    @Inject(StubAccessService) private accessService: AccessService,
    private marketsService: MarketsService,
    public readonly partnerService: PartnerService,
    private dynamicOpenCloseService: DynamicOpenCloseTemplateRefService,
    private readonly titleService: Title,
    private emailTemplateSaver: EmailTemplateSaver,
    private campaignsService: CampaignsService,
    private campaignStatsService: CampaignStatsService,
    @Inject('USER_ID') private userId$: Observable<string>,
    private dialogRef: MatDialogRef<PreviewDialogComponent>,
    private posthogService: ProductAnalyticsService,
    private readonly trialBannerService: TrialBannerService,
  ) {
    this.hasAccessToMarkets$ = this.accessService.hasAccessToFeature(Feature.markets);
  }

  private sender: SenderInterface = {
    type: SenderType.SENDER_TYPE_PARTNER,
    id: this.partnerService.partnerId,
  };

  ngOnInit(): void {
    this.emailCategoriesEnabled = this.partnerService.partnerId === 'VMF'; //TODO: EM-506 - Refactor after Trusted Tester period

    const campaignId$ = combineLatest([this.route.params.pipe(map((params) => params.campaignId)), this.reload$$]).pipe(
      map(([campaignId, _]: [string, null]) => campaignId),
    );
    this.campaignDetails$ = campaignId$.pipe(
      switchMap((campaignId: string) => this.campaignsService.getCampaign(campaignId)),
      map((campaign) => {
        const focusString = this.campaignsService.focusFromProto(campaign.focus);
        const schedule = campaign.campaignSchedule;
        schedule.forEach((step) => {
          if (!step.secondsAfterLastEmail) {
            step.secondsAfterLastEmail = 0;
          }
        });
        this.updateState({
          campaignStatus: campaign.status,
          campaignSteps: campaign.campaignSchedule as CampaignStep[],
          campaignFocus: focusString,
          campaignTitle: campaign.name,
          emailCategory: campaign.emailCategoryId,
          locale: campaign.locale,
        });
        this.campaignId = campaign.campaignId;
        this.campaignScheduleVersion = campaign.campaignScheduleVersion;
        this.draftMode = campaign.status === Statuses.STATUSES_DRAFT;
        return campaign;
      }),
      publishReplay(1),
      refCount(),
    );

    this.subscriptions.push(
      combineLatest([this.campaignSteps$, this.selectedMarketId$, this.locale$])
        .pipe(
          skipWhile(
            ([steps, marketID, locale]) => !steps || !marketID || !locale || this.partnerService.partnerId === '',
          ),
        )
        .subscribe(([steps, marketID, locale]) => {
          this.campaignsService.loadPreviews(
            this.partnerService.partnerId,
            marketID ? marketID : 'default',
            '',
            locale,
            steps as CampaignStepInterface[],
          );
        }),
    );

    const dateFilter$ = this.dateRangeService.getFilter();

    const campaignIdAndMarketIdAndDateFilter$ = combineLatest([campaignId$, this.selectedMarketId$, dateFilter$]);

    this.campaignStats$ = campaignIdAndMarketIdAndDateFilter$.pipe(
      switchMap(([campaignId, marketId, dateFilter]: [string, string, DateRangeFilter]) => {
        return this.campaignStatsService
          .getCampaignDetailsStats({
            partnerId: this.partnerService.partnerId,
            campaignId: campaignId,
            marketId: marketId,
            dateFilter: {
              dateLte: dateFilter.end,
              dateGte: dateFilter.start,
            },
          })
          .pipe(map((resp) => this.formatCampaignDetailsStatsResponse(resp)));
      }),
      map((statsData: CampaignStats) => {
        this.updateState({
          campaignState: this.determineState(statsData.active, statsData.waiting_on_rate_limit),
        });
        return statsData;
      }),
    );

    this.totalRecipients$ = this.campaignStats$.pipe(map((stats) => stats.total_recipients));

    this.campaignStepStats$ = combineLatest([
      campaignIdAndMarketIdAndDateFilter$,
      this.campaignSteps$,
      this.totalRecipients$,
    ]).pipe(
      switchMap(
        ([[campaignId], steps, totalRecipients]: [[string, string, DateRangeFilter], CampaignStep[], number]) => {
          return this.campaignStatsService
            .getCampaignStepStats({
              partnerId: this.partnerService.partnerId,
              campaignId: campaignId,
            })
            .pipe(map((resp) => this.formatCampaignStepStatsResponse(resp.stepStats, steps, totalRecipients)));
        },
      ),
      map((stepData: CampaignStepData[]) =>
        stepData.reduce((prev, current) => {
          prev[current.campaign_step_id] = current;
          return prev;
        }, {}),
      ),
    );

    this.subscriptions.push(
      this.campaignSteps$.subscribe((campaignSchedule) => {
        const dayList = [];
        let stepDelay = campaignSchedule[0]?.secondsAfterLastEmail ? campaignSchedule[0].secondsAfterLastEmail : 0;
        if (campaignSchedule.length > 0) {
          dayList.push(Math.floor(stepDelay ? stepDelay / 86400 + 1 : 0));
        }
        campaignSchedule.slice(1).forEach((step) => {
          stepDelay = step.secondsAfterLastEmail ? step.secondsAfterLastEmail : 0;
          const days = Math.floor(stepDelay / 86400);
          dayList.push(days + dayList[dayList.length - 1]);
        });
        this.dayList$$.next(dayList);
      }),
    );

    // Also, why are these separate subscriptions? Because we want to debounce but only for
    // saving the campaign steps to the server, not necessarily the calculation of which day
    // they will start on (i.e. the subscription above).
    this.subscriptions.push(
      combineLatest([this.campaignSteps$, this.campaignDetails$])
        .pipe(
          skipWhile(
            // Don't update on the first one, but after this isn't true start letting everything through.
            ([steps, campaignDetails]) => JSON.stringify(steps) === JSON.stringify(campaignDetails.campaignSchedule),
          ),
          debounceTime(1000),
          switchMap(([campaignSchedule, _]: [CampaignStep[], any]) => {
            if (this.campaignScheduleVersion === undefined) {
              return of([undefined]);
            }
            return this.campaignsService.updateCampaignSchedule(this.sender, this.campaignId, campaignSchedule);
          }),
        )
        .subscribe({
          next: () => {
            this.campaignScheduleVersion += this.campaignScheduleVersion;
            if (this.shouldShowSnack) {
              this.snackbarService.successSnack(this.translate.instant('CAMPAIGN_DETAILS.UPDATE.SUCCESS'));
              this.shouldShowSnack = false;
            }
          },
          error: (err) => {
            console.error(err);
            if (this.shouldShowSnack) {
              this.snackbarService.errorSnack(this.translate.instant('CAMPAIGN_DETAILS.UPDATE.FAILURE'));
              this.shouldShowSnack = false;
            }
          },
        }),
    );

    this.templateDetails$ = this.campaignSteps$.pipe(
      map((steps) => {
        return steps.map((step) => step.templateId).filter((templateId) => !!templateId);
      }),
      switchMap((templateIds) => {
        if (templateIds.length === 0) {
          return of([]);
        }
        return this.emailTemplateService.getMulti(templateIds);
      }),
    );

    this.dateRangeService.setAsCustom({ start: null, end: null });
    const dateFilterSub = dateFilter$.subscribe((filter) => {
      if (!filter) {
        return;
      }
    });

    this.subscriptions.push(dateFilterSub);

    this.oldTitle = this.titleService.getTitle();
    this.titleService.setTitle(this.translate.instant('PAGE.TITLES.MARKETING.CAMPAIGNS.DETAILS'));
  }

  formatCampaignDetailsStatsResponse(resp: GetCampaignDetailsStatsResponse): CampaignStats {
    return {
      active: resp.stats.active || 0,
      clicked_through: resp.stats.clicksPerOpenedEmail || 0,
      completed: resp.stats.completed || 0,
      delivered: resp.stats.delivered || 0,
      opened: resp.stats.opened || 0,
      sent: resp.stats.sent || 0,
      stopped: resp.stats.stopped || 0,
      total_accounts: resp.stats.totalAccounts || 0,
      total_leads: resp.stats.totalLeads || 0,
      total_recipients: resp.stats.totalRecipients || 0,
      undeliverable: resp.stats.undeliverable || 0,
      waiting_on_rate_limit: resp.stats.waitingOnRateLimit || 0,
    };
  }

  formatCampaignStepStatsResponse(
    stepStats: CampaignStepStats[],
    steps: CampaignStep[],
    totalRecipients: number,
  ): CampaignStepData[] {
    const stepsWithoutStats = steps.filter(
      (step) => !stepStats?.map((stepStat) => stepStat.campaignStepId).includes(step.campaignStepId),
    );

    const formattedStats: CampaignStepData[] = [];
    for (let i = 0; i < stepStats?.length; i++) {
      const currStepStats = stepStats[i];
      if (currStepStats) {
        formattedStats.push({
          created: currStepStats.created || 0,
          notRequired: currStepStats.notRequired || 0,
          onDeck: 0,
          refreshed: currStepStats.refreshed || 0,
          paused: currStepStats.stopped || 0,
          pending: currStepStats.pending || 0,
          sent: currStepStats.sent || 0,
          campaign_step_id: currStepStats.campaignStepId,
          delivered: currStepStats.delivered || 0,
          dropped: currStepStats.dropped || 0,
          opened: currStepStats.opens || 0,
          clickedThrough: currStepStats.clicks || 0,
          clickToOpenRate: currStepStats.clickToOpenRate || 0,
          bounced: currStepStats.bounced || 0,
          openRate: currStepStats.openRate || 0,
          spamReport: currStepStats.spam || 0,
          unsubscribed: currStepStats.unsubscribed || 0,
        });
      }
    }

    if (stepsWithoutStats) {
      let stopped = 0;
      if (stepStats) {
        stopped = stepStats[0].stopped || 0;
      }

      for (let i = 0; i < stepsWithoutStats.length; i++) {
        formattedStats.push({
          created: 0,
          notRequired: 0,
          onDeck: 0,
          refreshed: 0,
          paused: 0,
          pending: totalRecipients - stopped,
          sent: 0,
          campaign_step_id: stepsWithoutStats[i].campaignStepId,
          delivered: 0,
          dropped: 0,
          opened: 0,
          clickedThrough: 0,
          clickToOpenRate: 0,
          bounced: 0,
          openRate: 0,
          spamReport: 0,
          unsubscribed: 0,
        });
      }
    }
    return formattedStats;
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    this.titleService.setTitle(this.oldTitle);
  }

  determineState(active: number, waitingOnRateLimit: number): string {
    if (active !== 0 || waitingOnRateLimit !== 0) {
      return 'ongoing';
    }
    return 'published';
  }

  async publishCampaign(): Promise<void> {
    const isOnTrial = await firstValueFrom(
      this.trialBannerService.getTrialDetails$().pipe(map((trial) => trial.onTrial)),
    );
    const isFreePartner = await firstValueFrom(
      this.partnerService.subscriptionTier$.pipe(map((s) => SubscriptionTiersService.isFree(s))),
    );
    if (isOnTrial && isFreePartner) {
      this.dialog.closeAll();
      await this.dialog.open(AccessRestrictedDialogComponent, {
        width: AccessRestrictedDialogComponent.DEFAULT_WIDTH,
        maxWidth: AccessRestrictedDialogComponent.DEFAULT_MAX_WIDTH,
        data: { featureId: Feature.marketingAutomation },
      });
      return;
    }
    this.posthogService.trackEvent('user-clicked-publish-campaign', 'create-campaign-workflow', 'click', null);
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.PUBLISH.TITLE', {
          title: this.state.campaignTitle,
        }),
        message: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.PUBLISH.MESSAGE'),
        post: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.PUBLISH.WARNING'),
        buttonText: {
          confirm: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.PUBLISH.CONFIRM'),
          decline: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.PUBLISH.DECLINE'),
        },
      },
      maxWidth: '600px',
    });
    dialogRef.afterClosed().subscribe((confirmed: boolean) => {
      if (confirmed) {
        this.updatingStatus = true;
        this.campaignsService.updateStatus(this.sender, this.campaignId, Statuses.STATUSES_PUBLISHED).subscribe({
          next: () => {
            this.draftMode = !this.draftMode;
            this.updateState({ campaignStatus: Statuses.STATUSES_PUBLISHED });
            this.updatingStatus = false;
          },
          error: (err) => {
            console.error(err);
            this.snackbarService.errorSnack(
              this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.PUBLISH.UPDATE_FAILURE'),
            );
          },
        });
      }
    });
  }

  unpublishCampaign(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.UNPUBLISH.TITLE', {
          title: this.state.campaignTitle,
        }),
        message: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.UNPUBLISH.MESSAGE'),
        buttonText: {
          confirm: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.UNPUBLISH.CONFIRM'),
          decline: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.UNPUBLISH.DECLINE'),
        },
      },
      maxWidth: '600px',
    });
    dialogRef.afterClosed().subscribe((confirmed: boolean) => {
      if (confirmed) {
        this.updatingStatus = true;
        this.campaignsService.updateStatus(this.sender, this.campaignId, Statuses.STATUSES_DRAFT).subscribe({
          next: () => {
            this.draftMode = !this.draftMode;
            this.updateState({ campaignStatus: Statuses.STATUSES_DRAFT });
            this.updatingStatus = false;
          },
          error: (err) => {
            console.error(err);
            this.snackbarService.errorSnack(
              this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.UNPUBLISH.UPDATE_FAILURE'),
            );
          },
        });
      }
    });
  }

  pauseCampaign(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.PAUSE.TITLE', {
          title: this.state.campaignTitle,
        }),
        message: this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.PAUSE.MESSAGE'),
        buttonText: {
          confirm: this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.PAUSE.CONFIRM'),
          decline: this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.PAUSE.DECLINE'),
        },
      },
      maxWidth: '600px',
    });
    dialogRef.afterClosed().subscribe((confirmed: boolean) => {
      if (confirmed) {
        this.campaignsService.pauseCampaign(this.campaignId).subscribe({
          next: () =>
            this.snackbarService.successSnack(this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.PAUSE.SUCCESS')),
          error: (err) => {
            console.error(err);
            this.snackbarService.errorSnack(this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.PAUSE.FAILURE'));
          },
        });
      }
    });
  }

  resumeCampaign(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.RESUME.TITLE', {
          title: this.state.campaignTitle,
        }),
        message: this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.RESUME.MESSAGE'),
        buttonText: {
          confirm: this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.RESUME.CONFIRM'),
          decline: this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.RESUME.DECLINE'),
        },
      },
      maxWidth: '600px',
    });
    dialogRef.afterClosed().subscribe((confirmed: boolean) => {
      if (confirmed) {
        this.campaignsService.unpauseCampaign(this.campaignId).subscribe({
          next: () =>
            this.snackbarService.successSnack(this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.RESUME.SUCCESS')),
          error: (err) => {
            console.error(err);
            this.snackbarService.errorSnack(this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.RESUME.FAILURE'));
          },
        });
      }
    });
  }

  updateSelectedMarket(selectionEvent: MatSelectChange): void {
    this.updateState({ selectedMarketId: selectionEvent.value });
  }

  handleSelectExistingEmail(): void {
    this.posthogService.trackEvent('user-clicked-add-existing-email', 'create-campaign-workflow', 'click', null);
    const dialogRef = this.dialog.open(PageComponent, {
      data: {
        openAsReadOnly: true,
        ownerId: this.partnerService.partnerId,
        ownerType: OwnerType.OWNER_TYPE_PARTNER,
        showPageActions: true,
      },
      height: '75%',
      width: '75%',
    });
    this.subscriptions.push(
      combineLatest([dialogRef.afterClosed(), this.userId$])
        .pipe(
          take(1),
          switchMap(([email, userID]) => {
            if (!email) {
              return EMPTY;
            }
            this.dialogRef = this.dialog.open(PreviewDialogComponent, {
              data: {
                primaryAction: DefaultCardAction.USE,
                template: email,
                userId: userID,
                ownerId: this.partnerService.partnerId,
                partnerId: this.partnerService.partnerId,
              },
              height: '600px',
              width: '800px',
            });
            return this.dialogRef
              .afterClosed()
              .pipe(map((useSelected) => ({ shouldUse: useSelected, selectedEmail: email })));
          }),
          switchMap((result) => {
            if (result.shouldUse) {
              this.loadingStep = true;
              this.emailTemplateSaver.updateContentData(
                JSON.parse(result.selectedEmail.templateJson) as EmailContentData,
              );
              this.emailTemplateSaver.updateHTML(result.selectedEmail.templateHtml);
              const saveResult = this.emailTemplateSaver
                .save$(
                  this.partnerService.partnerId,
                  '',
                  Context.forNewTemplate(this.partnerService.partnerId),
                  this.campaignId,
                  null,
                  {
                    appNamespace: EMAIL_LIBRARY_NAMESPACE,
                    templateId: result.selectedEmail.templateId,
                  } as TemplateReferenceInterface,
                )
                .pipe(
                  map((s) => ({
                    name: result.selectedEmail.title,
                    campaignStepId: s.campaignStepId,
                    templateId: s.templateId,
                  })),
                );
              return combineLatest([saveResult, this.campaignSteps$]);
            } else {
              return of(null);
            }
          }),
          take(1),
        )
        .subscribe(
          ([s, currentSteps]) => {
            if (s != null) {
              this.campaignScheduleVersion += this.campaignScheduleVersion;
              const newStep = {
                campaignStepId: s.campaignStepId,
                name: s.name,
                secondsAfterLastEmail: currentSteps.length > 0 ? 604800 : 0,
                stepType: CampaignStepType.CAMPAIGN_STEP_TYPE_EMAIL,
                templateId: s.templateId,
              } as CampaignStep;
              const steps = [...currentSteps, newStep];
              this.updateState({ campaignSteps: steps });
            }
          },
          () => {
            this.snackbarService.openErrorSnack(this.translate.instant('CAMPAIGN_DETAILS.UPDATE.FAILURE'));
            this.loadingStep = false;
          },
          () => {
            this.loadingStep = false;
          },
        ),
    );
  }

  createNewEmail(campaignId: string): void {
    this.posthogService.trackEvent('user-clicked-create-new-email', 'create-campaign-workflow', 'click', null);
    this.subscriptions.push(
      this.locale$.pipe(take(1)).subscribe((locale) =>
        this.router.navigate([`/marketing/template/new`], {
          queryParams: { campaignId: campaignId, locale: locale },
        }),
      ),
    );
  }

  addSnapshotStep(campaignId: string): void {
    this.posthogService.trackEvent('user-clicked-add-snapshot-report', 'create-campaign-workflow', 'click', null);
    this.shouldShowSnack = true;
    this.loadingStep = true;
    const addStep$ = this.campaignsService.addSnapshotStep(this.sender, campaignId);
    combineLatest([addStep$, this.campaignSteps$.pipe(take(1))]).subscribe(([response, currentSteps]) => {
      this.campaignScheduleVersion += this.campaignScheduleVersion;
      const newStep = {
        campaignStepId: response.campaignStepId,
        name: SNAPSHOT_STEP_NAME,
        secondsAfterLastEmail: currentSteps.length > 0 ? 604800 : 0,
        stepType: CampaignStepType.CAMPAIGN_STEP_TYPE_SNAPSHOT_CREATION,
      } as CampaignStep;
      const steps = [...currentSteps, newStep];
      this.updateState({ campaignSteps: steps });
      this.loadingStep = false;
    });
  }

  editTitle(): void {
    this.editingTitle = true;
  }

  confirmEditing(newTitle: string): void {
    if (!newTitle) {
      this.snackbarService.errorSnack(this.translate.instant('CAMPAIGN_DETAILS.NAME.TOO_SHORT'));
    } else if (newTitle.length > 100) {
      this.snackbarService.errorSnack(this.translate.instant('CAMPAIGN_DETAILS.NAME.TOO_LONG'));
    } else if (newTitle !== this.state.campaignTitle) {
      this.campaignsService.updateName(this.sender, this.campaignId, newTitle).subscribe({
        next: () => this.snackbarService.successSnack(this.translate.instant('CAMPAIGN_DETAILS.NAME.UPDATE_SUCCESS')),
        error: (err) => {
          console.error(err);
          this.snackbarService.errorSnack(this.translate.instant('CAMPAIGN_DETAILS.NAME.UPDATE_FAILURE'));
        },
      });
      this.updateState({ campaignTitle: newTitle });
    }
    this.editingTitle = false;
  }

  cancelEditing(): void {
    this.editingTitle = false;
  }

  getHumanReadableFocuses(focus: string): [string, string] {
    switch (focus) {
      case 'acquire':
        return [
          this.translate.instant('CUSTOMER_ACQUISITION_CAMPAIGNS.TITLE'),
          this.translate.instant('CAMPAIGN_DETAILS.FOCUS.ACQUIRE'),
        ];
      case 'adopt':
        return [
          this.translate.instant('PRODUCT_ADOPTION_CAMPAIGNS.TITLE'),
          this.translate.instant('CAMPAIGN_DETAILS.FOCUS.ADOPT'),
        ];
      case 'upsell':
        return [
          this.translate.instant('PRODUCT_UPSELL_CAMPAIGNS.TITLE'),
          this.translate.instant('CAMPAIGN_DETAILS.FOCUS.UPSELL'),
        ];
      default:
        return [
          this.translate.instant('CUSTOMER_ACQUISITION_CAMPAIGNS.TITLE'),
          this.translate.instant('CAMPAIGN_DETAILS.FOCUS.ACQUIRE'),
        ];
    }
  }

  drop(event: CdkDragDrop<any>): void {
    // Nothing needs to update if they picked it up and put it back down.
    if (event.previousIndex === event.currentIndex) {
      return;
    }
    this.shouldShowSnack = true;
    const events = [...this.state.campaignSteps];

    moveItemInArray(events, event.previousIndex, event.currentIndex);
    this.updateState({ campaignSteps: events });
  }

  updateDelay([stepId, delayInSeconds]: [string, number]): void {
    // get the campaign steps
    const events = [...this.state.campaignSteps].map((step) => {
      if (step.campaignStepId !== stepId) {
        return step;
      }
      // mutate the delay in seconds of the step
      step = {
        campaignStepId: step.campaignStepId,
        name: step.name,
        secondsAfterLastEmail: delayInSeconds,
        stepType: step.stepType,
        templateId: step.templateId,
      } as CampaignStep;
      return step;
    });
    this.shouldShowSnack = true;
    // Re-set the steps and save to server
    this.updateState({ campaignSteps: events });
  }

  updateCampaignFocus(newFocus: string): void {
    this.campaignsService.updateCampaignFocus(this.campaignId, newFocus).subscribe({
      next: () => {
        this.snackbarService.successSnack(this.translate.instant('CAMPAIGN_DETAILS.FOCUS.UPDATE_SUCCESS'));
        this.updateState({ campaignFocus: newFocus });
      },
      error: (err) => {
        console.error(err);
        this.snackbarService.errorSnack(this.translate.instant('CAMPAIGN_DETAILS.FOCUS.UPDATE_FAILURE'));
      },
    });
  }

  updateCampaignEmailCategory(newCategory: string): void {
    this.campaignsService.updateCampaignEmailCategory(this.campaignId, newCategory).subscribe({
      next: () => {
        this.snackbarService.successSnack(this.translate.instant('CAMPAIGN_DETAILS.EMAIL_CATEGORY.UPDATE_SUCCESS'));
        this.updateState({ emailCategory: newCategory });
      },
      error: (err) => {
        console.error(err);
        this.snackbarService.errorSnack(this.translate.instant('CAMPAIGN_DETAILS.EMAIL_CATEGORY.UPDATE_FAILURE'));
      },
    });
  }

  updateCampaignLocale(newLocale: string): void {
    this.campaignsService.updateCampaignLocale(this.sender, this.campaignId, newLocale).subscribe({
      next: () => {
        this.updateState({ locale: newLocale });
        this.snackbarService.successSnack(this.translate.instant('CAMPAIGN_DETAILS.LOCALE.UPDATE_SUCCESS'));
      },
      error: (err) => {
        console.error(err);
        this.snackbarService.errorSnack(this.translate.instant('CAMPAIGN_DETAILS.LOCALE.UPDATE_FAILURE'));
      },
    });
  }

  removeStep([stepId, name]: [string, string]): void {
    let dialogRef;
    if (!name) {
      dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_STEP.SNAPSHOT.TITLE'),
          message: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_STEP.SNAPSHOT.MESSAGE'),
          warn: true,
          buttonText: {
            confirm: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_STEP.CONFIRM'),
            decline: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_STEP.DECLINE'),
          },
        },
      });
    } else {
      dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_STEP.EMAIL.TITLE', {
            name: name,
          }),
          message: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_STEP.EMAIL.MESSAGE', {
            name: name,
          }),
          warn: true,
          buttonText: {
            confirm: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_STEP.CONFIRM'),
            decline: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_STEP.DECLINE'),
          },
        },
      });
    }

    dialogRef.afterClosed().subscribe((confirmed: boolean) => {
      if (confirmed) {
        // get the campaign steps with selected step filtered out
        const events = [...this.state.campaignSteps].filter((step) => step.campaignStepId !== stepId);
        this.shouldShowSnack = true;
        // Re-set the steps and save to server
        this.updateState({ campaignSteps: events });
      }
    });
  }

  goToEmailEdit([stepId, templateId]: [string, string]): void {
    this.subscriptions.push(
      this.locale$.pipe(take(1)).subscribe((locale: string) => {
        this.router.navigate([`/marketing/template/${templateId}/edit`], {
          queryParams: { campaignId: this.campaignId, campaignStepId: stepId, locale: locale },
        });
      }),
    );
  }

  handleAddCRMContactsRequested(): void {
    const backUrl = encodeURIComponent(this.router.url);
    const campaignId = this.campaignId;
    const queryString = `backUrl=${backUrl}&campaignId=${campaignId}`;
    this.posthogService.trackEvent('user-clicked-add-crm-contacts', 'create-campaign-workflow', 'click', null);
    this.router.navigateByUrl(`/marketing/preview-send?${queryString}`);
    // this.dialog.open(CampaignContactsDialogComponent, {
    //   data: {
    //     campaignId: this.campaignId,
    //     senderId: this.partnerService.partnerId,
    //     senderType: SenderType.SENDER_TYPE_PARTNER,
    //   },
    //   width: '500px',
    // });
  }

  openAddListToCampaignModal(): void {
    this.actionListsStoreService.initializeStore(50);
    const dialogRef = this.dialog.open(AddToCampaignDialogComponent, {
      width: '660px',
    });

    this.campaignDetails$
      .pipe(
        map((campaignDetails) => {
          dialogRef.componentInstance.campaign = campaignDetails;
        }),
      )
      .subscribe();
    this.actionListsStoreService.actionLists$
      .pipe(
        map((actionLists) => {
          dialogRef.componentInstance.actionLists = actionLists;
        }),
      )
      .subscribe();

    dialogRef.afterClosed().subscribe({
      error: () => {
        this.snackbarService.openErrorSnack('LISTS.CAMPAIGN_DIALOG.CAMPAIGN_AVOIDED_GENERIC');
      },
    });
  }

  deleteCampaign(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_CAMPAIGN.TITLE', {
          title: this.state.campaignTitle,
        }),
        message: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_CAMPAIGN.MESSAGE'),
        warn: true,
        buttonText: {
          confirm: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_CAMPAIGN.CONFIRM'),
          decline: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_CAMPAIGN.DECLINE'),
        },
      },
      maxWidth: '600px',
    });
    dialogRef.afterClosed().subscribe((confirmed: boolean) => {
      if (confirmed) {
        return this.campaignsService.deleteCampaign(this.campaignId).subscribe({
          next: () => {
            this.snackbarService.successSnack(this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.DELETE_SUCCESS'));
            this.router.navigate([`/marketing/campaigns/all`]);
          },
          error: () => {
            this.snackbarService.errorSnack(this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.DELETE_FAILURE'));
          },
        });
      }
    });
  }

  archiveCampaign(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.ARCHIVE.TITLE', {
          title: this.state.campaignTitle,
        }),
        message: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.ARCHIVE.MESSAGE'),
        warn: true,
        buttonText: {
          confirm: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.ARCHIVE.CONFIRM'),
          decline: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.ARCHIVE.DECLINE'),
        },
      },
      maxWidth: '600px',
    });
    dialogRef.afterClosed().subscribe((confirmed: boolean) => {
      if (confirmed) {
        this.updatingStatus = true;
        return this.campaignsService.updateStatus(this.sender, this.campaignId, Statuses.STATUSES_ARCHIVED).subscribe({
          next: () => {
            this.updateState({ campaignStatus: Statuses.STATUSES_ARCHIVED });
            this.draftMode = !this.draftMode;
            this.updatingStatus = false;
            this.snackbarService.successSnack(this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.ARCHIVE_SUCCESS'));
          },
          error: () => {
            this.snackbarService.errorSnack(this.translate.instant('CAMPAIGN_DETAILS.ACTIONS.ARCHIVE_FAILURE'));
          },
        });
      }
    });
  }

  closePreview(): void {
    this.dynamicOpenCloseService.close();
  }

  goToOldPage(): void {
    this.router.navigate(['campaign', 'details', this.campaignId]);
  }

  handleCampaignDuplicated(newCampaignId: CampaignID): void {
    const data: Data = {
      newCampaignId: newCampaignId,
    };
    this.dialog.open(CampaignDuplicateSuccessDialogComponent, { data: data });
  }

  protected readonly Statuses = Statuses;
}
