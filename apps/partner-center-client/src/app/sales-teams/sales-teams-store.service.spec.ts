import { EMPTY as empty, Observable, of } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { TestScheduler } from 'rxjs/testing';
import { Market } from '../core/markets/market';
import { MarketsService } from '../core/markets/markets.service';
import { listSalesTeamsMock } from './mocks/list-sales-teams-mock';
import { Notification, NotificationType, SalesTeamsAction } from './notification';
import { SalesTeam, SalesTeamId } from './sales-team';
import { SalesTeamProviderMock } from './sales-team-provider.mock';
import { SalesTeamsStoreService } from './sales-teams-store.service';

let scheduler: TestScheduler;

const expectedTeams = listSalesTeamsMock;

const marketsMock = [
  new Market({
    name: 'scrummibears',
    partner_id: 'ABC',
    market_id: 'scrummi',
  }),
  new Market({
    name: 'not scrummibears',
    partner_id: 'ABC',
    market_id: 'default',
  }),
];

class SalesTeamMemberStoreServiceMock {
  get notification$(): Observable<Notification> {
    return empty;
  }
  get displayListError$(): Observable<boolean> {
    return empty;
  }
  public initializeStore(): void {
    return;
  }
}

class RouterMock {
  public navigate = jest.fn();
}

describe('SalesTeamsStoreService', () => {
  let salesTeamsStoreService: SalesTeamsStoreService;
  let salesTeamsServiceMock;
  const mocks = {
    salesTeamsService: {} as any,
  };

  beforeEach(() => {
    scheduler = new TestScheduler((a, b) => expect(a).toEqual(b));
    salesTeamsStoreService = new SalesTeamsStoreService(
      mocks.salesTeamsService as any,
      {
        hasAccessToAllMarkets$: of(false),
        userAccessibleMarkets$: of(marketsMock),
      } as MarketsService,
      new SalesTeamMemberStoreServiceMock() as any,
      new RouterMock() as any,
    );
    salesTeamsServiceMock = mocks.salesTeamsService;
  });

  afterEach(() => {
    scheduler.flush();
  });

  describe('initializeSalesTeamsStore', () => {
    describe('should correctly set store values on initialization', () => {
      beforeEach(() => {
        salesTeamsStoreService.initializeSalesTeamsStore();
      });
      test('salesTeams$', () => {
        scheduler.expectObservable(salesTeamsStoreService.salesTeams$).toBe('n', { n: null });
      });
      test('notification$', () => {
        scheduler.expectObservable(salesTeamsStoreService.notification$).toBe('', {});
      });
      test('displayListError$', () => {
        scheduler.expectObservable(salesTeamsStoreService.displayListError$).toBe('', {});
      });
      test('markets$', () => {
        scheduler.expectObservable(salesTeamsStoreService.markets$).toBe('a', { a: marketsMock });
      });
    });
  });

  describe('listAllTeams', () => {
    describe('should correctly set store values', () => {
      describe('when listing teams succeeds', () => {
        beforeEach(() => {
          salesTeamsServiceMock.list = jest
            .fn()
            .mockReturnValue(scheduler.createColdObservable('-a|', { a: listSalesTeamsMock }));
          salesTeamsStoreService.initializeSalesTeamsStore();
          scheduler.schedule(() => salesTeamsStoreService.listAllTeams(), 10);
        });
        test('salesTeams$', () => {
          scheduler.expectObservable(salesTeamsStoreService.salesTeams$).toBe('a-b', {
            a: null,
            b: expectedTeams,
          });
        });
        test('notification$', () => {
          scheduler.expectObservable(salesTeamsStoreService.notification$).toBe('', {});
        });
        test('displayListError$', () => {
          scheduler.expectObservable(salesTeamsStoreService.displayListError$).toBe('', {});
        });
        test('markets$', () => {
          scheduler.expectObservable(salesTeamsStoreService.markets$).toBe('a', { a: marketsMock });
        });
      });

      describe('when listing teams fails', () => {
        beforeEach(() => {
          salesTeamsServiceMock.list = jest.fn().mockReturnValue(scheduler.createColdObservable('-#|', null, {}));
          salesTeamsStoreService.initializeSalesTeamsStore();
          scheduler.schedule(() => salesTeamsStoreService.listAllTeams(), 10);
        });
        test('salesTeams$', () => {
          scheduler.expectObservable(salesTeamsStoreService.salesTeams$).toBe('a-b', {
            a: null,
            b: [],
          });
        });
        test('notification$', () => {
          scheduler.expectObservable(salesTeamsStoreService.notification$).toBe('--a', {
            a: {
              message: 'Could not get sales teams',
              type: NotificationType.ERROR,
              action: SalesTeamsAction.LIST,
            },
          });
        });
        test('displayListError$', () => {
          scheduler.expectObservable(salesTeamsStoreService.displayListError$).toBe('--a', {
            a: true,
          });
        });
        test('markets$', () => {
          scheduler.expectObservable(salesTeamsStoreService.markets$).toBe('a', { a: marketsMock });
        });
      });
    });
  });

  describe('listTeams', () => {
    describe('should correctly set store values', () => {
      describe('when listing teams with marketId succeeds', () => {
        beforeEach(() => {
          salesTeamsServiceMock.list = jest
            .fn()
            .mockReturnValue(scheduler.createColdObservable('-a|', { a: listSalesTeamsMock }));
          salesTeamsStoreService.initializeSalesTeamsStore();
          scheduler.schedule(() => salesTeamsStoreService.listTeams('scrummi'), 10);
        });
        test('salesTeams$', () => {
          scheduler.expectObservable(salesTeamsStoreService.salesTeams$).toBe('a-b', {
            a: null,
            b: expectedTeams,
          });
        });
        test('notification$', () => {
          scheduler.expectObservable(salesTeamsStoreService.notification$).toBe('', {});
        });
        test('displayListError$', () => {
          scheduler.expectObservable(salesTeamsStoreService.displayListError$).toBe('', {});
        });
        test('markets$', () => {
          scheduler.expectObservable(salesTeamsStoreService.markets$).toBe('a', { a: marketsMock });
        });
      });

      describe('when listTeams is called without marketId and admin has no access to all markets', () => {
        beforeEach(() => {
          salesTeamsServiceMock.list = jest.fn().mockReturnValue(scheduler.createColdObservable('a', { a: [] }));
          salesTeamsStoreService.initializeSalesTeamsStore();
          scheduler.schedule(() => salesTeamsStoreService.listTeams(), 10);
        });
        test('salesTeams$', () => {
          scheduler.expectObservable(salesTeamsStoreService.salesTeams$).toBe('ab', {
            a: null,
            b: [],
          });
        });
        test('notification$', () => {
          scheduler.expectObservable(salesTeamsStoreService.notification$).toBe('', {});
        });
        test('displayListError$', () => {
          scheduler.expectObservable(salesTeamsStoreService.displayListError$).toBe('', {});
        });
        test('markets$', () => {
          scheduler.expectObservable(salesTeamsStoreService.markets$).toBe('a', { a: marketsMock });
        });
      });

      describe('when listTeams is called without marketId and admin has access to all markets', () => {
        beforeEach(() => {
          salesTeamsStoreService = new SalesTeamsStoreService(
            mocks.salesTeamsService as any,
            {
              hasAccessToAllMarkets$: of(true),
              userAccessibleMarkets$: of(marketsMock),
            } as MarketsService,
            new SalesTeamMemberStoreServiceMock() as any,
            new RouterMock() as any,
          );
          salesTeamsServiceMock.list = jest
            .fn()
            .mockReturnValue(scheduler.createColdObservable('-a|', { a: listSalesTeamsMock }));
          salesTeamsStoreService.initializeSalesTeamsStore();
          scheduler.schedule(() => salesTeamsStoreService.listTeams(), 10);
        });
        test('salesTeams$', () => {
          scheduler.expectObservable(salesTeamsStoreService.salesTeams$).toBe('a-b', {
            a: null,
            b: expectedTeams,
          });
        });
      });
    });
  });

  describe('createTeam', () => {
    describe('should correctly set store values', () => {
      describe('when creating team succeeds', () => {
        beforeEach(() => {
          salesTeamsServiceMock.create = jest.fn().mockReturnValue(scheduler.createColdObservable('-a|', { a: {} }));
          salesTeamsServiceMock.get = jest
            .fn()
            .mockReturnValue(scheduler.createColdObservable('-a|', { a: listSalesTeamsMock[0] }));
          salesTeamsStoreService.initializeSalesTeamsStore();
          scheduler.schedule(() => salesTeamsStoreService.createTeam('Scrummibears', 'scrummi'), 10);
        });
        test('salesTeams$', () => {
          scheduler.expectObservable(salesTeamsStoreService.salesTeams$).toBe('a--b', {
            a: null,
            b: [expectedTeams[0]],
          });
        });
        test('notification$', () => {
          scheduler.expectObservable(salesTeamsStoreService.notification$).toBe('---a', {
            a: {
              message: 'Successfully created team',
              type: NotificationType.SUCCESS,
              action: SalesTeamsAction.CREATE,
            },
          });
        });
        test('displayListError$', () => {
          scheduler.expectObservable(salesTeamsStoreService.displayListError$).toBe('', {});
        });
        test('markets$', () => {
          scheduler.expectObservable(salesTeamsStoreService.markets$).toBe('a', { a: marketsMock });
        });
      });

      describe('when creating team fails', () => {
        beforeEach(() => {
          salesTeamsServiceMock.create = jest.fn().mockReturnValue(scheduler.createColdObservable('-#|', null, {}));
          salesTeamsServiceMock.get = jest.fn().mockReturnValue(scheduler.createColdObservable('-a|', {}));
          salesTeamsStoreService.initializeSalesTeamsStore();
          scheduler.schedule(() => salesTeamsStoreService.createTeam('Scrummibears', 'scrummi'), 10);
        });
        test('salesTeams$', () => {
          scheduler.expectObservable(salesTeamsStoreService.salesTeams$).toBe('n', { n: null });
        });
        test('notifications$', () => {
          scheduler.expectObservable(salesTeamsStoreService.notification$).toBe('--a', {
            a: {
              message: 'There was a problem creating the team',
              type: NotificationType.ERROR,
              action: SalesTeamsAction.CREATE,
            },
          });
        });
        test('displayListErrors$', () => {
          scheduler.expectObservable(salesTeamsStoreService.displayListError$).toBe('', {});
        });
        test('markets$', () => {
          scheduler.expectObservable(salesTeamsStoreService.markets$).toBe('a', { a: marketsMock });
        });
      });
    });
  });

  describe('deleteTeam', () => {
    let teamsMock: SalesTeamProviderMock;
    let svc: SalesTeamsStoreService;
    let routerMock: RouterMock;

    const createTeam = function (groupId: SalesTeamId, marbles: string): void {
      scheduler.schedule(() => {
        teamsMock.nextGroupId = groupId;
        svc.createTeam('arbitrary team name' + groupId, 'test-market');
      }, scheduler.createTime(marbles));
    };

    const expectTeamIdsToBe = function (marbles: string, values: any): void {
      const teamIds = svc.salesTeams$.pipe(
        filter((v) => v !== null),
        map((t: SalesTeam[]) => t.map((tt) => tt.groupId)),
      );
      scheduler.expectObservable(teamIds).toBe(marbles, values);
    };

    beforeEach(() => {
      teamsMock = new SalesTeamProviderMock();
      routerMock = new RouterMock();
      svc = new SalesTeamsStoreService(
        teamsMock as any,
        {
          hasAccessToAllMarkets$: of(true),
          userAccessibleMarkets$: of(marketsMock),
        } as MarketsService,
        new SalesTeamMemberStoreServiceMock() as any,
        routerMock as any,
      );
      svc.initializeSalesTeamsStore();
    });

    test('teams$ should get updated with a removed team', () => {
      createTeam('team1', '-|');
      createTeam('team2', '---|');
      scheduler.schedule(() => {
        svc.deleteTeam('team1');
      }, scheduler.createTime('-----|'));

      expectTeamIdsToBe('-a-b-c', {
        a: ['team1'],
        b: ['team1', 'team2'],
        c: ['team2'],
      });
    });

    test('teams$ should remain unchanged if non-existent team is deleted', () => {
      createTeam('team1', '-|');
      createTeam('team2', '---|');
      scheduler.schedule(() => {
        svc.deleteTeam('team999');
      }, scheduler.createTime('-----|'));

      expectTeamIdsToBe('-a-b--', {
        a: ['team1'],
        b: ['team1', 'team2'],
      });
    });

    test('teams$ should get correct states after create-remove-create-remove', () => {
      createTeam('team1', '-|');
      scheduler.schedule(() => {
        svc.deleteTeam('team1');
      }, scheduler.createTime('--|'));
      createTeam('team1', '---|');
      scheduler.schedule(() => {
        svc.deleteTeam('team1');
      }, scheduler.createTime('----|'));

      expectTeamIdsToBe('-abcd', {
        a: ['team1'],
        b: [],
        c: ['team1'],
        d: [],
      });
    });
  });
});
