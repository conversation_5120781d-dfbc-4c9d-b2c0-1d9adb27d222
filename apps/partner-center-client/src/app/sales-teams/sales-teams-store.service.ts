import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { ManualStartWorkStateMap } from '@vendasta/rx-utils/work-state';
import { BehaviorSubject, combineLatest, Observable, ReplaySubject, Subject } from 'rxjs';
import { concatMap, distinctUntilChanged, filter, first, tap, take } from 'rxjs/operators';
import { Market } from '../core/markets/market';
import { MarketsService } from '../core/markets/markets.service';
import { AjaxSalesTeamsService } from './ajax-sales-teams.service';
import { Notification, NotificationType, SalesTeamsAction } from './notification';
import { SalesTeam, SalesTeamId } from './sales-team';
import { SalesTeamMemberStoreService } from './sales-team-member-store.service';

class SalesTeamsStore {
  readonly salesTeams$$: BehaviorSubject<SalesTeam[] | null>;
  readonly notification$$: Subject<Notification>;
  readonly markets$$: ReplaySubject<Market[]>;
  readonly displayListError$$: ReplaySubject<boolean>;
  teamToDelete$$: Subject<string | null>;
  deletingTeam$$: Subject<boolean>;
  failedToDeleteTeam$$: Subject<boolean>;
  deleteStatus: ManualStartWorkStateMap<SalesTeamId, null> = new ManualStartWorkStateMap();

  constructor() {
    this.salesTeams$$ = new BehaviorSubject(null);
    this.notification$$ = new Subject();
    this.markets$$ = new ReplaySubject(1);
    this.displayListError$$ = new ReplaySubject(1);
    this.teamToDelete$$ = new Subject();
    this.deletingTeam$$ = new Subject();
    this.failedToDeleteTeam$$ = new Subject();
  }
}

@Injectable({ providedIn: 'root' })
export class SalesTeamsStoreService {
  private store: SalesTeamsStore;

  constructor(
    private salesTeamsService: AjaxSalesTeamsService,
    private marketsService: MarketsService,
    private teamMemberStoreService: SalesTeamMemberStoreService,
    private router: Router,
  ) {
    this.store = new SalesTeamsStore();
  }

  get salesTeams$(): Observable<SalesTeam[]> {
    return this.store.salesTeams$$.asObservable().pipe(distinctUntilChanged());
  }

  get notification$(): Observable<Notification> {
    return this.store.notification$$.asObservable();
  }

  get displayListError$(): Observable<boolean> {
    return this.store.displayListError$$.asObservable();
  }

  get markets$(): Observable<Market[]> {
    return this.store.markets$$.asObservable();
  }

  public initializeSalesTeamsStore(): void {
    this.teamMemberStoreService.initializeStore();

    this.marketsService.userAccessibleMarkets$
      .pipe(filter((markets) => markets.length > 0))
      .subscribe((markets) => this.store.markets$$.next(markets));

    this.teamMemberStoreService.displayListError$.subscribe((displayListError) =>
      this.store.displayListError$$.next(displayListError),
    );
    this.teamMemberStoreService.notification$.subscribe((notification) => this.store.notification$$.next(notification));
  }

  public listTeams(marketId?: string): void {
    this.store.salesTeams$$.next(null); // TODO: Stop using null to mean "loading"  Use WorkStateMap

    if (marketId) {
      this.doListTeams(marketId);
      return;
    }

    this.marketsService.hasAccessToAllMarkets$
      .pipe(
        take(1),
        tap((hasAccess) => {
          if (hasAccess) {
            this.doListTeams(undefined);
          } else {
            this.store.salesTeams$$.next([]);
          }
        }),
      )
      .subscribe();
  }

  public listAllTeams(): void {
    this.store.salesTeams$$.next(null); // TODO: Stop using null to mean "loading"  Use WorkStateMap
    this.doListTeams(undefined);
  }

  private doListTeams(marketId: string): void {
    const salesTeams$ = this.salesTeamsService.list(marketId);
    salesTeams$.subscribe({
      next: (salesTeams) => {
        const sortedSalesTeams = SalesTeam.sortSalesTeamsAlphabetically(salesTeams);
        this.store.salesTeams$$.next(sortedSalesTeams || []);
      },
      error: () => {
        this.store.displayListError$$.next(true);
        this.store.salesTeams$$.next([]);
        this.store.notification$$.next({
          message: 'Could not get sales teams',
          type: NotificationType.ERROR,
          action: SalesTeamsAction.LIST,
        });
      },
    });
  }

  public listTeamsAndInsertNewTeam(unSyncedTeam: SalesTeam): void {
    this.store.salesTeams$$.next(null);

    const salesTeams$ = this.salesTeamsService.list(unSyncedTeam.marketId);
    salesTeams$.subscribe((salesTeams) => {
      const prevSalesTeams = salesTeams || [];
      const insertUnsyncedTeam = !prevSalesTeams.find((team) => team.groupId === unSyncedTeam.groupId);
      const newSalesTeamList = insertUnsyncedTeam ? [...prevSalesTeams, unSyncedTeam] : prevSalesTeams;
      const sortedSalesTeams = SalesTeam.sortSalesTeamsAlphabetically(newSalesTeamList);
      this.store.salesTeams$$.next(sortedSalesTeams || []);
    });
  }

  public createTeam(teamName: string, marketId: string): void {
    const salesTeamId$ = this.salesTeamsService.create(marketId, teamName);
    const unsyncedTeam$ = salesTeamId$.pipe(
      concatMap((salesTeamId) => this.salesTeamsService.get(salesTeamId)),
      first(),
    );
    combineLatest([unsyncedTeam$, this.store.salesTeams$$])
      .pipe(first())
      .subscribe({
        next: ([unsyncedTeam, salesTeams]) => {
          this.store.notification$$.next({
            message: 'Successfully created team',
            type: NotificationType.SUCCESS,
            action: SalesTeamsAction.CREATE,
          });
          const prevSalesTeams = salesTeams || [];
          const newSalesTeams = SalesTeam.sortSalesTeamsAlphabetically([...prevSalesTeams, unsyncedTeam]);
          this.store.salesTeams$$.next(newSalesTeams);
        },
        error: () => {
          this.store.notification$$.next({
            message: 'There was a problem creating the team',
            type: NotificationType.ERROR,
            action: SalesTeamsAction.CREATE,
          });
        },
      });
  }

  deleteTeam(teamGroupId: SalesTeamId): void {
    this.store.deleteStatus.startWork(teamGroupId, (onFinish, onFailure) => {
      this.salesTeamsService.delete(teamGroupId).subscribe(
        () => {
          const currentTeams = this.store.salesTeams$$.getValue();
          if (currentTeams === null) {
            this.router.navigate(['/st/sales-teams']);
            onFinish(null);
            return;
          }
          const i = currentTeams.findIndex((t) => t.groupId === teamGroupId);
          if (i < 0) {
            return onFailure();
          }
          const teamsCopy = currentTeams.slice();
          teamsCopy.splice(i, 1);
          this.store.salesTeams$$.next(teamsCopy);
          this.router.navigate(['/st/sales-teams']);
          onFinish(null);
        },
        () => onFailure(),
      );
    });
  }

  isDeleting$(teamId: string): Observable<boolean> {
    return this.store.deleteStatus.isLoading$(teamId);
  }

  isSuccess$(teamId: string): Observable<boolean> {
    return this.store.deleteStatus.isSuccess$(teamId);
  }
}
