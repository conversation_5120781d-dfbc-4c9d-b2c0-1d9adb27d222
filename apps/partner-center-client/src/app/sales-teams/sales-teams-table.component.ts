import { Component, Inject, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { FeatureFlagService } from '@vendasta/businesses';
import { GalaxyDatePipe } from '@vendasta/galaxy/pipes';
import { DateFormat } from '@vendasta/galaxy/utility/date-utils';
import { SubscriptionList } from '@vendasta/rx-utils';
import { MenuCell, MenuItem, MultilineCell, TableViewConfig, TableViewModel } from '@vendasta/uikit';
import { Observable } from 'rxjs';
import { map, share, switchMap } from 'rxjs/operators';
import { AccessService, Feature, StubAccessService } from '../core/access';
import { FeatureFlags } from '../core/features';
import { Market } from '../core/markets/market';
import { AddMembersDialogComponent } from './add-members-dialog/add-members-dialog.component';
import { CreateSalesTeamDialogComponent } from './create-team-dialog';
import { DeleteSalesTeamDialogComponent } from './delete-team-dialog/';
import { SalesTeam } from './sales-team';
import { SalesTeamsStoreService } from './sales-teams-store.service';

const SALES_TEAM_DIALOG_WIDTH = '660px';

@Component({
  templateUrl: './sales-teams-table.component.html',
  styleUrls: ['sales-team-tables.scss'],
  selector: 'app-sales-teams-table',
  standalone: false,
})
export class SalesTeamsTableComponent implements OnInit, OnDestroy {
  @Input() markets: Market[];
  salesTeams$: Observable<SalesTeam[]>;
  showEmptyTeamsMessage$: Observable<boolean>;
  loading$: Observable<boolean>;
  readonly hasAccessToMarkets$: Observable<boolean>;

  teamsTable: TableViewModel<SalesTeam> = new TableViewModel<SalesTeam>({
    emptyText: ' ',
    columns: [
      { title: 'Name', cellForItem: (team) => this.teamNameCell(team) },
      { title: 'Modified', cellForItem: (team) => this.formatDate(team.updated) },
    ],
  } as TableViewConfig<SalesTeam>);

  private deleteFeature: Observable<boolean>;
  private subscriptions = SubscriptionList.new();

  constructor(
    private dialog: MatDialog,
    private storeService: SalesTeamsStoreService,
    @Inject(StubAccessService) private accessService: AccessService,
    @Inject('PARTNER_ID') partnerId$: Observable<string>,
    private feature: FeatureFlagService,
  ) {
    this.hasAccessToMarkets$ = this.accessService.hasAccessToFeature(Feature.markets);
    this.deleteFeature = partnerId$.pipe(
      switchMap((pid) => this.feature.checkFeatureFlag(pid, '', FeatureFlags.SALES_TEAM_DELETE)),
      share(),
    );
  }

  ngOnInit(): void {
    this.subscriptions.add(this.hasAccessToMarkets$, (canAccessMarkets) => {
      if (canAccessMarkets) {
        this.teamsTable.addColumnAtIndex(
          { title: 'Market', cellForItem: (team) => this.getMarketName(team.marketId) },
          1,
        );
      }
    });

    this.salesTeams$ = this.storeService.salesTeams$;

    // Load all teams without market filtering
    this.storeService.listAllTeams();

    this.showEmptyTeamsMessage$ = this.salesTeams$.pipe(map((salesTeams) => salesTeams && salesTeams.length === 0));

    this.subscriptions.add(this.deleteFeature, (isEnabled) => {
      this.teamsTable.addColumn({
        title: '',
        cellForItem: (team) => this.menuCell(team, isEnabled),
        config: { width: '50px' },
      });
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.destroy();
  }

  private getMarketName(marketId: string): string {
    const market = this.markets.find((thisMarket) => thisMarket.market_id === marketId);
    return market ? market.name : '';
  }

  private formatDate(date: Date): string {
    return new GalaxyDatePipe().transform(date, DateFormat.longDate);
  }

  private menuCell(salesTeam: SalesTeam, isDeleteEnable: boolean): MenuCell {
    const menuItems: MenuItem[] = [
      { text: 'Add Team Members', click: () => this.openAddMembersDialog(salesTeam) },
      { text: 'Edit', link: { url: this.buildEditPageURL(salesTeam.groupId) } },
    ];
    const cell = new MenuCell({
      menuItems: menuItems,
    });
    const deleteItem = { text: 'Delete', click: () => this.openDeleteTeamDialog(salesTeam) };
    if (isDeleteEnable) {
      cell.config.menuItems.push(deleteItem);
    }
    return cell;
  }

  private teamNameCell(salesTeam: SalesTeam): MultilineCell {
    return new MultilineCell({
      title: salesTeam.teamName,
      link: { url: this.buildEditPageURL(salesTeam.groupId) },
    });
  }

  private buildEditPageURL(teamId: string): string {
    return `/st/sales-teams/${teamId}/edit/`;
  }

  private openAddMembersDialog(salesTeam: SalesTeam): void {
    const addMembersDialogRef = this.dialog.open(AddMembersDialogComponent, { width: SALES_TEAM_DIALOG_WIDTH });
    addMembersDialogRef.componentInstance.salesTeam = salesTeam;
  }

  private openDeleteTeamDialog(salesTeam: SalesTeam): void {
    this.dialog.closeAll();
    const deleteTeamDialogRef = this.dialog.open(DeleteSalesTeamDialogComponent, { width: SALES_TEAM_DIALOG_WIDTH });
    deleteTeamDialogRef.componentInstance.salesTeam = salesTeam;
  }

  openCreateTeamDialog(): void {
    this.dialog.open(CreateSalesTeamDialogComponent, {
      width: '500px',
      height: '340px',
    });
  }
}
