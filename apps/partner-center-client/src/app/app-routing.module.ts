import { inject, NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { KnowledgeSourceManagementComponent } from '@galaxy/ai-knowledge';
import { Language } from '@galaxy/atlas/core';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  LOCALE_DATA_TYPE,
  PREVIEW_DATA_SELECTOR_COMPONENT_TOKEN,
  SUPPORTED_LOCALES_TOKEN,
} from '@galaxy/campaign/dependencies';
import { ActivityTableComponent, LinkActivityComponent } from '@galaxy/email-ui/email-activity';
import { EMAIL_LIBRARY_BASE_URL_TOKEN } from '@galaxy/email-ui/email-library/src/shared';
import { LanguageMap } from '@vendasta/galaxy/i18n';
import { of } from 'rxjs';
import { devServer } from '../globals';
import { AccessRestrictedComponent } from './access';
import { AccessDeniedComponent } from './access-denied/access-denied.component';
import { AccountDeleteComponent } from './accounts';
import { AccountAddToListComponent } from './accounts/account-add-to-list.component';
import { ActionListsOverviewComponent } from './action-lists';
import { ActionListsDetailsComponent } from './action-lists/action-lists-details.component';
import { VendastaPaymentsGuard } from './billing/vendasta-payments.guard';
import { BulkImportComponent } from './bulk-import/bulk-import.component';
import { CampaignAccountSelectorComponent } from './campaigns/account-selector/campaign-account-selector.component';
import { CampaignConfigFactory } from './campaigns/providers';
import { VariableMenuDialogComponent } from './common/variable-menu/variable-menu-dialog/variable-menu-dialog.component';
import { Feature } from './core/access';
import { Views } from './core/access/interface';
import { AccessDashboardGuard, AccessFeatureFlagGuard } from './core/guards';
import { AccessAllMarketsGuard } from './core/guards/access-all-markets.service';
import { AccessBillingGuard } from './core/guards/access-billing-guard.service';
import { AccessBusinessPrioritiesGuard } from './core/guards/access-business-priorities-guard.service';
import { AccessFeatureGuard } from './core/guards/access-feature-guard.service';
import { AccessIsAdminGuard } from './core/guards/access-is-admin-guard.service';
import { AccessPartnerAdminViewGuard } from './core/guards/access-partner-admin-view-guard.service';
import { AccessViewGuard } from './core/guards/access-view-guard.service';
import { AutomationsGuard } from './core/guards/automations-guard';
import { EmbedGuard } from './core/guards/embed.guard';
import { FacebookAdLeadsGuard } from './core/guards/facebook-ad-leads-guard';
import { InboxModalGuard } from './core/guards/inbox-modal.guard';
import { ManageAccountsGuard } from './core/guards/manage-accounts-guard.service';
import { SalespeopleGuard } from './core/guards/salespeople-guard.service';
import { PendingChangesGuard } from './core/guards/pending-changes-guard.service';
import { RetailBillingGuard } from './core/guards/retail-billing.guard';
import { SignUpGuard } from './core/guards/sign-up-guard.service';
import { CustomizeExecReportComponent } from './customize-design';
import { OrderConfigComponent } from './customize-design/order-config/order-config.component';
import { OrderDeclineReasonsConfigComponent } from './customize-design/sales-orders-decline-reasons/sales-orders-decline-reasons.component';
import { EmailBuilderSandboxComponent } from './email-builder-sandbox/email-builder-sandbox.component';
import { EmailSettingsComponent } from './email-settings/email-settings.component';
import { EmbedComponent, EmbedLocalComponent } from './embed';
import { FacebookRoutes } from './facebook-ad-leads/facebook-ad-leads.module';
import { HelpCenterComponent } from './help-center/help-center.component';
import { LocalLoginComponent } from './local-login';
import { PageNotFoundComponent } from './page-not-found/page-not-found.component';
import { CompanyProfileComponent } from './partner/company-profile.component';
import { PipelineViewRouterGuard } from './pipeline/view-router';
import { ProductOrderFormProxyComponent } from './product-order-form-proxy/product-order-form-proxy.component';
import { TermsOfServiceComponent } from './terms-of-service';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { CONFIG_TOKEN } from '@galaxy/campaign/shared/tokens';
import { ForgotPasswordGuard } from './core/guards/forgot-password-guard';
import { AI_ASSISTANT_ROUTES } from '@galaxy/ai-assistant';
import { CategoryService } from '@galaxy/crm/static';
import { AccessFullscreenInboxGuard } from './core/guards/access-fullscreen-inbox-guard';
import { UnifiedOrderActivationsGuard } from './core/guards/unified-order-activations.guard';
import { AccessIsSuperAdminGuard } from './core/guards/access-is-super-admin-guard.service';
import { FeatureFlags } from './core/features';

/** Add routes for new angular component here, or as legacy components are replaced */
export const routes: Routes = [
  {
    path: 'login-two',
    loadChildren: () => import('./login/login.module').then((m) => m.LoginModule),
    data: { noNavbar: true, noCardCheck: true },
  },
  {
    path: 'forgot-password',
    component: ForgotPasswordGuard,
    canActivate: [ForgotPasswordGuard],
    data: { noCardCheck: true },
  },
  {
    path: 'forgot-password-two',
    component: ForgotPasswordGuard,
    canActivate: [ForgotPasswordGuard],
    data: { noCardCheck: true },
  },
  { path: 'app', component: EmbedComponent, canActivate: [EmbedGuard], data: { noCardCheck: true } },
  { path: 'vstatic', component: EmbedComponent, canActivate: [EmbedGuard], data: { noCardCheck: true } },
  {
    path: 'signup',
    pathMatch: 'full',
    component: SignUpGuard,
    canActivate: [SignUpGuard],
    data: { noCardCheck: true },
  },
  {
    path: 'sign-up/',
    pathMatch: 'full',
    component: SignUpGuard,
    canActivate: [SignUpGuard],
    data: { noCardCheck: true },
  },
  {
    path: 'sign-up',
    pathMatch: 'full',
    component: SignUpGuard,
    canActivate: [SignUpGuard],
    data: { noCardCheck: true },
  },
  { path: 'terms-of-service', component: TermsOfServiceComponent, data: { noNavbar: true, noCardCheck: true } },
  { path: 'access-denied', component: AccessDeniedComponent, data: { noCardCheck: true } },
  {
    path: 'superadmin',
    loadChildren: () => import('./superadmin/superadmin.module').then((m) => m.SuperadminModule),
    data: { noCardCheck: true },
  },
  {
    path: 'user-onboarding',
    pathMatch: 'prefix',
    loadChildren: () =>
      import('./user-onboarding/new-user-onboarding-flow/user-onboarding-flow.module').then(
        (m) => m.UserOnboardingModule,
      ),
  },
  {
    path: '',
    loadChildren: () => import('./dashboard/dashboard.module').then((m) => m.DashboardModule),
  },
  {
    path: 'affiliates',
    loadChildren: () => import('./affiliates/affiliates.module').then((m) => m.AffiliateModule),
  },
  {
    path: 'marketplace/saas-metrics',
    redirectTo: 'saas-metrics',
    pathMatch: 'full',
  },
  { path: 'arm-dashboard', redirectTo: 'task-manager', pathMatch: 'full' },
  {
    path: 'concierge/users/manage',
    redirectTo: 'task-manager/users/manage',
    pathMatch: 'full',
  },
  {
    path: 'campaign/:campaignId/template/create',
    redirectTo: 'marketing/campaign/:campaignId/template/create',
  },
  {
    path: 'campaign/:campaignId/:campaignStepId/template/:templateId',
    redirectTo: 'marketing/campaign/:campaignId/template/create',
  },
  {
    path: 'campaign/:campaignId/:campaignStepId/template/:templateId',
    redirectTo: 'marketing/campaign/:campaignId/template/create',
  },
  { path: 'account/', redirectTo: 'account' },
  {
    path: 'account',
    loadChildren: () => import('./account/account.module').then((m) => m.AccountModule),
  },
  {
    path: 'bc-admin',
    loadChildren: () => import('./users/users.routing').then((m) => m.UsersRoutingModule),
    canActivate: [ManageAccountsGuard],
    data: { objectType: 'contact' },
  },
  {
    path: 'users',
    pathMatch: 'prefix',
    loadChildren: () => import('./unified-users/unified-users.module').then((m) => m.UnifiedUsersModule),
  },
  { path: 'manage-admins', pathMatch: 'prefix', redirectTo: '/my-team' },
  {
    path: 'my-team',
    pathMatch: 'prefix',
    canActivate: [AccessIsAdminGuard],
    loadChildren: () => import('./manage-team/manage-team.module').then((m) => m.ManageTeamModule),
  },
  {
    path: 'manage-accounts/bulk-update/wizard',
    component: devServer ? EmbedLocalComponent : EmbedComponent,
    canActivate: [AccessViewGuard],
    data: { view: Views.manageAccounts },
  },
  {
    path: 'manage-accounts/bulk-update/wizard/',
    redirectTo: 'manage-accounts/bulk-update/wizard',
  },
  {
    path: 'manage-accounts/bulk-update/history/',
    component: devServer ? EmbedLocalComponent : EmbedComponent,
    canActivate: [AccessViewGuard],
    data: { view: Views.manageAccounts },
  },
  {
    path: 'manage-accounts/bulk-update/history',
    redirectTo: 'manage-accounts/bulk-update/history/',
  },
  {
    path: 'customize-design/prospect-workflow/wizard/',
    component: devServer ? EmbedLocalComponent : EmbedComponent,
  },
  {
    path: 'customize-design/prospect-workflow/wizard',
    redirectTo: 'customize-design/prospect-workflow/wizard/',
  },
  {
    path: 'customize-design/markets/:marketId/customize-sales-orders',
    component: OrderConfigComponent,
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.salesOrders },
  },
  {
    path: 'customize-design/markets/:marketId/customize-executive-report',
    component: CustomizeExecReportComponent,
    canActivate: [AccessFeatureGuard, AccessViewGuard],
    data: { feature: Feature.executiveReport },
  },
  {
    path: 'customize-design/customize-sales-orders',
    component: OrderConfigComponent,
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.salesOrders },
  },
  {
    path: 'customize-design/sales-orders-decline-reasons',
    component: OrderDeclineReasonsConfigComponent,
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.salesOrders },
  },
  {
    path: 'customize-design/markets/:marketId/',
    redirectTo: 'customize-design/markets',
  },
  {
    path: 'customize-design/markets/',
    component: devServer ? EmbedLocalComponent : EmbedComponent,
    canActivate: [AccessFeatureGuard, AccessViewGuard],
    data: { feature: Feature.whitelabel, view: Views.customizePlatform },
  },
  {
    path: 'customize-design/markets',
    redirectTo: 'customize-design/markets/',
  },
  {
    path: 'customize-design',
    component: devServer ? EmbedLocalComponent : EmbedComponent,
    canActivate: [AccessFeatureGuard, AccessViewGuard],
    data: { feature: Feature.whitelabel, view: Views.customizePlatform },
  },
  {
    path: 'customize-design/',
    redirectTo: 'customize-design',
  },
  {
    path: 'customize-branding',
    loadChildren: () => import('./customize-branding/customize-branding.module').then((m) => m.CustomizeBrandingModule),
    canActivate: [AccessFeatureGuard, AccessViewGuard],
    data: { feature: Feature.whitelabel, view: Views.customizePlatform },
  },
  {
    path: 'customize-design/default-notifications',
    loadChildren: () =>
      import('./user-notifications/notification-preferences.module').then((m) => m.NotificationPreferencesModule),
    canActivate: [AccessViewGuard],
    data: { view: Views.customizePlatform },
  },
  {
    path: 'customize-design/customize-executive-report',
    component: CustomizeExecReportComponent,
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.executiveReport },
  },
  {
    path: 'manage-business-app',
    loadChildren: () =>
      import('./manage-business-app/manage-business-app.module').then((m) => m.ManageBusinessAppModule),
    canActivate: [AccessViewGuard],
    data: { view: Views.manageAccounts },
  },
  {
    path: 'manage-accounts/files',
    loadChildren: () => import('./files/files.module').then((m) => m.FilesModule),
    canActivate: [AccessViewGuard],
    data: { view: Views.manageAccounts },
  },
  {
    path: 'action-lists/manage',
    component: ActionListsOverviewComponent,
    canActivate: [AccessFeatureGuard, AccessViewGuard],
    data: { feature: Feature.lists, view: Views.manageAccounts },
  },
  {
    path: 'action-lists/manage/:listType',
    component: ActionListsOverviewComponent,
    canActivate: [AccessFeatureGuard, AccessViewGuard],
    data: { feature: Feature.lists, view: Views.manageAccounts },
  },
  {
    path: 'action-lists/:actionListId/:tabId',
    component: ActionListsDetailsComponent,
    canActivate: [AccessFeatureGuard, AccessViewGuard],
    data: { feature: Feature.lists, view: Views.manageAccounts },
  },
  { path: 'bulk-import', component: BulkImportComponent },
  // TODO: If you know of a spot that navigates to these urls, just navigate directly to the activation/order-form-page route.
  {
    path: 'manage-accounts/activate-products',
    component: ProductOrderFormProxyComponent, // use temporarily until account create navigation is fixed
    canActivate: [AccessViewGuard],
    data: { view: Views.manageAccounts },
  },
  {
    path: 'company-profile',
    component: CompanyProfileComponent,
    canActivate: [AccessViewGuard],
    data: { view: Views.companyProfile },
  },
  { path: 'restricted/:featureId', component: AccessRestrictedComponent },
  {
    path: 'configure/brand-analytics',
    component: devServer ? EmbedLocalComponent : EmbedComponent,
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.brands },
  },
  {
    path: 'brands',
    loadChildren: () => import('./brands/brands.module').then((m) => m.BrandsModule),
    canActivate: [AccessFeatureGuard, AccessViewGuard],
    data: { feature: Feature.brands, view: Views.manageAccounts },
  },
  {
    path: 'nb',
    component: devServer ? EmbedLocalComponent : EmbedComponent,
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.brands },
  },
  {
    path: 'campaigns/acquire',
    component: devServer ? EmbedLocalComponent : EmbedComponent,
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.marketingAutomation },
  },
  {
    path: 'campaigns/adopt',
    component: devServer ? EmbedLocalComponent : EmbedComponent,
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.marketingAutomation },
  },
  {
    path: 'campaigns/upsell',
    component: devServer ? EmbedLocalComponent : EmbedComponent,
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.marketingAutomation },
  },
  {
    path: 'campaign/details',
    component: devServer ? EmbedLocalComponent : EmbedComponent,
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.marketingAutomation },
  },
  {
    path: 'campaign/accounts/:id',
    component: devServer ? EmbedLocalComponent : EmbedComponent,
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.marketingAutomation },
  },
  {
    path: 'campaign-settings',
    redirectTo: 'email-settings',
  },
  {
    path: 'email-settings',
    component: EmailSettingsComponent,
    canActivate: [AccessAllMarketsGuard, AccessViewGuard],
    canDeactivate: [PendingChangesGuard],
    data: { view: Views.partnerMarketing },
  },
  {
    path: 'email-builder-sandbox',
    component: EmailBuilderSandboxComponent,
  },
  {
    path: 'help',
    component: HelpCenterComponent,
  },
  //Lazy Loaded Routes
  {
    path: 'saas-metrics',
    loadChildren: () => import('./saas-metrics/saas-metrics.module').then((m) => m.SaasMetricsModule),
    canActivate: [AccessDashboardGuard],
  },
  {
    path: 'reports',
    loadChildren: () => import('@galaxy/reports/dynamic').then((m) => m.ReportingModule),
  },
  {
    path: 'marketplace',
    loadChildren: () => import('./marketplace-app/marketplace-app.module').then((m) => m.MarketplaceAppModule),
    canActivate: [AccessViewGuard],
    data: { view: Views.marketplace },
  },
  {
    path: 'billing',
    loadChildren: () => import('./billing/billing.module').then((m) => m.BillingModule),
    canLoad: [AccessBillingGuard],
  },
  {
    path: 'failed-payments',
    loadChildren: () => import('./billing/failed-payments/failed-payments.module').then((m) => m.FailedPaymentsModule),
  },
  {
    path: 'payouts',
    loadComponent: () => import('./billing/payouts/payouts-page.component').then((m) => m.PayoutsPageComponent),
    canActivate: [RetailBillingGuard, VendastaPaymentsGuard],
  },
  {
    path: 'payouts/:payoutId',
    loadComponent: () =>
      import('./billing/payouts/payout-details/payout-details-page.component').then(
        (m) => m.PayoutDetailsPageComponent,
      ),
    canActivate: [RetailBillingGuard, VendastaPaymentsGuard],
  },
  {
    path: 'subscriptions',
    loadComponent: () =>
      import('@galaxy/partner-center-client/billing/dynamic/pages/subscription-page').then(
        (m) => m.SubscriptionsPageComponent,
      ),
    canActivate: [RetailBillingGuard],
  },
  {
    path: 'settings',
    loadChildren: () => import('./settings/settings.module').then((m) => m.SettingsModule),
    canActivate: [AccessViewGuard],
    data: { view: Views.partnerAll },
  },
  {
    path: 'administration',
    loadChildren: () => import('./administration/administration.module').then((m) => m.AdministrationModule),
  },
  {
    path: 'auxiliary-data',
    loadChildren: () =>
      import('./auxiliary-data/crm-custom-fields/crm-custom-field-management.module').then(
        (m) => m.CrmCustomFieldManagementModule,
      ),
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.customFields },
  },
  {
    path: 'custom-fields',
    loadChildren: () =>
      import('./auxiliary-data/crm-custom-fields/crm-custom-field-management.module').then(
        (m) => m.CrmCustomFieldManagementModule,
      ),
    canActivate: [AccessFeatureGuard, AccessViewGuard],
    data: { feature: Feature.customFields, view: Views.partnerAll },
  },
  {
    path: 'custom-forms',
    loadChildren: () => import('./custom-forms/custom-forms.module').then((m) => m.CustomFormsModule),
    canActivate: [AccessViewGuard],
    data: { view: Views.partnerMarketing },
  },
  {
    path: 'st', // TODO: Stop using `st` and use `sales` instead.
    loadChildren: () => import('./sales/sales.module').then((m) => m.SalesModule),
    canActivate: [SalespeopleGuard],
  },
  {
    path: 'sales',
    loadChildren: () => import('./sales/sales.module').then((m) => m.SalesModule),
    canActivate: [SalespeopleGuard],
  },
  {
    path: 'marketing',
    loadChildren: () => import('./marketing/marketing.module').then((m) => m.MarketingModule),
    providers: [{ provide: CONFIG_TOKEN, useFactory: CampaignConfigFactory }],
  },
  {
    path: 'task-manager',
    loadChildren: () => import('./concierge/concierge.module').then((m) => m.ConciergeModule),
    canActivate: [AccessFeatureGuard, AccessViewGuard],
    data: { feature: Feature.concierge, view: Views.taskManager },
  },
  {
    path: 'order-management',
    loadChildren: () => import('./sales-orders/sales-orders.module').then((m) => m.SalesOrdersModule),
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.salesOrders },
  },
  {
    path: 'pipeline/table',
    loadChildren: () => import('./pipeline-table/pipeline-table-page.module').then((m) => m.PipelineTablePageModule),
    canActivate: [AccessFeatureGuard, PipelineViewRouterGuard],
    data: { feature: Feature.pipeline },
  },
  {
    path: 'pipeline/board',
    loadChildren: () => import('./pipeline-board/pipeline-board.module').then((m) => m.PipelineBoardModule),
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.pipeline },
  },
  {
    path: 'pipeline/settings',
    loadChildren: () =>
      import('./pipeline-settings-page/pipeline-settings-page.module').then((m) => m.PipelineSettingsPageModule),
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.pipeline },
  },
  {
    path: 'integrations/service-accounts',
    loadChildren: () =>
      import('./integrations/service-account/service-account.module').then((m) => m.ServiceAccountModule),
    canActivate: [AccessFeatureGuard, AccessViewGuard],
    data: { feature: Feature.serviceAccounts, view: Views.manageAdmins },
  },
  {
    path: 'integrations/sso',
    canActivate: [AccessViewGuard],
    data: { view: Views.partnerAll },
    loadChildren: () =>
      import('./integrations/identity-provider-config/identity-provider-config.module').then(
        (m) => m.IdentityProviderConfigModule,
      ),
  },
  {
    path: 'action-lists/user-list',
    loadChildren: () => import('./action-lists/users/user-list.module').then((m) => m.UserListModule),
    canActivate: [AccessFeatureGuard, AccessViewGuard],
    data: { feature: Feature.lists, view: Views.manageAccounts },
  },
  {
    path: 'upgrade',
    loadChildren: () =>
      import('./upgrade-subscription-tier/upgrade-subscription-tier.module').then(
        (m) => m.UpgradeSubscriptionTierModule,
      ),
  },
  {
    path: 'upgrade-subscription-tier',
    loadChildren: () =>
      import('./upgrade-subscription-tier/upgrade-subscription-tier.module').then(
        (m) => m.UpgradeSubscriptionTierModule,
      ),
  },
  {
    path: 'subscription-tier',
    loadChildren: () => import('./subscription-tier/subscription-tier.module').then((m) => m.SubscriptionTierModule),
    canLoad: [AccessBillingGuard],
  },
  {
    path: 'translate',
    loadChildren: () => import('./translate-portal/translate-portal.module').then((m) => m.TranslatePortalModule),
    canActivate: [() => inject(AccessPartnerAdminViewGuard).canActivate()],
  },
  {
    path: 'manage-accounts/duplicates',
    loadChildren: () =>
      import('./duplicate-accounts/duplicate-accounts.module').then((m) => m.DuplicateAccountsFeatureModule),
    canActivate: [AccessViewGuard],
    data: { view: Views.manageAccounts },
  },
  {
    path: 'manage-accounts',
    loadChildren: () => import('./manage_accounts/manage-accounts.module').then((m) => m.ManageAccountsModule),
    canActivate: [AccessViewGuard],
    data: { objectType: 'company', view: Views.manageAccounts },
  },
  {
    path: 'business',
    loadChildren: () => import('./business/business.module').then((m) => m.BusinessModule),
    canActivate: [AccessViewGuard],
    data: { view: Views.manageAccounts },
  },
  {
    // This route will remain on the same page after adding to the list.
    path: 'businesses/accounts/:accountGroupId/addToList',
    outlet: 'action',
    component: AccountAddToListComponent,
  },
  {
    path: 'order-fulfillment',
    loadComponent: () =>
      import('./fulfillment/order-fulfillment-table/order-fulfillment-table.component').then(
        (m) => m.OrderFulfillmentTableComponent,
      ),
  },
  {
    // This route will remain on the same page after the delete succeeds.
    path: 'businesses/accounts/:accountGroupId/delete',
    outlet: 'action',
    component: AccountDeleteComponent,
  },
  {
    // This route will redirect the user to the manage accounts screen when the delete succeeds.
    path: 'businesses/accounts/:accountGroupId/delete/redirect',
    outlet: 'action',
    component: AccountDeleteComponent,
  },
  {
    path: 'partnerId/:partnerId/custom-fields/:objectTypeId/object/:objectId',
    outlet: 'action',
    loadComponent: () => import('@vendasta/auxiliary-data-components').then((m) => m.CustomFieldsDialogComponent),
  },
  {
    path: 'partnerId/:partnerId/custom-fields/:objectTypeId/object/:objectId/disableManage/:disableManage',
    outlet: 'action',
    loadComponent: () => import('@vendasta/auxiliary-data-components').then((m) => m.CustomFieldsDialogComponent),
  },
  {
    path: 'businesses/accounts/:accountGroupId/activation',
    loadChildren: () => import('./activation/activation.module').then((m) => m.ActivationModule),
    canActivate: [UnifiedOrderActivationsGuard],
  },
  {
    path: 'businesses',
    loadChildren: () => import('./businesses/business.module').then((m) => m.BusinessModule),
  },
  {
    path: 'customize-business-priorities',
    loadChildren: () =>
      import('./customize-business-priorities/customize-business-priorities.module').then(
        (m) => m.CustomizeBusinessPrioritiesModule,
      ),
    canActivate: [AccessBusinessPrioritiesGuard],
  },
  {
    path: 'customize-business-app',
    loadChildren: () =>
      import('./customize-business-app/customize-business-app.module').then((m) => m.CustomizeBusinessAppModule),
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.customizeBusinessApp },
  },
  {
    path: 'automations',
    loadChildren: () => import('@galaxy/automata/pages').then((m) => m.AutomationsModule),
    canActivate: [AutomationsGuard],
    data: { feature: Feature.automations },
  },
  {
    path: 'automation-templates',
    redirectTo: '/automations?tab=templates',
  },
  {
    path: 'account-configurations',
    loadChildren: () => import('./config-manager/config-manager.module').then((m) => m.ConfigManagerModule),
    canMatch: [AccessIsSuperAdminGuard, AccessFeatureFlagGuard],
    data: { featureFlag: FeatureFlags.ACCOUNT_CONFIGURATIONS },
  },
  {
    path: FacebookRoutes.ROOT,
    loadChildren: () => import('./facebook-ad-leads/facebook-ad-leads.module').then((m) => m.FacebookAdLeadsModule),
    canActivate: [FacebookAdLeadsGuard],
  },
  {
    path: 'ai-knowledge',
    component: KnowledgeSourceManagementComponent,
    canActivate: [AccessViewGuard],
    data: {
      view: Views.partnerAll,
      previousPageTitle: 'ADMINISTRATION.PAGE_TITLE',
      previousPageUrl: '/settings',
    },
  },
  {
    path: 'integrations',
    canActivate: [AccessViewGuard],
    data: { view: Views.partnerAll },
    loadChildren: () => import('@galaxy/platform-integrations').then((m) => m.PlatformIntegrationsLibModule),
  },
  {
    path: 'invoices',
    loadChildren: () => import('./invoice/invoice.module').then((m) => m.InvoiceModule),
    canActivate: [RetailBillingGuard],
  },
  { path: 'variable-menu', component: VariableMenuDialogComponent, data: { noNavbar: true } },
  {
    path: 'crm',
    loadChildren: () => import('./crm/partner-crm.module').then((m) => m.PartnerCrmModule),
    resolve: { partnerCRMCategory: () => inject(CategoryService) },
  },
  {
    path: 'ai',
    children: [
      ...AI_ASSISTANT_ROUTES,
      {
        path: 'knowledge-base',
        component: KnowledgeSourceManagementComponent,
      },
    ],
    canActivate: [AccessViewGuard],
    data: { view: Views.partnerAll },
  },
  {
    path: 'score/Contact/:fieldId',
    redirectTo: 'score',
    pathMatch: 'full',
  },
  {
    path: 'score',
    loadChildren: () => import('./crm/partner-score.module').then((m) => m.PartnerScoreModule),
    canActivate: [AccessFeatureGuard, AccessViewGuard],
    data: { feature: Feature.crmLeadScore, view: Views.editCrmLeadScore },
  },
  {
    path: 'lists',
    loadChildren: () => import('./crm/partner-dynamic-lists.module').then((m) => m.PartnerDynamicListsModule),
  },
  {
    path: 'prospect',
    loadChildren: () => import('./crm/prospector/prospector.module').then((m) => m.CrmProspectorModule),
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.crmLeadProspector },
  },
  {
    path: 'email-activity/:attributeKey/:attributeValue',
    component: ActivityTableComponent,
  },
  {
    path: 'link-activity/:attributeKey/:attributeValue',
    component: LinkActivityComponent,
  },
  {
    path: 'products',
    redirectTo: '/marketplace/products',
  },
  {
    path: 'business-directory',
    redirectTo: 'businesses/directory',
  },
  {
    path: 'arm-dashboard',
    redirectTo: 'task-manager',
  },
  {
    path: 'concierge/users/manage',
    redirectTo: 'task-manager/users/manage',
  },
  {
    path: 'concierge/reporting',
    redirectTo: 'task-manager/reporting',
  },
  {
    path: 'manage-admins/user/create',
    redirectTo: 'my-team',
  },
  {
    path: 'manage-admins/user/edit',
    redirectTo: 'my-team',
  },
  {
    path: 'manage-admins',
    redirectTo: 'my-team',
  },
  {
    path: 'billing-reports',
    redirectTo: 'billing/reports',
  },
  {
    path: 'billing-metrics',
    redirectTo: 'billing/metrics',
  },
  {
    path: 'billing/invoices',
    redirectTo: 'billing/documents/sales-invoices',
  },
  {
    path: 'service-accounts',
    redirectTo: 'integrations/service-accounts',
  },
  {
    path: 'st/campaign-stats',
    redirectTo: 'st/sales-overview',
  },
  {
    path: 'st/campaign-stats/details',
    redirectTo: 'st/sales-overview',
  },
  {
    path: 'manage-accounts/create',
    redirectTo: 'business/create',
  },
  {
    path: 'st/manage-leads/manage-accounts/create',
    redirectTo: 'business/create',
  },
  {
    path: 'manage-accounts/business-search',
    redirectTo: 'business/search',
  },
  {
    path: 'st/manage-leads/manage-accounts/business-search',
    redirectTo: 'business/search',
  },
  {
    path: 'manage-accounts/verify',
    redirectTo: 'business/search',
  },
  {
    path: 'st/manage-leads/manage-accounts/verify',
    redirectTo: 'business/search',
  },
  {
    path: 'manageAccounts',
    redirectTo: 'manage-accounts',
  },
  {
    path: 'manage-accounts/import-list/wizard',
    redirectTo: 'bulk-import',
  },
  {
    path: 'marketing/snapshot-widget',
    redirectTo: 'marketing/widget',
  },
  {
    path: 'marketing/snapshot-widget/wizard',
    redirectTo: 'marketing/widget',
  },
  {
    path: 'inbox/ai-assistant/:assistantId',
    loadChildren: () => import('@galaxy/conversation/inbox').then((m) => m.INBOX_AI_ASSISTANT_CHAT_ROUTES),
  },
  // Could nest this under ai/assistants route instead of inbox...
  // {
  //   path: 'ai/assistants/:assistantId',
  //   loadChildren: () => import('@galaxy/conversation/inbox').then((m) => m.INBOX_AI_ASSISTANT_CHAT_ROUTES),
  // },
  {
    path: 'inbox/ai',
    outlet: 'inbox',
    loadChildren: () => import('@galaxy/conversation/inbox').then((m) => m.INBOX_AI_AURORA),
  },
  {
    path: 'conversation/:id',
    outlet: 'inbox',
    loadChildren: () => import('@galaxy/conversation/inbox').then((m) => m.INBOX_CONVERSATION_OVERLAY),
  },
  //TODO: MEGA-2072 this path is temporary until we completely move Inbox to fullscreen in partner center, once we clean up the feature flag this can be removed
  {
    path: 'inbox',
    outlet: 'inbox',
    loadChildren: () => import('@galaxy/conversation/inbox').then((m) => m.INBOX_ROUTES),
    canMatch: [AccessFullscreenInboxGuard],
  },
  {
    path: 'inbox',
    loadChildren: () => import('@galaxy/conversation/inbox').then((m) => m.INBOX_ROUTESV2),
    canMatch: [AccessFullscreenInboxGuard],
  },
  {
    path: 'inbox/settings',
    loadChildren: () => import('@galaxy/conversation/inbox').then((m) => m.INBOX_SETTINGS_ROUTES),
  },
  {
    path: 'inbox/widgets',
    loadChildren: () => import('@galaxy/conversation/inbox').then((m) => m.INBOX_WIDGETS_ROUTES),
  },
  {
    path: 'inbox/channel/:id',
    children: [],
    canActivate: [InboxModalGuard],
  },
  {
    // This is a hidden URL for an in-development feature.
    path: 'sequences',
    loadChildren: () => import('@galaxy/sequences').then((m) => m.SequencesModule),
  },
  {
    // Note, this is a hidden URL for an in-development feature.
    // The released/legacy campaign UI is under "marketing".
    path: 'campaigns',
    loadChildren: () => import('@galaxy/campaign').then((m) => m.CampaignModule),
    providers: [
      { provide: CONFIG_TOKEN, useFactory: CampaignConfigFactory },
      {
        provide: PREVIEW_DATA_SELECTOR_COMPONENT_TOKEN,
        useValue: CampaignAccountSelectorComponent,
      },
      {
        provide: SUPPORTED_LOCALES_TOKEN,
        useFactory: (): LOCALE_DATA_TYPE => {
          return of(
            LanguageMap.filter(
              (lang) =>
                [Language.ENGLISH, Language.FRENCH_FRANCE, Language.CZECH, Language.DUTCH]
                  .map((l) => l.toString())
                  .indexOf(lang.code) > -1,
            ).map((lang) => {
              return { value: lang.code, name: lang.label };
            }),
          );
        },
      },
    ],
  },
  {
    path: 'goals',
    loadChildren: () => import('./goals/goals.module').then((m) => m.GoalsModule),
    canActivate: [AccessFeatureGuard],
    data: { feature: Feature.goals },
  },
  {
    path: 'training-resources',
    loadChildren: () =>
      import('./training-resources/partner-training-resources.module').then((m) => m.PartnerTrainingResourcesModule),
    canActivate: [SalespeopleGuard],
  },
  {
    path: 'events-and-meetings',
    loadChildren: () =>
      import('./meeting-scheduler/meeting-scheduler.module').then((m) => m.MeetingSchedulerRoutingModule),
    canActivate: [AccessFeatureGuard, AccessViewGuard],
    data: { feature: Feature.myMeetings, view: Views.salespersonAll },
  },
  {
    path: 'meetings',
    loadChildren: () => import('@galaxy/meeting-analysis').then((m) => m.MeetingAnalysisModule),
  },
  { path: '**', component: PageNotFoundComponent },
];

@NgModule({
  imports: [
    RouterModule.forRoot(
      devServer
        ? [
            {
              path: 'login',
              component: LocalLoginComponent,
              data: { noNavbar: true },
            },
            ...routes,
          ]
        : routes,
      {
        anchorScrolling: 'enabled',
        paramsInheritanceStrategy: 'always',
        bindToComponentInputs: true,
      },
    ),
  ],
  providers: [
    {
      provide: EMAIL_LIBRARY_BASE_URL_TOKEN,
      useFactory: () => {
        return of('marketing/email-library');
      },
    },
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
