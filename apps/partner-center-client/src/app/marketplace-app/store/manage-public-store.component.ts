import { Component, Inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSlideToggleChange } from '@angular/material/slide-toggle';
import { <PERSON><PERSON>anitizer, SafeHtml } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { SelectInputOption } from '@vendasta/galaxy/input';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  AppPartnerSettingsService,
  PublicStoreTheme,
  Category as SDKCategory,
  StoreService as SDKStoreService,
} from '@vendasta/marketplace-packages';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { Breadcrumbs } from '@vendasta/uikit';
import { BehaviorSubject, Observable, Subject, Subscription, combineLatest, of } from 'rxjs';
import { catchError, debounceTime, first, map, startWith, switchMap, take, withLatestFrom } from 'rxjs/operators';
import { VaDialogService } from '../../common/va-dialog.service';
import { Market } from '../../core/markets/market';
import { MarketsService } from '../../core/markets/markets.service';
import { PackageService } from '../../core/package.service';
import { PartnerService } from '../../core/partner.service';
import { Salesperson } from '../../core/salesperson';
import { SalespersonService } from '../../core/salesperson.service';
import { StoreService } from '../../core/store.service';
import { Currencies, Currency } from '../../core/types';
import { copyTextToClipboard } from '../../core/utils/clipboard';
import { StoreCategory } from './category';
import { ManageStoreService } from './manage-store.service';
import { Store } from './store';
import { StoreCurrencyDialogComponent, StoreCurrencyDialogData } from './store-currency-dialog.component';
import { ALL_MARKETS_DEFAULT } from '../../constants';

interface State {
  currencyLocked: boolean;
  isConversionRunning: boolean;
}

const CurrenciesSupportingMSRPs = ['USD', 'CAD', 'AUD'];
const SIGNIN_HEADER = 'signin-header';
const NOSIGNIN_NOHEADER = 'nosignin-noheader';
const NOSIGNIN_HEADER = 'nosignin-header';

@Component({
  templateUrl: './manage-public-store.component.html',
  styleUrls: ['manage-public-store.component.scss'],
  standalone: false,
})
export class ManagePublicStoreComponent implements OnInit, OnDestroy {
  breadcrumbs: Breadcrumbs[] = [{ url: '/marketplace/manage-store', text: 'Manage Store' }, { text: 'Public Store' }];

  state$: Observable<State>;

  markets$: Observable<Market[]>;
  showMarketSelector$: Observable<boolean>;
  private subscriptions: Subscription[] = [];

  readonly emptySalesperson = {
    name: 'No Salesperson',
    salesPersonId: null,
  } as Salesperson;
  market$: Observable<Market>;
  marketId$: Observable<string>;
  storeUrl: Observable<string>;
  salespeople: Observable<Salesperson[]>;
  selectedSalesperson: Salesperson = this.emptySalesperson;
  embedCode$: Observable<string>;
  embedCodeWithSignIn$: Observable<string>;
  embedCodeWithoutSignInWithTopBar$: Observable<string>;
  embedCodeHTML$: Observable<SafeHtml>;
  embedCodeWithCacheBreak$: Observable<string>;
  embedCodeHTMLWithCacheBreak$: Observable<SafeHtml>;
  store$: Observable<Store>;
  categories$$: BehaviorSubject<StoreCategory[]> = new BehaviorSubject([]);
  inflateCustomCategories$$: Subject<void> = new Subject();
  theme$$: BehaviorSubject<PublicStoreTheme> = new BehaviorSubject<PublicStoreTheme>(null);

  currencyLocked$$: BehaviorSubject<boolean> = new BehaviorSubject(true);
  selectedCurrency: Currency;
  currentMarketCurrency: Currency;
  conversionRate: number;
  convertMSRPs = false;
  isConversionRunning$$: BehaviorSubject<boolean> = new BehaviorSubject(false);
  refreshCurrencyData$$: BehaviorSubject<null> = new BehaviorSubject(null);
  allPackageCount$: Observable<number>;
  pageTitle = 'MARKETPLACE.MANAGE_STORE.SETTINGS.NEW_TITLE';

  currencies: Currency[] = Currencies;
  currenciesSupportingMSRPs = CurrenciesSupportingMSRPs;

  warningText =
    'Changing the currency of your Public Store will affect all product and package prices that are currently set in a different currency from the one you select here.' +
    '\nWe suggest reviewing the products and packages in your Public Store to ensure that they are priced in the intended currency before making changes to this setting.';

  embedCodeSelected: string;
  embedCodeOptions = [
    {
      label: 'Store with sign-in',
      value: SIGNIN_HEADER,
    },
    {
      label: 'Store without sign-in',
      value: NOSIGNIN_HEADER,
    },
    {
      label: 'Store without sign-in and top bar',
      value: NOSIGNIN_NOHEADER,
    },
  ] as SelectInputOption[];

  embedTooltipText =
    'These embed codes can be used as many times as you like to display your store on any website that you can edit.' +
    "\n\nCopy a code and paste it into your website's HTML editor to display your store in an iFrame on your website.";

  static sortCategories(storeCategoryOrder: string[], catA: StoreCategory, catB: StoreCategory): number {
    const catAIdx = storeCategoryOrder.indexOf(catA.categoryId);
    const catBIdx = storeCategoryOrder.indexOf(catB.categoryId);
    if (catAIdx === -1 && catBIdx !== -1) {
      return 1;
    }
    if (catBIdx === -1 && catAIdx !== -1) {
      return -1;
    }
    return catAIdx - catBIdx;
  }

  constructor(
    public storeService: StoreService,
    public marketsService: MarketsService,
    private snackbarService: SnackbarService,
    private dialogService: VaDialogService,
    private salespersonService: SalespersonService,
    private domSanitizer: DomSanitizer,
    private partnerService: PartnerService,
    @Inject('PARTNER_ID') private readonly partnerId$: Observable<string>,
    private packageService: PackageService,
    private manageStoreService: ManageStoreService,
    private sdkStoreService: SDKStoreService,
    private appPartnerService: AppPartnerSettingsService,
    private readonly snowplow: ProductAnalyticsService,
    public dialog: MatDialog,
    private translateService: TranslateService,
  ) {
    this.markets$ = this.marketsService.markets;
    this.showMarketSelector$ = combineLatest([this.marketsService.hasAccessToAllMarkets$, this.markets$]).pipe(
      map(([hasAccessToMarkets, markets]) => {
        return markets.length > 1 && hasAccessToMarkets;
      }),
    );
  }

  ngOnInit(): void {
    this.salespersonService.loadAllSalespeople();
    this.market$ = this.marketsService.currentMarket$;
    this.storeUrl = this.market$.pipe(switchMap((market) => this.storeService.getPublicStoreUrl(market.market_id)));
    this.salespeople = this.market$.pipe(switchMap((market) => this.getSalespeopleForMarket(market.market_id)));
    this.market$
      .pipe(switchMap((market) => this.storeService.getPublicStoreSalesperson(market.partner_id, market.market_id)))
      .subscribe(
        (salesperson) => {
          this.selectedSalesperson = salesperson || this.emptySalesperson;
        },
        () => {
          this.snackbarService.errorSnack("Oops, we couldn't get the Salesperson for your Public Store");
          this.selectedSalesperson = this.emptySalesperson;
        },
      );
    this.embedCodeWithSignIn$ = this.storeUrl.pipe(
      map((storeUrl) => {
        return `<iframe id="storePreview" src="${storeUrl}" width="1200" height="680" frameborder="0" style="max-width:100%;display:block;margin:0 auto;"></iframe>`;
      }),
    );
    this.embedCode$ = this.storeUrl.pipe(
      map((storeUrl) => {
        return `<iframe id="storePreview" src="${storeUrl}?hide=topbar" width="1200" height="680" frameborder="0" style="max-width:100%;display:block;margin:0 auto;"></iframe>`;
      }),
    );
    this.embedCodeWithoutSignInWithTopBar$ = this.storeUrl.pipe(
      map((storeUrl) => {
        return `<iframe id="storePreview" src="${storeUrl}?hide=signin" width="1200" height="680" frameborder="0" style="max-width:100%;display:block;margin:0 auto;"></iframe>`;
      }),
    );
    this.embedCodeHTML$ = this.embedCode$.pipe(
      map((embedCode) => {
        return this.domSanitizer.bypassSecurityTrustHtml(embedCode);
      }),
    );
    const debouncedUpdatedTheme = this.theme$$.asObservable().pipe(debounceTime(1000));
    this.embedCodeWithCacheBreak$ = combineLatest([this.storeUrl, debouncedUpdatedTheme]).pipe(
      map(([storeUrl, theme]) => {
        return `<iframe id="storePreview" src="${storeUrl}?breakCache=1${this.themeToQueryParams(
          theme,
        )}" width="1200" height="680" frameborder="0" style="max-width:100%;display:block;margin:0 auto;"></iframe>`;
      }),
    );
    this.embedCodeHTMLWithCacheBreak$ = this.embedCodeWithCacheBreak$.pipe(
      map((embedCode) => {
        return this.domSanitizer.bypassSecurityTrustHtml(embedCode);
      }),
    );
    this.marketId$ = this.market$.pipe(map((market) => (market ? market.market_id : 'default')));
    this.store$ = this.marketId$.pipe(
      switchMap((marketId) => this.manageStoreService.getStore(this.partnerService.partnerId, marketId)),
    );
    this.store$
      .pipe(
        switchMap((store) => {
          return this.manageStoreService.getCategories(store.partnerId, store.marketId).pipe(
            map((cats) =>
              cats.sort((catA, catB) => {
                return ManagePublicStoreComponent.sortCategories(store.categoryOrder, catA, catB);
              }),
            ),
          );
        }),
      )
      .subscribe((v) => {
        this.categories$$.next(v);
        this.inflateCustomCategories$$.next();
      });

    const currencySub = combineLatest([this.partnerId$, this.marketsService.currentMarket$, this.refreshCurrencyData$$])
      .pipe(
        switchMap(([partnerId, market]) => {
          return this.manageStoreService.getRetailConfiguration(partnerId, market.market_id);
        }),
      )
      .subscribe(
        (result) => {
          this.currentMarketCurrency = result.currency;
          this.selectedCurrency = result.currency;
          this.conversionRate = result.currencyConversionRate / 100;
        },
        () =>
          this.snackbarService.errorSnack(
            this.translateService.instant('MARKETPLACE.MANAGE_STORE.SETTINGS.CURRENCY.CONVERSION_RATE.FETCH_ERROR'),
          ),
      );
    this.subscriptions.push(currencySub);

    this.subscriptions.push(
      this.inflateCustomCategories$$
        .pipe(
          withLatestFrom(this.partnerId$, this.marketsService.currentMarket$),
          switchMap(([_, p, m]) => this.sdkStoreService.enableCustomCategoriesForStore(p, m.market_id)),
          catchError(() => {
            this.snackbarService.errorSnack(`Error inflating custom categories for store`);
            return of([]);
          }),
          withLatestFrom(this.partnerId$, this.marketsService.currentMarket$),
        )
        .subscribe(([cats, p, m]) => {
          // TODO: @klutzer-va this pattern of tacking on the partner and market ids will be fixed in an upcoming change to marketplace packges.
          const storeCats = (cats as SDKCategory[]).map((cat) => {
            return {
              partnerId: p,
              marketId: m.market_id,
              categoryId: cat.categoryId,
              name: cat.categoryName || '',
              packageIds: cat.packageIds || [],
              showTabInStore: !!cat.visibleInStore,
            } as StoreCategory;
          });
          this.categories$$.next(storeCats);
        }),
    );

    this.subscriptions.push(
      this.marketsService.currentMarket$.subscribe((market) => {
        this.packageService.loadAll(market.market_id, true);
      }),
    );

    this.allPackageCount$ = combineLatest([this.packageService.packages, this.marketsService.currentMarket$]).pipe(
      map(([packageMaps, market]) => {
        if (market) {
          const selectedMarketPackages = packageMaps[market.market_id];
          return selectedMarketPackages || [];
        }
        return [];
      }),
      map((packages) => packages.filter((pkg) => (pkg ? pkg.isPublished : false))),
      map((packages) => packages.length),
    );

    this.state$ = combineLatest([this.currencyLocked$$, this.isConversionRunning$$]).pipe(
      map(([currencyLocked, isConversionRunning]) => ({
        currencyLocked: currencyLocked,
        isConversionRunning: isConversionRunning,
      })),
      startWith({
        currencyLocked: true,
        conversionRateVisible: false,
        isConversionRunning: false,
      }),
    );
  }

  public salespersonSelected(salesperson: Salesperson): void {
    this.selectedSalesperson = salesperson;
  }

  public copyStoreUrl(storeUrl: string): void {
    if (copyTextToClipboard(storeUrl)) {
      this.snackbarService.successSnack(`URL for Public Store was copied to your clipboard`);
    } else {
      this.snackbarService.errorSnack('There was an issue copying the URL to your clipboard');
    }
  }

  public copyEmbedCode(embedCode: string): void {
    if (copyTextToClipboard(embedCode)) {
      this.snackbarService.successSnack(`Embed code for Public Store was copied to your clipboard`);
    } else {
      this.snackbarService.errorSnack('There was an issue copying the embed code to your clipboard');
    }
  }

  public saveSalesperson(salesperson: Salesperson, market: Market): void {
    if (salesperson === this.emptySalesperson) {
      this.confirmSaveNoSalesperson(salesperson, market);
    } else {
      this.setPublicStoreSalesPerson(salesperson, market);
    }
  }

  private confirmSaveNoSalesperson(salesperson: Salesperson, market: Market): void {
    this.dialogService
      .confirm(
        'Are You Sure?',
        'You do not have a salesperson selected for your Public Store',
        "Yes I'm Sure",
        'Cancel',
        500,
      )
      .subscribe((result) => {
        if (result === 'yes') {
          this.setPublicStoreSalesPerson(salesperson, market);
        }
      });
  }

  private setPublicStoreSalesPerson(salesperson: Salesperson, market: Market): void {
    this.storeService
      .setPublicStoreSalesperson(salesperson.salesPersonId, market.partner_id, market.market_id)
      .subscribe({
        next: () => {
          const successText =
            salesperson !== this.emptySalesperson
              ? `${salesperson.name} is now the Salesperson for the ${market.name} Public Store!`
              : `The ${market.name} Public Store has no salesperson`;
          this.snackbarService.successSnack(successText);
        },
        error: () => {
          this.snackbarService.errorSnack('Oops, there was an error setting the Salesperson for your Public Store');
        },
      });
  }

  private getSalespeopleForMarket(marketId: string): Observable<Salesperson[]> {
    return this.salespersonService.salespeople.pipe(
      map((salespeople) => {
        const marketSalespeople = salespeople.filter((salesperson) =>
          this.isSalespersonInMarket(salesperson, marketId),
        );
        return [this.emptySalesperson, ...marketSalespeople];
      }),
    );
  }

  private isSalespersonInMarket(salesperson: Salesperson, marketId: string): boolean {
    if (salesperson.marketId === '' || salesperson.marketId === ALL_MARKETS_DEFAULT) {
      return true;
    }
    return marketId ? salesperson.marketId === marketId : false;
  }

  setCurrencyLock(locked: boolean): void {
    this.selectedCurrency = this.currentMarketCurrency;
    this.currencyLocked$$.next(locked);
  }

  setIsConversionRunning(isRunning: boolean): void {
    this.isConversionRunning$$.next(isRunning);
  }

  saveSelectedCurrency(lock: boolean): void {
    combineLatest([this.partnerId$, this.marketsService.currentMarket$])
      .pipe(
        switchMap(([partnerId, market]) => {
          return this.manageStoreService.saveRetailConfiguration(
            partnerId,
            market.market_id,
            this.selectedCurrency,
            !CurrenciesSupportingMSRPs.includes(this.selectedCurrency)
              ? Math.round(this.conversionRate * 100) // need to strip decimals as somehow this ends up like 220.00000000000003
              : 0,
          );
        }),
        take(1),
      )
      .subscribe(
        () => {
          this.refreshCurrencyData$$.next(null);
          if (lock) {
            this.setCurrencyLock(true);
          }
          this.snackbarService.successSnack(
            this.translateService.instant('MARKETPLACE.MANAGE_STORE.SETTINGS.CURRENCY.CONVERSION_RATE.SUCCESS_MSG'),
          );
        },
        () =>
          this.snackbarService.errorSnack(
            this.translateService.instant('MARKETPLACE.MANAGE_STORE.SETTINGS.CURRENCY.CONVERSION_RATE.UPDATE_ERROR'),
          ),
      );
  }

  convertRetailPricesForMarket(): void {
    if (!this.conversionRate || this.conversionRate <= 0) {
      this.snackbarService.errorSnack(
        this.translateService.instant('MARKETPLACE.MANAGE_STORE.SETTINGS.CURRENCY.CONVERSION_RATE.ZERO_ERROR'),
      );
      return;
    }
    this.setIsConversionRunning(true);
    combineLatest([this.partnerId$, this.marketsService.currentMarket$])
      .pipe(
        switchMap(([partnerId, market]) => {
          return this.appPartnerService.convertRetailPricesForMarket(
            partnerId,
            market.market_id,
            this.selectedCurrency,
            this.conversionRate,
          );
        }),
        take(1),
      )
      .subscribe(
        () => {
          this.delay(5000).then(() => {
            this.setCurrencyLock(true);
            this.setIsConversionRunning(false);
            this.snackbarService.successSnack(
              this.translateService.instant(
                'MARKETPLACE.MANAGE_STORE.SETTINGS.CURRENCY.CONVERSION_PROCESS.SUCCESS_MSG',
              ),
            );
          });
        },
        () => {
          this.setIsConversionRunning(false);
          this.snackbarService.errorSnack(
            this.translateService.instant('MARKETPLACE.MANAGE_STORE.SETTINGS.CURRENCY.CONVERSION_PROCESS.START_ERROR'),
          );
        },
      );
  }
  async delay(ms: number): Promise<void> {
    await new Promise<void>((resolve) => window.setTimeout(() => resolve(), ms)).then();
  }

  changeUseCustomCategories(event: MatSlideToggleChange, marketId: string): void {
    this.subscriptions.push(
      this.manageStoreService
        .saveUseCustomCategories(this.partnerService.partnerId, marketId, event.checked)
        .pipe(
          first(),
          catchError((err) => {
            this.snackbarService.errorSnack(`Error updating custom category usage`);

            return err;
          }),
          map(() => this.snackbarService.successSnack(`Updated custom category usage`)),
        )
        .subscribe(() => {
          // FIXME this is a pretty nasty way of telling the store preview to refresh,
          // probably because the wrong component is currently responsible for doing that
          this.categories$$.next(this.categories$$.getValue());
        }),
    );
  }

  updateSelectedMarket(selectionEvent: any): void {
    this.marketsService.selectMarketByID(selectionEvent.value);
  }

  themeUpdated(theme: PublicStoreTheme): void {
    this.theme$$.next(theme);
  }

  themeToQueryParams(theme: PublicStoreTheme): string {
    if (!theme) {
      return null;
    }
    return (
      '&' +
      Object.keys(theme)
        .map((key) => `${key}=${encodeURIComponent(theme[key])}`)
        .join('&')
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }

  getCode(inputCode: string): Observable<string> {
    switch (inputCode) {
      case SIGNIN_HEADER: {
        return this.embedCodeWithSignIn$;
      }
      case NOSIGNIN_NOHEADER: {
        return this.embedCode$;
      }
      case NOSIGNIN_HEADER: {
        return this.embedCodeWithoutSignInWithTopBar$;
      }
    }
  }

  getCurrencyConfirmationDialogData$(): Observable<StoreCurrencyDialogData> {
    return combineLatest([this.markets$, this.market$]).pipe(
      map(([markets, market]) => {
        if (markets && markets.length > 1) {
          return market.name;
        }
        return null;
      }),
      map((marketName) => {
        return {
          currency: this.selectedCurrency,
          conversionRate: this.conversionRate,
          convertMSRPs: this.convertMSRPs,
          marketName: marketName,
        } as StoreCurrencyDialogData;
      }),
    );
  }

  confirmConversionRate(): void {
    const isMSRPCurrency = CurrenciesSupportingMSRPs.includes(this.selectedCurrency);
    if (!isMSRPCurrency && !!this.conversionRate && this.conversionRate > 0) {
      this.getCurrencyConfirmationDialogData$()
        .pipe(first())
        .subscribe((data) => {
          const storeCurrencyDialog = this.dialog.open(StoreCurrencyDialogComponent, {
            width: '620px',
            data,
          });
          storeCurrencyDialog
            .afterClosed()
            .pipe(first())
            .subscribe((confirmed: boolean) => {
              if (confirmed) {
                this.saveSelectedCurrency(!this.convertMSRPs);
                if (this.convertMSRPs) {
                  this.convertRetailPricesForMarket();
                }
              }
            });
        });
    } else if (isMSRPCurrency) {
      this.saveSelectedCurrency(true);
    } else {
      this.snackbarService.errorSnack(
        this.translateService.instant('MARKETPLACE.MANAGE_STORE.SETTINGS.CURRENCY.CONVERSION_RATE.ZERO_ERROR'),
      );
    }
  }

  cancel(): void {
    this.setCurrencyLock(true);
    this.refreshCurrencyData$$.next(null);
  }
}
