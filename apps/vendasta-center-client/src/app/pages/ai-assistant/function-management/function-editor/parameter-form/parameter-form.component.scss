@import 'design-tokens';

.parameter-form {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
  width: 100%;

  .fields {
    display: flex;
    gap: $spacing-3;
    align-items: flex-start;

    .name-field {
      width: 200px;
      flex-shrink: 0;
    }

    .type-field {
      width: 200px;
      flex-shrink: 0;
    }

    .type-field-array {
      width: 200px;
      flex-shrink: 0;
    }

    .description-field {
      flex-grow: 1;
    }
  }

  .nested-button {
    display: flex;
    align-items: center;
    gap: $spacing-1;
    align-self: flex-start;
  }

  .auto-fill-section {
    display: flex;
    gap: $spacing-3;
    align-items: center;

    mat-checkbox {
      margin: 0;
      min-width: 140px;
    }

    .value-field {
      flex-grow: 1;
    }
  }
}

.nested-parameters {
  margin-top: 0.5rem;
}

.parameter-count {
  margin-left: 0.5rem;
  opacity: 0.7;
}
