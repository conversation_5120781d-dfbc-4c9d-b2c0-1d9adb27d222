import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { FunctionParameterInterface, FunctionParameterParameterLocation } from '@vendasta/ai-assistants';

import { ParameterType } from '../shared/parameter.types';
import { ParameterFormComponent } from '../parameter-form/parameter-form.component';

interface DialogData {
  parameterForm: FormGroup;
  parameterTypeOptions: { value: string; label: string }[];
  parameterLocationOptions: { value: string; label: string }[];
}

@Component({
  selector: 'app-parameter-editor-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    GalaxyFormFieldModule,
    ParameterFormComponent,
  ],
  templateUrl: './parameter-editor-modal.component.html',
  styleUrls: ['./parameter-editor-modal.component.scss'],
})
export class ParameterEditorModalComponent implements OnInit {
  private readonly data = inject<DialogData>(MAT_DIALOG_DATA);
  private readonly dialogRef = inject(MatDialogRef<ParameterEditorModalComponent>);
  private readonly dialog = inject(MatDialog);

  form!: FormGroup;
  parameterTypeOptions = this.data.parameterTypeOptions;
  parameterLocationOptions = this.data.parameterLocationOptions;
  isArrayType = false;

  ngOnInit() {
    this.isArrayType = this.data.parameterForm.value.type === ParameterType.ARRAY;
    this.initForm();
  }

  private initForm() {
    const parameters = new FormArray([]);

    if (this.isArrayType) {
      const existingItem = this.data.parameterForm.value.items;
      if (existingItem) {
        parameters.push(this.createParameterFormGroup(existingItem));
      } else {
        parameters.push(this.createParameterFormGroup());
      }
    } else {
      const existingProperties = this.data.parameterForm.value.properties || [];
      if (existingProperties.length > 0) {
        existingProperties.forEach((prop: FunctionParameterInterface) => {
          parameters.push(this.createParameterFormGroup(prop));
        });
      } else {
        parameters.push(this.createParameterFormGroup());
      }
    }

    this.form = new FormGroup({
      parameters,
    });
  }

  private createParameterFormGroup(
    parameter?: FunctionParameterInterface & { autoFill?: boolean; value?: string },
  ): FormGroup {
    const hasValue = parameter?.autoFill || (parameter?.value && parameter.value.length > 0);
    // Always force location to BODY for properties and disable it
    const locationControl = new FormControl(FunctionParameterParameterLocation.LOCATION_BODY, [Validators.required]);
    // Disable the location field as it should always be BODY for properties
    locationControl.disable();

    return new FormGroup({
      name: new FormControl(parameter?.name || '', this.isArrayType ? [] : [Validators.required]),
      type: new FormControl(parameter?.type || ParameterType.STRING, [Validators.required]),
      description: new FormControl(parameter?.description || ''),
      location: locationControl,
      properties: new FormControl<FunctionParameterInterface[] | null>(parameter?.properties || null),
      items: new FormControl<FunctionParameterInterface | null>(parameter?.items || null),
      autoFill: new FormControl(hasValue),
      value: new FormControl({ value: parameter?.value || '', disabled: !hasValue }),
    });
  }

  get parametersFormArray(): FormArray {
    return this.form.get('parameters') as FormArray;
  }

  canAddParameter(): boolean {
    return !this.isArrayType || this.parametersFormArray.length === 0;
  }

  canRemoveParameter(): boolean {
    return this.parametersFormArray.length > 1;
  }

  addParameter(): void {
    if (this.canAddParameter()) {
      this.parametersFormArray.push(this.createParameterFormGroup());
    }
  }

  removeParameter(index: number): void {
    if (this.canRemoveParameter()) {
      this.parametersFormArray.removeAt(index);
    }
  }

  close(): void {
    this.dialogRef.close();
  }

  submit(): void {
    if (this.isArrayType || this.form.valid) {
      this.dialogRef.close(this.parametersFormArray.value);
    }
  }

  async editNestedParameters(parameterForm: FormGroup): Promise<void> {
    const dialogRef = this.dialog.open(ParameterEditorModalComponent, {
      width: '800px',
      data: {
        parameterForm,
        parameterTypeOptions: this.parameterTypeOptions,
        parameterLocationOptions: this.parameterLocationOptions,
      },
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (parameterForm.value.type === ParameterType.OBJECT) {
          parameterForm.patchValue({ properties: result });
        } else if (parameterForm.value.type === ParameterType.ARRAY) {
          parameterForm.patchValue({ items: result[0] });
        }
      }
    });
  }
}
