import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';

import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';

import { ParameterType } from '../shared/parameter.types';
import { FunctionParameterParameterLocation } from '@vendasta/ai-assistants';

@Component({
  selector: 'app-parameter-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatCheckboxModule,
    GalaxyFormFieldModule,
    GalaxyTooltipModule,
  ],
  templateUrl: './parameter-form.component.html',
  styleUrls: ['./parameter-form.component.scss'],
})
export class ParameterFormComponent implements OnInit {
  @Input() parameterForm!: FormGroup;
  @Input() parameterTypeOptions!: { value: string; label: string }[];
  @Input() parameterLocationOptions!: { value: string; label: string }[];
  @Input() isArrayItemEditor = false;
  @Output() editNestedParametersClicked = new EventEmitter<FormGroup>();

  readonly ParameterType = ParameterType;

  ngOnInit(): void {
    this.onLocationChange();
  }

  onTypeChange(): void {
    const type = this.parameterForm.get('type')?.value;

    if (type === ParameterType.OBJECT) {
      this.parameterForm.patchValue({
        properties: [],
        items: null,
        location: FunctionParameterParameterLocation.LOCATION_BODY,
      });
      // Lock the location to Body for object type
      this.parameterForm.get('location')?.disable();
    } else if (type === ParameterType.ARRAY) {
      this.parameterForm.patchValue({
        properties: null,
        items: null,
        location: FunctionParameterParameterLocation.LOCATION_BODY,
      });
      // Lock the location to Body for array type
      this.parameterForm.get('location')?.disable();
    } else {
      this.parameterForm.patchValue({
        properties: null,
        items: null,
        autoFill: false,
        value: '',
      });
      const valueControl = this.parameterForm.get('value');
      if (valueControl) {
        valueControl.disable();
      }
      // Re-enable location selection for non-nested types
      this.parameterForm.get('location')?.enable();
    }
  }

  hasNestedParameters(): boolean {
    const type = this.parameterForm.get('type')?.value;
    return type === ParameterType.OBJECT || type === ParameterType.ARRAY;
  }

  showAutoFill(): boolean {
    return !this.hasNestedParameters();
  }

  onAutoFillChange(): void {
    const autoFill = this.parameterForm.get('autoFill')?.value;
    const valueControl = this.parameterForm.get('value');

    if (autoFill) {
      valueControl?.enable();
    } else {
      valueControl?.disable();
      valueControl?.setValue('');
    }
  }

  onLocationChange(): void {
    const location = this.parameterForm.get('location')?.value;
    const type = this.parameterForm.get('type')?.value;

    // For nested types, always set location to Body
    if (type === ParameterType.OBJECT || type === ParameterType.ARRAY) {
      this.parameterForm.patchValue({ location: FunctionParameterParameterLocation.LOCATION_BODY });
      this.parameterForm.get('location')?.disable();
      return;
    }

    if (location === FunctionParameterParameterLocation.LOCATION_QUERY_PARAM) {
      this.parameterForm.patchValue({ type: ParameterType.STRING });
      this.parameterForm.get('type')?.disable();
    } else {
      this.parameterForm.get('type')?.enable();
    }
  }

  getNestedParametersCount(): number {
    const type = this.parameterForm.get('type')?.value;
    if (type === ParameterType.OBJECT) {
      return this.parameterForm.value.properties?.length || 0;
    } else if (type === ParameterType.ARRAY) {
      return this.parameterForm.value.items ? 1 : 0;
    }
    return 0;
  }

  getNestedParametersLabel(): string {
    const type = this.parameterForm.get('type')?.value;
    return type === ParameterType.OBJECT ? 'Properties' : 'Items';
  }
}
