import { Component, EventEmitter, Input, Output, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDialog } from '@angular/material/dialog';

import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';

import { ParameterType } from '../shared/parameter.types';
import { ParameterFormComponent } from '../parameter-form/parameter-form.component';

@Component({
  selector: 'app-parameter-editor',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    GalaxyFormFieldModule,
    GalaxyTooltipModule,
    ParameterFormComponent,
  ],
  templateUrl: './parameter-editor.component.html',
  styleUrls: ['./parameter-editor.component.scss'],
})
export class ParameterEditorComponent {
  @Input() parametersFormArray!: FormArray<FormGroup>;
  @Input() parameterTypeOptions!: { value: string; label: string }[];
  @Input() parameterLocationOptions!: { value: string; label: string }[];
  @Input() isArrayItemEditor = false;
  @Input() generatesAnswer = false;
  @Output() removeParameter = new EventEmitter<number>();

  private readonly dialog = inject(MatDialog);

  async editNestedParameters(parameterForm: FormGroup): Promise<void> {
    const { ParameterEditorModalComponent } = await import(
      '../parameter-editor-modal/parameter-editor-modal.component'
    );

    const dialogRef = this.dialog.open(ParameterEditorModalComponent, {
      width: '800px',
      data: {
        parameterForm,
        parameterTypeOptions: this.parameterTypeOptions,
        parameterLocationOptions: this.parameterLocationOptions,
      },
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (parameterForm.value.type === ParameterType.OBJECT) {
          parameterForm.patchValue({ properties: result });
        } else if (parameterForm.value.type === ParameterType.ARRAY) {
          parameterForm.patchValue({ items: result[0] });
        }
        parameterForm.markAsDirty();
      }
    });
  }
}
