@import 'design-tokens';

.parameters-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

.parameter-item {
  position: relative;
  padding: $spacing-3;
  background-color: $primary-background-color;
  border-radius: $spacing-1;

  app-parameter-form {
    display: block;
    width: calc(100% - 48px); // 48px accounts for the delete button width + some spacing
  }

  .remove-button {
    position: absolute;
    top: calc($spacing-3 + 24px); // Adjusted to account for label height
    right: $spacing-3;
  }
}
