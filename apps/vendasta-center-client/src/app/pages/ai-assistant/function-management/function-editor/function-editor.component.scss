@import 'design-tokens';

:host {
  display: block;
  width: 800px;
  max-width: 90vw;
}

.dialog-content {
  padding: 20px;
  min-height: 400px;
  max-height: 80vh;
  overflow-y: auto;
  height: calc(100vh - 300px);
  overflow: auto;
}

.content {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

glxy-form-field {
  width: 100%;
}

.code-field {
  margin-top: $spacing-3;

  textarea {
    font-family: monospace;
  }
}

.namespace {
  margin: 0;
}

.group-path,
.partner-id,
.account-group-id {
  margin: 0;
  margin-top: $spacing-1;
  color: $secondary-text-color;
}

mat-dialog-actions {
  padding: $spacing-3;
  gap: $spacing-2;
}

.url-container {
  display: flex;
  gap: $spacing-3;
  align-items: flex-start;

  .method-field {
    width: 120px;
    flex-shrink: 0;
  }

  .url-field {
    flex-grow: 1;
  }
}

h4 {
  margin: 0;
}

.add-form-container {
  display: flex;
  justify-content: flex-end;
}

.parameters-container {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
  margin-top: $spacing-3;

  .parameters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .parameters-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-3;
  }

  .parameter-item {
    display: flex;
    gap: $spacing-3;
    align-items: flex-start;
    padding: $spacing-3;
    background-color: $primary-background-color;
    border-radius: $spacing-1;

    .name-field {
      width: 200px;
      flex-shrink: 0;
    }

    .type-field {
      width: 120px;
      flex-shrink: 0;
    }

    .description-field {
      flex-grow: 1;
    }

    .remove-button {
      margin-top: $spacing-3;
    }
  }
}

.headers-container {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
  margin-top: $spacing-3;

  .headers-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .headers-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-3;
  }

  .header-item {
    display: flex;
    gap: $spacing-3;
    align-items: flex-start;
    padding: $spacing-3;
    background-color: $primary-background-color;
    border-radius: $spacing-1;

    .header-fields {
      display: flex;
      gap: $spacing-3;
      flex-grow: 1;
    }

    .header-key-field {
      width: 200px;
      flex-shrink: 0;
    }

    .header-value-field {
      flex-grow: 1;
    }

    .remove-header-button {
      margin-top: $spacing-3;
    }
  }
}

.info-icon {
  height: $spacing-3;
  width: $spacing-3;
  margin-left: $spacing-1;
  font-size: $font-preset-3-size;
  vertical-align: text-bottom;
}

.namespace-radio-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 0.5rem 0;

  .radio-option {
    display: flex;
    align-items: center;
  }
}

.no-headers-message {
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 16px;

  p {
    margin: 0;
    color: rgba(0, 0, 0, 0.6);
  }
}

.id-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.id {
  margin: 0;
}

.button-container {
  display: flex;
  gap: $spacing-2;
}

.copy-icon,
.import-icon {
  margin-right: $spacing-1;
  font-size: 16px;
  height: 16px;
  width: 16px;
}
