<div class="parameters-list">
  <div *ngFor="let parameterForm of parametersFormArray.controls; let i = index" class="parameter-item">
    <app-parameter-form
      [parameterForm]="parameterForm"
      [parameterTypeOptions]="parameterTypeOptions"
      [parameterLocationOptions]="parameterLocationOptions"
      [isArrayItemEditor]="isArrayItemEditor"
      (editNestedParametersClicked)="editNestedParameters($event)"
    ></app-parameter-form>

    <button
      *ngIf="!isArrayItemEditor || parametersFormArray.length > 1"
      mat-icon-button
      color="warn"
      type="button"
      (click)="removeParameter.emit(i)"
      class="remove-button"
      [disabled]="generatesAnswer && parameterForm.get('name')?.value === 'Output'"
      [glxyTooltip]="
        generatesAnswer && parameterForm.get('name')?.value === 'Output'
          ? 'This parameter is required when the function doesn\'t do external processing.'
          : null
      "
    >
      <mat-icon>delete</mat-icon>
    </button>
  </div>
</div>
