import { TestBed } from '@angular/core/testing';
import { MatDialogRef } from '@angular/material/dialog';
import { LexiconModule } from '@galaxy/lexicon';
import { CurlImportDialogComponent } from './curl-import-dialog.component';
import { HttpMethod, ParameterType } from './function-editor.component';
import { HttpClient } from '@angular/common/http';

describe('CurlImportDialogComponent', () => {
  let component: CurlImportDialogComponent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [LexiconModule.forRoot()],
      providers: [
        { provide: MatDialogRef, useValue: { close: jest.fn() } },
        {
          provide: HttpClient,
          useValue: {
            _HttpClient: {},
          },
        },
      ],
    });
    component = TestBed.createComponent(CurlImportDialogComponent).componentInstance;
  });

  it('parses method and url', () => {
    const curl = `curl -X POST 'https://example.com/api'`;
    const result = component.parseCurlCommand(curl);
    expect(result.methodType).toEqual(HttpMethod.POST);
    expect(result.url).toEqual('https://example.com/api');
  });

  it('parses headers', () => {
    const curl = `curl -X GET -H 'Authorization: Bearer token' -H 'Content-Type: application/json' 'https://example.com/api'`;
    const result = component.parseCurlCommand(curl);
    expect(result.headers).toEqual([
      { key: 'Authorization', value: 'Bearer token' },
      { key: 'Content-Type', value: 'application/json' },
    ]);
  });

  it('parses query params', () => {
    const curl = `curl -X GET 'https://example.com/api?foo=bar&baz=qux'`;
    const result = component.parseCurlCommand(curl);
    expect(result.queryParams).toEqual([{ name: 'foo' }, { name: 'baz' }]);
  });

  it('parses JSON body params', () => {
    const curl = `curl -X POST -d '{"id":123,"pi":3.141,"name":"test","active":true}' 'https://example.com/api'`;
    const result = component.parseCurlCommand(curl);
    expect(result.bodyParams).toEqual([
      { name: 'id', type: ParameterType.INTEGER, properties: [], item: {} },
      { name: 'pi', type: ParameterType.NUMBER, properties: [], item: {} },
      { name: 'name', type: ParameterType.STRING, properties: [], item: {} },
      { name: 'active', type: ParameterType.BOOLEAN, properties: [], item: {} },
    ]);
  });

  it('parses array body param', () => {
    const curl = `curl -X POST -d '{"tags":["a","b"]}' 'https://example.com/api'`;
    const result = component.parseCurlCommand(curl);
    expect(result.bodyParams[0].name).toEqual('tags');
    expect(result.bodyParams[0].type).toEqual(ParameterType.ARRAY);
    expect(result.bodyParams[0].item?.type).toEqual(ParameterType.STRING);
  });

  it('parses object in array body param', () => {
    const curl = `curl -X POST -d '{"items":[{"foo":1,"bar":"baz"}]}' 'https://example.com/api'`;
    const result = component.parseCurlCommand(curl);
    expect(result.bodyParams[0].name).toEqual('items');
    expect(result.bodyParams[0].type).toEqual(ParameterType.ARRAY);
    expect(result.bodyParams[0].item?.type).toEqual(ParameterType.OBJECT);
    expect(result.bodyParams[0].item?.properties?.[0]?.name).toEqual('foo');
    expect(result.bodyParams[0].item?.properties?.[1]?.name).toEqual('bar');
  });

  it('parses nested object in array body param', () => {
    const curl = `curl -X POST -d '{"items":[{"foo":{"bar":1,"baz":2,"innerArr":["a","b"],"innerObj":{"innerFoo":"bar"}}}]}' 'https://example.com/api'`;
    const result = component.parseCurlCommand(curl);
    expect(result.bodyParams[0].name).toEqual('items');
    expect(result.bodyParams[0].type).toEqual(ParameterType.ARRAY);
    expect(result.bodyParams[0].item?.type).toEqual(ParameterType.OBJECT);
    expect(result.bodyParams[0].item?.properties?.[0]?.name).toEqual('foo');
    expect(result.bodyParams[0].item?.properties?.[0]?.properties?.[0]?.name).toEqual('bar');
    expect(result.bodyParams[0].item?.properties?.[0]?.properties?.[1]?.name).toEqual('baz');
    expect(result.bodyParams[0].item?.properties?.[0]?.properties?.[2]?.name).toEqual('innerArr');
    expect(result.bodyParams[0].item?.properties?.[0]?.properties?.[2]?.type).toEqual(ParameterType.ARRAY);
    expect(result.bodyParams[0].item?.properties?.[0]?.properties?.[2]?.item?.type).toEqual(ParameterType.STRING);
    expect(result.bodyParams[0].item?.properties?.[0]?.properties?.[3]?.name).toEqual('innerObj');
    expect(result.bodyParams[0].item?.properties?.[0]?.properties?.[3]?.type).toEqual(ParameterType.OBJECT);
    expect(result.bodyParams[0].item?.properties?.[0]?.properties?.[3]?.properties?.[0]?.name).toEqual('innerFoo');
  });

  it('parses curl command with line breaks', () => {
    const curl = `curl -X GET \\
      'https://example.com/api?foo=bar&baz=qux' \\
      -H 'Authorization: Bearer token' \\
      -H 'Content-Type: application/json'`;
    const result = component.parseCurlCommand(curl);
    expect(result.methodType).toEqual(HttpMethod.GET);
    expect(result.url).toEqual('https://example.com/api');
    expect(result.headers).toEqual([
      { key: 'Authorization', value: 'Bearer token' },
      { key: 'Content-Type', value: 'application/json' },
    ]);
    expect(result.queryParams).toEqual([{ name: 'foo' }, { name: 'baz' }]);
  });

  it('fails to parse non-cURL', () => {
    const curl = `./mine_bitcoin.sh`;
    expect(() => component.parseCurlCommand(curl)).toThrow('Failed to parse cURL command');
  });

  it('fails to parse non-JSON body', () => {
    const curl = `curl -X POST -d '{"foo"}' 'https://example.com/api'`;
    expect(() => component.parseCurlCommand(curl)).toThrow('Failed to parse cURL command');
  });
});
