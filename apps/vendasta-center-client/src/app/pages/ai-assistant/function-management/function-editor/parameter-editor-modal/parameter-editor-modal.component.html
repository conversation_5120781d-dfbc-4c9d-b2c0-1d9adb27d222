<h2 mat-dialog-title>Edit {{ isArrayType ? 'Array Items' : 'Object Properties' }}</h2>

<mat-dialog-content>
  <form [formGroup]="form">
    <div formArrayName="parameters" class="parameters-list">
      <div *ngFor="let parameterForm of parametersFormArray.controls; let i = index" class="parameter-item">
        <app-parameter-form
          [parameterForm]="parameterForm"
          [parameterTypeOptions]="parameterTypeOptions"
          [parameterLocationOptions]="parameterLocationOptions"
          [isArrayItemEditor]="isArrayType"
          (editNestedParametersClicked)="editNestedParameters($event)"
        ></app-parameter-form>

        <button
          *ngIf="canRemoveParameter()"
          mat-icon-button
          color="warn"
          type="button"
          (click)="removeParameter(i)"
          class="remove-button"
        >
          <mat-icon>delete</mat-icon>
        </button>
      </div>
    </div>

    <button
      *ngIf="canAddParameter()"
      mat-flat-button
      color="primary"
      type="button"
      (click)="addParameter()"
      class="add-button"
    >
      Add {{ isArrayType ? 'Item' : 'Property' }}
    </button>
  </form>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button (click)="close()">Cancel</button>
  <button mat-flat-button color="primary" [disabled]="!form.valid" (click)="submit()">Save</button>
</mat-dialog-actions>
