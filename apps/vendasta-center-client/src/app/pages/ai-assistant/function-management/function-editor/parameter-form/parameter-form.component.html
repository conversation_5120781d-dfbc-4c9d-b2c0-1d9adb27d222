<div [formGroup]="parameterForm" class="parameter-form">
  <div class="fields">
    <glxy-form-field *ngIf="!isArrayItemEditor" class="name-field">
      <glxy-label>Name</glxy-label>
      <input type="text" matInput formControlName="name" />
    </glxy-form-field>

    <glxy-form-field class="description-field">
      <glxy-label>Description</glxy-label>
      <input type="text" matInput formControlName="description" />
    </glxy-form-field>
  </div>

  <div *ngIf="hasNestedParameters()" class="nested-parameters">
    <button
      mat-flat-button
      color="primary"
      type="button"
      (click)="editNestedParametersClicked.emit(parameterForm)"
      class="nested-button"
    >
      <mat-icon>list</mat-icon>
      {{ getNestedParametersCount() }} nested params
    </button>
  </div>

  <div class="fields">
    <glxy-form-field [class.type-field]="!isArrayItemEditor" [class.type-field-array]="isArrayItemEditor">
      <glxy-label>Type</glxy-label>
      <mat-select formControlName="type" (selectionChange)="onTypeChange()">
        <mat-option *ngFor="let option of parameterTypeOptions" [value]="option.value">
          {{ option.label }}
        </mat-option>
      </mat-select>
    </glxy-form-field>

    <glxy-form-field class="type-field">
      <glxy-label>Location</glxy-label>
      <mat-select formControlName="location" (selectionChange)="onLocationChange()">
        <mat-option *ngFor="let option of parameterLocationOptions" [value]="option.value">
          {{ option.label }}
        </mat-option>
      </mat-select>
    </glxy-form-field>
  </div>

  <div *ngIf="showAutoFill()" class="auto-fill-section">
    <mat-checkbox formControlName="autoFill" (change)="onAutoFillChange()"> Automatically filled </mat-checkbox>
    <glxy-form-field class="value-field" [disabled]="!parameterForm.get('autoFill')?.value">
      <glxy-label>Variable name</glxy-label>
      <input type="text" matInput formControlName="value" />
    </glxy-form-field>
  </div>
</div>
