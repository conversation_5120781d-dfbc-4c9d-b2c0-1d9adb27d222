@if (isEditMode()) {
  <h2 mat-dialog-title>Edit Function</h2>
} @else {
  <h2 mat-dialog-title>Create Function</h2>
}

@if (isLoading()) {
  <glxy-loading-spinner />
} @else {
  <mat-dialog-content [formGroup]="form()" class="dialog-content">
    <div class="content">
      @if (!isEditMode()) {
        <glxy-form-field>
          <glxy-label>ID</glxy-label>
          <input type="text" matInput formControlName="id" [readonly]="isEditMode()" />
        </glxy-form-field>
      } @else {
        <div class="id-container">
          <h4 class="id">ID: {{ func()?.id }}</h4>
          <div class="button-container">
            <button mat-flat-button color="primary" (click)="importFromCurl()">
              <mat-icon class="import-icon">file_upload</mat-icon>
              Import from cURL
            </button>
            <button mat-flat-button color="primary" (click)="copyCurlCommand()">
              <mat-icon class="copy-icon">content_copy</mat-icon>
              Copy to cURL
            </button>
          </div>
        </div>
      }

      @if (!isEditMode()) {
        <glxy-form-field>
          <glxy-label>Namespace</glxy-label>
          <mat-radio-group formControlName="namespaceType" class="namespace-radio-group">
            <div class="radio-option">
              <mat-radio-button [value]="'GLOBAL'"
                >Global - Available to all partner-configurable AI employees</mat-radio-button
              >
            </div>
            <div class="radio-option">
              <mat-radio-button [value]="'SYSTEM'"
                >System - Used by all system-wide applications (i.e. Support Bot)</mat-radio-button
              >
            </div>
            <div class="radio-option">
              <mat-radio-button [value]="'ACCOUNT_GROUP'"
                >Account group - Available to a specific account group</mat-radio-button
              >
            </div>
            <div class="radio-option">
              <mat-radio-button [value]="'PARTNER'">Partner - Available to a specific partner</mat-radio-button>
            </div>
          </mat-radio-group>
        </glxy-form-field>

        @if (form().get('namespaceType')?.value === 'ACCOUNT_GROUP') {
          <glxy-form-field>
            <glxy-label>Namespace - Account Group ID</glxy-label>
            <input type="text" matInput formControlName="accountGroupId" required />
          </glxy-form-field>
        }

        @if (form().get('namespaceType')?.value === 'PARTNER') {
          <glxy-form-field>
            <glxy-label>Namespace - Partner ID</glxy-label>
            <input type="text" matInput formControlName="partnerId" required />
          </glxy-form-field>
        }
      } @else {
        <h4 class="namespace">Namespace: {{ NamespaceUtils.getName(func()?.namespace) }}</h4>
        @if (func()?.namespace?.accountGroupNamespace) {
          <h4 class="account-group-id">
            Namespace - Account Group ID: {{ func()?.namespace?.accountGroupNamespace?.accountGroupId || 'N/A' }}
          </h4>
        }
        @if (func()?.namespace?.partnerNamespace) {
          <h4 class="partner-id">
            Namespace - Partner ID: {{ func()?.namespace?.partnerNamespace?.partnerId || 'N/A' }}
          </h4>
        }
      }

      <glxy-form-field>
        <glxy-label>
          Description
          <mat-icon
            [glxyTooltip]="'A short description of the function for the AI, so it knows when to call it.'"
            class="info-icon"
          >
            info_outline
          </mat-icon>
        </glxy-label>
        <input type="text" matInput formControlName="description" />
      </glxy-form-field>

      @if (!isEditMode()) {
        <div class="button-container" style="justify-content: flex-end; margin-bottom: 16px">
          <button mat-flat-button color="primary" (click)="importFromCurl()">
            <mat-icon class="import-icon">file_upload</mat-icon>
            Import from cURL
          </button>
        </div>
      }

      <div class="url-container">
        <glxy-form-field class="method-field" [disabled]="form().get('generatesAnswer')?.value">
          <glxy-label>Method</glxy-label>
          <mat-select formControlName="methodType">
            @for (option of httpMethodOptions; track option.value) {
              <mat-option [value]="option.value">{{ option.label }}</mat-option>
            }
          </mat-select>
        </glxy-form-field>

        <glxy-form-field class="url-field" [disabled]="form().get('generatesAnswer')?.value">
          <glxy-label>URL</glxy-label>
          <input type="text" matInput formControlName="url" />
        </glxy-form-field>
      </div>

      <div class="parameters-container">
        <div class="parameters-header">
          <h4>Parameters</h4>
        </div>

        <app-parameter-editor
          [parametersFormArray]="parametersFormArray"
          [parameterTypeOptions]="parameterTypeOptions"
          [parameterLocationOptions]="parameterLocationOptions"
          [generatesAnswer]="form().get('generatesAnswer')?.value"
          (removeParameter)="removeParameter($event)"
        ></app-parameter-editor>
        <div class="add-form-container">
          <button mat-flat-button color="primary" (click)="addParameter()">Add Parameter</button>
        </div>
      </div>

      @if (!form().get('generatesAnswer')?.value) {
        <div class="headers-container">
          <div class="headers-header">
            <h4>
              Headers
              <mat-icon
                [glxyTooltip]="'HTTP headers to be sent with the function request (e.g., Authorization, Content-Type)'"
                class="info-icon"
              >
                info_outline
              </mat-icon>
            </h4>
          </div>

          <div class="headers-list" formArrayName="headers">
            @if (headersFormArray.length === 0) {
              <div class="no-headers-message">
                <p>No headers added yet. Click "Add Header" to add HTTP headers for this function.</p>
              </div>
            } @else {
              @for (header of headersFormArray.controls; track header; let i = $index) {
                <div class="header-item" [formGroupName]="i">
                  <div class="header-fields">
                    <glxy-form-field class="header-key-field">
                      <glxy-label>Key</glxy-label>
                      <input type="text" matInput formControlName="key" />
                    </glxy-form-field>

                    <glxy-form-field class="header-value-field">
                      <glxy-label>Value</glxy-label>
                      <input type="text" matInput formControlName="value" />
                    </glxy-form-field>
                  </div>

                  <button mat-icon-button type="button" class="remove-header-button" (click)="removeHeader(i)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              }
            }
          </div>
          <div class="add-form-container">
            <button mat-flat-button color="primary" (click)="addHeader()">Add Header</button>
          </div>
        </div>
      }

      <glxy-form-field>
        <mat-checkbox formControlName="usePlatformManagedAuth"> Use Vendasta IAM Authentication </mat-checkbox>
      </glxy-form-field>

      @if (usePlatformManagedAuthFormControl.value) {
        <ng-container *ngIf="scopeOptions$ | async as scopeOptions; else loadingScopes">
          <glxy-form-field>
            <glxy-label>Required Scopes</glxy-label>
            <mat-select formControlName="platformManagedAuthRequiredScopes" multiple>
              <mat-option *ngFor="let scopeOption of scopeOptions" [value]="scopeOption">
                {{ scopeOption }}
              </mat-option>
            </mat-select>
          </glxy-form-field>
        </ng-container>
        <ng-template #loadingScopes>Loading scope options...</ng-template>
      }

      <glxy-form-field>
        <mat-checkbox formControlName="generatesAnswer"
          >This function only helps the AI format its response (no external processing needed).</mat-checkbox
        >
      </glxy-form-field>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="close()">Cancel</button>
    <button mat-flat-button color="primary" (click)="submit()" [disabled]="!form()?.valid || !form()?.dirty">
      Save
    </button>
  </mat-dialog-actions>
}
