import { TestBed } from '@angular/core/testing';
import { Clipboard } from '@angular/cdk/clipboard';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FunctionEditorComponent, ParameterType, HttpMethod } from './function-editor.component';
import { FormControl, FormGroup, FormArray } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { LexiconModule } from '@galaxy/lexicon';
import { ConsentAdminApiService } from '@vendasta/sso';
import { of } from 'rxjs';
import { FunctionParameterParameterLocation } from '@vendasta/ai-assistants';

describe('FunctionEditorComponent', () => {
  let component: FunctionEditorComponent;
  let clipboardSpy: { copy: jest.Mock };

  beforeEach(() => {
    clipboardSpy = { copy: jest.fn() };

    TestBed.configureTestingModule({
      imports: [LexiconModule.forRoot()],
      providers: [
        { provide: Clipboard, useValue: clipboardSpy },
        { provide: MatDialogRef, useValue: {} },
        { provide: MAT_DIALOG_DATA, useValue: {} },
        {
          provide: HttpClient,
          useValue: {
            _HttpClient: {},
          },
        },
        {
          provide: ConsentAdminApiService,
          useValue: {
            listScopes: jest.fn().mockReturnValue(of({ scopes: [] })),
          },
        },
      ],
    });
    component = TestBed.createComponent(FunctionEditorComponent).componentInstance;

    // Minimal form setup
    component.form.set(
      new FormGroup({
        id: new FormControl('test-id'),
        description: new FormControl('desc'),
        namespaceType: new FormControl('GLOBAL'),
        groupPath: new FormControl(''),
        partnerId: new FormControl(''),
        accountGroupId: new FormControl(''),
        methodType: new FormControl(HttpMethod.POST),
        url: new FormControl('https://example.com/api'),
        parameters: new FormArray([]),
        headers: new FormArray([]),
        generatesAnswer: new FormControl(false),
        usePlatformManagedAuth: new FormControl(false),
        platformManagedAuthRequiredScopes: new FormControl([]),
      }),
    );
  });

  it('copies cURL command with no params or headers', () => {
    component.func.set({
      id: 'test-id',
      description: 'desc',
      namespace: {},
      methodType: HttpMethod.POST,
      url: 'https://example.com/api',
      functionParameters: [],
      generatesAnswer: false,
    } as any);
    component.copyCurlCommand();
    expect(clipboardSpy.copy).toHaveBeenCalledWith(`curl -X POST 'https://example.com/api'`);
  });

  it('copies cURL command with headers and parameters', () => {
    // Add a header
    (component.headersFormArray as FormArray).push(
      new FormGroup({
        key: new FormControl('Authorization'),
        value: new FormControl('Bearer token'),
      }),
    );
    // Add a query parameter
    (component.parametersFormArray as FormArray).push(
      new FormGroup({
        name: new FormControl('foo'),
        type: new FormControl(ParameterType.STRING),
        location: new FormControl(FunctionParameterParameterLocation.LOCATION_QUERY_PARAM),
        description: new FormControl(''),
        value: new FormControl(''),
        properties: new FormControl(null),
        items: new FormControl(null),
        autoFill: new FormControl(false),
      }),
    );
    // Add a body parameter
    (component.parametersFormArray as FormArray).push(
      new FormGroup({
        name: new FormControl('bar'),
        type: new FormControl(ParameterType.INTEGER),
        location: new FormControl(FunctionParameterParameterLocation.LOCATION_BODY),
        description: new FormControl(''),
        value: new FormControl(''),
        properties: new FormControl(null),
        items: new FormControl(null),
        autoFill: new FormControl(false),
      }),
    );
    component.func.set({
      id: 'test-id',
      description: 'desc',
      namespace: {},
      methodType: HttpMethod.POST,
      url: 'https://example.com/api',
      functionParameters: [],
      generatesAnswer: false,
    } as any);
    component.copyCurlCommand();
    expect(clipboardSpy.copy).toHaveBeenCalledWith(
      `curl -X POST -H 'Authorization: Bearer token' -d '{"bar":0}' 'https://example.com/api?foo='`,
    );
  });

  it('copies cURL command with headers, nested parameters, array parameters, query parameters', () => {
    // Add 2 headers
    (component.headersFormArray as FormArray).push(
      new FormGroup({
        key: new FormControl('Authorization'),
        value: new FormControl('Bearer token'),
      }),
    );
    (component.headersFormArray as FormArray).push(
      new FormGroup({
        key: new FormControl('Content-Type'),
        value: new FormControl('application/json'),
      }),
    );
    // Add a query parameter
    (component.parametersFormArray as FormArray).push(
      new FormGroup({
        name: new FormControl('foo'),
        type: new FormControl(ParameterType.STRING),
        location: new FormControl(FunctionParameterParameterLocation.LOCATION_QUERY_PARAM),
        description: new FormControl(''),
        value: new FormControl(''),
        properties: new FormControl(null),
        items: new FormControl(null),
        autoFill: new FormControl(false),
      }),
    );
    // Add a body parameter
    (component.parametersFormArray as FormArray).push(
      new FormGroup({
        name: new FormControl('bar'),
        type: new FormControl(ParameterType.INTEGER),
        location: new FormControl(FunctionParameterParameterLocation.LOCATION_BODY),
        description: new FormControl(''),
        value: new FormControl(''),
        properties: new FormControl(null),
        items: new FormControl(null),
        autoFill: new FormControl(false),
      }),
    );
    // Add a nested body parameter
    (component.parametersFormArray as FormArray).push(
      new FormGroup({
        name: new FormControl('nestedObj'),
        type: new FormControl(ParameterType.OBJECT),
        location: new FormControl(FunctionParameterParameterLocation.LOCATION_BODY),
        description: new FormControl(''),
        value: new FormControl(''),
        properties: new FormControl([
          { name: 'field1', type: ParameterType.STRING },
          { name: 'field2', type: ParameterType.STRING },
        ]),
        items: new FormControl(null),
        autoFill: new FormControl(false),
      }),
    );
    (component.parametersFormArray as FormArray).push(
      new FormGroup({
        name: new FormControl('arrayParam'),
        type: new FormControl(ParameterType.ARRAY),
        location: new FormControl(FunctionParameterParameterLocation.LOCATION_BODY),
        description: new FormControl(''),
        value: new FormControl(''),
        properties: new FormControl(null),
        items: new FormControl({
          type: ParameterType.OBJECT,
          properties: [{ name: 'something', type: ParameterType.STRING }],
        }),
        autoFill: new FormControl(false),
      }),
    );
    component.func.set({
      id: 'test-id',
      description: 'desc',
      namespace: {},
      methodType: HttpMethod.POST,
      url: 'https://example.com/api',
      functionParameters: [],
      generatesAnswer: false,
    } as any);
    component.copyCurlCommand();
    expect(clipboardSpy.copy).toHaveBeenCalledWith(
      `curl -X POST -H 'Authorization: Bearer token' -H 'Content-Type: application/json' -d '{"bar":0,"nestedObj":{"field1":"","field2":""},"arrayParam":[{"something":""}]}' 'https://example.com/api?foo='`,
    );
  });
});
