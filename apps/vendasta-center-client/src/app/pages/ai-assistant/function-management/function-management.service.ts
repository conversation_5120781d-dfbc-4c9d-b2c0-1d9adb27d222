import { HttpResponse } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { FunctionApiService, Namespace, FunctionInterface, NamespaceInterface } from '@vendasta/ai-assistants';
import { firstValueFrom } from 'rxjs';

export interface FunctionFilters {
  namespaceType?: string;
  namespaceId?: string;
}

@Injectable()
export class FunctionManagementService {
  private readonly functionApiService = inject(FunctionApiService);

  async getFunction(id: string, namespace: Namespace): Promise<FunctionInterface> {
    const response = await firstValueFrom(
      this.functionApiService.get({
        id: id,
        namespace: namespace,
      }),
    );
    return response.function;
  }

  async listFunctions(namespace?: NamespaceInterface): Promise<FunctionInterface[]> {
    let namespaces = [namespace];
    if (!namespace) {
      namespaces = [
        new Namespace({ globalNamespace: {} }),
        new Namespace({ systemNamespace: {} }),
        new Namespace({ accountGroupNamespace: { accountGroupId: null } }),
        new Namespace({ partnerNamespace: { partnerId: null } }),
      ];
    }

    // Query each namespace
    const allFunctions: FunctionInterface[] = [];
    for (const namespace of namespaces) {
      const response = await firstValueFrom(
        this.functionApiService.list({
          filters: {
            namespace: namespace,
          },
          pagingOptions: {
            pageSize: 100,
          },
        }),
      );
      allFunctions.push(...(response.functions ?? []));
    }
    return allFunctions;
  }

  async upsertFunction(func: FunctionInterface): Promise<HttpResponse<null>> {
    return await firstValueFrom(
      this.functionApiService.upsert({
        function: {
          ...func,
        },
      }),
    );
  }
}
