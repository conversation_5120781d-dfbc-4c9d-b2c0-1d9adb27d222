import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';
import { firstValueFrom } from 'rxjs';

import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';

import { FunctionInterface, Namespace, NamespaceInterface } from '@vendasta/ai-assistants';

import { FunctionManagementService } from './function-management.service';
import { FunctionEditorComponent } from './function-editor/function-editor.component';
import { NamespaceUtils } from '../shared/utils';
import { NamespaceSelectorComponent } from '../namespace-selector/namespace-selector.component';

enum FilterNamespaceType {
  ALL = 'ALL',
  GLOBAL = 'GLOBAL',
  SYSTEM = 'SYSTEM',
  ACCOUNT_GROUP = 'ACCOUNT_GROUP',
  PARTNER = 'PARTNER',
}

@Component({
  selector: 'app-function-management',
  standalone: true,
  imports: [
    CommonModule,
    DatePipe,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatListModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatIconModule,
    GalaxyAlertModule,
    GalaxyLoadingSpinnerModule,
    GalaxyPageModule,
    GalaxyFormFieldModule,
    NamespaceSelectorComponent,
  ],
  providers: [FunctionManagementService],
  templateUrl: './function-management.component.html',
  styleUrls: ['./function-management.component.scss'],
})
export class FunctionManagementComponent implements OnInit {
  private readonly functionManagementService = inject(FunctionManagementService);
  private readonly dialog = inject(MatDialog);
  private readonly activeRoute = inject(ActivatedRoute);
  private readonly router = inject(Router);

  protected readonly NamespaceUtils = NamespaceUtils;
  protected readonly FilterNamespaceType = FilterNamespaceType;

  functions: FunctionInterface[] = [];
  filteredFunctions: FunctionInterface[] = [];
  isLoading = true;

  ngOnInit() {
    const editParam = this.activeRoute.snapshot.queryParamMap.get('edit');
    const namespaceTypeParam = this.activeRoute.snapshot.queryParamMap.get('namespaceType');
    const accountGroupIdParam = this.activeRoute.snapshot.queryParamMap.get('accountGroupId');
    const partnerIdParam = this.activeRoute.snapshot.queryParamMap.get('partnerId');

    if (editParam) {
      let namespace;

      if (namespaceTypeParam === 'Global') {
        namespace = new Namespace({ globalNamespace: {} });
      } else if (namespaceTypeParam === 'System') {
        namespace = new Namespace({ systemNamespace: {} });
      } else if (namespaceTypeParam === 'Account Group' && accountGroupIdParam) {
        namespace = new Namespace({
          accountGroupNamespace: { accountGroupId: accountGroupIdParam },
        });
      } else if (namespaceTypeParam === 'Partner' && partnerIdParam) {
        namespace = new Namespace({
          partnerNamespace: { partnerId: partnerIdParam },
        });
      } else {
        namespace = new Namespace({ globalNamespace: {} }); // Default to global
      }

      this.editFunction(editParam, namespace);
    }

    this.refreshFunctions();
  }

  protected async refreshFunctions(namespace?: NamespaceInterface) {
    this.isLoading = true;
    this.functions = await this.functionManagementService.listFunctions(namespace);
    this.filteredFunctions = this.functions;
    this.isLoading = false;
  }

  async editFunction(functionId: string = null, namespace: NamespaceInterface = null) {
    const dialogRef = this.dialog.open(FunctionEditorComponent, {
      data: {
        functionId,
        namespace,
      },
      disableClose: true,
    });

    await this.setQueryParam(functionId, namespace);
    await firstValueFrom(dialogRef.afterClosed());
    await this.setQueryParam(null, null);
    await this.refreshFunctions(namespace);
  }

  async createNewFunction() {
    const dialogRef = this.dialog.open(FunctionEditorComponent, {
      disableClose: true,
    });

    await firstValueFrom(dialogRef.afterClosed());
    await this.refreshFunctions();
  }

  async setQueryParam(functionId: string, namespace: NamespaceInterface) {
    const queryParams: any = {
      edit: null,
      namespaceType: null,
      accountGroupId: null,
      partnerId: null,
    };

    if (functionId && namespace) {
      queryParams.edit = functionId;

      if (namespace.globalNamespace) {
        queryParams.namespaceType = 'Global';
      } else if (namespace.systemNamespace) {
        queryParams.namespaceType = 'System';
      } else if (namespace.accountGroupNamespace) {
        queryParams.namespaceType = 'Account Group';
        queryParams.accountGroupId = namespace.accountGroupNamespace.accountGroupId;
      } else if (namespace.partnerNamespace) {
        queryParams.namespaceType = 'Partner';
        queryParams.partnerId = namespace.partnerNamespace.partnerId;
      }
    }

    await this.router.navigate([], {
      relativeTo: this.activeRoute,
      queryParams,
      queryParamsHandling: 'merge',
    });
  }
}
