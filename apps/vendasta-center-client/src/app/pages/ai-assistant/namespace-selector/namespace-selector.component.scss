@use 'design-tokens' as *;

.filter-container {
  margin: $spacing-3 0;
  padding: $spacing-3;
  background-color: white;
  border: 1px solid $border-color;
  border-radius: $spacing-1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-3;
    align-items: center;
  }

  .namespace-type-filter {
    min-width: 250px;
    max-width: 400px;
    width: 100%;
  }

  .namespace-id-filter {
    min-width: 250px;
    flex: 1;
  }

  button {
    margin-top: $spacing-1;
  }
}
