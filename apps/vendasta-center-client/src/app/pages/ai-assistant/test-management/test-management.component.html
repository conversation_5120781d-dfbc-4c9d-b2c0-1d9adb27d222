<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-nav>
      <glxy-page-nav-button useHistory="true"></glxy-page-nav-button>
    </glxy-page-nav>
    @if (assistant$ | async; as assistant) {
      <glxy-page-title>
        <span>{{ assistant | assistant<PERSON><PERSON> }} - Testing</span>
      </glxy-page-title>
    }
  </glxy-page-toolbar>

  <glxy-page-wrapper widthPreset="default">
    <div class="row row-gutters">
      <div class="col col-xs-12">
        <div class="content">
          <mat-card>
            <mat-card-header>
              <mat-card-title>Upload JSON</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="actions">
                <button mat-raised-button color="primary" (click)="openUploadDialog()">Upload Test Cases</button>
                <button mat-button (click)="downloadJSONTemplate()">
                  <mat-icon>download</mat-icon>
                  Download Template
                </button>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>
    @if (assistantKey) {
      <div class="row row-gutters">
        <div class="col col-xs-12">
          <app-test-case-display
            (runTests)="runTests($event)"
            [assistantKey]="assistantKey"
            #table
          ></app-test-case-display>
        </div>
      </div>
    }

    <div class="row row-gutters">
      <div class="col col-xs-12">
        <div class="actions">
          <button mat-raised-button color="primary" (click)="runTests()">Run All Tests</button>
          @if (testRunLoading) {
            <glxy-loading-spinner fullWidth="false" size="small"></glxy-loading-spinner>
          } @else if (testResultsLink) {
            <a [href]="testResultsLink" target="_blank">View Test Run</a>
          }
        </div>
      </div>
    </div>

    @if (assistantKey) {
      <div class="row row-gutters">
        <div class="col col-xs-12">
          <app-test-run-table [assistantKey]="assistantKey" #testRunTable></app-test-run-table>
        </div>
      </div>
    }
  </glxy-page-wrapper>
</glxy-page>
