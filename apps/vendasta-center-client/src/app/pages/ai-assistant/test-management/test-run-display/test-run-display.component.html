<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-nav>
      <glxy-page-nav-button useHistory="true"></glxy-page-nav-button>
    </glxy-page-nav>
    @if (assistant$ | async; as assistant) {
      <glxy-page-title>
        <span>{{ assistant | assistant<PERSON><PERSON> }} - Test Run</span>
      </glxy-page-title>
    }
  </glxy-page-toolbar>
  <glxy-page-wrapper widthPreset="wide">
    <mat-card>
      <mat-card-header>
        @if (testRun$ | async; as testRun) {
          <mat-card-title>
            <div class="title">
              <span>Test Results - {{ testRun.created | date: 'medium' }}</span>
              @if (statusMap.get(testRun.status); as status) {
                <div class="status" [ngClass]="status.class">
                  <mat-icon>{{ status.icon }}</mat-icon
                  >{{ status.name }}
                </div>
              }
              <div>{{ getPassedCount(testRun) }} Tests Passed</div>
              @if (testRun.status === 'in_progress') {
                <button mat-stroked-button (click)="cancelTestRun()" class="refresh-button">Cancel Run</button>
              }
            </div>
          </mat-card-title>
        }
      </mat-card-header>
      <mat-card-content>
        <glxy-table-container
          class="simple-table"
          [dataSource]="dataSource"
          [columns]="columns"
          [pageSizeOptions]="[10, 20, 30]"
          [pageSize]="10"
          [border]="false"
          [fullWidth]="false"
        >
          <glxy-table-content-header
            showFilters="false"
            showFiltersButton="false"
            showSort="false"
          ></glxy-table-content-header>
          <table mat-table matSort (matSortChange)="sortData($event)">
            <tr mat-header-row *matHeaderRowDef="[]"></tr>

            <ng-container matColumnDef="select">
              <th mat-header-cell *matHeaderCellDef>
                <glxy-table-selection />
              </th>
              <td mat-cell *matCellDef="let row">
                <glxy-table-selection [row]="row" />
              </td>
            </ng-container>

            <ng-container matColumnDef="name">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
              <td mat-cell *matCellDef="let element">
                <a (click)="openDialog(element)">{{ element.name }}</a>
              </td>
            </ng-container>

            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef>Status</th>
              <td mat-cell *matCellDef="let element">
                @if (statusMap.get(element.status); as status) {
                  <div class="status" [ngClass]="status.class">
                    <mat-icon>{{ status.icon }}</mat-icon
                    >{{ status.name }}
                  </div>
                }
              </td>
            </ng-container>

            <ng-container matColumnDef="score">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Score</th>
              <td mat-cell *matCellDef="let element">
                {{ element.score || 0 | percent }}
              </td>
            </ng-container>

            <tr mat-row *matRowDef="let row; columns: []"></tr>
          </table>
        </glxy-table-container>
      </mat-card-content>
    </mat-card>
  </glxy-page-wrapper>
</glxy-page>
