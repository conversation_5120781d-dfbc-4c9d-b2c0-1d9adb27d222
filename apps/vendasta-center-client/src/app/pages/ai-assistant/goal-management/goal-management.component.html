<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-title>AI Goals</glxy-page-title>
    <glxy-page-actions>
      <button mat-flat-button color="primary" (click)="createNewGoal()">Create</button>
    </glxy-page-actions>
  </glxy-page-toolbar>
  <glxy-page-wrapper>
    <mat-card>
      <mat-card-header>
        <mat-card-title>Manage goals</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <glxy-alert type="warning">
          <strong>Warning</strong>
          &mdash; Every goal here is likely being used by one or more assistants. Editing goals may have unintended
          consequences. Please proceed with caution.
        </glxy-alert>
        <glxy-alert type="info">
          View the official documentation to learn more about
          <a href="https://docs.vendasta-internal.com/ai-assistants/guides/managing-goals/" target="_blank"
            >managing goals<mat-icon class="half-size-icon">open_in_new</mat-icon></a
          >.
        </glxy-alert>

        <app-namespace-selector (selected)="refreshGoals($event)"></app-namespace-selector>

        <div>
          @if (isLoading) {
            <div class="loading-container">
              <glxy-loading-spinner />
              <div class="loading-text">Loading goals...</div>
            </div>
          } @else {
            <mat-list>
              @if (filteredGoals.length === 0) {
                <mat-list-item>
                  <div class="goal">
                    <div class="text">No goals found</div>
                  </div>
                </mat-list-item>
              } @else {
                @for (goal of filteredGoals; track goal) {
                  <mat-list-item>
                    <div class="goal">
                      <div class="title">
                        <a (click)="editGoal(goal.id, goal.namespace)"
                          >[{{ NamespaceUtils.getName(goal.namespace) }}] {{ goal.name }}</a
                        >
                      </div>
                      <div class="description">{{ goal.description }}</div>
                      <div class="last-updated">
                        Last updated on {{ goal.updated | date: 'YYYY-MM-dd h:mm a' }} by {{ goal.updatedByFullName }}
                      </div>
                    </div>
                  </mat-list-item>
                }
              }
            </mat-list>
          }
        </div>
      </mat-card-content>
    </mat-card>
  </glxy-page-wrapper>
</glxy-page>
