import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatListModule } from '@angular/material/list';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';

import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { Namespace, NamespaceInterface } from '@vendasta/ai-assistants';

import { firstValueFrom } from 'rxjs';

import { GoalManagementService } from './goal-management.service';
import { Goal } from './goal';
import { GoalEditorComponent } from './goal-editor/goal-editor.component';
import { NamespaceType, NamespaceUtils } from '../shared/utils';
import { NamespaceSelectorComponent } from '../namespace-selector/namespace-selector.component';

@Component({
  selector: 'app-goal-management',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatDialogModule,
    MatListModule,
    MatSelectModule,
    MatInputModule,
    MatIconModule,
    GalaxyPageModule,
    GalaxyLoadingSpinnerModule,
    GalaxyAlertModule,
    GalaxyFormFieldModule,
    NamespaceSelectorComponent,
  ],
  templateUrl: './goal-management.component.html',
  styleUrls: ['./goal-management.component.scss'],
  providers: [GoalManagementService],
})
export class GoalManagementComponent implements OnInit {
  private readonly dialog = inject(MatDialog);
  private readonly goalManagementService = inject(GoalManagementService);
  private readonly activeRoute = inject(ActivatedRoute);
  private readonly router = inject(Router);

  protected readonly NamespaceUtils = NamespaceUtils;
  protected readonly NamespaceType = NamespaceType;

  isLoading = false;
  goals: Goal[] = [];
  filteredGoals: Goal[] = [];

  ngOnInit() {
    const editParam = this.activeRoute.snapshot.queryParamMap.get('edit');
    const namespaceTypeParam = this.activeRoute.snapshot.queryParamMap.get('namespaceType');
    const accountGroupIdParam = this.activeRoute.snapshot.queryParamMap.get('accountGroupId');
    const partnerIdParam = this.activeRoute.snapshot.queryParamMap.get('partnerId');

    if (editParam) {
      let namespace;

      if (namespaceTypeParam === 'Global') {
        namespace = new Namespace({ globalNamespace: {} });
      } else if (namespaceTypeParam === 'System') {
        namespace = new Namespace({ systemNamespace: {} });
      } else if (namespaceTypeParam === 'Account Group' && accountGroupIdParam) {
        namespace = new Namespace({
          accountGroupNamespace: { accountGroupId: accountGroupIdParam },
        });
      } else if (namespaceTypeParam === 'Partner' && partnerIdParam) {
        namespace = new Namespace({
          partnerNamespace: { partnerId: partnerIdParam },
        });
      } else {
        namespace = new Namespace({ globalNamespace: {} }); // Default to global
      }

      this.editGoal(editParam, namespace);
    }

    this.refreshGoals();
  }

  createNewGoal(): void {
    const dialogRef = this.dialog.open(GoalEditorComponent, {
      data: {
        namespace: new Namespace({ globalNamespace: {} }),
      },
    });

    dialogRef.afterClosed().subscribe(() => {
      this.refreshGoals();
    });
  }

  async editGoal(goalId: string, namespace: NamespaceInterface = null): Promise<void> {
    const dialogRef = this.dialog.open(GoalEditorComponent, {
      data: {
        goalId,
        namespace: namespace || new Namespace({ globalNamespace: {} }),
      },
    });

    await this.setQueryParam(goalId, namespace || new Namespace({ globalNamespace: {} }));
    await firstValueFrom(dialogRef.afterClosed());
    await this.setQueryParam(null, null);
    await this.refreshGoals();
  }

  private async setQueryParam(goalId: string, namespace: NamespaceInterface) {
    const queryParams: any = {
      edit: null,
      namespaceType: null,
      accountGroupId: null,
      partnerId: null,
    };

    if (goalId && namespace) {
      queryParams.edit = goalId;

      if (namespace.globalNamespace) {
        queryParams.namespaceType = 'Global';
      } else if (namespace.systemNamespace) {
        queryParams.namespaceType = 'System';
      } else if (namespace.accountGroupNamespace) {
        queryParams.namespaceType = 'Account Group';
        queryParams.accountGroupId = namespace.accountGroupNamespace.accountGroupId;
      } else if (namespace.partnerNamespace) {
        queryParams.namespaceType = 'Partner';
        queryParams.partnerId = namespace.partnerNamespace.partnerId;
      }
    }

    await this.router.navigate([], {
      relativeTo: this.activeRoute,
      queryParams,
      queryParamsHandling: 'merge',
    });
  }

  protected async refreshGoals(namespace?: NamespaceInterface): Promise<void> {
    this.isLoading = true;
    try {
      this.goals = await this.goalManagementService.listGoals(namespace);
      this.filteredGoals = this.goals;
    } finally {
      this.isLoading = false;
    }
  }
}
