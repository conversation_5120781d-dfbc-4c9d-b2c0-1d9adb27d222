@import 'design-tokens';

mat-card {
  padding: $spacing-3;
}

mat-list-item {
  height: auto !important;
  margin-bottom: $spacing-2;
  margin-top: $spacing-2;
}

.goal {
  display: flex;
  flex-direction: column;
  text-align: justify;
}

.title {
  font-size: $font-preset-3-size;
  font-weight: bold;
  margin-bottom: $spacing-1;

  a {
    color: $primary-color;
    cursor: pointer;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.description {
  margin-bottom: $spacing-1;
  text-wrap: auto;
}

.last-updated {
  margin: 0;
  display: flex;
  align-items: center;
  margin-top: $spacing-1;
  color: $glxy-grey-500;
  font-style: italic;
}

.text {
  color: $secondary-text-color;
}

.goals-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

.goal-item {
  padding: $spacing-4;
  border: 1px solid $border-color;
  border-radius: $default-border-radius;
  background-color: $card-background-color;

  &:hover {
    border-color: $primary-color;
  }
}

.goal-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
}

.goal-name {
  font-size: 16px;
  font-weight: 500;
  color: $primary-color;
  cursor: pointer;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

.goal-description {
  color: $primary-text-color;
  font-size: 14px;
  line-height: 1.5;
}

.goal-meta {
  color: $secondary-text-color;
  font-size: 12px;
}

.no-goals {
  color: $secondary-text-color;
  font-size: 14px;
  padding: $spacing-4;
  text-align: center;
}

.filter-container {
  margin: $spacing-3 0;
  padding: $spacing-3;
  background-color: white;
  border: 1px solid $border-color;
  border-radius: $spacing-1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-3;
    align-items: flex-start;
  }

  .namespace-type-filter {
    min-width: 250px;
    max-width: 400px;
    width: 100%;
  }

  .namespace-id-filter {
    min-width: 250px;
    flex: 1;
  }
}

glxy-form-field {
  width: 100%;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
}

.loading-text {
  color: var(--glxy-text-secondary);
  font-size: 1rem;
}

.half-size-icon {
  transform: scale(0.5);
}
