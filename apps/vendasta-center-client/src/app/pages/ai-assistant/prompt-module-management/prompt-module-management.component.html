<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-title>AI Prompt Modules</glxy-page-title>
    <glxy-page-actions>
      <button mat-flat-button color="primary" (click)="createNewPromptModule()">Create</button>
    </glxy-page-actions>
  </glxy-page-toolbar>

  <glxy-page-wrapper>
    <mat-card>
      <mat-card-header>
        <mat-card-title>Manage prompt modules</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <glxy-alert type="warning">
          <strong>Warning</strong>
          &mdash; Every prompt here is likely being used by one or more applications. Editing prompts may have
          unintended consequences. Please proceed with caution.
          <br /><br />
          Prompts in the demo and prod environments should be <b>identical</b> - except when testing a new prompt on
          demo. If you edit a prompt on demo, you should also edit it on prod, and vice-versa.
        </glxy-alert>
        <glxy-alert type="info">
          View the official documentation to learn more about
          <a href="https://docs.vendasta-internal.com/ai-assistants/guides/managing-prompts/" target="_blank"
            >managing prompts<mat-icon class="half-size-icon">open_in_new</mat-icon></a
          >
          and
          <a href="https://docs.vendasta-internal.com/ai-assistants/guides/writing-prompts/" target="_blank"
            >prompt design best practices<mat-icon class="half-size-icon">open_in_new</mat-icon></a
          >.
        </glxy-alert>

        <app-namespace-selector (selected)="refreshPrompts($event)"></app-namespace-selector>
        <!-- Filter controls -->

        <div>
          @if (isLoading) {
            <div class="loading-container">
              <glxy-loading-spinner />
              <div class="loading-text">Loading prompts...</div>
            </div>
          } @else {
            <mat-list>
              @if (filteredPrompts.length === 0) {
                <mat-list-item>
                  <div class="prompt">
                    <div class="text">No prompts found</div>
                  </div>
                </mat-list-item>
              } @else {
                @for (prompt of filteredPrompts; track prompt.id) {
                  <mat-list-item>
                    <div class="prompt">
                      <div class="title">
                        <a (click)="editPromptModule(prompt.id, prompt.namespace)"
                          >[{{ NamespaceUtils.getName(prompt.namespace) }}] {{ prompt.name }}</a
                        >
                      </div>
                      <div class="description">{{ prompt.description }}</div>
                      <div class="instructions-label">Instructions:</div>
                      <div class="text">{{ prompt.content }}</div>
                      <div class="last-updated">
                        Last updated on {{ prompt.lastModifiedAt | date: 'YYYY-MM-dd h:mm a' }} by
                        {{ prompt.createdByFullName }}
                        <br />
                        Deployed on {{ prompt.deployedAt | date: 'YYYY-MM-dd h:mm a' }} by
                        {{ prompt.deployedByFullName }}
                      </div>
                    </div>
                  </mat-list-item>
                  <mat-divider />
                }
              }
            </mat-list>
          }
        </div>
      </mat-card-content>
    </mat-card>
  </glxy-page-wrapper>
</glxy-page>
