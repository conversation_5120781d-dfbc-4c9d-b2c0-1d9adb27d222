@use 'design-tokens' as *;

.aieo-nav-tabs {
  display: flex;
  gap: $spacing-2;
  margin-bottom: $spacing-4;
  padding: 0 $spacing-3;
  border-bottom: 1px solid $border-color;
  background: $card-background-color;
  border-radius: $default-border-radius $default-border-radius 0 0;
  box-shadow: 0 $spacing-1 $spacing-2 rgba(0, 0, 0, 0.05);

  .nav-tab {
    padding: $spacing-3 $spacing-4;
    border-radius: $default-border-radius $default-border-radius 0 0;
    border: 1px solid transparent;
    border-bottom: none;
    background: transparent;
    color: $secondary-text-color;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    position: relative;
    overflow: hidden;

    mat-icon {
      margin-right: $spacing-2;
      transition: transform 0.3s ease;
    }

    &:hover {
      background: $secondary-background-color;
      color: $primary-text-color;
      transform: translateY($negative-1);

      mat-icon {
        transform: scale(1.1);
      }
    }

    &.active {
      background: $card-background-color;
      color: $primary-color;
      border-color: $border-color;
      border-bottom-color: $card-background-color;
      margin-bottom: $negative-1;
      box-shadow: 0 $negative-2 $spacing-2 rgba(0, 0, 0, 0.1);
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, $primary-color, $blue);
        border-radius: $default-border-radius $default-border-radius 0 0;
      }
    }
  }
}

.success-message-container {
  margin-bottom: $spacing-4;
  max-width: 600px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex: 1;
}

.success-message-card {
  background: linear-gradient(135deg, $success-background-color 0%, rgba($glxy-green-100, 0.8) 100%);
  border-radius: $default-border-radius * 3;
  padding: $spacing-5;
  box-shadow: 0 $spacing-3 $spacing-5 rgba($glxy-green-500, 0.2);
  border: 2px solid $success-border-color;
  display: flex;
  align-items: center;
  gap: $spacing-4;
  width: 100%;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
  }

  @media (max-width: $mobile-breakpoint-max) {
    flex-direction: column;
    text-align: center;
    padding: $spacing-4;
  }
}

.success-icon {
  flex-shrink: 0;
  position: relative;
  z-index: 1;

  mat-icon {
    font-size: $spacing-5;
    width: $spacing-5;
    height: $spacing-5;
    color: $success-text-color;
    filter: drop-shadow(0 $spacing-2 $spacing-4 rgba(0, 0, 0, 0.2));
    animation: bounceIn 0.8s ease-out;
  }

  @media (max-width: $mobile-breakpoint-max) {
    mat-icon {
      font-size: $spacing-5;
      width: $spacing-5;
      height: $spacing-5;
    }
  }
}

.success-content {
  flex: 1;
  color: $success-text-color;
  position: relative;
  z-index: 1;
}

.success-title {
  @include text-preset-2;
  font-weight: 700;
  margin: 0 0 $spacing-2 0;
  text-shadow: 0 $spacing-1 $spacing-2 rgba(0, 0, 0, 0.1);
  line-height: 1.2;
  animation: slideInUp 0.6s ease-out 0.2s both;

  @media (max-width: $mobile-breakpoint-max) {
    @include text-preset-3;
  }
}

.success-text {
  @include text-preset-4;
  margin: 0 0 $spacing-3 0;
  opacity: 0.95;
  line-height: 1.5;
  font-weight: 500;
  animation: slideInUp 0.6s ease-out 0.4s both;

  @media (max-width: $mobile-breakpoint-max) {
    @include text-preset-4;
  }
}

.success-actions {
  display: flex;
  gap: $spacing-2;
  align-items: center;
  flex-wrap: wrap;
  animation: slideInUp 0.6s ease-out 0.6s both;

  @media (max-width: $mobile-breakpoint-max) {
    justify-content: center;
  }
}

.success-action-button {
  background: rgba($success-text-color, 0.2) !important;
  color: $success-text-color !important;
  border: 2px solid rgba($success-text-color, 0.3) !important;
  font-weight: 600;
  text-transform: none;
  border-radius: $default-border-radius * 2;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);

  &:hover {
    background: rgba($success-text-color, 0.3) !important;
    border-color: rgba($success-text-color, 0.5) !important;
    transform: translateY($negative-2);
    box-shadow: 0 $spacing-4 $spacing-3 rgba($glxy-green-500, 0.3);
  }

  &:active {
    transform: translateY($negative-1);
  }
}

.dismiss-button {
  color: rgba($success-text-color, 0.8) !important;
  font-weight: 500;
  text-transform: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    color: $success-text-color !important;
    background: rgba($success-text-color, 0.1) !important;
    transform: translateY($negative-1);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY($spacing-3);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-message-container {
  margin-bottom: $spacing-4;
  max-width: 600px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex: 1;
}

.error-message-card {
  background: linear-gradient(135deg, $error-background-color 0%, rgba($glxy-red-100, 0.8) 100%);
  border-radius: $default-border-radius * 3;
  padding: $spacing-5;
  box-shadow: 0 $spacing-3 $spacing-5 rgba($glxy-red-500, 0.2);
  border: 2px solid $error-border-color;
  display: flex;
  align-items: center;
  gap: $spacing-4;
  width: 100%;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
  }

  @media (max-width: $mobile-breakpoint-max) {
    flex-direction: column;
    text-align: center;
    padding: $spacing-4;
  }
}

.error-icon {
  flex-shrink: 0;
  position: relative;
  z-index: 1;

  mat-icon {
    font-size: $spacing-5;
    width: $spacing-5;
    height: $spacing-5;
    color: $error-text-color;
    filter: drop-shadow(0 $spacing-2 $spacing-4 rgba(0, 0, 0, 0.2));
    animation: shake 0.8s ease-out;
  }

  @media (max-width: $mobile-breakpoint-max) {
    mat-icon {
      font-size: $spacing-5;
      width: $spacing-5;
      height: $spacing-5;
    }
  }
}

.error-content {
  flex: 1;
  color: $error-text-color;
  position: relative;
  z-index: 1;
}

.error-title {
  @include text-preset-2;
  font-weight: 700;
  margin: 0 0 $spacing-2 0;
  text-shadow: 0 $spacing-1 $spacing-2 rgba(0, 0, 0, 0.1);
  line-height: 1.2;
  animation: slideInUp 0.6s ease-out 0.2s both;

  @media (max-width: $mobile-breakpoint-max) {
    @include text-preset-3;
  }
}

.error-text {
  @include text-preset-4;
  margin: 0 0 $spacing-3 0;
  opacity: 0.95;
  line-height: 1.5;
  font-weight: 500;
  animation: slideInUp 0.6s ease-out 0.4s both;

  @media (max-width: $mobile-breakpoint-max) {
    @include text-preset-4;
  }
}

.error-actions {
  display: flex;
  gap: $spacing-2;
  align-items: center;
  flex-wrap: wrap;
  animation: slideInUp 0.6s ease-out 0.6s both;

  @media (max-width: $mobile-breakpoint-max) {
    justify-content: center;
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-$spacing-1);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX($spacing-1);
  }
}

.input-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding: $spacing-4;
  background: linear-gradient(135deg, $primary-background-color 0%, $secondary-background-color 100%);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba($primary-color, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba($blue, 0.03) 0%, transparent 50%);
    pointer-events: none;
  }
}

.input-header {
  text-align: center;
  margin-bottom: $spacing-5;
  max-width: 600px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;

  &.hidden {
    opacity: 0;
    transform: translateY($negative-3);
    pointer-events: none;
    visibility: hidden;
    height: 0;
    margin: 0;
    overflow: hidden;
  }
}

.input-title {
  @include text-preset-1;
  color: $primary-text-color;
  margin-bottom: $spacing-3;
  line-height: 1.2;
  font-weight: 700;
  background: linear-gradient(135deg, $primary-color, $blue);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: fadeInDown 0.8s ease-out;

  @media (max-width: $mobile-breakpoint-max) {
    @include text-preset-2;
  }

  @media (max-width: $media--phone-large-minimum) {
    @include text-preset-3;
  }
}

.input-subtitle {
  @include text-preset-4;
  color: $secondary-text-color;
  margin: 0;
  line-height: 1.6;
  animation: fadeInUp 0.8s ease-out 0.2s both;

  @media (max-width: $mobile-breakpoint-max) {
    @include text-preset-4;
  }
}

.input-form-container {
  width: 100%;
  max-width: 500px;
  margin-bottom: $spacing-4;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;

  &.hidden {
    opacity: 0;
    transform: translateY($spacing-3);
    pointer-events: none;
    visibility: hidden;
    height: 0;
    margin: 0;
    overflow: hidden;
  }
}

.input-card {
  border-radius: $default-border-radius * 3;
  box-shadow: 0 $spacing-2 $spacing-5 $shadow-color;
  border: none;
  background: $card-background-color;
  overflow: hidden;
  backdrop-filter: blur(10px);
  animation: slideInUp 0.8s ease-out 0.4s both;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    box-shadow: 0 $spacing-3 $spacing-5 rgba($primary-color, 0.1);
    transform: translateY($negative-1);
  }
}

.audit-form {
  padding: $spacing-4;

  @media (max-width: $media--phone-large-minimum) {
    padding: $spacing-3;
  }
}

.form-field {
  margin-bottom: $spacing-3;
  animation: fadeInUp 0.6s ease-out;

  &:nth-child(1) {
    animation-delay: 0.6s;
  }

  &:nth-child(2) {
    animation-delay: 0.8s;
  }
}

.full-width {
  width: 100%;
}

.form-input {
  @include text-preset-4;
}

::ng-deep .glxy-form-field {
  .input-wrapper {
    border-radius: $default-border-radius * 2;
    border-color: $border-color;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: $field-background-color;

    &:focus-within {
      border-color: $primary-color;
      box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
      transform: translateY($negative-1);
    }

    &:hover {
      border-color: rgba($primary-color, 0.5);
    }
  }

  .label-and-hint label {
    color: $secondary-text-color;
    font-weight: 500;
    @include text-preset-4;
  }

  input {
    color: $primary-text-color;
    padding: $spacing-2 0;
    @include text-preset-4;
  }

  &.glxy-form-field-invalid .input-wrapper:not(:focus-within) {
    border-color: $error-border-color;
    box-shadow: 0 0 0 2px rgba($error-border-color, 0.1);
  }
}

::ng-deep .glxy-error {
  color: $error-text-color;
  @include text-preset-5;
  margin-top: $spacing-1;
  animation: shake 0.5s ease-out;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: $spacing-4;
  animation: fadeInUp 0.6s ease-out 1s both;
}

.submit-button {
  min-width: 140px;
  height: $spacing-5;
  @include text-preset-4;
  font-weight: 600;
  border-radius: $default-border-radius * 2;
  text-transform: none;
  letter-spacing: 0.025em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover:not(:disabled) {
    transform: translateY($negative-2);
    box-shadow: 0 $spacing-2 $spacing-4 rgba($primary-color, 0.3);

    &::before {
      left: 100%;
    }
  }

  &:active:not(:disabled) {
    transform: translateY($negative-1);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  &.processing {
    background: linear-gradient(45deg, $primary-color, $blue);
    animation: pulse 1.5s ease-in-out infinite;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba($primary-color, 0.7);
  }
  70% {
    box-shadow: 0 0 0 $spacing-2 rgba($primary-color, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba($primary-color, 0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY($negative-3);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY($spacing-3);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: $mobile-breakpoint-max) {
  .input-container {
    padding: $spacing-3;
  }

  .input-header {
    margin-bottom: $spacing-4;
  }

  .audit-form {
    padding: $spacing-3;
  }

  .form-field {
    margin-bottom: $spacing-2;
  }
}

@media (max-width: $media--phone-large-minimum) {
  .input-container {
    padding: $spacing-2;
  }

  .input-header {
    margin-bottom: $spacing-3;
  }

  .audit-form {
    padding: $spacing-3;
  }

  .submit-button {
    width: 100%;
  }

  .input-header .input-title {
    @include text-preset-3;
  }

  .input-header .input-subtitle {
    @include text-preset-4;
  }
}
