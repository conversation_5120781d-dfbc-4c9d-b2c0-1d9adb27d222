import { Component, ChangeDetectionStrategy, On<PERSON>nit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Mat<PERSON>ard, MatCardContent } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { SharedModule } from '../shared/shared.module';
import { IconComponent } from '@vendasta/uikit';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { AuditData } from './audit-results/audit.interface';
import { AuditDataService } from './audit-data.service';
import { ReportsService } from './reports/reports.service';
import { SEOService, AIEOAuditRequest, AIEOAuditResponse } from './seo.service';
import { Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { TranslateModule } from '@ngx-translate/core';
import { trigger, state, style, transition, animate } from '@angular/animations';

@Component({
  selector: 'app-aieo-input',
  templateUrl: './aieo-input.component.html',
  styleUrls: ['./aieo-input.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default,
  animations: [
    trigger('fadeInOut', [
      state(
        'void',
        style({
          opacity: 0,
          transform: 'translateY(-20px)',
        }),
      ),
      state(
        '*',
        style({
          opacity: 1,
          transform: 'translateY(0)',
        }),
      ),
      transition('void <=> *', [animate('0.3s ease-in-out')]),
    ]),
    trigger('slideInOut', [
      state(
        'void',
        style({
          opacity: 0,
          transform: 'translateY(20px)',
        }),
      ),
      state(
        '*',
        style({
          opacity: 1,
          transform: 'translateY(0)',
        }),
      ),
      transition('void <=> *', [animate('0.3s ease-in-out')]),
    ]),
  ],
  imports: [
    CommonModule,
    MatCard,
    SharedModule,
    MatCardContent,
    IconComponent,
    MatButtonModule,
    MatInputModule,
    ReactiveFormsModule,
    GalaxyLoadingSpinnerModule,
    GalaxyFormFieldModule,
    TranslateModule,
  ],
  standalone: true,
})
export class AieoInputComponent implements OnInit, OnDestroy {
  auditForm: FormGroup;
  isSubmitting = false;
  showSuccessMessage = false;
  showErrorState = false;
  errorMessage = '';
  auditData: AuditData | null = null;
  private websiteUrlSubscription: Subscription | null = null;
  private isAutoPrepending = false;
  private autoDismissTimer: any = null;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private auditDataService: AuditDataService,
    private reportsService: ReportsService,
    private seoService: SEOService,
    private dialog: MatDialog,
    private translateService: TranslateService,
  ) {
    this.auditForm = this.fb.group({
      brandName: ['', [Validators.required, Validators.minLength(2)]],
      websiteUrl: ['', [Validators.required, this.urlValidator()]],
    });
  }

  ngOnInit(): void {
    this.setupWebsiteUrlAutoPrepend();
  }

  ngOnDestroy(): void {
    if (this.websiteUrlSubscription) {
      this.websiteUrlSubscription.unsubscribe();
    }
    this.clearAutoDismissTimer();
  }

  private setupWebsiteUrlAutoPrepend(): void {
    const websiteUrlControl = this.auditForm.get('websiteUrl');
    if (websiteUrlControl) {
      this.websiteUrlSubscription = websiteUrlControl.valueChanges.subscribe((value: string) => {
        if (this.isAutoPrepending) {
          return;
        }

        if (value && typeof value === 'string') {
          const trimmedValue = value.trim();

          if (trimmedValue && !trimmedValue.match(/^https?:\/\//i)) {
            if (trimmedValue.includes('.') || /^[a-zA-Z0-9-]+$/.test(trimmedValue)) {
              this.isAutoPrepending = true;
              const newValue = `https://${trimmedValue}`;
              websiteUrlControl.setValue(newValue, { emitEvent: false });
              this.isAutoPrepending = false;
            }
          }
        }
      });
    }
  }

  urlValidator() {
    return (control: any) => {
      const url = control.value;
      if (!url) {
        return null;
      }
      return this.isValidUrl(url) ? null : { invalidUrl: true };
    };
  }

  private isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return this.validateUrlComponents(urlObj, url);
    } catch (error) {
      return false;
    }
  }

  private validateUrlComponents(urlObj: URL, originalUrl: string): boolean {
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return false;
    }

    const hostname = urlObj.hostname;
    if (!hostname || hostname.length > 253) {
      return false;
    }

    if (!this.isValidDomain(hostname)) {
      return false;
    }

    if (urlObj.pathname.length > 2048) {
      return false;
    }

    if (originalUrl.length > 2048) {
      return false;
    }

    return true;
  }

  private isValidDomain(hostname: string): boolean {
    const cleanHostname = hostname.replace(/^\.+|\.+$/g, '');

    if (!cleanHostname) {
      return false;
    }

    const labels = cleanHostname.split('.');

    if (labels.length > 127) {
      return false;
    }

    for (const label of labels) {
      if (!this.isValidLabel(label)) {
        return false;
      }
    }

    const tld = labels[labels.length - 1];
    if (tld.length < 2 || tld.length > 63) {
      return false;
    }

    return true;
  }

  private isValidLabel(label: string): boolean {
    if (label.length < 1 || label.length > 63) {
      return false;
    }

    if (!/^[a-z0-9]/i.test(label) || !/[a-z0-9]$/i.test(label)) {
      return false;
    }

    if (!/^[a-z0-9-]+$/i.test(label)) {
      return false;
    }

    if (label.includes('--')) {
      return false;
    }

    return true;
  }

  async onSubmit(): Promise<void> {
    if (this.auditForm.valid) {
      try {
        this.isSubmitting = true;
        this.auditData = null;

        const formData = this.auditForm.value;

        const currentUrl = window.location.href;
        const extractedBusinessId = this.extractBusinessIdFromUrl(currentUrl);
        if (!extractedBusinessId) {
          throw new Error('Business ID not found in URL');
        }

        const requestPayload: AIEOAuditRequest = {
          brand_name: formData.brandName,
          website_url: formData.websiteUrl,
          business_id: extractedBusinessId,
        };

        this.seoService.TriggerAIOAudit(requestPayload).subscribe({
          next: (response: AIEOAuditResponse) => {
            const auditStatus = response.auditStatus || response.data?.auditStatus;
            const message = response.message || response.data?.message;

            if (response.success && auditStatus === 'running') {
              this.reportsService.addReport(formData.brandName, formData.websiteUrl, extractedBusinessId);
              this.showSuccessMessage = true;
              this.showErrorState = false;
              this.isSubmitting = false;
              this.clearFormFields();
              this.startAutoDismissTimer();
            } else {
              this.isSubmitting = false;
              this.showErrorState = true;
              this.errorMessage = message || this.translateService.instant('AIEO.INPUT.ERROR.API_ERROR');
              this.showSuccessMessage = false;
            }
          },
          error: (error) => {
            this.isSubmitting = false;
            this.showErrorState = true;
            let errorMessage = '';
            if (error?.error?.message) {
              errorMessage = error.error.message;
            } else if (error?.message) {
              errorMessage = error.message;
            } else if (error?.error?.error) {
              errorMessage = error.error.error;
            } else if (typeof error === 'string') {
              errorMessage = error;
            } else {
              errorMessage = this.translateService.instant('AIEO.INPUT.ERROR.API_FAILED');
            }

            this.errorMessage = errorMessage;
            this.showSuccessMessage = false;
          },
        });
      } catch (error) {
        this.isSubmitting = false;
        this.showErrorState = true;
        this.errorMessage =
          error instanceof Error ? error.message : this.translateService.instant('AIEO.INPUT.ERROR.API_FAILED');
        this.showSuccessMessage = false;
      }
    }
  }

  onRunAnotherAudit(): void {
    this.auditData = null;
    this.auditDataService.clearAuditData();
    this.auditForm.reset();
    this.showSuccessMessage = false;
    this.showErrorState = false;
    this.errorMessage = '';
  }

  private clearFormFields(): void {
    this.auditForm.reset();

    Object.keys(this.auditForm.controls).forEach((key) => {
      const control = this.auditForm.get(key);
      if (control) {
        control.markAsPristine();
        control.markAsUntouched();
      }
    });

    this.closeAllDialogs();
  }

  private closeAllDialogs(): void {
    this.dialog.closeAll();
  }

  goToReports(): void {
    this.router.navigate(['./reports'], { relativeTo: this.route });
  }

  markFormGroupTouched() {
    Object.keys(this.auditForm.controls).forEach((key) => {
      const control = this.auditForm.get(key);
      control?.markAsTouched();
    });
  }

  getErrorMessage(controlName: string): string {
    const control = this.auditForm.get(controlName);

    if (control?.hasError('required')) {
      return controlName === 'brandName'
        ? this.translateService.instant('AIEO.INPUT.ERROR.BRAND_NAME_REQUIRED')
        : this.translateService.instant('AIEO.INPUT.ERROR.WEBSITE_REQUIRED');
    }

    if (control?.hasError('minlength')) {
      return this.translateService.instant('AIEO.INPUT.ERROR.BRAND_NAME_MIN_LENGTH');
    }

    if (control?.hasError('invalidUrl')) {
      return this.translateService.instant('AIEO.INPUT.ERROR.INVALID_URL');
    }

    return '';
  }

  private startAutoDismissTimer(): void {
    this.clearAutoDismissTimer();
    this.autoDismissTimer = setTimeout(() => {
      this.dismissSuccessMessage();
    }, 5000);
  }

  private clearAutoDismissTimer(): void {
    if (this.autoDismissTimer) {
      clearTimeout(this.autoDismissTimer);
      this.autoDismissTimer = null;
    }
  }

  dismissSuccessMessage(): void {
    this.showSuccessMessage = false;
    this.clearAutoDismissTimer();
  }

  dismissErrorMessage(): void {
    this.showErrorState = false;
    this.errorMessage = '';
  }

  private extractBusinessIdFromUrl(url: string): string | null {
    try {
      const urlToParse = url.startsWith('http') ? url : `http://${url}`;
      const urlObj = new URL(urlToParse);

      const pathSegments = urlObj.pathname.split('/').filter((segment) => segment.length > 0);

      for (let i = 0; i < pathSegments.length - 2; i++) {
        if (
          pathSegments[i] === 'edit' &&
          pathSegments[i + 1] === 'account' &&
          pathSegments[i + 3] === 'app' &&
          pathSegments[i + 4] === 'aieo'
        ) {
          const businessId = pathSegments[i + 2];
          return businessId;
        }
      }
      return null;
    } catch (error) {
      return null;
    }
  }
}
