import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import {
  SEOApiService,
  TriggerAIOAuditRequest,
  TriggerAIOAuditResponse,
  GetAllAIOAuditRequest,
  GetAllAIOAuditResponse,
  GetAIOAuditRequest,
  GetAIOAuditResponse,
  GetAllAIOAuditScoreResultsRequest,
  GetAllAIOAuditScoreResultsResponse,
  GetAIOAuditStatusRequest,
  GetAIOAuditStatusResponse,
  AIOAuditResults,
  AuditScoreResults,
} from '@vendasta/listing-products';

export interface AIEOAuditRequest {
  brand_name: string;
  website_url: string;
  business_id: string;
}

export interface AIEOAuditResponse {
  success: boolean;
  message?: string;
  data?: any;
  businessId?: string;
  brandName?: string;
  websiteUrl?: string;
  auditStatus?: string;
}

export interface AIEOAuditError {
  error: string;
  message: string;
  statusCode?: number;
}

export interface AIEOAuditReport {
  id: string;
  businessId: string;
  brandName: string;
  websiteUrl: string;
  auditDate: string;
  startDate: Date;
  totalPages: number;
  auditStatus: string;
  auditSummary: string;
  auditId: string;
  auditUrl: string;
}

export interface AIEOAuditScoreResult {
  auditPageUrl: string;
  auditScores: {
    auditScoreScopeName: string;
    auditScoreScopeValue: number;
    auditScoreScopeSummary: string[];
    auditScoreRecommendations: string[];
  }[];
}

@Injectable({
  providedIn: 'root',
})
export class SEOService {
  constructor(
    private http: HttpClient,
    private lpSEOService: SEOApiService,
  ) {}

  TriggerAIOAudit(request: AIEOAuditRequest): Observable<AIEOAuditResponse> {
    if (!this.validateAuditRequest(request)) {
      return throwError(() => new Error('Invalid audit request parameters'));
    }

    const apiRequest = new TriggerAIOAuditRequest({
      businessId: request.business_id,
      brandName: request.brand_name,
      websiteUrl: request.website_url,
    });

    return this.lpSEOService.triggerAioAudit(apiRequest).pipe(
      map((response: TriggerAIOAuditResponse) => {
        return {
          success: true,
          message: response.message,
          data: response,
          businessId: response.businessId,
          brandName: response.brandName,
          websiteUrl: response.websiteUrl,
          auditStatus: response.auditStatus,
        } as AIEOAuditResponse;
      }),
      catchError((error: HttpErrorResponse) => {
        if (error.status === 400) {
          return throwError(() => new Error('Invalid request parameters'));
        } else if (error.status === 401) {
          return throwError(() => new Error('Unauthorized access'));
        } else if (error.status === 403) {
          return throwError(() => new Error('Access forbidden'));
        } else if (error.status === 404) {
          return throwError(() => new Error('API endpoint not found'));
        } else if (error.status >= 500) {
          return throwError(() => new Error('Server error occurred'));
        } else {
          return throwError(() => new Error('An unexpected error occurred'));
        }
      }),
    );
  }

  getAllAioAudit(businessId: string): Observable<AIEOAuditReport[]> {
    if (!businessId || businessId.trim() === '') {
      return throwError(() => new Error('Invalid business ID'));
    }

    const apiRequest = new GetAllAIOAuditRequest({
      businessId: businessId,
    });

    return this.lpSEOService.getAllAioAudit(apiRequest).pipe(
      map((response: GetAllAIOAuditResponse) => {
        if (!response.audit || !Array.isArray(response.audit)) {
          return [];
        }

        return response.audit.map((audit: AIOAuditResults) => ({
          id: audit.auditId || this.generateAuditId(audit),
          businessId: audit.businessId,
          brandName: audit.brandName,
          websiteUrl: audit.websiteUrl,
          auditDate: audit.auditDate,
          startDate: audit.startDate,
          totalPages: audit.totalPages,
          auditStatus: audit.auditStatus,
          auditSummary: audit.auditSummary,
          auditId: audit.auditId,
          auditUrl: audit.auditUrl,
        }));
      }),
      catchError((error: HttpErrorResponse) => {
        return this.handleApiError(error, 'Failed to fetch audit reports');
      }),
    );
  }

  getAioAudit(businessId: string, brandName: string, websiteUrl: string): Observable<AIEOAuditReport> {
    if (!this.validateAuditRequest({ business_id: businessId, brand_name: brandName, website_url: websiteUrl })) {
      return throwError(() => new Error('Invalid audit request parameters'));
    }

    const apiRequest = new GetAIOAuditRequest({
      businessId: businessId,
      brandName: brandName,
      websiteUrl: websiteUrl,
    });
    console.log(this.lpSEOService.getAioAudit(apiRequest));
    return this.lpSEOService.getAioAudit(apiRequest).pipe(
      map((response: GetAIOAuditResponse) => {
        if (!response.audit) {
          throw new Error('No audit data found');
        }

        const audit = response.audit;
        return {
          id: audit.auditId || this.generateAuditId(audit),
          businessId: audit.businessId,
          brandName: audit.brandName,
          websiteUrl: audit.websiteUrl,
          auditDate: audit.auditDate,
          startDate: audit.startDate,
          totalPages: audit.totalPages,
          auditStatus: audit.auditStatus,
          auditSummary: audit.auditSummary,
          auditId: audit.auditId,
          auditUrl: audit.auditUrl,
        };
      }),
      catchError((error: HttpErrorResponse) => {
        return this.handleApiError(error, 'Failed to fetch audit details');
      }),
    );
  }

  getAllAioAuditScoreResults(
    businessId: string,
    brandName: string,
    websiteUrl: string,
  ): Observable<AIEOAuditScoreResult[]> {
    if (!this.validateAuditRequest({ business_id: businessId, brand_name: brandName, website_url: websiteUrl })) {
      return throwError(() => new Error('Invalid audit request parameters'));
    }

    const apiRequest = new GetAllAIOAuditScoreResultsRequest({
      businessId: businessId,
      brandName: brandName,
      websiteUrl: websiteUrl,
    });

    return this.lpSEOService.getAllAioAuditScoreResults(apiRequest).pipe(
      map((response: GetAllAIOAuditScoreResultsResponse) => {
        if (!response.auditScoreResults || !Array.isArray(response.auditScoreResults)) {
          return [];
        }

        return response.auditScoreResults.map((scoreResult: AuditScoreResults) => ({
          auditPageUrl: scoreResult.auditPageUrl,
          auditScores:
            scoreResult.auditScores?.map((score) => ({
              auditScoreScopeName: score.auditScoreScopeName,
              auditScoreScopeValue: score.auditScoreScopeValue,
              auditScoreScopeSummary: score.auditScoreScopeSummary || [],
              auditScoreRecommendations: score.auditScoreRecommendations || [],
            })) || [],
        }));
      }),
      catchError((error: HttpErrorResponse) => {
        return this.handleApiError(error, 'Failed to fetch audit score results');
      }),
    );
  }

  getAioAuditStatus(businessId: string, brandName: string, websiteUrl: string): Observable<{ auditStatus: string }> {
    if (!this.validateAuditRequest({ business_id: businessId, brand_name: brandName, website_url: websiteUrl })) {
      return throwError(() => new Error('Invalid audit request parameters'));
    }

    const apiRequest = new GetAIOAuditStatusRequest({
      businessId: businessId,
      brandName: brandName,
      websiteUrl: websiteUrl,
    });

    return this.lpSEOService.getAioAuditStatus(apiRequest).pipe(
      map((response: GetAIOAuditStatusResponse) => {
        return {
          auditStatus: response.auditStatus || 'unknown',
        };
      }),
      catchError((error: HttpErrorResponse) => {
        return this.handleApiError(error, 'Failed to fetch audit status');
      }),
    );
  }

  getAuditStatus(auditId: string): Observable<AIEOAuditResponse> {
    if (!auditId || auditId.trim() === '') {
      return throwError(() => new Error('Invalid audit ID'));
    }

    const apiUrl = `/api/v1/aieo/audit/${auditId}/status`;

    return this.http.get<AIEOAuditResponse>(apiUrl).pipe(
      map((response) => {
        return response;
      }),
      catchError((error: HttpErrorResponse) => {
        return this.handleApiError(error, 'Failed to fetch audit status');
      }),
    );
  }

  private validateAuditRequest(request: AIEOAuditRequest): boolean {
    if (!request) {
      return false;
    }

    if (!request.brand_name || request.brand_name.trim() === '') {
      return false;
    }

    if (!request.website_url || request.website_url.trim() === '') {
      return false;
    }

    if (!request.business_id || request.business_id.trim() === '') {
      return false;
    }

    if (!request.business_id.startsWith('AG-')) {
      return false;
    }

    try {
      new URL(request.website_url);
    } catch {
      return false;
    }

    return true;
  }

  private handleApiError(error: HttpErrorResponse, defaultMessage: string): Observable<never> {
    let errorMessage = defaultMessage;

    if (error.status === 400) {
      errorMessage = 'Invalid request parameters';
    } else if (error.status === 401) {
      errorMessage = 'Unauthorized access';
    } else if (error.status === 403) {
      errorMessage = 'Access forbidden';
    } else if (error.status === 404) {
      errorMessage = 'Resource not found';
    } else if (error.status >= 500) {
      errorMessage = 'Server error occurred';
    } else if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return throwError(() => new Error(errorMessage));
  }

  private generateAuditId(audit: AIOAuditResults): string {
    return `${audit.businessId}-${audit.brandName}-${Date.now()}`.replace(/[^a-zA-Z0-9-]/g, '-');
  }
}
