@use 'design-tokens' as *;

.audit-results-page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $spacing-4;
  background: $primary-background-color;
  min-height: 100vh;
}

.website-url-header {
  margin-bottom: $spacing-4;
  padding: $spacing-4;
  background: linear-gradient(135deg, $glxy-blue-700 0%, darken($glxy-blue-700, 10%) 100%);
  border-radius: $default-border-radius * 2;
  box-shadow: 0 $spacing-2 $spacing-4 rgba(0, 0, 0, 0.1);
}

.website-url-title {
  @include text-preset-1;
  color: white;
  margin: 0;
  text-align: center;
  font-weight: 600;
  letter-spacing: 0.5px;
  word-break: break-all;
  line-height: 1.3;

  @media (max-width: $mobile-breakpoint-max) {
    @include text-preset-2;
  }

  @media (max-width: $media--phone-large-minimum) {
    @include text-preset-3;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loading-card {
  max-width: 400px;
  width: 100%;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-3;
  text-align: center;
}

.loading-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: $glxy-blue-700;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.error-card {
  max-width: 400px;
  width: 100%;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-3;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #d32f2f;
}

.error-message {
  @include text-preset-4;
  color: #d32f2f;
  margin: 0;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: $spacing-2;
  flex-wrap: wrap;
  justify-content: center;

  @media (max-width: $mobile-breakpoint-max) {
    flex-direction: column;
    width: 100%;
  }
}

.retry-button,
.new-audit-button {
  min-width: 120px;
  height: 40px;
  @include text-preset-4;
  font-weight: 600;
  border-radius: $default-border-radius * 2;
  text-transform: none;
  letter-spacing: 0.025em;
  transition: all 0.2s ease-in-out;

  &:hover:not(:disabled) {
    transform: translateY($negative-1);
    box-shadow: 0 $spacing-1 $spacing-2 -1px rgba(0, 0, 0, 0.1);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 $spacing-2 $spacing-2 -1px rgba(0, 0, 0, 0.15);
  }
}

@media (max-width: $mobile-breakpoint-max) {
  .audit-results-page-container {
    padding: $spacing-3;
  }

  .loading-container {
    min-height: 300px;
  }
}

@media (max-width: $media--phone-large-minimum) {
  .audit-results-page-container {
    padding: $spacing-2;
  }

  .loading-container {
    min-height: 250px;
  }
}
