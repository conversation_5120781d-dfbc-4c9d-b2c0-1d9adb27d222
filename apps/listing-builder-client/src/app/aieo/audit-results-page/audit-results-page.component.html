<app-page [pageTitle]="'AIEO.RESULTS.PAGE_TITLE' | translate">
  <div class="results-container">
    <!-- Website URL Header -->

    <div class="results-header">
      <h1 class="results-title">{{ 'AIEO.RESULTS.TITLE' | translate }}</h1>
      <p class="results-subtitle">{{ 'AIEO.RESULTS.SUBTITLE' | translate }}</p>
    </div>

    <div *ngIf="loading" class="loading-container">
      <mat-card class="loading-card">
        <mat-card-content>
          <div class="loading-content">
            <mat-icon class="loading-icon">hourglass_empty</mat-icon>
            <p>{{ 'AIEO.RESULTS.LOADING' | translate }}</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <div *ngIf="error" class="error-container">
      <mat-card class="error-card">
        <mat-card-content>
          <div class="error-content">
            <mat-icon class="error-icon">error_outline</mat-icon>
            <p class="error-message">{{ errorMessage }}</p>
            <div class="error-actions">
              <button mat-raised-button color="primary" (click)="onRetryLoad()" class="retry-button">
                {{ 'AIEO.RESULTS.ERROR.RETRY' | translate }}
              </button>
              <button mat-stroked-button (click)="onRunAnotherAudit()" class="new-audit-button">
                {{ 'AIEO.RESULTS.ERROR.NEW_AUDIT' | translate }}
              </button>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <app-audit-results
      *ngIf="auditData && !loading"
      [auditData]="auditData"
      [websiteUrl]="currentReport?.websiteUrl || ''"
      (runAnotherAudit)="onRunAnotherAudit()"
    ></app-audit-results>

    <div *ngIf="!auditData && !loading" class="no-data-container">
      <mat-card class="no-data-card">
        <mat-card-content>
          <p>{{ 'AIEO.RESULTS.NO_DATA.MESSAGE' | translate }}</p>
          <button mat-raised-button color="primary" (click)="onRunAnotherAudit()">
            {{ 'AIEO.RESULTS.NO_DATA.ACTION' | translate }}
          </button>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</app-page>
