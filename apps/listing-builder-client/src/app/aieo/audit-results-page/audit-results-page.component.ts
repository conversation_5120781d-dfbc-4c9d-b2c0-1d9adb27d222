import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { MatCard, MatCardContent } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { SharedModule } from '../../shared/shared.module';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { AuditResultsComponent } from '../audit-results/audit-results.component';
import { AuditDataService } from '../audit-data.service';
import { ReportsService } from '../reports/reports.service';
import { AuditReport } from '../reports/reports.interface';
import { AuditData } from '../audit-results/audit.interface';
import { SEOService, AIEOAuditScoreResult } from '../seo.service';
import { Observable, Subject, takeUntil, take, switchMap, catchError, of } from 'rxjs';
import { AGIDTOKEN } from '../../app.module';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-audit-results-page',
  templateUrl: './audit-results-page.component.html',
  styleUrls: ['./audit-results-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default,
  imports: [
    CommonModule,
    MatCard,
    MatCardContent,
    MatButtonModule,
    MatIconModule,
    SharedModule,
    GalaxyFormFieldModule,
    AuditResultsComponent,
    TranslateModule,
  ],
  standalone: true,
})
export class AuditResultsPageComponent implements OnInit, OnDestroy {
  auditData: AuditData | null = null;
  currentReport: AuditReport | null = null;
  loading = false;
  error = false;
  errorMessage = '';
  private destroy$ = new Subject<void>();

  constructor(
    private auditDataService: AuditDataService,
    private reportsService: ReportsService,
    private seoService: SEOService,
    private router: Router,
    private route: ActivatedRoute,
    @Inject(AGIDTOKEN) private accountGroupId$: Observable<string>,
  ) {}

  ngOnInit(): void {
    this.currentReport = this.reportsService.getCurrentReport();

    if (this.currentReport && this.currentReport.auditData) {
      // Validate existing audit data
      if (this.isValidAuditData(this.currentReport.auditData)) {
        this.auditData = this.currentReport.auditData;
        this.auditDataService.setAuditData(this.auditData);
      } else {
        // Invalid audit data, reload from API
        this.loadAuditDataFromAPI();
      }
    } else if (this.currentReport) {
      this.loadAuditDataFromAPI();
    } else {
      this.auditDataService.auditData$.pipe(takeUntil(this.destroy$)).subscribe((data) => {
        this.auditData = data;
        if (!data) {
          this.redirectToInput();
        }
      });

      const existingData = this.auditDataService.getAuditData();
      if (existingData && this.isValidAuditData(existingData)) {
        this.auditData = existingData;
      } else {
        this.redirectToInput();
      }
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadAuditDataFromAPI(): void {
    if (!this.currentReport) {
      this.redirectToInput();
      return;
    }

    this.loading = true;
    this.error = false;
    this.errorMessage = '';

    this.accountGroupId$
      .pipe(
        take(1),
        takeUntil(this.destroy$),
        switchMap((accountGroupId) =>
          this.reportsService.getAuditResults(
            accountGroupId,
            this.currentReport!.brandName,
            this.currentReport!.websiteUrl,
          ),
        ),
        catchError((error) => {
          this.loading = false;
          this.error = true;
          this.errorMessage = error?.message || 'Failed to load audit results. Please try again.';
          return of([]);
        }),
      )
      .subscribe({
        next: (scoreResults: AIEOAuditScoreResult[]) => {
          this.loading = false;

          if (scoreResults && scoreResults.length > 0) {
            this.auditData = this.convertScoreResultsToAuditData(scoreResults);
            this.auditDataService.setAuditData(this.auditData);

            this.reportsService.updateReportStatus(this.currentReport!.id, 'completed', this.auditData);
          } else {
            this.error = true;
            this.errorMessage = 'No audit data available. Please run a new audit.';
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = true;
          this.errorMessage = error?.message || 'An unexpected error occurred. Please try again.';
        },
      });
  }

  private convertScoreResultsToAuditData(scoreResults: AIEOAuditScoreResult[]): AuditData {
    if (!scoreResults || scoreResults.length === 0) {
      return this.createEmptyAuditData();
    }

    // Find the audit result that contains the "overall" scope
    let overallAuditResult: AIEOAuditScoreResult | null = null;

    for (const result of scoreResults) {
      if (result.auditScores && result.auditScores.length > 0) {
        const hasOverallScope = result.auditScores.some((score) => score.auditScoreScopeName === 'overall');
        if (hasOverallScope) {
          overallAuditResult = result;
          break;
        }
      }
    }

    // If no overall scope found, fall back to the first result
    if (!overallAuditResult && scoreResults.length > 0) {
      overallAuditResult = scoreResults[0];
    }

    if (!overallAuditResult || !overallAuditResult.auditScores || overallAuditResult.auditScores.length === 0) {
      return this.createEmptyAuditData();
    }

    // Extract the overall score from the found audit result
    const overallScoreData = overallAuditResult.auditScores.find((score) => score.auditScoreScopeName === 'overall');

    const overallScore = overallScoreData?.auditScoreScopeValue || 0;
    const sections: any[] = [];
    let overallSummary = '';

    // Process all audit scores from the overall audit result
    overallAuditResult.auditScores.forEach((score) => {
      if (score.auditScoreScopeValue !== undefined && score.auditScoreScopeValue !== null) {
        // Map the specific audit categories mentioned in requirements
        const categoryName = this.mapCategoryName(score.auditScoreScopeName);

        // Extract overall summary from the "overall" scope
        if (score.auditScoreScopeName === 'overall') {
          overallSummary = this.generateCategorySummary(score.auditScoreScopeSummary, score.auditScoreScopeValue);
        } else {
          // Only add non-overall categories to the sections
          sections.push({
            title: categoryName,
            recommendations: score.auditScoreRecommendations || [],
            summary: this.generateCategorySummary(score.auditScoreScopeSummary, score.auditScoreScopeValue),
            score: score.auditScoreScopeValue,
          });
        }
      }
    });

    return {
      overallScore,
      description: this.generateDescription(overallScore, this.currentReport?.websiteUrl || 'your business'),
      overallSummary: overallSummary,
      sections: sections.length > 0 ? sections : this.createDefaultSections(),
    };
  }

  private createEmptyAuditData(): AuditData {
    return {
      overallScore: 0,
      description: this.generateDescription(0, 'your business'),
      overallSummary: '',
      sections: this.createDefaultSections(),
    };
  }

  private createDefaultSections(): any[] {
    return [
      {
        title: 'Schema Markup',
        recommendations: ['No data available'],
        summary: 'No audit data available for this category.',
        score: 0,
      },
      {
        title: 'Structured Data',
        recommendations: ['No data available'],
        summary: 'No audit data available for this category.',
        score: 0,
      },
      {
        title: 'Robots.txt Enablement',
        recommendations: ['No data available'],
        summary: 'No audit data available for this category.',
        score: 0,
      },
      {
        title: 'Metadata',
        recommendations: ['No data available'],
        summary: 'No audit data available for this category.',
        score: 0,
      },
      {
        title: 'Content Quality',
        recommendations: ['No data available'],
        summary: 'No audit data available for this category.',
        score: 0,
      },
      {
        title: 'Performance',
        recommendations: ['No data available'],
        summary: 'No audit data available for this category.',
        score: 0,
      },
    ];
  }

  private mapCategoryName(apiCategoryName: string): string {
    const categoryMap: { [key: string]: string } = {
      'Schema Markup': 'Schema Markup',
      'Structured Data': 'Structured Data',
      'Robots.txt Enablement': 'Robots.txt Enablement',
      Metadata: 'Metadata',
      'Content Quality': 'Content Quality',
      Performance: 'Performance',
      schema_markup: 'Schema Markup',
      structured_data: 'Structured Data',
      robots_txt: 'Robots.txt Enablement',
      metadata: 'Metadata',
      content_quality: 'Content Quality',
      performance: 'Performance',
    };

    return categoryMap[apiCategoryName] || apiCategoryName;
  }

  private generateCategorySummary(summaryArray: string[], score: number): string {
    if (summaryArray && Array.isArray(summaryArray) && summaryArray.length > 0) {
      // Filter out empty strings and join with spaces
      const validSummaries = summaryArray.filter((summary) => summary && summary.trim().length > 0);
      return validSummaries.length > 0 ? validSummaries.join(' ') : this.getFallbackSummary(score);
    }

    return this.getFallbackSummary(score);
  }

  private getFallbackSummary(score: number): string {
    if (score >= 80) {
      return 'Excellent performance in this category.';
    } else if (score >= 60) {
      return 'Good performance with room for improvement.';
    } else if (score >= 40) {
      return 'Moderate performance that needs attention.';
    } else if (score >= 20) {
      return 'Poor performance requiring immediate action.';
    } else {
      return 'Critical issues that need urgent attention.';
    }
  }

  private generateDescription(overallScore: number, websiteUrl: string): string {
    if (overallScore < 50) {
      return `Analysis of <strong class="brand-name">${websiteUrl}</strong> shows significant room for improvement in AI search engine optimization. The site needs improvements in structured data, metadata, and content optimization to better serve AI-powered search engines.`;
    } else if (overallScore < 75) {
      return `Analysis of <strong class="brand-name">${websiteUrl}</strong> shows good potential with some enhancements needed in AI search engine optimization. The site could benefit from improvements in structured data, metadata, and content optimization.`;
    } else {
      return `Analysis of <strong class="brand-name">${websiteUrl}</strong> shows strong AI visibility in search engine optimization. The site is well-optimized for AI-powered search engines.`;
    }
  }

  private redirectToInput(): void {
    this.accountGroupId$.pipe(take(1)).subscribe((accountGroupId) => {
      this.router.navigate([`/edit/account/${accountGroupId}/app/aieo`]);
    });
  }

  onRunAnotherAudit(): void {
    this.auditDataService.clearAuditData();
    this.reportsService.clearCurrentReport();
    this.accountGroupId$.pipe(take(1)).subscribe((accountGroupId) => {
      this.router.navigate([`/edit/account/${accountGroupId}/app/aieo`]);
    });
  }

  onRetryLoad(): void {
    if (this.currentReport) {
      this.loadAuditDataFromAPI();
    } else {
      this.onRunAnotherAudit();
    }
  }

  private isValidAuditData(auditData: AuditData): boolean {
    return (
      auditData &&
      typeof auditData.overallScore === 'number' &&
      auditData.overallScore >= 0 &&
      auditData.overallScore <= 100 &&
      Array.isArray(auditData.sections) &&
      auditData.sections.length > 0
    );
  }
}
