import {
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectionStrategy,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCard, MatCardContent } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { SharedModule } from '../../shared/shared.module';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyProgressBarModule } from '@vendasta/galaxy/progress-bar';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { AuditData, AuditSection } from './audit.interface';
import { TranslateService } from '@ngx-translate/core';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-audit-results',
  templateUrl: './audit-results.component.html',
  styleUrls: ['./audit-results.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default,
  imports: [
    CommonModule,
    MatCard,
    MatCardContent,
    MatButtonModule,
    MatIconModule,
    SharedModule,
    GalaxyFormFieldModule,
    GalaxyProgressBarModule,
    GalaxyBadgeModule,
    TranslateModule,
  ],
  standalone: true,
})
export class AuditResultsComponent implements OnChanges {
  @Input() auditData: AuditData | null = null;
  @Input() websiteUrl = '';
  @Output() runAnotherAudit = new EventEmitter<void>();

  constructor(private translateService: TranslateService) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['auditData']) {
      if (this.auditData && !this.auditData.sections) {
        this.auditData.sections = [];
      }
    }
  }

  onRunAnotherAudit(): void {
    this.runAnotherAudit.emit();
  }

  trackBySection(index: number, section: AuditSection): string | number {
    return section?.title || index;
  }

  trackByRecommendation(index: number, _recommendation: string): number {
    return index;
  }

  hasRecommendations(section: AuditSection): boolean {
    return section?.recommendations && section.recommendations.length > 0;
  }

  hasSummary(section: AuditSection): boolean {
    return section?.summary && section.summary.trim().length > 0;
  }

  hasOverallSummary(): boolean {
    return this.auditData?.overallSummary && this.auditData.overallSummary.trim().length > 0;
  }

  getOverallSummary(): string {
    return this.auditData?.overallSummary || '';
  }

  get sections(): AuditSection[] {
    return this.auditData?.sections || [];
  }

  get overallScore(): number {
    return this.auditData?.overallScore || 0;
  }

  get scoreLabel(): string {
    return this.getScoreLabel(this.overallScore);
  }

  getScoreLabel(score: number): string {
    if (score >= 80) return this.translateService.instant('AIEO.SCORES.EXCELLENT');
    if (score >= 60) return this.translateService.instant('AIEO.SCORES.VERY_GOOD');
    if (score >= 40) return this.translateService.instant('AIEO.SCORES.GOOD');
    if (score >= 20) return this.translateService.instant('AIEO.SCORES.FAIR');
    return this.translateService.instant('AIEO.SCORES.POOR');
  }

  getGaugeCircumference(): number {
    return 2 * Math.PI * 55;
  }

  getGaugeOffset(): number {
    const circumference = this.getGaugeCircumference();
    const progress = this.overallScore / 100;
    return circumference * (1 - progress);
  }

  getGaugeColor(percentage: number): string {
    if (percentage >= 80) return 'var(--success-icon-color, #2e7d32)';
    if (percentage >= 60) return 'var(--success-icon-color, #2e7d32)';
    if (percentage >= 40) return 'var(--warn-icon-color, #ff8f00)';
    if (percentage >= 20) return 'var(--warn-icon-color, #ff8f00)';
    return 'var(--error-icon-color, #c62828)';
  }

  getBadgeColor(score: number): string {
    if (score >= 80) return 'green-solid';
    if (score >= 60) return 'green';
    if (score >= 40) return 'yellow';
    if (score >= 20) return 'yellow';
    return 'red';
  }

  getScoreIndicators(): Array<{ value: number; label: string }> {
    return [
      { value: 0, label: this.translateService.instant('AIEO.SCORES.POOR') },
      { value: 20, label: this.translateService.instant('AIEO.SCORES.FAIR') },
      { value: 40, label: this.translateService.instant('AIEO.SCORES.GOOD') },
      { value: 60, label: this.translateService.instant('AIEO.SCORES.VERY_GOOD') },
      { value: 80, label: this.translateService.instant('AIEO.SCORES.EXCELLENT') },
    ];
  }

  trackByScoreIndicator(index: number, indicator: { value: number; label: string }): number {
    return indicator.value;
  }

  // New methods for category gauge
  getCategoryGaugeCircumference(): number {
    return 2 * Math.PI * 40; // Smaller radius for category gauges
  }

  getCategoryGaugeOffset(score: number): number {
    const circumference = this.getCategoryGaugeCircumference();
    const progress = score / 100;
    return circumference * (1 - progress);
  }

  getCategoryGaugeColor(score: number): string {
    if (score >= 80) return 'var(--success-icon-color, #2e7d32)';
    if (score >= 60) return 'var(--success-icon-color, #2e7d32)';
    if (score >= 40) return 'var(--warn-icon-color, #ff8f00)';
    if (score >= 20) return 'var(--warn-icon-color, #ff8f00)';
    return 'var(--error-icon-color, #c62828)';
  }

  getCategoryScoreLabel(score: number): string {
    return this.getScoreLabel(score);
  }
}
