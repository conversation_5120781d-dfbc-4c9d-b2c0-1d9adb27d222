@use 'design-tokens' as *;

.audit-results-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $spacing-4;
  background: $primary-background-color;
  min-height: 100vh;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba($primary-color, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba($blue, 0.03) 0%, transparent 50%);
    pointer-events: none;
  }
}

.audit-section {
  margin-bottom: $spacing-5;
  animation: fadeInUp 0.8s ease-out;

  &:nth-child(1) {
    animation-delay: 0.2s;
  }

  &:nth-child(2) {
    animation-delay: 0.4s;
  }

  &:nth-child(3) {
    animation-delay: 0.6s;
  }
}

.audit-card {
  background: $card-background-color;
  border-radius: $default-border-radius * 2;
  border: 1px solid $border-color;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow: 0 $spacing-1 $spacing-3 rgba(0, 0, 0, 0.08);

  &:hover {
    box-shadow: 0 $spacing-2 $spacing-5 rgba($primary-color, 0.15);
    transform: translateY($negative-1);
  }
}

.overall-score-card {
  background: linear-gradient(135deg, rgba($primary-color, 0.05) 0%, rgba($primary-color, 0.02) 100%);
  border: 2px solid rgba($primary-color, 0.2);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba($primary-color, 0.02) 50%, transparent 70%);
    animation: shimmer 3s infinite;
  }
}

.audit-card-content {
  padding: $spacing-5;
  position: relative;
  z-index: 1;
}

.progress-card-layout {
  display: flex;
  align-items: center;
  gap: $spacing-5;

  @media (max-width: $mobile-breakpoint-max) {
    flex-direction: column;
    gap: $spacing-4;
  }
}

.progress-column {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.circular-gauge-container {
  position: relative;
  width: 160px;
  height: 160px;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeInScale 0.8s ease-out;

  @media (max-width: $mobile-breakpoint-max) {
    width: 140px;
    height: 140px;
  }

  @media (max-width: $media--phone-large-minimum) {
    width: 120px;
    height: 120px;
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.gauge-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.gauge-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
  overflow: visible;
  filter: drop-shadow(0 $spacing-2 $spacing-4 rgba(0, 0, 0, 0.1));
}

.gauge-background {
  fill: none;
  stroke: $border-color;
  stroke-width: 6;
  stroke-linecap: round;
  opacity: 0.3;
}

.gauge-progress {
  fill: none;
  stroke-width: 6;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.8s ease-in-out;
  filter: drop-shadow(0 $spacing-1 $spacing-2 rgba(0, 0, 0, 0.1));
}

.gauge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 1;
}

.gauge-score {
  @include text-preset-1;
  font-weight: 700;
  color: $primary-text-color;
  margin: 0;
  line-height: 1;
  font-size: 2rem;
  animation: bounceIn 0.8s ease-out 0.4s both;

  @media (max-width: $mobile-breakpoint-max) {
    font-size: 1.75rem;
  }

  @media (max-width: $media--phone-large-minimum) {
    font-size: 1.5rem;
  }
}

.gauge-label {
  @include text-preset-5;
  color: $secondary-text-color;
  margin: $spacing-1 0 0 0;
  font-weight: 500;
  letter-spacing: 0.5px;
  font-size: 0.875rem;
  animation: fadeInUp 0.6s ease-out 0.6s both;

  @media (max-width: $mobile-breakpoint-max) {
    font-size: 0.8rem;
  }

  @media (max-width: $media--phone-large-minimum) {
    font-size: 0.75rem;
  }
}

.content-column {
  flex: 1;
  min-width: 0;
}

.audit-summary {
  .overall-score-title {
    @include text-preset-2;
    font-weight: 600;
    color: $primary-text-color;
    margin: 0 0 $spacing-2 0;
    animation: fadeInDown 0.6s ease-out 0.4s both;
  }

  .audit-description {
    @include text-preset-4;
    color: $secondary-text-color;
    margin: 0 0 $spacing-3 0;
    line-height: 1.6;
    animation: fadeInUp 0.6s ease-out 0.6s both;

    .brand-name {
      color: $primary-color;
      font-weight: 700;
      background: rgba($primary-color, 0.1);
      padding: $spacing-1 $spacing-2;
      border-radius: $default-border-radius;
      border: 1px solid rgba($primary-color, 0.2);
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        background: rgba($primary-color, 0.15);
        transform: translateY($negative-1);
        box-shadow: 0 $spacing-1 $spacing-2 rgba($primary-color, 0.2);
      }
    }
  }

  .overall-summary-section {
    margin-top: $spacing-3;
    padding-top: $spacing-3;
    border-top: 1px solid var(--border-color, #e0e0e0);
    animation: fadeInUp 0.6s ease-out 0.8s both;

    .summary-title {
      @include text-preset-3;
      font-weight: 600;
      color: $primary-text-color;
      margin: 0 0 $spacing-2 0;
    }

    .summary-content {
      @include text-preset-4;
      color: $secondary-text-color;
      line-height: 1.6;
      margin: 0;
    }
  }
}

.audit-section-header {
  margin-bottom: $spacing-3;
  animation: fadeInDown 0.6s ease-out;
}

.section-title {
  @include text-preset-3;
  font-weight: 600;
  color: $primary-text-color;
  margin: 0;
  position: relative;
  padding-bottom: $spacing-2;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, $primary-color, $blue);
    border-radius: $default-border-radius;
  }
}

.audit-section-content {
  background: $card-background-color;
  border-radius: $default-border-radius * 2;
  padding: $spacing-4;
  border: 1px solid $border-color;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    box-shadow: 0 $spacing-2 $spacing-4 rgba($primary-color, 0.1);
  }
}

.category-layout {
  display: flex;
  align-items: flex-start;
  gap: $spacing-4;

  @media (max-width: $mobile-breakpoint-max) {
    flex-direction: column;
    gap: $spacing-3;
  }
}

.category-gauge-column {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.category-gauge-container {
  position: relative;
  width: 120px;
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeInScale 0.8s ease-out;

  @media (max-width: $mobile-breakpoint-max) {
    width: 100px;
    height: 100px;
  }

  @media (max-width: $media--phone-large-minimum) {
    width: 80px;
    height: 80px;
  }
}

.category-gauge-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.category-gauge-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
  overflow: visible;
  filter: drop-shadow(0 $spacing-1 $spacing-2 rgba(0, 0, 0, 0.1));
}

.category-gauge-background {
  fill: none;
  stroke: $border-color;
  stroke-width: 4;
  stroke-linecap: round;
  opacity: 0.3;
}

.category-gauge-progress {
  fill: none;
  stroke-width: 4;
  stroke-linecap: round;
  transition: stroke-dashoffset 0.8s ease-in-out;
  filter: drop-shadow(0 $spacing-1 $spacing-2 rgba(0, 0, 0, 0.1));
}

.category-gauge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 1;
}

.category-gauge-score {
  @include text-preset-3;
  font-weight: 700;
  color: $primary-text-color;
  margin: 0;
  line-height: 1;
  font-size: 1.25rem;
  animation: bounceIn 0.8s ease-out 0.4s both;

  @media (max-width: $mobile-breakpoint-max) {
    font-size: 1.125rem;
  }

  @media (max-width: $media--phone-large-minimum) {
    font-size: 1rem;
  }
}

.category-gauge-label {
  @include text-preset-5;
  color: $secondary-text-color;
  margin: $spacing-1 0 0 0;
  font-weight: 500;
  letter-spacing: 0.25px;
  font-size: 0.75rem;
  animation: fadeInUp 0.6s ease-out 0.6s both;

  @media (max-width: $mobile-breakpoint-max) {
    font-size: 0.7rem;
  }

  @media (max-width: $media--phone-large-minimum) {
    font-size: 0.65rem;
  }
}

.category-content-column {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

.recommendations-section,
.summary-section {
  .recommendations-title,
  .summary-title {
    @include text-preset-4;
    font-weight: 600;
    color: $primary-text-color;
    margin: 0 0 $spacing-2 0;
    position: relative;
    padding-bottom: $spacing-1;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 40px;
      height: 2px;
      background: $primary-color;
      border-radius: $default-border-radius;
    }
  }
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-1;
}

.recommendation-item {
  @include text-preset-4;
  color: $primary-text-color;
  line-height: 1.5;
  padding: $spacing-2 $spacing-3;
  border-bottom: 1px solid rgba($border-color, 0.3);
  transition: all 0.3s ease;
  border-radius: $default-border-radius;
  position: relative;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: $primary-background-color;
    transform: translateX($spacing-1);
    box-shadow: 0 $spacing-1 $spacing-2 rgba($primary-color, 0.1);
  }

  &::before {
    content: '•';
    color: $primary-color;
    font-weight: bold;
    margin-right: $spacing-2;
    position: absolute;
    left: $spacing-1;
  }
}

.summary-content {
  @include text-preset-4;
  color: $secondary-text-color;
  line-height: 1.6;
  margin: 0;
  padding: $spacing-2 $spacing-3;
  background: $primary-background-color;
  border-radius: $default-border-radius;
  border-left: 3px solid $primary-color;
}

.audit-footer {
  display: flex;
  justify-content: center;
  margin-top: $spacing-5;
  padding-top: $spacing-4;
  border-top: 1px solid $border-color;
  animation: fadeInUp 0.8s ease-out 0.8s both;
}

.run-another-button {
  min-width: 160px;
  height: 48px;
  @include text-preset-4;
  font-weight: 600;
  border-radius: $default-border-radius * 2;
  text-transform: none;
  letter-spacing: 0.025em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover:not(:disabled) {
    transform: translateY($negative-2);
    box-shadow: 0 $spacing-2 $spacing-4 rgba($primary-color, 0.3);

    &::before {
      left: 100%;
    }
  }

  &:active:not(:disabled) {
    transform: translateY($negative-1);
    box-shadow: 0 $spacing-1 $spacing-2 rgba($primary-color, 0.2);
  }

  @media (max-width: $mobile-breakpoint-max) {
    min-width: 140px;
    height: 44px;
  }

  @media (max-width: $media--phone-large-minimum) {
    min-width: 120px;
    height: 40px;
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY($negative-3);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY($spacing-3);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: $mobile-breakpoint-max) {
  .audit-results-container {
    padding: $spacing-3;
  }

  .audit-card-content {
    padding: $spacing-4;
  }

  .progress-card-layout {
    gap: $spacing-3;
  }

  .circular-gauge-container {
    width: 140px;
    height: 140px;
  }

  .gauge-score {
    font-size: 1.75rem;
  }

  .gauge-label {
    font-size: 0.8rem;
  }

  .audit-section-content {
    padding: $spacing-3;
  }

  .category-layout {
    gap: $spacing-3;
  }

  .category-gauge-container {
    width: 100px;
    height: 100px;
  }

  .category-gauge-score {
    font-size: 1.125rem;
  }

  .category-gauge-label {
    font-size: 0.7rem;
  }

  .run-another-button {
    min-width: 140px;
    height: 44px;
  }
}

@media (max-width: $media--phone-large-minimum) {
  .audit-results-container {
    padding: $spacing-2;
  }

  .audit-card-content {
    padding: $spacing-3;
  }

  .progress-card-layout {
    gap: $spacing-2;
  }

  .circular-gauge-container {
    width: 120px;
    height: 120px;
  }

  .gauge-score {
    font-size: 1.5rem;
  }

  .gauge-label {
    font-size: 0.75rem;
  }

  .audit-section-content {
    padding: $spacing-2;
  }

  .category-layout {
    gap: $spacing-2;
  }

  .category-gauge-container {
    width: 80px;
    height: 80px;
  }

  .category-gauge-score {
    font-size: 1rem;
  }

  .category-gauge-label {
    font-size: 0.65rem;
  }

  .run-another-button {
    min-width: 120px;
    height: 40px;
  }
}
