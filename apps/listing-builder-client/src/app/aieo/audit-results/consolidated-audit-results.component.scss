@use 'design-tokens' as *;

.consolidated-audit-results-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: $spacing-4;
  background: $primary-background-color;
  min-height: 100vh;
  font-family: $default-font-family;
}

.audit-section {
  margin-bottom: $spacing-5;
}

.audit-card {
  border-radius: $default-border-radius * 2;
  box-shadow: 0 $spacing-1 $spacing-3 $shadow-color;
  transition: all 0.3s ease;
  border: 1px solid $border-color;
  background: $card-background-color;

  &:hover {
    box-shadow: 0 $spacing-2 $spacing-4 rgba($primary-color, 0.15);
  }
}

.audit-card-content {
  padding: $spacing-4;
}

.progress-card-layout {
  display: flex;
  align-items: center;
  gap: $spacing-5;

  @media (max-width: $mobile-breakpoint-max) {
    flex-direction: column;
    gap: $spacing-4;
  }
}

.progress-column {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.content-column {
  flex: 1;
  min-width: 0;
}

.circular-gauge-container {
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeInScale 0.6s ease-out;
}

.gauge-wrapper {
  position: relative;
  width: 140px;
  height: 140px;
  display: flex;
  justify-content: center;
  align-items: center;

  @media (max-width: $mobile-breakpoint-max) {
    width: 120px;
    height: 120px;
  }

  @media (max-width: $media--phone-large-minimum) {
    width: 100px;
    height: 100px;
  }
}

.gauge-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
  overflow: visible;
  filter: drop-shadow(0 $spacing-1 $spacing-2 rgba(0, 0, 0, 0.1));
}

.gauge-background {
  stroke: $border-color;
  fill: none;
  stroke-width: 6;
  stroke-linecap: round;
  opacity: 0.3;
}

.gauge-progress {
  transition: stroke-dashoffset 0.8s ease-in-out;
  fill: none;
  stroke-width: 6;
  stroke-linecap: round;
  filter: drop-shadow(0 $spacing-1 $spacing-2 rgba(0, 0, 0, 0.1));
}

.gauge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
  z-index: 1;
}

.gauge-score {
  @include text-preset-1;
  font-weight: 700;
  color: $primary-text-color;
  line-height: 1;
  margin: 0;

  @media (max-width: $mobile-breakpoint-max) {
    @include text-preset-2;
  }

  @media (max-width: $media--phone-large-minimum) {
    @include text-preset-3;
  }
}

.gauge-label {
  @include text-preset-5;
  color: $secondary-text-color;
  margin-top: $spacing-1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;

  @media (max-width: $mobile-breakpoint-max) {
    @include text-preset-6;
  }
}

.audit-summary {
  h2.overall-score-title {
    @include text-preset-2;
    font-weight: 700;
    color: $primary-text-color;
    margin: 0 0 $spacing-3 0;
    line-height: 1.2;

    @media (max-width: $mobile-breakpoint-max) {
      @include text-preset-3;
    }
  }

  .audit-description {
    @include text-preset-4;
    line-height: 1.6;
    color: $secondary-text-color;
    margin: 0 0 $spacing-4 0;
  }
}

.report-actions {
  display: flex;
  gap: $spacing-2;
  align-items: center;
  margin-top: $spacing-3;
}

.audit-section-header {
  margin-bottom: $spacing-3;

  .section-title {
    @include text-preset-3;
    font-weight: 600;
    color: $primary-text-color;
    margin: 0;
    padding-bottom: $spacing-2;
    border-bottom: 2px solid $primary-color;
  }
}

.audit-section-content {
  background: $card-background-color;
  border-radius: $default-border-radius * 2;
  padding: $spacing-4;
  border: 1px solid $border-color;
}

.category-layout {
  display: flex;
  gap: $spacing-4;

  @media (max-width: $mobile-breakpoint-max) {
    flex-direction: column;
    gap: $spacing-3;
  }
}

.category-gauge-column {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

.category-gauge-container {
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeInScale 0.6s ease-out;
}

.category-gauge-wrapper {
  position: relative;
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;

  @media (max-width: $mobile-breakpoint-max) {
    width: 80px;
    height: 80px;
  }

  @media (max-width: $media--phone-large-minimum) {
    width: 70px;
    height: 70px;
  }
}

.category-gauge-svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
  overflow: visible;
  filter: drop-shadow(0 $spacing-1 $spacing-2 rgba(0, 0, 0, 0.1));
}

.category-gauge-background {
  stroke: $border-color;
  fill: none;
  stroke-width: 4;
  stroke-linecap: round;
  opacity: 0.3;
}

.category-gauge-progress {
  transition: stroke-dashoffset 0.8s ease-in-out;
  fill: none;
  stroke-width: 4;
  stroke-linecap: round;
  filter: drop-shadow(0 $spacing-1 $spacing-2 rgba(0, 0, 0, 0.1));
}

.category-gauge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
  z-index: 1;
}

.category-gauge-score {
  @include text-preset-3;
  font-weight: 700;
  color: $primary-text-color;
  line-height: 1;
  margin: 0;

  @media (max-width: $mobile-breakpoint-max) {
    @include text-preset-4;
  }

  @media (max-width: $media--phone-large-minimum) {
    @include text-preset-5;
  }
}

.category-gauge-label {
  @include text-preset-6;
  color: $secondary-text-color;
  margin-top: $spacing-1;
  text-transform: uppercase;
  letter-spacing: 0.25px;
  font-weight: 500;
}

.page-scores-section {
  .page-scores-title {
    @include text-preset-5;
    font-weight: 600;
    color: $primary-text-color;
    margin: 0 0 $spacing-2 0;
  }

  .page-scores-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-1;
  }

  .page-score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-2 $spacing-3;
    background: $primary-background-color;
    border-radius: $default-border-radius * 1.5;
    border: 1px solid $weak-border-color;
    transition: all 0.2s ease;

    &:hover {
      background: $secondary-background-color;
      border-color: $border-color;
    }

    .page-name {
      @include text-preset-5;
      font-weight: 500;
      color: $primary-text-color;
      flex: 1;
      margin-right: $spacing-2;
      word-break: break-word;
    }

    .page-score {
      @include text-preset-5;
      font-weight: 700;
      color: $primary-color;
      flex-shrink: 0;
    }
  }
}

.category-content-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
}

.recommendations-section,
.summary-section {
  h4 {
    @include text-preset-4;
    font-weight: 600;
    color: $primary-text-color;
    margin: 0 0 $spacing-3 0;
    padding-bottom: $spacing-2;
    border-bottom: 1px solid $border-color;
  }
}

.recommendations-list,
.summary-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
}

.recommendation-item,
.summary-point {
  padding: $spacing-3 $spacing-4;
  background: $primary-background-color;
  border-left: 4px solid $primary-color;
  border-radius: 0 $default-border-radius * 1.5 $default-border-radius * 1.5 0;
  @include text-preset-4;
  line-height: 1.5;
  color: $primary-text-color;
  transition: all 0.2s ease;

  &:hover {
    background: $secondary-background-color;
    transform: translateX($spacing-1);
  }

  &:before {
    content: '•';
    color: $primary-color;
    font-weight: bold;
    margin-right: $spacing-2;
  }
}

.audit-footer {
  display: flex;
  justify-content: center;
  margin-top: $spacing-5;
  padding-top: $spacing-4;
  border-top: 1px solid $border-color;

  .run-another-button {
    min-width: 160px;
    height: $spacing-5;
    @include text-preset-4;
    font-weight: 600;
    border-radius: $default-border-radius * 2;
    text-transform: none;
    letter-spacing: 0.025em;
    transition: all 0.2s ease-in-out;

    &:hover:not(:disabled) {
      transform: translateY($negative-1);
      box-shadow: 0 $spacing-1 $spacing-3 rgba($primary-color, 0.3);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: 0 $spacing-2 $spacing-2 rgba($primary-color, 0.2);
    }

    @media (max-width: $mobile-breakpoint-max) {
      min-width: 140px;
      height: 44px;
    }

    @media (max-width: $media--phone-large-minimum) {
      min-width: 120px;
      height: 40px;
    }
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// Animation for gauge progress
.gauge-progress,
.category-gauge-progress {
  animation: gaugeFill 1.5s ease-out forwards;
}

@keyframes gaugeFill {
  from {
    stroke-dashoffset: 345.575; // Full circumference
  }
  to {
    stroke-dashoffset: var(--gauge-offset);
  }
}

// Accessibility improvements
.gauge-wrapper,
.category-gauge-wrapper {
  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: $spacing-1;
  }
}

// Responsive adjustments
@media (max-width: $media--desktop-minimum) {
  .consolidated-audit-results-container {
    padding: $spacing-3;
  }

  .audit-card-content {
    padding: $spacing-3;
  }

  .audit-section-content {
    padding: $spacing-3;
  }
}

@media (max-width: $mobile-breakpoint-max) {
  .consolidated-audit-results-container {
    padding: $spacing-2;
  }

  .audit-card-content {
    padding: $spacing-3;
  }

  .audit-section-content {
    padding: $spacing-3;
  }

  .gauge-wrapper {
    width: 120px;
    height: 120px;
  }

  .gauge-score {
    @include text-preset-2;
  }

  .gauge-label {
    @include text-preset-6;
  }

  .category-gauge-wrapper {
    width: 80px;
    height: 80px;
  }

  .category-gauge-score {
    @include text-preset-4;
  }

  .category-gauge-label {
    @include text-preset-6;
  }

  .audit-summary h2.overall-score-title {
    @include text-preset-3;
  }

  .audit-section-header .section-title {
    @include text-preset-4;
  }
}

// Print styles
@media print {
  .consolidated-audit-results-container {
    max-width: none;
    padding: 0;
  }

  .audit-card {
    box-shadow: none;
    border: 1px solid $border-color;
  }

  .audit-footer {
    display: none;
  }

  .report-actions {
    display: none;
  }
}
