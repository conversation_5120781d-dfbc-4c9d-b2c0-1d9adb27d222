import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { LexiconModule } from '@galaxy/lexicon';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';

import { AieoInputComponent } from './aieo-input.component';
import { AuditResultsPageComponent } from './audit-results-page/audit-results-page.component';
import { ReportsComponent } from './reports/reports.component';
import { SharedModule } from '../shared/shared.module';
import { WEBLATE_COMPONENT_NAME } from '../core/constants';
import baseTranslation from '../../assets/i18n/en_devel.json';

const routes: Routes = [
  {
    path: '',
    component: AieoInputComponent,
  },
  {
    path: 'reports',
    component: ReportsComponent,
  },
  {
    path: 'results',
    component: AuditResultsPageComponent,
  },
];

@NgModule({
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild(routes),
    TranslateModule,
    LexiconModule.forChild({
      componentName: WEBLATE_COMPONENT_NAME,
      baseTranslation: baseTranslation,
    }),
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatInputModule,
    ReactiveFormsModule,
    GalaxyAlertModule,
    GalaxyLoadingSpinnerModule,
    GalaxyFormFieldModule,
    GalaxyBadgeModule,
  ],
})
export class AieoModule {}
