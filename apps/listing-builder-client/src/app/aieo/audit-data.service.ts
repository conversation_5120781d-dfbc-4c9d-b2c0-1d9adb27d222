import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { AuditData } from './audit-results/audit.interface';

@Injectable({
  providedIn: 'root',
})
export class AuditDataService {
  private auditDataSubject = new BehaviorSubject<AuditData | null>(null);
  public auditData$ = this.auditDataSubject.asObservable();

  setAuditData(auditData: AuditData): void {
    this.auditDataSubject.next(auditData);
  }

  getAuditData(): AuditData | null {
    return this.auditDataSubject.value;
  }

  clearAuditData(): void {
    this.auditDataSubject.next(null);
  }
}
