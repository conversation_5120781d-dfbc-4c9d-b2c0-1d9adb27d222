@use 'design-tokens' as *;

.aieo-nav-tabs {
  display: flex;
  gap: $spacing-2;
  margin-bottom: $spacing-4;
  padding: 0 $spacing-3;
  border-bottom: 1px solid $border-color;
  background: $card-background-color;
  border-radius: $default-border-radius $default-border-radius 0 0;
  box-shadow: 0 $spacing-1 $spacing-2 rgba(0, 0, 0, 0.05);

  .nav-tab {
    padding: $spacing-3 $spacing-4;
    border-radius: $default-border-radius $default-border-radius 0 0;
    border: 1px solid transparent;
    border-bottom: none;
    background: transparent;
    color: $secondary-text-color;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    position: relative;
    overflow: hidden;

    mat-icon {
      margin-right: $spacing-2;
      font-size: $font-preset-3-size;
      width: $font-preset-3-size;
      height: $font-preset-3-size;
      transition: transform 0.3s ease;
    }

    &:hover {
      background: $secondary-background-color;
      color: $primary-text-color;
      transform: translateY($negative-1);

      mat-icon {
        transform: scale(1.1);
      }
    }

    &.active {
      background: $card-background-color;
      color: $primary-color;
      border-color: $border-color;
      border-bottom-color: $card-background-color;
      margin-bottom: $negative-1;
      box-shadow: 0 $negative-2 $spacing-2 rgba(0, 0, 0, 0.1);
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, $primary-color, $blue);
        border-radius: $default-border-radius $default-border-radius 0 0;
      }
    }
  }
}

.reports-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding: $spacing-4;
  background: linear-gradient(135deg, $primary-background-color 0%, $secondary-background-color 100%);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba($primary-color, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba($blue, 0.03) 0%, transparent 50%);
    pointer-events: none;
  }
}

.reports-header {
  text-align: center;
  margin-bottom: $spacing-5;
  max-width: 600px;
  padding: $spacing-4;
  background: $card-background-color;
  border-radius: $default-border-radius * 2;
  box-shadow: 0 $spacing-1 $spacing-3 rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  animation: fadeInDown 0.8s ease-out;
  position: relative;
  z-index: 1;
}

.reports-title {
  @include text-preset-1;
  color: $primary-text-color;
  margin-bottom: $spacing-3;
  line-height: 1.2;
  font-weight: 700;
  background: linear-gradient(135deg, $primary-color, $blue);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  @media (max-width: $mobile-breakpoint-max) {
    @include text-preset-2;
  }

  @media (max-width: $media--phone-large-minimum) {
    @include text-preset-3;
  }
}

.reports-subtitle {
  @include text-preset-4;
  color: $secondary-text-color;
  margin: 0;
  line-height: 1.6;
  animation: fadeInUp 0.8s ease-out 0.2s both;

  @media (max-width: $mobile-breakpoint-max) {
    @include text-preset-4;
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  width: 100%;
  max-width: 500px;
  animation: fadeIn 0.6s ease-out;

  .loading-card {
    width: 100%;
    border-radius: $default-border-radius * 3;
    box-shadow: 0 $spacing-2 $spacing-5 rgba(0, 0, 0, 0.12);
    border: none;
    background: $card-background-color;
    overflow: hidden;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);

    &:hover {
      transform: translateY($negative-2);
      box-shadow: 0 $spacing-3 $spacing-5 rgba($primary-color, 0.1);
    }

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: $spacing-3;
      padding: $spacing-5;

      .loading-icon {
        font-size: $spacing-5;
        width: $spacing-5;
        height: $spacing-5;
        color: $primary-color;
        animation: spin 2s linear infinite;
        filter: drop-shadow(0 $spacing-1 $spacing-2 rgba($primary-color, 0.3));
      }

      p {
        margin: 0;
        color: $secondary-text-color;
        @include text-preset-4;
        font-weight: 500;
        animation: pulse 2s ease-in-out infinite;
      }
    }
  }
}

.reports-list {
  width: 100%;
  max-width: 1200px;
  animation: fadeInUp 0.8s ease-out 0.4s both;

  .reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: $spacing-4;
    margin-top: $spacing-4;

    .report-card {
      border: 1px solid $border-color;
      border-radius: $default-border-radius * 3;
      box-shadow: 0 $spacing-1 $spacing-3 rgba(0, 0, 0, 0.08);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;
      background: $card-background-color;
      position: relative;
      backdrop-filter: blur(10px);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, $primary-color, $blue);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        box-shadow: 0 $spacing-2 $spacing-5 rgba(0, 0, 0, 0.15);
        transform: translateY($negative-4);

        &::before {
          opacity: 1;
        }
      }

      mat-card-content {
        padding: $spacing-4;
      }

      .report-header-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: $spacing-3;
        gap: $spacing-3;

        .report-domain-section {
          flex: 1;
          min-width: 0;

          .report-domain {
            @include text-preset-3;
            font-weight: 600;
            color: $primary-color;
            margin: 0 0 $spacing-1 0;
            word-break: break-all;
            line-height: 1.4;
            text-decoration: none;
            transition: color 0.3s ease;

            &:hover {
              color: $dark-blue;
            }
          }

          .report-brand {
            @include text-preset-5;
            color: $secondary-text-color;
            font-weight: 500;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }

        .report-status-section {
          flex-shrink: 0;

          .status-badge {
            display: flex;
            align-items: center;
            gap: $spacing-1;
            padding: $spacing-1 $spacing-2;
            border-radius: $default-border-radius * 2;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 11px;
            transition: all 0.3s ease;

            .status-icon {
              font-size: $spacing-2;
              width: $spacing-2;
              height: $spacing-2;
              transition: transform 0.3s ease;
            }

            &:hover .status-icon {
              transform: scale(1.2);
            }
          }
        }
      }

      .report-timestamps {
        margin-bottom: $spacing-3;
        padding: $spacing-3;
        background: $primary-background-color;
        border-radius: $default-border-radius * 2;
        border: 1px solid $weak-border-color;
        transition: all 0.3s ease;

        &:hover {
          background: $secondary-background-color;
          border-color: $border-color;
        }

        .timestamp-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: $spacing-2;

          &:last-child {
            margin-bottom: 0;
          }

          .timestamp-label {
            @include text-preset-5;
            color: $secondary-text-color;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .timestamp-value {
            @include text-preset-5;
            color: $primary-text-color;
            font-weight: 600;
          }
        }
      }

      .progress-section {
        margin-bottom: $spacing-3;
        padding: $spacing-3;
        background: $primary-background-color;
        border-radius: $default-border-radius * 2;
        border: 1px solid $weak-border-color;
        transition: all 0.3s ease;

        &:hover {
          background: $secondary-background-color;
          border-color: $border-color;
        }

        .progress-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: $spacing-2;

          .progress-label {
            @include text-preset-5;
            color: $secondary-text-color;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .progress-percentage {
            @include text-preset-5;
            color: $primary-color;
            font-weight: 700;
          }
        }

        .progress-bar {
          height: $spacing-1;
          border-radius: $spacing-1;
        }
      }

      .report-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding-top: $spacing-3;
        border-top: 1px solid $weak-border-color;

        .action-button {
          min-width: 140px;
          height: 40px;
          border-radius: $default-border-radius * 2;
          font-weight: 600;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: $spacing-2;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          text-transform: uppercase;
          letter-spacing: 0.5px;
          font-size: 12px;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          mat-icon {
            font-size: $spacing-3;
            width: $spacing-3;
            height: $spacing-3;
            transition: transform 0.3s ease;
          }

          span {
            @include text-preset-5;
            font-weight: 600;
          }

          &:hover:not(:disabled) {
            transform: translateY($negative-2);
            box-shadow: 0 $spacing-1 $spacing-3 rgba(0, 0, 0, 0.2);

            &::before {
              left: 100%;
            }

            mat-icon {
              transform: scale(1.1);
            }
          }

          &:active:not(:disabled) {
            transform: translateY($negative-1);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
          }
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  width: 100%;
  max-width: 500px;
  animation: fadeIn 0.8s ease-out;

  .empty-card {
    width: 100%;
    border-radius: $default-border-radius * 3;
    box-shadow: 0 $spacing-2 $spacing-5 rgba(0, 0, 0, 0.12);
    border: none;
    background: $card-background-color;
    overflow: hidden;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);

    &:hover {
      transform: translateY($negative-2);
      box-shadow: 0 $spacing-3 $spacing-5 rgba($primary-color, 0.1);
    }

    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: $spacing-5 $spacing-4;
      gap: $spacing-3;

      .empty-icon {
        font-size: $spacing-5 * 1.6;
        width: $spacing-5 * 1.6;
        height: $spacing-5 * 1.6;
        color: $tertiary-text-color;
        opacity: 0.6;
        animation: float 3s ease-in-out infinite;
      }

      h2 {
        @include text-preset-2;
        font-weight: 600;
        color: $primary-text-color;
        margin: 0;
        animation: fadeInUp 0.6s ease-out 0.2s both;
      }

      p {
        color: $secondary-text-color;
        margin: 0;
        @include text-preset-4;
        line-height: 1.6;
        animation: fadeInUp 0.6s ease-out 0.4s both;
      }

      button {
        margin-top: $spacing-2;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        padding: $spacing-2 $spacing-4;
        border-radius: $default-border-radius * 2;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        animation: fadeInUp 0.6s ease-out 0.6s both;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s;
        }

        &:hover {
          transform: translateY($negative-2);
          box-shadow: 0 $spacing-1 $spacing-3 rgba(0, 0, 0, 0.2);

          &::before {
            left: 100%;
          }
        }

        mat-icon {
          margin-right: $spacing-2;
          transition: transform 0.3s ease;
        }

        &:hover mat-icon {
          transform: scale(1.1);
        }
      }
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-$spacing-2);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY($negative-3);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY($spacing-3);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: $mobile-breakpoint-max) {
  .reports-container {
    padding: $spacing-3;
  }

  .reports-header {
    margin-bottom: $spacing-4;
    padding: $spacing-3;
  }

  .reports-list {
    .reports-grid {
      grid-template-columns: 1fr;
      gap: $spacing-3;

      .report-card {
        mat-card-content {
          padding: $spacing-3;
        }

        .report-header-row {
          flex-direction: column;
          align-items: stretch;
          gap: $spacing-2;

          .report-status-section {
            align-self: flex-start;
          }
        }

        .report-timestamps {
          .timestamp-item {
            flex-direction: column;
            align-items: flex-start;
            gap: $spacing-1;
          }
        }

        .report-actions {
          justify-content: stretch;

          .action-button {
            width: 100%;
          }
        }
      }
    }
  }
}

@media (max-width: $media--phone-large-minimum) {
  .reports-container {
    padding: $spacing-2;
  }

  .reports-header {
    margin-bottom: $spacing-3;
    padding: $spacing-3;
  }

  .reports-list {
    .reports-grid {
      .report-card {
        mat-card-content {
          padding: $spacing-3;
        }

        .report-header-row {
          .report-domain-section {
            .report-domain {
              @include text-preset-4;
            }
          }
        }
      }
    }
  }
}
