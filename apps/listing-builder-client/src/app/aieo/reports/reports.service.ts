import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, timer, switchMap, catchError, of } from 'rxjs';
import { AuditReport } from './reports.interface';
import { AuditData } from '../audit-results/audit.interface';
import { SEOService, AIEOAuditReport, AIEOAuditScoreResult } from '../seo.service';

@Injectable({
  providedIn: 'root',
})
export class ReportsService {
  private reportsSubject = new BehaviorSubject<AuditReport[]>([]);
  private currentReportSubject = new BehaviorSubject<AuditReport | null>(null);
  private pollingSubscriptions = new Map<string, any>();

  public reports$ = this.reportsSubject.asObservable();
  public currentReport$ = this.currentReportSubject.asObservable();

  constructor(private seoService: SEOService) {}

  loadReportsFromAPI(businessId: string): Observable<AuditReport[]> {
    return this.seoService.getAllAioAudit(businessId).pipe(
      switchMap((apiReports: AIEOAuditReport[]) => {
        const reports: AuditReport[] = apiReports.map((apiReport) => ({
          id: apiReport.id,
          brandName: apiReport.brandName,
          websiteUrl: apiReport.websiteUrl,
          status: this.mapApiStatusToLocalStatus(apiReport.auditStatus),
          createdAt: apiReport.startDate,
          completedAt: apiReport.auditStatus === 'completed' ? new Date(apiReport.auditDate) : undefined,
          progress: this.calculateProgress(apiReport.auditStatus),
        }));

        this.reportsSubject.next(reports);
        return of(reports);
      }),
      catchError((_error) => {
        return of(this.reportsSubject.value);
      }),
    );
  }

  getReports(): Observable<AuditReport[]> {
    return this.reports$;
  }

  addReport(brandName: string, websiteUrl: string, businessId: string): string {
    const newReport: AuditReport = {
      id: this.generateId(),
      brandName,
      websiteUrl,
      status: 'pending',
      createdAt: new Date(),
    };

    const currentReports = this.reportsSubject.value;
    const updatedReports = [newReport, ...currentReports];
    this.reportsSubject.next(updatedReports);

    this.startStatusPolling(newReport.id, businessId, brandName, websiteUrl);

    return newReport.id;
  }

  updateReportStatus(reportId: string, status: AuditReport['status'], auditData?: AuditData): void {
    const currentReports = this.reportsSubject.value;
    const reportIndex = currentReports.findIndex((report) => report.id === reportId);

    if (reportIndex !== -1) {
      const updatedReport = {
        ...currentReports[reportIndex],
        status,
        ...(status === 'completed' && { completedAt: new Date() }),
        ...(auditData && { auditData }),
      };

      const updatedReports = [...currentReports];
      updatedReports[reportIndex] = updatedReport;

      this.reportsSubject.next(updatedReports);

      if (status === 'completed' || status === 'failed') {
        this.stopStatusPolling(reportId);
      }
    }
  }

  updateReportProgress(reportId: string, progress: number): void {
    const currentReports = this.reportsSubject.value;
    const reportIndex = currentReports.findIndex((report) => report.id === reportId);

    if (reportIndex !== -1) {
      const updatedReport = {
        ...currentReports[reportIndex],
        progress,
      };

      const updatedReports = [...currentReports];
      updatedReports[reportIndex] = updatedReport;

      this.reportsSubject.next(updatedReports);
    }
  }

  getAuditResults(businessId: string, brandName: string, websiteUrl: string): Observable<AIEOAuditScoreResult[]> {
    return this.seoService.getAllAioAuditScoreResults(businessId, brandName, websiteUrl);
  }

  getAuditDetails(businessId: string, brandName: string, websiteUrl: string): Observable<AIEOAuditReport> {
    return this.seoService.getAioAudit(businessId, brandName, websiteUrl);
  }

  setCurrentReport(report: AuditReport): void {
    this.currentReportSubject.next(report);
  }

  getCurrentReport(): AuditReport | null {
    return this.currentReportSubject.value;
  }

  clearCurrentReport(): void {
    this.currentReportSubject.next(null);
  }

  deleteReport(reportId: string): void {
    const currentReports = this.reportsSubject.value;
    const updatedReports = currentReports.filter((report) => report.id !== reportId);
    this.reportsSubject.next(updatedReports);

    this.stopStatusPolling(reportId);
  }

  private startStatusPolling(reportId: string, businessId: string, brandName: string, websiteUrl: string): void {
    this.stopStatusPolling(reportId);

    const subscription = timer(0, 5000)
      .pipe(switchMap(() => this.seoService.getAioAuditStatus(businessId, brandName, websiteUrl)))
      .subscribe({
        next: (statusResponse) => {
          const apiStatus = statusResponse.auditStatus;
          const localStatus = this.mapApiStatusToLocalStatus(apiStatus);
          const progress = this.calculateProgress(apiStatus);

          this.updateReportStatus(reportId, localStatus);
          this.updateReportProgress(reportId, progress);

          if (localStatus === 'completed' || localStatus === 'failed') {
            this.stopStatusPolling(reportId);
          }
        },
        error: (_error) => {
          // Error handling is silent to avoid console spam
        },
      });

    this.pollingSubscriptions.set(reportId, subscription);
  }

  private stopStatusPolling(reportId: string): void {
    const subscription = this.pollingSubscriptions.get(reportId);
    if (subscription) {
      subscription.unsubscribe();
      this.pollingSubscriptions.delete(reportId);
    }
  }

  private mapApiStatusToLocalStatus(apiStatus: string): AuditReport['status'] {
    switch (apiStatus?.toLowerCase()) {
      case 'pending':
      case 'queued':
        return 'pending';
      case 'running':
      case 'in_progress':
      case 'processing':
        return 'in_progress';
      case 'completed':
      case 'finished':
      case 'success':
        return 'completed';
      case 'failed':
      case 'error':
      case 'cancelled':
        return 'failed';
      default:
        return 'pending';
    }
  }

  private calculateProgress(apiStatus: string): number {
    switch (apiStatus?.toLowerCase()) {
      case 'pending':
      case 'queued':
        return 0;
      case 'running':
      case 'in_progress':
      case 'processing':
        return 50;
      case 'completed':
      case 'finished':
      case 'success':
        return 100;
      case 'failed':
      case 'error':
      case 'cancelled':
        return 0;
      default:
        return 0;
    }
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  ngOnDestroy(): void {
    this.pollingSubscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
    this.pollingSubscriptions.clear();
  }
}
