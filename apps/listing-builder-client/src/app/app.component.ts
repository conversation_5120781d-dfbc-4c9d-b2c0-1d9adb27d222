import { DOCUMENT, registerLocaleData } from '@angular/common';
import localeCs from '@angular/common/locales/cs';
import localeDe from '@angular/common/locales/de';
import localeEs419 from '@angular/common/locales/es-419';
import localeFr from '@angular/common/locales/fr';
import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router, RoutesRecognized } from '@angular/router';
import { EnvironmentService } from '@galaxy/core';
import { WhitelabelService } from '@galaxy/partner';
import { AccountsService, ActivationStatus } from '@vendasta/accounts';
import { FeatureFlagMultiResponse } from '@vendasta/businesses';
import { IAMService as IAMServiceV2, User } from '@vendasta/iamv2';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { BehaviorSubject, combineLatest, Observable, of, Subscription } from 'rxjs';
import { catchError, distinctUntilChanged, filter, map, shareReplay, switchMap, take, tap } from 'rxjs/operators';
import { AGIDTOKEN, FEATURE_FLAGS_TOKEN, IS_LB_FREE_EDITION, IS_PARTNER_USER_TOKEN, PIDTOKEN } from './app.module';
import { AppConfig, AppConfigService } from './core/app-config.service';
import { LanguageService } from './core/language-service.service';
import { UserService } from './core/user.service';
import { GetListingDistributionActivationStatusRequest, ListingProductsApiService } from '@vendasta/listing-products';
import { BadgeColor } from '@vendasta/galaxy/badge';
import { SuggestionsService } from './partner/suggestions.service';
import { PagedResponseInterface } from '@vendasta/galaxy/table';
import { ComponentSize } from '@vendasta/galaxy/badge/src/badge.component';
import { newPartnerServiceContext, SSOService } from '@vendasta/sso';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ListingSyncProApiService, ServiceProvider } from '@vendasta/listing-sync-pro';

declare let deployment: string; // Stamped down by vStatic

interface LanguageChoice {
  display: string;
  locale: string;
}

interface NavItem {
  icon?: string;
  label: string;
  url: string;
  badgeText?: string;
  badgeColor?: BadgeColor;
  badgeSize?: ComponentSize;
}

// the second parameter 'fr' is optional
registerLocaleData(localeFr, 'fr');
registerLocaleData(localeCs, 'cs');
registerLocaleData(localeDe, 'de');
registerLocaleData(localeEs419, 'es-419');

@Component({
  selector: 'app-lb-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  providers: [WhitelabelService],
  standalone: false,
})
export class AppComponent implements OnInit, OnDestroy {
  config$: Observable<AppConfig>;
  displayAppNav$: Observable<boolean>;
  navItems$: Observable<NavItem[]>;
  partnerDashboardNavItems$: Observable<NavItem[]>;
  subscriptions_: Subscription[] = [];
  appName$: Observable<string>;
  debugMode: boolean;
  brandingName$: Observable<unknown>;
  appIconUrl$: Observable<unknown>;
  reputationManagementActive$: Observable<boolean>;
  listingDistributionActive$: Observable<boolean>;

  supportedLangs: LanguageChoice[] = [
    { display: 'Čeština', locale: 'cs' },
    { display: 'Deutsch', locale: 'de' },
    { display: 'English', locale: 'en' },
    { display: 'Español', locale: 'es-419' },
    { display: 'Français', locale: 'fr-fr' },
    { display: 'Français canadien', locale: 'fr-ca' },
    { display: 'Nederlands', locale: 'nl' },
  ];
  currentLang$: Observable<string>;
  isBusinessView$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(null);
  isBusinessView$: Observable<boolean>;
  isPartnerView$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  isPartnerView$: Observable<boolean>;
  isWelcomeView$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  isWelcomeView$: Observable<boolean>;
  cobrandingLogoUrl$: Observable<string>;
  currentUser$: Observable<User>;

  hasYext$: Observable<boolean>;

  constructor(
    private router: Router,
    private appConfigService: AppConfigService,
    private analyticsService: ProductAnalyticsService,
    private environmentService: EnvironmentService,
    private languageService: LanguageService,
    private route: ActivatedRoute,
    private accountsService: AccountsService,
    private readonly listingProducts: ListingProductsApiService,
    private iamServiceV2: IAMServiceV2,
    private userService: UserService,
    private suggestionService: SuggestionsService,
    private readonly ssoService: SSOService,
    private readonly snackbarService: SnackbarService,
    private readonly listingSyncProService: ListingSyncProApiService,
    @Inject(DOCUMENT) private document: HTMLDocument,
    @Inject(AGIDTOKEN) private agid$: Observable<string>,
    @Inject(PIDTOKEN) public partnerId$: Observable<string>,
    @Inject(FEATURE_FLAGS_TOKEN) private featureFlags$: Observable<FeatureFlagMultiResponse>,
    @Inject(IS_LB_FREE_EDITION) public isLbFreeEdition$: Observable<boolean>,
    @Inject(IS_PARTNER_USER_TOKEN) public isPartnerUser$: Observable<boolean>,
  ) {
    this.router.events
      .pipe(
        filter((event) => event instanceof RoutesRecognized),
        map((event: RoutesRecognized) => {
          const urlItems = event.url.split('/');
          this.isPartnerView$$.next(urlItems[1] === 'partner');
          this.isBusinessView$$.next(!!event.state.root.firstChild.params.accountGroupId);
          this.isWelcomeView$$.next(urlItems.indexOf('welcome') !== -1);
        }),
        distinctUntilChanged(),
      )
      .subscribe();
    this.config$ = this.appConfigService.appConfig$;
    this.isPartnerView$ = this.isPartnerView$$.asObservable();
    this.isWelcomeView$ = this.isWelcomeView$$.asObservable();
    this.isBusinessView$ = this.isBusinessView$$.asObservable();
  }

  ngOnInit(): void {
    this.hasYext$ = this.agid$.pipe(
      take(1),
      switchMap((agid) => {
        return this.listingSyncProService.isListingSyncProActiveForAccount({
          accountGroupId: agid,
        });
      }),
      map((resp) => {
        if (resp?.isActive) {
          return resp.provider === ServiceProvider.SERVICE_PROVIDER_PROVIDER_1;
        }
        return false;
      }),
    );

    this.currentLang$ = this.languageService.currentLang$;

    combineLatest([this.config$, this.partnerId$])
      .pipe(take(1))
      .subscribe(([config, partnerId]) => {
        this.document.getElementById('faviconPlaceholder').setAttribute('href', config.faviconUrl);
        if (typeof deployment !== 'undefined') {
          this.analyticsService.initialize({
            environment: this.environmentService.getEnvironment(),
            projectUUID: 'a34aadb1-09c4-4088-8d54-a61f5be68ec0',
            postHogID: 'WXn7i_3R_M_xDdR5_5hxZKTsYnXA34c_W6L2Sx0v9fk',
            projectName: 'listing-builder-client',
            partner: {
              pid: partnerId,
            },
            businessID: config.agid,
          });
        }
      });

    this.cobrandingLogoUrl$ = combineLatest([this.config$, this.partnerId$]).pipe(
      switchMap(([c, partnerId]: [AppConfig, string]) =>
        this.appConfigService.getWhitelabelConfig(partnerId, c.marketId),
      ),
      map((c) => {
        if (c.enabledFeatures && !c.enabledFeatures.includes('co-branding-disabled')) {
          return 'https://vstatic-prod.apigateway.co/listing-builder-client/assets/images/cobranding-logo.png';
        }
        return '';
      }),
    );

    this.appName$ = this.appConfigService.getAppName();

    this.analyticsService.trackProperties({
      userRole: 'unset',
    });
    this.userService.userInfo$.subscribe((userInfo) => {
      let roles = 'smb';
      // default to smb if no other roles. If roles work like personas then there will be cases where the user will
      //  have other roles but SMB will not be specifically mentioned. We will have to check for those in posthog.
      //  I added a default role here so it would not be empty and we could see the roles have been set or not.
      // Example covered case: no role would be SMB.
      // Ex case not covered: crm_role with nothing else would be an SMB.
      // Counter example: having both a crm_role and partner roles would mean they are not an SMB.
      if (userInfo.roles.length > 0) {
        roles = userInfo.roles.join(' ');
      }
      this.analyticsService.trackProperties({
        userRole: roles,
      });
    });

    this.isLbFreeEdition$.subscribe((isLBFreeEdition) => {
      this.analyticsService.trackProperties({
        isStandardEdition: String(isLBFreeEdition),
      });
    });

    this.brandingName$ = combineLatest([this.config$, this.partnerId$]).pipe(
      switchMap(([config, partnerId]: [AppConfig, string]) => {
        // We set a default name here because the branding service will return an error if the partner does not have
        return this.appConfigService
          .getBranding(partnerId, config.marketId)
          .pipe(map((branding) => branding.name || 'Company Name'));
      }),
      catchError(() => {
        return of(undefined);
      }),
    );

    this.route.queryParams
      .pipe(
        tap((queryParams) => {
          if (queryParams.debug) {
            this.debugMode = queryParams.debug === 'true';
          }
        }),
      )
      .subscribe();

    this.displayAppNav$ = this.featureFlags$.pipe(map((flags) => flags['business_navigation']));

    this.reputationManagementActive$ = this.agid$.pipe(
      switchMap((businessID) =>
        this.accountsService.getMultiActivationStatuses([businessID]).pipe(
          map((activations) => {
            return !!activations.filter(
              (activation) =>
                activation.productId === 'RM' &&
                (activation.status === ActivationStatus.ACTIVATION_STATUS_ACTIVATED ||
                  activation.status == ActivationStatus.ACTIVATION_STATUS_CANCELED),
            ).length;
          }),
        ),
      ),
    );

    this.listingDistributionActive$ = this.agid$.pipe(
      switchMap((businessID) =>
        this.listingProducts
          .getListingDistributionActivationStatus(
            new GetListingDistributionActivationStatusRequest({
              accountGroupId: businessID,
            }),
          )
          .pipe(map((resp) => resp?.isActive || false)),
      ),
      shareReplay(1),
    );

    this.navItems$ = combineLatest([
      this.config$,
      this.featureFlags$,
      this.reputationManagementActive$,
      this.appConfigService.lspActive$,
      this.listingDistributionActive$,
    ]).pipe(
      map(
        ([config, featureFlags, rmActive, lspActive, ldActive]: [
          AppConfig,
          FeatureFlagMultiResponse,
          boolean,
          boolean,
          boolean,
        ]) => {
          return this.buildNavItems(
            config,
            featureFlags['lb_display_listing_profile_tab'],
            rmActive,
            lspActive,
            ldActive,
            featureFlags['local_seo_bing_insights'],
          );
        },
      ),
    );

    this.appIconUrl$ = combineLatest([this.config$]).pipe(switchMap(() => this.appConfigService.getAppIcon()));

    this.subscriptions_.push(this.agid$.subscribe());

    this.partnerDashboardNavItems$ = combineLatest([
      this.partnerId$,
      this.suggestionService.get({
        pagingOptions: {
          pageSize: 1,
        },
      }),
    ]).pipe(
      map(([partnerId, suggestionResponse]: [string, PagedResponseInterface<any>]) =>
        this.buildPartnerNavItems(partnerId, suggestionResponse.pagingMetadata.totalResults),
      ),
    );
  }

  nagivateToAdminPage(partnerId: string) {
    const urlTree = this.router.createUrlTree([`/partner/${partnerId}/dashboard`], {
      queryParamsHandling: 'preserve',
    });
    const url = this.router.serializeUrl(urlTree);
    window.open(url, '_blank');
  }

  navigateToYext(partnerID: string): void {
    this.ssoService.getEntryUrl('yext', newPartnerServiceContext(partnerID)).subscribe({
      next: (url) => window.open(url, '_blank'),
      error: (_) => this.snackbarService.openErrorSnack('YEXT_SSO.FAILED_TO_GET_URL'),
    });
  }

  private buildNavItems(
    config: AppConfig,
    displayListingProfileTab: boolean,
    reputationManagementActive: boolean,
    listingSyncProActive: boolean,
    citationBuilderActive: boolean,
    analyticsTab: boolean,
  ): NavItem[] {
    const navItems = [];
    if (config.enableOverviewPage) {
      navItems.push({
        id: 'OV',
        label: 'NAVBAR.OVERVIEW',
        url: `/edit/account/${config.agid}/app/overview`,
        icon: 'view_compact',
      });
    }
    if (displayListingProfileTab) {
      navItems.push({
        id: 'LP',
        label: 'NAVBAR.LISTING_PROFILE',
        url: `/edit/account/${config.agid}/app/listing-profile`,
        icon: 'business',
        children: null,
      });
    }
    navItems.push({
      id: 'LS',
      label: 'LISTING_SYNC.PRODUCT.TITLE',
      // This actually refers to the listings-management module. Eventually we will refactor that module into a new
      // listing sync page and a listings-management page but for now it is inconsistently named.
      url: `/edit/account/${config.agid}/app/listing-sync`,
      icon: 'autorenew',
    });
    navItems.push({
      id: 'SE',
      label: 'NAVBAR.SEO',
      url: `/edit/account/${config.agid}/app/local-seo`,
      icon: 'trending_up',
      children: null,
    });
    if (config.enableLocationPage) {
      navItems.push({
        id: 'ML',
        label: 'NAVBAR.MY_LISTING',
        url: `/edit/account/${config.agid}/app/website`,
        icon: 'place',
      });
    }
    if (config.enableCitationsPage && (reputationManagementActive || listingSyncProActive || citationBuilderActive)) {
      const label = citationBuilderActive ? 'CITATIONS.PRODUCT_TITLE' : 'NAVBAR.CITATIONS';
      navItems.push({
        id: 'CI',
        label: label,
        url: `/edit/account/${config.agid}/app/citations`,
        icon: 'pageview',
      });
    }

    if (analyticsTab) {
      navItems.push({
        id: 'GI',
        label: 'NAVBAR.ANALYTICS',
        url: `/edit/account/${config.agid}/app/analytics`,
        icon: 'assessment',
        showUpgradeChip: false,
      });
    } else {
      navItems.push({
        id: 'GI',
        label: 'NAVBAR.GOOGLE_INSIGHTS',
        url: `/edit/account/${config.agid}/app/analytics`,
        icon: 'assessment',
        showUpgradeChip: false,
      });
    }
    navItems.push({
      id: 'AI',
      label: 'NAVBAR.AIEO',
      url: `/edit/account/${config.agid}/app/aieo`,
      icon: 'smart_toy',
      showUpgradeChip: false,
    });

    return navItems;
  }

  private buildPartnerNavItems(partnerId: string, totalSuggestions: number): NavItem[] {
    return [
      {
        label: 'PARTNER.SETTINGS.NAVBAR.ITEM.LISTING_SYNC',
        url: `/partner/${partnerId}/lsp`,
        icon: 'repeat',
      },
      {
        label: 'PARTNER.SETTINGS.NAVBAR.ITEM.DATA_OPTIMIZATION',
        url: `/partner/${partnerId}/data`,
        icon: 'storefront',
        badgeText: totalSuggestions.toString(),
        badgeColor: 'blue',
        badgeSize: 'normal',
      },
      {
        label: 'PARTNER.SETTINGS.NAVBAR.ITEM.PRODUCT_SETTINGS',
        url: `/partner/${partnerId}/dashboard`,
        icon: 'settings',
      },
    ];
  }

  ngOnDestroy(): void {
    this.subscriptions_.forEach((s) => s.unsubscribe());
  }
}
