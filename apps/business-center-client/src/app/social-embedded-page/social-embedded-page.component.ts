import { Component } from '@angular/core';
import { Observable, map } from 'rxjs';
import { TranslateModule } from '@ngx-translate/core';
import { ComposeButtonComponent } from '../brands/compose-button/compose-button.component';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { NavTabsComponent } from '@vendasta/business-nav';
import { LaunchAppButtonComponent } from '../launch-app-button/launch-app-button.component';
import { productIds } from '../../globals';
import { ProductService } from '../core/product.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-social-embedded-page',
  imports: [
    GalaxyPageModule,
    ComposeButtonComponent,
    TranslateModule,
    NavTabsComponent,
    LaunchAppButtonComponent,
    CommonModule,
  ],
  templateUrl: './social-embedded-page.component.html',
  styleUrls: ['./social-embedded-page.component.scss'],
})
export default class SocialEmbeddedPageComponent {
  protected readonly title$: Observable<string>;
  constructor(private readonly productService: ProductService) {
    this.title$ = this.productService.activeAccessibleProducts$.pipe(
      map((products) => {
        const product = products.find((p) => p.productId === productIds.socialMarketing);
        return product?.name ?? 'PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.SOCIAL';
      }),
    );
  }
}
