import { productIds } from '../../../globals';

export interface MetricBreakdown {
  nameKey: string;
  marketplaceId: string;
  productName?: string;
  anchorLink?: string;
  tooltip?: string;
  iconURL?: string; // If not provided, we will fetch the product image URL using marketplaceId
}

export interface MetricInfo {
  socialFacebookImpressions: MetricBreakdown;
  adIntelImpressions: MetricBreakdown;
  gmbViews: MetricBreakdown;
  gmbSearches: MetricBreakdown;
  bingViews: MetricBreakdown;
  emailsOpened: MetricBreakdown;
  socialFacebookEngagement: MetricBreakdown;
  adIntelClicks: MetricBreakdown;
  gmbVisits: MetricBreakdown;
  rmReviews: MetricBreakdown;
  bingWebsiteClicks: MetricBreakdown;
  emailClicks: MetricBreakdown;
  adIntelConversions: MetricBreakdown;
  adIntelPhoneCalls: MetricBreakdown;
  gmbPhoneCalls: MetricBreakdown;
  gmbDirections: MetricBreakdown;
  gmbBookings: MetricBreakdown;
  gmbConversations: MetricBreakdown;
  gmbFoodOrders: MetricBreakdown;
  bingPhoneClicks: MetricBreakdown;
  inboxWebChat: MetricBreakdown;
  inboxGMB: MetricBreakdown;
  inboxFacebook: MetricBreakdown;
  inboxSMS: MetricBreakdown;
  inboxAIVoice: MetricBreakdown;
  inboxWhatsApp: MetricBreakdown;
  inboxInstagram: MetricBreakdown;
  yesware: MetricBreakdown;
  inboxConversationEngagement: MetricBreakdown;
}

export enum ANCHOR_LINKS {
  SOCIAL = 'social',
  ADVERTISING = 'advertising',
  GOOGLE_BUSINESS_PROFILE = 'gmb',
  BING = 'bing',
  REPUTATION = 'reputation',
  LEADS = 'leads',
}

export const METRICS: MetricInfo = {
  // impressions
  socialFacebookImpressions: {
    nameKey: 'FUNNEL_METRICS.METRICS.IMPRESSIONS.SOURCES.FACEBOOK.REACH.TITLE',
    marketplaceId: 'SM',
    anchorLink: ANCHOR_LINKS.SOCIAL,
  },
  adIntelImpressions: {
    nameKey: 'FUNNEL_METRICS.METRICS.IMPRESSIONS.SOURCES.ADINTEL.IMPRESSIONS.TITLE',
    marketplaceId: 'MP-94072e44d5364872b672d7ab4fc7a7e8',
    anchorLink: ANCHOR_LINKS.ADVERTISING,
  },
  gmbViews: {
    nameKey: 'FUNNEL_METRICS.METRICS.IMPRESSIONS.SOURCES.GMB.VIEWS.TITLE',
    marketplaceId: 'MS',
    productName: 'Google Business Profile',
    anchorLink: ANCHOR_LINKS.GOOGLE_BUSINESS_PROFILE,
    iconURL: 'google-logo.jpg',
  },
  gmbSearches: {
    nameKey: 'FUNNEL_METRICS.METRICS.IMPRESSIONS.SOURCES.GMB.SEARCHES.TITLE',
    marketplaceId: 'MS',
    productName: 'Google Business Profile',
    anchorLink: ANCHOR_LINKS.GOOGLE_BUSINESS_PROFILE,
    tooltip: 'FUNNEL_METRICS.METRICS.IMPRESSIONS.SOURCES.GMB.SEARCHES.TOOLTIP',
    iconURL: 'google-logo.jpg',
  },
  bingViews: {
    nameKey: 'FUNNEL_METRICS.METRICS.IMPRESSIONS.SOURCES.BING.VIEWS.TITLE',
    marketplaceId: 'MS',
    productName: 'Views',
    anchorLink: ANCHOR_LINKS.BING,
    iconURL: 'bing-logo.jpg',
  },
  emailsOpened: {
    nameKey: 'FUNNEL_METRICS.METRICS.IMPRESSIONS.SOURCES.CTCT.EMAILS_OPENED.TITLE',
    marketplaceId: productIds.constantContact,
  },
  // engagement
  socialFacebookEngagement: {
    nameKey: 'FUNNEL_METRICS.METRICS.ENGAGEMENT.SOURCES.FACEBOOK.ENGAGEMENT.TITLE',
    marketplaceId: 'SM',
    anchorLink: ANCHOR_LINKS.SOCIAL,
  },
  adIntelClicks: {
    nameKey: 'FUNNEL_METRICS.METRICS.ENGAGEMENT.SOURCES.ADINTEL.AD_CLICKED.TITLE',
    marketplaceId: 'MP-94072e44d5364872b672d7ab4fc7a7e8',
    anchorLink: ANCHOR_LINKS.ADVERTISING,
  },
  gmbVisits: {
    nameKey: 'FUNNEL_METRICS.METRICS.ENGAGEMENT.SOURCES.GMB.VISITS.TITLE',
    marketplaceId: 'MS',
    productName: 'Google Business Profile',
    anchorLink: ANCHOR_LINKS.GOOGLE_BUSINESS_PROFILE,
    iconURL: 'google-logo.jpg',
  },
  rmReviews: {
    nameKey: 'FUNNEL_METRICS.METRICS.ENGAGEMENT.SOURCES.RM.REVIEWS.TITLE',
    marketplaceId: 'RM',
    anchorLink: ANCHOR_LINKS.REPUTATION,
  },
  bingWebsiteClicks: {
    nameKey: 'FUNNEL_METRICS.METRICS.ENGAGEMENT.SOURCES.BING.WEBSITE_VISIT.TITLE',
    marketplaceId: 'MS',
    productName: 'Website',
    anchorLink: ANCHOR_LINKS.BING,
    iconURL: 'bing-logo.jpg',
  },
  emailClicks: {
    nameKey: 'FUNNEL_METRICS.METRICS.ENGAGEMENT.SOURCES.CTCT.EMAILS_CLICKED.TITLE',
    marketplaceId: productIds.constantContact,
  },
  inboxConversationEngagement: {
    nameKey: 'FUNNEL_METRICS.METRICS.ENGAGEMENT.SOURCES.INBOX.CONVERSATION.TITLE',
    marketplaceId: productIds.inboxPro,
    productName: 'Inbox Pro',
    anchorLink: ANCHOR_LINKS.LEADS,
  },
  // leads
  adIntelConversions: {
    nameKey: 'FUNNEL_METRICS.METRICS.LEADS.SOURCES.ADINTEL.AD_CONVERSION.TITLE',
    marketplaceId: 'MP-94072e44d5364872b672d7ab4fc7a7e8',
    anchorLink: ANCHOR_LINKS.ADVERTISING,
  },
  adIntelPhoneCalls: {
    nameKey: 'FUNNEL_METRICS.METRICS.LEADS.SOURCES.ADINTEL.PHONE_CALLS.TITLE',
    marketplaceId: 'MP-94072e44d5364872b672d7ab4fc7a7e8',
    anchorLink: ANCHOR_LINKS.ADVERTISING,
  },
  gmbPhoneCalls: {
    nameKey: 'FUNNEL_METRICS.METRICS.LEADS.SOURCES.GMB.PHONE_CALLS.TITLE',
    marketplaceId: 'MS',
    productName: 'Google Business Profile',
    anchorLink: ANCHOR_LINKS.GOOGLE_BUSINESS_PROFILE,
    iconURL: 'google-logo.jpg',
  },
  gmbDirections: {
    nameKey: 'FUNNEL_METRICS.METRICS.LEADS.SOURCES.GMB.DIRECTIONS.TITLE',
    marketplaceId: 'MS',
    productName: 'Google Business Profile',
    anchorLink: ANCHOR_LINKS.GOOGLE_BUSINESS_PROFILE,
    iconURL: 'google-logo.jpg',
  },
  gmbBookings: {
    nameKey: 'FUNNEL_METRICS.METRICS.LEADS.SOURCES.GMB.BOOKINGS.TITLE',
    marketplaceId: 'MS',
    productName: 'Google Business Profile',
    anchorLink: ANCHOR_LINKS.GOOGLE_BUSINESS_PROFILE,
    iconURL: 'google-logo.jpg',
  },
  gmbConversations: {
    nameKey: 'FUNNEL_METRICS.METRICS.LEADS.SOURCES.GMB.CONVERSATIONS.TITLE',
    marketplaceId: 'MS',
    productName: 'Google Business Profile',
    anchorLink: ANCHOR_LINKS.GOOGLE_BUSINESS_PROFILE,
    iconURL: 'google-logo.jpg',
  },
  gmbFoodOrders: {
    nameKey: 'FUNNEL_METRICS.METRICS.LEADS.SOURCES.GMB.FOOD_ORDERS.TITLE',
    marketplaceId: 'MS',
    productName: 'Google Business Profile',
    anchorLink: ANCHOR_LINKS.GOOGLE_BUSINESS_PROFILE,
    iconURL: 'google-logo.jpg',
  },
  bingPhoneClicks: {
    nameKey: 'FUNNEL_METRICS.METRICS.LEADS.SOURCES.BING.PHONE_CALLS.TITLE',
    marketplaceId: 'MS',
    productName: 'Phone',
    anchorLink: ANCHOR_LINKS.BING,
    iconURL: 'bing-logo.jpg',
  },
  inboxWebChat: {
    nameKey: 'Web Chat',
    marketplaceId: productIds.conversationAI,
    anchorLink: '',
    iconURL: 'webchat-icon.svg',
  },
  inboxGMB: {
    nameKey: 'Google Business Messages',
    marketplaceId: productIds.conversationAI,
    anchorLink: '',
    iconURL: 'google-logo.jpg',
  },
  inboxFacebook: {
    nameKey: 'Facebook Messenger',
    marketplaceId: productIds.conversationAI,
    anchorLink: '',
    iconURL: 'facebook.svg',
  },
  inboxSMS: {
    nameKey: 'FUNNEL_METRICS.METRICS.LEADS.SOURCES.SMS.TITLE',
    marketplaceId: productIds.conversationAI,
    anchorLink: '',
    iconURL: 'phone.svg',
  },
  inboxAIVoice: {
    nameKey: 'AI Voice Receptionist',
    marketplaceId: productIds.conversationAI,
    anchorLink: '',
    iconURL: 'ai-icon.svg',
  },
  inboxWhatsApp: {
    nameKey: 'Whatsapp',
    marketplaceId: productIds.conversationAI,
    anchorLink: '',
    iconURL: 'whatsapp-icon.svg',
  },
  inboxInstagram: {
    nameKey: 'Instagram',
    marketplaceId: productIds.conversationAI,
    anchorLink: '',
    iconURL: 'instagram.svg',
  },
  yesware: {
    nameKey: 'Yesware',
    marketplaceId: productIds.yesware,
    anchorLink: '',
  },
};
