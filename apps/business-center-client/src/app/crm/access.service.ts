import { inject, Injectable } from '@angular/core';
import { combineLatest, map, Observable, of, shareReplay } from 'rxjs';
import { Views } from '../core/access/constants';
import { StubAccessService } from '../core/access';
import { CrmAccessService, ObjectType } from '@galaxy/crm/static';
import { Feature, PartnerMarketConfigService } from '../partner-market-config';

@Injectable({ providedIn: 'root' })
export class CRMAccessService implements CrmAccessService {
  private readonly accessService = inject(StubAccessService);
  private readonly marketConfigService = inject(PartnerMarketConfigService);
  private readonly viewAccess$ = this.accessService
    .hasAccessToViews([Views.viewAllCampaigns, Views.crmContactEdit, Views.crmCompanyEdit])
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  private readonly hasAutomationFeatureAccess$ = this.marketConfigService.hasAccessToFeature(Feature.automations);
  readonly canAccessAutomations$ = this.hasAutomationFeatureAccess$.pipe(
    shareReplay({ refCount: true, bufferSize: 1 }),
  );
  readonly canRunManualAutomations$ = combineLatest([this.canAccessAutomations$]).pipe(
    map(([canAccessAutomations]) => canAccessAutomations),
  );

  readonly canAccessInbox$ = of(true);

  canAccessCrmObjectType$(objectType: ObjectType): Observable<boolean> {
    switch (objectType) {
      case 'Contact':
        return this.viewAccess$.pipe(
          map((response) => response.get(Views.crmContactEdit)),
          shareReplay({ refCount: true, bufferSize: 1 }),
        );
      case 'Company':
        return this.viewAccess$.pipe(
          map((response) => response.get(Views.crmCompanyEdit)),
          shareReplay({ refCount: true, bufferSize: 1 }),
        );
      default:
        return of(false);
    }
  }
}
