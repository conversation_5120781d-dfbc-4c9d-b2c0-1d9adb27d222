import { Observable } from 'rxjs';

export interface AccessService {
  hasAccessToFeature(feature: Feature): Observable<boolean>;
  hasAccessToFeatures(features: Feature[]): Observable<boolean>;
  hasAccessToViews(views: Views[]): Observable<Map<Views, boolean>>;
}

// This Views enum contains all of the views in business center client that users might request access to
// These should be used to decide if something is viewable and/or accessible to a user or not.
// These are NOT a substitute for proper access checks in the backend
// Intended to be used with hasAccessToViews and ViewAccessApiService
export const enum Views {
  viewAllCampaigns = 'view-all-campaigns',
  crmCompanyEdit = 'crm-company-edit',
  crmContactEdit = 'crm-contact-edit',
  editCrmLeadScore = 'view-edit-crm-score',
}

// This Feature enum contains all of the features that business center client needs to know about.
// These are Product-Led-Growth (PLG) features, not names of feature-flags.
// https://vendasta.jira.com/wiki/spaces/AC/pages/1898807374/Platform+Subscription+Feature+IDs
export const enum Feature {
  crmLeadScore = 'crm-lead-scoring',
}
