{"name": "snapshot-client", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "generators": {"@schematics/angular:component": {"style": "scss"}}, "sourceRoot": "apps/snapshot-client/src", "prefix": "app", "targets": {"static": {"executor": "./tools/builders/static-server:serve", "options": {"buildPath": "dist/apps/snapshot-client", "port": 4201}}, "build": {"executor": "@nx/angular:browser-esbuild", "options": {"outputPath": "dist/apps/snapshot-client", "index": "apps/snapshot-client/src/index.html", "main": "apps/snapshot-client/src/main.ts", "polyfills": ["apps/snapshot-client/src/polyfills.ts"], "tsConfig": "apps/snapshot-client/tsconfig.app.json", "assets": ["apps/snapshot-client/src/favicon.ico", "apps/snapshot-client/src/assets", "apps/snapshot-client/src/service-worker.js", "apps/snapshot-client/src/silent-refresh.html"], "styles": ["apps/snapshot-client/src/styles.scss", "apps/snapshot-client/src/style-overrides.scss", "libs/snapshot/src/lib/sections/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["apps/snapshot-client/src/", "libs/galaxy/styles/", "libs/uikit/src/lib/", "libs/snapshot/src/lib/styles/"]}, "scripts": ["apps/snapshot-client/src/lib/hotjar.js"], "preserveSymlinks": true, "allowedCommonJsDependencies": ["chart.js", "md5", "util", "chartjs-plugin-datalabels"], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"deployUrl": "https://cdn.apigateway.co/snapshot-client/", "optimization": true, "outputHashing": "all", "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "6mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "8kb", "maximumError": "10kb"}]}, "devServer": {"indexHtmlTransformer": "index-html-transform.ts"}, "fast": {"indexHtmlTransformer": "index-html-transform.ts", "sourceMap": false, "optimization": false}}}, "serve": {"options": {"proxyConfig": "apps/snapshot-client/proxy.conf.js"}}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "snapshot-client:build"}}, "test": {"executor": "@nx/jest:jest", "options": {"jestConfig": "apps/snapshot-client/jest.config.ts"}}, "vstatic-publish": {"executor": "./tools/builders/vstatic:publish", "options": {"buildPath": "dist/apps/snapshot-client", "appId": "snapshot-client"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "weblate-upload": {"executor": "./tools/builders/weblate-upload:upload", "options": {"filePath": "apps/snapshot-client/src/assets/i18n/en_devel.json", "weblateProject": "snapshot-report", "weblateComponent": "snapshot-client"}}, "weblate-commit": {"executor": "./tools/builders/weblate-commit:commit", "options": {"weblateProject": "snapshot-report", "weblateComponent": "snapshot-client"}}}, "tags": ["scope:client"]}