import { Injectable } from '@angular/core';
import { FeatureFlagService } from '@galaxy/partner';
import { SnapshotReportService } from '@galaxy/snapshot';
import { SnapshotAIService } from '@vendasta/snapshot';
import { catchError, firstValueFrom, map, of } from 'rxjs';

@Injectable()
export class ChatWidgetService {
  SNAPSHOT_ASSISTANT_WIDGET_FF = 'ai_assistant_in_snapshot';

  constructor(
    private readonly featureFlagService: FeatureFlagService,
    public snapshotService: SnapshotReportService,
    public aiService: SnapshotAIService,
  ) {}

  public async LoadWebchatWidget(snapshotId: string): Promise<void> {
    if (!snapshotId) {
      return;
    }

    const partnerId = await firstValueFrom(this.snapshotService.getPartnerId());
    const hasAccess = await this.hasSnapshotWidgetAccess(partnerId);
    if (!hasAccess) {
      return;
    }

    const widget = await firstValueFrom(
      this.aiService.getWidget(snapshotId).pipe(
        catchError((_) => {
          return of(null);
        }),
      ),
    );
    if (!widget?.enabled) {
      return;
    }

    this.clearWebchatTokens();
    const script = document.createElement('script');
    script.src = widget.widgetUrl;
    script.setAttribute('data-widget-id', widget.widgetId);
    script.setAttribute('snapshot-id', snapshotId);

    document.head.appendChild(script);
    return;
  }

  private hasSnapshotWidgetAccess(partnerId: string): Promise<boolean> {
    return firstValueFrom(
      this.featureFlagService.batchGetStatus(partnerId, '', [this.SNAPSHOT_ASSISTANT_WIDGET_FF]).pipe(
        map((status) => status[this.SNAPSHOT_ASSISTANT_WIDGET_FF]),
        catchError((error) => {
          console.error(error);
          return of(false);
        }),
      ),
    );
  }

  private clearWebchatTokens(): void {
    // Clear the webchat token cookie to force a new conversation for each snapshot
    const webchatWidgetToken = '_webchat_widget_token';
    const expireNow = new Date(0).toUTCString();
    document.cookie = `${webchatWidgetToken}=;expires=${expireNow};path=/`;
  }
}
