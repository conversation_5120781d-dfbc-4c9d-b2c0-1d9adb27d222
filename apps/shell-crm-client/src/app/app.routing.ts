import { Route, RouterModule } from '@angular/router';
import { PageNotFoundComponent } from './page-not-found/page-not-found.component';
import { ModuleWithProviders } from '@angular/core';
import { RedirectPageComponent } from './redirect-page/redirect-page.component';
import { DropdownPageComponent } from './crm/dropdown/dropdown-page.component';
import { LegacyFieldsComponent } from './auxiliary-data/legacy-fields.component';
import { SANDBOX_ROUTES } from './sandbox/sandbox.routes';

const routes: Route[] = [
  {
    path: '',
    component: PageNotFoundComponent,
  },
  {
    path: 'crm',
    loadChildren: () => import('./crm/crm.module').then((m) => m.ShellCrmModule),
  },
  {
    path: 'score',
    loadChildren: () => import('./crm/score.module').then((m) => m.ShellScoreModule),
  },
  {
    path: 'lists',
    loadChildren: () => import('./dynamic-lists/dynamic-lists.module').then((m) => m.DynamicListsModule),
  },
  {
    path: 'legacy-fields',
    component: LegacyFieldsComponent,
  },
  {
    path: 'custom-fields',
    loadChildren: () =>
      import('./custom-field-management/custom-field-management.module').then((m) => m.CrmCustomFieldManagementModule),
  },
  {
    path: 'sandbox',
    loadChildren: () => import('./sandbox/sandbox.routes').then((m) => m.SANDBOX_ROUTES),
  },
  {
    path: 'pipeline/settings',
    loadChildren: () => import('./pipeline-settings/pipeline-settings.module').then((m) => m.CrmPipelineSettingsModule),
  },
  {
    path: 'leaderboard',
    loadChildren: () => import('./leaderboard/leaderboard.module').then((m) => m.ShellLeaderboardModule),
  },
  // external redirects - these routes will allow us to redirect to PCC using the relative path and params
  {
    path: 'task-manager/reporting',
    component: RedirectPageComponent,
  },
  {
    path: 'dropdown',
    component: DropdownPageComponent,
  },
  // fallback
  {
    path: '**',
    component: RedirectPageComponent,
  },
];

export const AppRoutingModule: ModuleWithProviders<any> = RouterModule.forRoot(routes);
