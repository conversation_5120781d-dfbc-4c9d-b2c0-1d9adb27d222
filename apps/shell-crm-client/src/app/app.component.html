<div class="top-nav-bar">
  <button mat-icon-button (click)="nav.toggle()">
    <mat-icon>menu</mat-icon>
  </button>
</div>

<glxy-nav #nav [fixedTopGap]="40" appName="reports" class="glxy-nav--light-theme">
  <glxy-nav-panel style="width: 276px">
    <glxy-nav-header>
      <div class="sidenav-header">
        <mat-icon inline style="font-size: 54px; opacity: 0.75">bolt</mat-icon>
        <div class="sidenav-header-text">CRM</div>
      </div>
    </glxy-nav-header>

    <glxy-nav-item route="/crm/company" icon="business">Company</glxy-nav-item>
    <glxy-nav-item route="/crm/contact" icon="contacts">Contact</glxy-nav-item>
    <glxy-nav-item route="/crm/task" icon="contacts">Sales task</glxy-nav-item>
    <glxy-nav-item route="/crm/opportunity" icon="monetization_on">Opportunity</glxy-nav-item>
    <glxy-nav-item route="/crm/activities/feed" icon="feed">Activity feed</glxy-nav-item>
    <glxy-nav-item route="/custom-fields" icon="contacts">Custom fields</glxy-nav-item>
    <glxy-nav-item route="/score" icon="tune">Score settings</glxy-nav-item>
    <glxy-nav-item route="/lists" icon="list">Lists</glxy-nav-item>
    <glxy-nav-item route="/sandbox" icon="widgets">Sandbox</glxy-nav-item>
    <glxy-nav-item route="/crm/pipeline/settings" icon="settings">Pipeline settings</glxy-nav-item>
    <glxy-nav-item route="/leaderboard" icon="leaderboard">Leaderboard</glxy-nav-item>
    <glxy-nav-item route="/crm/import" icon="download">Import</glxy-nav-item>
    @for (customObjectRoute of customObjectRoutes$ | async; track customObjectRoute.id) {
      <glxy-nav-item [route]="customObjectRoute.route" icon="dashboard_customize">{{
        customObjectRoute.name
      }}</glxy-nav-item>
    }

    <glxy-nav-footer class="footer-actions">
      <button type="button" mat-stroked-button (click)="toggleColorScheme()">
        @if (currentTheme === 'glxy-light-theme') {
          <mat-icon>brightness_2</mat-icon>
        } @else {
          <mat-icon>wb_sunny</mat-icon>
        }
        Color theme
      </button>
      <button type="button" mat-stroked-button (click)="changePartnerId()">
        <mat-icon>cached</mat-icon>
        Partner ID ({{ partnerIdService.partnerId$ | async }})
      </button>
      <button type="button" mat-stroked-button (click)="changeMarketId()">
        <mat-icon>cached</mat-icon>
        Market ({{ marketIdService.marketId$ | async }})
      </button>
      <button type="button" mat-stroked-button (click)="changeUserId()">
        <mat-icon>cached</mat-icon>
        User ID
      </button>
      <button type="button" mat-stroked-button (click)="changeLocalSession()">
        <mat-icon>cached</mat-icon>
        Session Token
      </button>
      <div>
        <a href="https://iam-demo.apigateway.co/" target="_blank">IAM Demo</a>
        |
        <a href="https://iam-prod.apigateway.co/" target="_blank">IAM Prod</a>
      </div>
    </glxy-nav-footer>
  </glxy-nav-panel>

  <router-outlet></router-outlet>
</glxy-nav>
