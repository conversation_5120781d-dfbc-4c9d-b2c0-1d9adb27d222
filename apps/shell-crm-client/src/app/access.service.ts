import { Injectable } from '@angular/core';
import { map, Observable, of, shareReplay } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { ObjectType } from '@galaxy/crm/static';
import { HasViewAccessRequestInterface, ViewAccessApiService } from '@vendasta/platform-users';
import { PartnerIdService } from './partner-id.service';

export const enum Views {
  crmCompanyEdit = 'crm-company-edit',
  crmContactEdit = 'crm-contact-edit',
}

@Injectable({ providedIn: 'root' })
export class CRMAccessService {
  private readonly viewAccess$ = this.hasAccessToViews([Views.crmContactEdit, Views.crmCompanyEdit]).pipe(
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  features: Observable<string[]>;

  constructor(
    private readonly viewAccessService: ViewAccessApiService,
    private partnerIdService: PartnerIdService,
  ) {}

  hasAccessToViews(views: Views[]): Observable<Map<Views, boolean>> {
    return this.partnerIdService.partnerId$.pipe(
      switchMap((pid) =>
        this.viewAccessService.hasViewAccess({
          partnerId: pid,
          viewIds: views,
        } as HasViewAccessRequestInterface),
      ),
      map((response) => {
        const hasAccess = new Map<Views, boolean>();
        response.access.forEach((view) => {
          const viewId = view.viewId as Views;
          hasAccess.set(viewId, view.hasAccess ?? false);
        });
        return hasAccess;
      }),
    );
  }

  canAccessCrmObjectType$(objectType: ObjectType): Observable<boolean> {
    switch (objectType) {
      case 'Contact':
        return this.viewAccess$.pipe(
          map((response) => response.get(Views.crmContactEdit)),
          shareReplay({ refCount: true, bufferSize: 1 }),
        );
      case 'Company':
        return this.viewAccess$.pipe(
          map((response) => response.get(Views.crmCompanyEdit)),
          shareReplay({ refCount: true, bufferSize: 1 }),
        );
      default:
        return of(false);
    }
  }
}
