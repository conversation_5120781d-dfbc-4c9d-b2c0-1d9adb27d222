import { HttpResponse } from '@angular/common/http';
import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { FieldMask, AppPricingService as MarketplaceAppsPricingService } from '@galaxy/marketplace-apps';
import {
  AppType,
  ApprovalProcess,
  BillingProcess,
  Edition,
  EditionsService,
  Integration,
  MarketplaceApp,
  Release,
  State,
} from '@galaxy/marketplace-apps/v1';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { FulfillmentOrderConfigApiService, ReleaseType } from '@vendasta/marketplace-apps';
import { AppPrice } from '@vendasta/marketplace-apps/lib/_internal/objects/app-price';
import { convertCentsToDollars } from '@vendasta/shared';
import {
  BehaviorSubject,
  Observable,
  Subscription,
  combineLatest,
  identity,
  combineLatest as observableCombineLatest,
  of as observableOf,
  of,
} from 'rxjs';
import {
  catchError,
  distinctUntilChanged,
  filter,
  map,
  mergeMap,
  scan,
  shareReplay,
  startWith,
  switchMap,
  take,
} from 'rxjs/operators';
import { AddonService } from '../../core/addon.service';
import { AuthService } from '../../core/auth/auth.service';
import { CategoryService } from '../../core/category.service';
import { EditionDiffModel } from '../../core/edition-diff/edition-diff-model';
import { EditionDiffService } from '../../core/edition-diff/edition-diff.service';
import { EditionMarketingDiffModel } from '../../core/edition-marketing-diff/edition-marketing-diff-model';
import { EditionMarketingDiffService } from '../../core/edition-marketing-diff/edition-marketing-diff.service';
import { FulfillmentConfigDiffService } from '../../core/fulfillment-config-diff/fulfillment-config-diff.service';
import { ChangedField, MarketplaceAppDiffModel } from '../../core/marketplace-app-diff/marketplace-app-diff-model';
import { MarketplaceAppDiffService } from '../../core/marketplace-app-diff/marketplace-app-diff.service';
import { MarketplaceAppService } from '../../core/marketplace-app/marketplace-app.service';
import { SuspendAppWarningDialogComponent } from './suspend-warning-dialog/suspend-app-warning-dialog.component';
import {
  SetReportingCodeDialogComponent,
  SetReportingCodeDialogData,
} from './set-reporting-code-dialog/set-reporting-code-dialog.component';
import { AppReportingCodeService } from '@galaxy/marketplace-apps';
import { AppReportingCodeOverrideInterface } from '@vendasta/marketplace-apps';
import { reportingCodeOverrideTypeForDisplay } from './utils/reporting-code-override';
import { PARTNER_ID } from '../../tokens';

const LIST_EDITIONS_DEFAULT_PAGE_SIZE = 100;

@Component({
  selector: 'app-marketplace-app-diff',
  templateUrl: './app-diff.component.html',
  styleUrls: ['./app-diff.component.scss'],
  standalone: false,
})
export class AppDiffComponent implements OnInit, OnDestroy {
  public commonFormElementsMap: { [ID: string]: string } = {
    businessName: 'Company Name',
    businessAddress: 'Business Address',
    businessPhoneNumber: 'Business Phone Number',
    businessWebsite: 'Business Website',
    businessAccountGroupId: 'Account ID',
    salespersonName: 'Sales Person Name',
    salespersonPhoneNumber: 'Sales Person Phone Number',
    salespersonEmail: 'Sales Person Email',
    contactName: 'Contact Name',
    contactPhoneNumber: 'Contact Phone Number',
    contactEmail: 'Contact Email',
  };

  public commonFormElementsList = [
    'businessName',
    'businessAddress',
    'businessPhoneNumber',
    'businessWebsite',
    'businessAccountGroupId',
    'salespersonName',
    'salespersonPhoneNumber',
    'salespersonEmail',
    'contactName',
    'contactPhoneNumber',
    'contactEmail',
  ];

  public differences$: Observable<MarketplaceAppDiffModel>;
  public unpublishedAddonNames$: Observable<string[]>;
  public editionMarketingdiffs$: Observable<EditionMarketingDiffModel[]>;
  public editionDiffs$: Observable<EditionDiffModel[]>;
  appId$: Observable<string>;
  draft$$: BehaviorSubject<MarketplaceApp> = new BehaviorSubject(null);
  published$$: BehaviorSubject<MarketplaceApp> = new BehaviorSubject(null);
  isSuperAdmin$: Observable<boolean>;
  dateChanged$: Observable<string>;
  areChanges$: Observable<boolean>;
  generalSetting: any[];
  integration: any[];
  resellerMarketing: any[];
  endUserMarketing: any[];
  orderForm: any[];
  fulfillmentConfig$: Observable<ChangedField[]>;
  fulfillmentDisplayNameOverrides = {
    orderForm: 'Fulfillment Form Question',
    footerText: 'Footer',
    usesFulfillmentForm: 'Fulfillment Form Enabled',
  };
  public lmiCategoriesMap: { [ID: number]: string } = {
    7: 'Advertising',
    2: 'Content and Experience',
    4: 'Reputation',
    5: 'SEO',
    6: 'Social',
    1: 'Website',
    3: 'Listings',
  };
  public deliveryMethodMap: { [ID: number]: string } = {
    1: 'Product',
    2: 'Service',
  };
  public serviceModelMap: { [ID: string]: string } = {
    diy: 'DIY (Do it yourself)',
    diwm: 'DIWM (Do it with me)',
    difm: 'DIFM (Do it for me)',
  };
  private editions$: Observable<Map<string, Edition>>;
  private fetchDiffs$$: BehaviorSubject<null> = new BehaviorSubject(null);
  private _loading = true;
  private _apiError = false;
  private subscriptions: Subscription[] = [];
  public AppType = AppType;
  ApprovalProcess = ApprovalProcess;

  isOpen$: Observable<boolean>;
  isSuspended: boolean;
  requestedReview = false;
  isMarketingServicesVendor$: Observable<boolean>;
  currentReportingCode$: Observable<AppReportingCodeOverrideInterface | null>;

  categoryMap: Map<string, string>;

  constructor(
    private snackbarService: SnackbarService,
    private marketplaceAppService: MarketplaceAppService,
    private differenceService: MarketplaceAppDiffService,
    private authService: AuthService,
    private editionService: EditionsService,
    private editionMarketingDiffService: EditionMarketingDiffService,
    private editionDiffService: EditionDiffService,
    private dialog: MatDialog,
    private categoryService: CategoryService,
    private addonService: AddonService,
    private fulfillmentOrderConfigApiService: FulfillmentOrderConfigApiService,
    private appPricingService: MarketplaceAppsPricingService,
    private fulfillmentConfigDiffService: FulfillmentConfigDiffService,
    private appReportingCodeService: AppReportingCodeService,
    @Inject(PARTNER_ID) private partnerId$: Observable<string>,
  ) {}

  /**
   * Returns the edition name, defaults to edition ID if edition is not found in `editions`
   * @param editionId - the edition ID to get the display name for
   * @param editions - a map of edition ID to Edition
   */
  static getDisplayNameForEdition(editionId: string, editions: Map<string, Edition>): string {
    if (!editions) {
      return editionId;
    }
    const e = editions.get(editionId);
    return e ? e.name : editionId;
  }

  public ngOnInit(): void {
    this.generalSetting = [];
    this.integration = [];
    this.resellerMarketing = [];
    this.endUserMarketing = [];
    this.orderForm = [];
    this.isSuperAdmin$ = this.authService.isSuperAdmin$.pipe(shareReplay(1));

    this.isMarketingServicesVendor$ = this.partnerId$.pipe(
      //TODO: ADD VMKA back in once frontend is functional
      // map((partnerId) => partnerId === 'VMKA' || partnerId === 'T41P'),
      map((partnerId) => partnerId === 'T41P'),
      shareReplay(1),
    );

    this.appId$ = combineLatest([
      this.marketplaceAppService.currentAppId$,
      this.addonService.currentAddonId$.pipe(startWith('')),
    ]).pipe(
      map(([appId, addonId]) => (addonId ? addonId : appId)),
      take(2),
    );

    this.currentReportingCode$ = this.appId$.pipe(
      distinctUntilChanged(),
      filter((appId) => !!appId),
      switchMap((appId) => this.appReportingCodeService.getAppReportingCode(appId)),
      shareReplay(1),
    );

    this.editions$ = this.appId$.pipe(
      distinctUntilChanged(),
      filter((appId) => !!appId),
      switchMap((appId) => this.editionService.listDraftEditionsForApp(appId, null, LIST_EDITIONS_DEFAULT_PAGE_SIZE)),
      map((editions) => {
        const editionMap = new Map<string, Edition>();
        if (editions) {
          editions.forEach((e) => editionMap.set(e.editionId || '', e));
        }
        return editionMap;
      }),
    );
    this.subscriptions.push(
      this.appId$
        .pipe(
          distinctUntilChanged(),
          filter((appId) => !!appId),
          switchMap((appId) => {
            return observableCombineLatest([
              this.marketplaceAppService.getDraft(appId).pipe(
                catchError((err) => {
                  console.error('Failed to get draft > ', err);
                  this.snackbarService.openErrorSnack('Failed to get the latest draft app');
                  return this.marketplaceAppService.currentApp$;
                }),
              ),
              this.marketplaceAppService.getPublished(appId).pipe(
                catchError((err) => {
                  console.error('Failed to get published app > ', err);
                  this.snackbarService.openErrorSnack('Failed to get the latest published app');
                  return this.marketplaceAppService.currentPublishedApp;
                }),
              ),
            ]);
          }),
        )
        .subscribe(
          ([draft, published]) => {
            this.draft$$.next(draft);
            this.published$$.next(published);
          },
          (err) => {
            console.error('Failed to get apps to compare > ', err);
            this._apiError = true;
            this.snackbarService.openErrorSnack('Failed to compute app differences');
          },
        ),
    );

    const appPrices$ = this.appId$.pipe(
      switchMap((appId) =>
        this.appPricingService.getMultiPricing(
          [appId],
          new FieldMask({ paths: ['draft_msrp', 'draft_vendor_set_wholesale', 'msrp', 'vendor_set_wholesale'] }),
          '',
          '',
        ),
      ),
    );

    const draft$ = this.draft$$.pipe(
      distinctUntilChanged(),
      filter((draft) => !!draft),
    );
    const published$ = this.published$$.pipe(
      distinctUntilChanged(),
      filter((current) => !!current),
    );
    this.differences$ = observableCombineLatest([draft$, published$, appPrices$]).pipe(
      map(([draft, current, appPrices]) => {
        if (draft.usesEditions) {
          return this.differenceService.getMarketPlaceAppDiffs(draft, current);
        }
        const draftWithBillingPrices = this.UpdatePricesFromBilling(
          draft,
          appPrices[0].pricesForContexts['draft_msrp'],
          appPrices[0].pricesForContexts['draft_vendor_set_wholesale'],
        );
        const publishedWithBillingPrices = this.UpdatePricesFromBilling(
          current,
          appPrices[0].pricesForContexts['msrp'],
          appPrices[0].pricesForContexts['vendor_set_wholesale'],
        );
        return this.differenceService.getMarketPlaceAppDiffs(
          draftWithBillingPrices.app,
          publishedWithBillingPrices.app,
          draftWithBillingPrices.suggestedRetailSetupFee,
          publishedWithBillingPrices.suggestedRetailSetupFee,
        );
      }),
      catchError((err) => {
        console.error('Error when computing differences > ', err);
        this.snackbarService.openErrorSnack('Failed to compute differences between apps');
        return observableOf(new MarketplaceAppDiffModel());
      }),
      shareReplay(1),
    );

    this.fulfillmentConfig$ = this.appId$.pipe(
      // Handle case where no config has been published yet.
      distinctUntilChanged(),
      switchMap((appId) =>
        observableCombineLatest([
          this.fulfillmentOrderConfigApiService
            .getFulfillmentOrderConfig({
              appId: appId,
              release: ReleaseType.RELEASE_TYPE_DRAFT,
              projectionFilter: {
                includeFormConfig: true,
                includeExternalFormUrl: true,
              },
            })
            .pipe(
              map((resp) => resp?.config),
              catchError(() => of(null)),
            ),
          this.fulfillmentOrderConfigApiService
            .getFulfillmentOrderConfig({
              appId: appId,
              release: ReleaseType.RELEASE_TYPE_PUBLISHED,
              projectionFilter: {
                includeFormConfig: true,
                includeExternalFormUrl: true,
              },
            })
            .pipe(
              map((resp) => resp?.config),
              catchError(() => of(null)),
            ),
        ]),
      ),
      map(([draft, published]) => {
        if (!draft) {
          return;
        }
        return this.fulfillmentConfigDiffService.getFulfillmentConfigDiffs(draft, published);
      }),
    );

    const addons$ = this.appId$.pipe(switchMap((appId) => this.addonService.list(appId)));
    const addonsReleases$ = addons$.pipe(
      mergeMap((addons) =>
        addons.map((addon) =>
          combineLatest([
            this.marketplaceAppService.getDraft(addon.addonId),
            this.marketplaceAppService.getPublished(addon.addonId),
          ]).pipe(map(([draft, published]) => ({ draft, published }))),
        ),
      ),
      mergeMap(identity),
    );
    this.unpublishedAddonNames$ = addonsReleases$.pipe(
      filter((releases) =>
        this.differenceService.getMarketPlaceAppDiffs(releases.draft, releases.published).hasChanges(),
      ),
      map((releases) => releases.draft.name),
      scan((acc, v) => acc.concat(v), <string[]>[]),
      startWith(<string[]>[]),
      shareReplay(1),
    );

    const editionsDraft$ = observableCombineLatest([this.appId$, this.fetchDiffs$$.asObservable()]).pipe(
      switchMap(([appId]) => {
        return this.editionService.listDraftEditionsForApp(appId, null, LIST_EDITIONS_DEFAULT_PAGE_SIZE);
      }),
    );

    const editionsPublished$ = observableCombineLatest([this.appId$, this.fetchDiffs$$.asObservable()]).pipe(
      switchMap(([appId]) => {
        return this.editionService.listEditionsForApp(appId, null, LIST_EDITIONS_DEFAULT_PAGE_SIZE);
      }),
    );

    this.editionDiffs$ = observableCombineLatest([editionsDraft$, editionsPublished$]).pipe(
      map(([draft, published]) => {
        if (!draft) {
          return [];
        }
        const diffs: EditionDiffModel[] = [];
        for (let i = 0; i < draft.length; i++) {
          const editionDiff = this.editionDiffService.getDiffs(
            draft[i],
            !!published && typeof published[i] !== 'undefined' ? published[i] : null,
          );
          if (editionDiff.hasChanges()) {
            editionDiff.appId = draft[i].appId;
            editionDiff.editionId = draft[i].editionId;
            editionDiff.editionName = draft[i].name;
            editionDiff.wholesalePrice = draft[i].pricing.wholesalePrice;
            editionDiff.suggestedRetailPrice = draft[i].pricing.suggestedRetailPrice;
            diffs.push(editionDiff);
          }
        }

        return diffs;
      }),
      catchError(() => {
        this.snackbarService.openErrorSnack('Failed to compute differences between editions');

        return observableOf([]);
      }),
      shareReplay(1),
    );

    const emDraft$ = observableCombineLatest([this.appId$, this.fetchDiffs$$.asObservable()]).pipe(
      switchMap(([appId]) => {
        return this.editionService.listEditionsMarketingForApp(appId, Release.RELEASE_DRAFT);
      }),
    );

    const emPublished$ = observableCombineLatest([this.appId$, this.fetchDiffs$$.asObservable()]).pipe(
      switchMap(([appId]) => {
        return this.editionService.listEditionsMarketingForApp(appId, Release.RELEASE_PUBLISH);
      }),
    );

    this.editionMarketingdiffs$ = observableCombineLatest([emDraft$, emPublished$]).pipe(
      map(([draft, published]) => {
        if (!draft) {
          return [];
        }

        const diffs: EditionMarketingDiffModel[] = [];
        for (let i = 0; i < draft.length; i++) {
          const emd = this.editionMarketingDiffService.getDiffs(
            draft[i],
            !!published && typeof published[i] !== 'undefined' ? published[i] : null,
          );
          if (emd.hasChanges()) {
            emd.editionName = draft[i].name;
            diffs.push(emd);
          }
        }

        return diffs;
      }),
      catchError(() => {
        this.snackbarService.openErrorSnack('Failed to compute differences between edition marketing information');

        return observableOf([]);
      }),
      shareReplay(1),
    );

    this.areChanges$ = observableCombineLatest([
      this.differences$,
      this.editionMarketingdiffs$,
      this.editionDiffs$,
      this.fulfillmentConfig$,
    ]).pipe(
      map(([appDiff, editionMarketingDiff, editionDiff, fulfillmentDiff]) => {
        const hasEditionMarketingChanges = editionMarketingDiff.reduce((prev, curr) => {
          if (curr) {
            prev = true;
          }
          return prev;
        }, false);
        const hasEditionChanges = editionDiff.reduce((prev, curr) => {
          if (curr) {
            prev = true;
          }
          return prev;
        }, false);
        return appDiff.hasChanges() || hasEditionMarketingChanges || hasEditionChanges || fulfillmentDiff.length > 0;
      }),
    );

    this.dateChanged$ = this.differences$.pipe(
      map((diff) => {
        if (diff && diff.dateChanged) {
          return diff.dateChanged.toString();
        }
      }),
    );
    this.subscriptions.push(
      combineLatest([this.differences$, this.editions$]).subscribe(([diff, editions]) => {
        this._loading = false;
        for (const key in diff) {
          if (key) {
            const form = diff[key];
            if (form.displayType) {
              this.setDiffElements(form, key);
            }
            if (Symbol.iterator in Object(form)) {
              for (const obj of form) {
                if (obj.displayType) {
                  this.setDiffElements(obj, key, editions);
                }
              }
            }
          }
        }
      }),
    );
    this.isOpen$ = draft$.pipe(
      map((d) => d.integration.appApprovalProcess === ApprovalProcess.APPROVAL_PROCESS_VENDASTA),
    );
    this.subscriptions.push(published$.subscribe((d) => (this.isSuspended = !!d.suspended)));

    this.categoryService.categoryMap$.pipe(take(1)).subscribe((categoryMap) => {
      this.categoryMap = categoryMap;
    });
  }

  private hasPricingFromBilling(pricing: AppPrice): boolean {
    const pricesForEditions = pricing?.pricesForEditions;
    const pricesForCurrencies = pricesForEditions?.['']?.pricesForCurrencies;
    const usdPricing = pricesForCurrencies?.['USD'];

    return pricesForEditions && pricesForCurrencies && usdPricing?.pricesForFrequencies?.length > 0;
  }

  public UpdatePricesFromBilling(app: any, retailPricing: any, wholesalePricing: any): any {
    const newApp = app;
    let suggestedRetailSetupFee = null;
    if (this.hasPricingFromBilling(retailPricing)) {
      const retailFrequencyPrice =
        retailPricing.pricesForEditions['']?.pricesForCurrencies?.['USD']?.pricesForFrequencies?.[0];
      newApp.suggestedRetailPrice = convertCentsToDollars(retailFrequencyPrice?.pricingRules?.[0].price);
      suggestedRetailSetupFee = convertCentsToDollars(retailFrequencyPrice?.setupFee);
    }

    if (this.hasPricingFromBilling(wholesalePricing)) {
      const wholesaleFrequencyPrice =
        wholesalePricing.pricesForEditions['']?.pricesForCurrencies?.['USD']?.pricesForFrequencies?.[0];
      newApp.price = convertCentsToDollars(wholesaleFrequencyPrice?.pricingRules?.[0].price);
      newApp.setupFee = convertCentsToDollars(wholesaleFrequencyPrice?.setupFee);
      newApp.commitment = wholesaleFrequencyPrice?.commitment;
      newApp.billingFrequency = wholesaleFrequencyPrice?.billingFrequency;
      if (wholesaleFrequencyPrice?.fees?.[0].amountType === 1) {
        newApp.managementFee = convertCentsToDollars(wholesaleFrequencyPrice?.fees?.[0].amount);
        newApp.managementFeeType = 'fixed';
      }
      if (wholesaleFrequencyPrice?.fees?.[0].amountType === 2) {
        newApp.managementFee = wholesaleFrequencyPrice.fees?.[0].amount;
        newApp.managementFeeType = 'percent';
      }
    }

    return { app: newApp, suggestedRetailSetupFee: suggestedRetailSetupFee };
  }

  public publishAppChanges(draft?: MarketplaceApp): void {
    const currentDraft = draft || this.draft$$.getValue();
    /* Updating the version is a slight workaround for a ui glitch. We update the draft app, then
     * try to publish it. However, we only want to set the draft subject once. If we don't the ui
     * will glitch with changes that don't actually exist.
     */
    currentDraft.version++;

    this.subscriptions.push(
      this.marketplaceAppService.publishApp(currentDraft.appId).subscribe(
        (app: MarketplaceApp) => {
          // They should both be the same now.
          this.draft$$.next(app);
          this.published$$.next(app);
          this.fetchDiffs$$.next(null);
          this.snackbarService.openSuccessSnack('Application published successfully.');
        },
        (err) => {
          this.onError(err);
        },
      ),
    );
  }

  public publishAddons(): void {
    this.subscriptions.push(
      this.addonService
        .list(this.draft$$.getValue().appId)
        .pipe(
          mergeMap((addons) => addons.map((addon) => addon.addonId)),
          mergeMap((addonId) => this.marketplaceAppService.publishApp(addonId)),
        )
        .subscribe({
          next: () => {
            this.snackbarService.openSuccessSnack('Addon published successfully.');
          },
          error: (err) => {
            this.onError(err);
          },
        }),
    );
  }

  requestReview(appId: string): void {
    this.requestedReview = true;
    this.subscriptions.push(
      this.marketplaceAppService.requestAppReview(appId).subscribe(
        () => {
          this.snackbarService.openSuccessSnack('A review request was submitted.');
        },
        (err) => {
          console.error('Error requesting review > ', err);
          this.snackbarService.openErrorSnack('An unexpected error occurred.');
          this.requestedReview = false;
        },
      ),
    );
  }

  public onApprovalStatusSet(isApproved: boolean): void {
    const currentDraft = this.draft$$.getValue();
    if (!currentDraft.integration) {
      currentDraft.integration = new Integration();
    }
    currentDraft.integration.approved = isApproved;
    const resp$ = isApproved
      ? this.marketplaceAppService.approve(currentDraft.appId)
      : this.marketplaceAppService.unapprove(currentDraft.appId);

    this.subscriptions.push(
      resp$.subscribe(
        () => {
          this.snackbarService.openSuccessSnack(`Application successfully ${isApproved ? 'approved' : 'unapproved'}`);
          this.publishAppChanges(currentDraft);
        },
        (err) => {
          this.onError(err);
        },
      ),
    );
  }

  public openApp(): void {
    const currentDraft = this.draft$$.getValue();
    if (currentDraft.state === State.STATE_DRAFT) {
      this.snackbarService.openErrorSnack('Please publish before opening application to the marketplace');
      return;
    }
    const resp$ = this.marketplaceAppService.openAppToMarketplace(currentDraft.appId);
    this.subscriptions.push(
      resp$.subscribe(
        () => {
          this.snackbarService.openSuccessSnack('Application successfully opened to marketplace');
          this.publishAppChanges(currentDraft);
          this.publishAddons();
        },
        (err) => {
          this.onError(err);
        },
      ),
    );
  }

  public suspendApp(appId: string, name: string): void {
    const dialogRef = this.dialog.open(SuspendAppWarningDialogComponent, {
      width: '600px',
      data: {
        isSuspending: true,
        name: name,
      },
    });

    this.subscriptions.push(
      dialogRef
        .afterClosed()
        .pipe(switchMap((option) => this.handleSuspendDialog(appId, option)))
        .subscribe(
          () => {
            this.isSuspended = true;
            this.snackbarService.openSuccessSnack('Successfully suspended new activations');
          },
          () => {
            this.snackbarService.openErrorSnack('Unable to suspend new activations');
          },
        ),
    );
  }

  unSuspendApp(appId: string, name: string): void {
    const dialogRef = this.dialog.open(SuspendAppWarningDialogComponent, {
      width: '600px',
      data: {
        isSuspending: false,
        name: name,
      },
    });

    this.subscriptions.push(
      dialogRef
        .afterClosed()
        .pipe(switchMap((option) => this.handleSuspendDialog(appId, option)))
        .subscribe(
          () => {
            this.isSuspended = false;
            this.snackbarService.openSuccessSnack('Successfully resumed activations');
          },
          () => {
            this.snackbarService.openErrorSnack('Unable to resume activations');
          },
        ),
    );
  }

  handleSuspendDialog(appId: string, option: string): Observable<HttpResponse<null>> {
    if (option === 'suspend') {
      return this.marketplaceAppService.suspend(appId);
    }
    if (option === 'unsuspend') {
      return this.marketplaceAppService.unsuspend(appId);
    }
    return;
  }

  setReportingCode(): void {
    const subscription = this.currentReportingCode$
      .pipe(
        take(1),
        switchMap((reportingCodeOverride) => {
          const dialogData: SetReportingCodeDialogData = {
            currentReportingCode: reportingCodeOverride?.reportingCodeType,
          };

          const dialogRef = this.dialog.open(SetReportingCodeDialogComponent, {
            width: '600px',
            data: dialogData,
          });

          return dialogRef.afterClosed();
        }),
      )
      .subscribe();

    this.subscriptions.push(subscription);
  }

  public getReportingCodeDisplayName(reportingCodeType: any): string {
    return reportingCodeOverrideTypeForDisplay(reportingCodeType);
  }

  public setDiffElements(obj: any, key: string, editions?: Map<string, Edition>): void {
    switch (key) {
      case 'generalSettingsChanges': {
        if (obj.fieldName === 'lmiCategories') {
          for (let i = 0; i < obj.newValue.length; i++) {
            obj.newValue[i] = this.lmiCategoriesMap[obj.newValue[i]];
          }
          for (let i = 0; i < obj.oldValue.length; i++) {
            obj.oldValue[i] = this.lmiCategoriesMap[obj.oldValue[i]];
          }
        }
        if (obj.fieldName === 'categories') {
          for (let i = 0; i < obj.newValue.length; i++) {
            obj.newValue[i] = this.categoryMap.get(obj.newValue[i]);
          }
          for (let i = 0; i < obj.oldValue.length; i++) {
            obj.oldValue[i] = this.categoryMap.get(obj.oldValue[i]);
          }
        } else if (obj.fieldName === 'editionIds' && editions) {
          for (let i = 0; i < obj.newValue.length; i++) {
            obj.newValue[i] = AppDiffComponent.getDisplayNameForEdition(obj.newValue[i], editions);
          }
          for (let i = 0; i < obj.oldValue.length; i++) {
            obj.oldValue[i] = AppDiffComponent.getDisplayNameForEdition(obj.oldValue[i], editions);
          }
        } else if (obj.fieldName === 'deliveryMethod') {
          obj.newValue = this.deliveryMethodMap[obj.newValue];
          obj.oldValue = this.deliveryMethodMap[obj.oldValue];
        } else if (obj.fieldName === 'billingProcess') {
          obj.newValue = this.billingProcessToString(obj.newValue);
          obj.oldValue = this.billingProcessToString(obj.oldValue);
        } else if (obj.fieldName === 'serviceModel') {
          obj.newValue = obj.newValue.map((value) => this.serviceModelMap[value]);
          obj.oldValue = obj.oldValue.map((value) => this.serviceModelMap[value]);
        }
        this.generalSetting.push(obj);
        break;
      }
      case 'integrationChanges': {
        if (obj.fieldName === 'partnerContactRecipients') {
          obj.newValue = obj.newValue.email;
          obj.oldValue = obj.oldValue.email;
        } else if (obj.fieldName === 'appApprovalProcess') {
          obj.newValue = this.approvalProcessToString(obj.newValue);
          obj.oldValue = this.approvalProcessToString(obj.oldValue);
        }
        this.integration.push(obj);
        break;
      }
      case 'resellerChanges': {
        this.resellerMarketing.push(obj);
        break;
      }
      case 'endUserChanges': {
        this.endUserMarketing.push(obj);
        break;
      }
      case 'orderFormChanges': {
        if (obj.fieldName === 'commonFormRequiredFields') {
          const newArray = [];
          const oldArray = [];
          for (const k of this.commonFormElementsList) {
            if (!obj.newValue[k] && obj.oldValue[k]) {
              newArray.push({ k: this.commonFormElementsMap[k], value: false });
              oldArray.push({ k: this.commonFormElementsMap[k], value: true });
            } else if (obj.newValue[k] && !obj.oldValue[k]) {
              newArray.push({ k: this.commonFormElementsMap[k], value: true });
              oldArray.push({ k: this.commonFormElementsMap[k], value: false });
            }
          }
          obj.newValue = newArray;
          obj.oldValue = oldArray;
          this.orderForm.push(obj);
        } else {
          this.orderForm.push(obj);
        }
        break;
      }
      default: {
        break;
      }
    }
  }

  public loading(): boolean {
    return this._loading && !this._apiError;
  }

  public hasError(): boolean {
    return this._apiError;
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => {
      sub.unsubscribe();
    });
  }

  private onError(err: Error): void {
    console.error('Error publishing app > ', err);
    this.snackbarService.openErrorSnack('An unexpected error occurred.');
  }

  private billingProcessToString(p: BillingProcess): string {
    switch (p) {
      case BillingProcess.BILLING_PROCESS_INVALID:
        return 'Invalid';
      case BillingProcess.BILLING_PROCESS_NO_BILLING:
        return 'No Billing';
      case BillingProcess.BILLING_PROCESS_VENDASTA_BILLING:
        return 'Vendasta Billing';
      default:
        return 'Unknown Billing Process';
    }
  }
  private approvalProcessToString(p: ApprovalProcess): string {
    switch (p) {
      case ApprovalProcess.APPROVAL_PROCESS_INVALID:
        return 'Invalid';
      case ApprovalProcess.APPROVAL_PROCESS_VENDASTA:
        return 'Vendasta Approval';
      case ApprovalProcess.APPROVAL_PROCESS_VENDOR:
        return 'Vendor Approval';
      default:
        return 'Unknown Approval Process';
    }
  }
}
