<div *ngIf="loading()" class="loading-container">
  <mat-spinner [diameter]="100" [strokeWidth]="6"></mat-spinner>
</div>
<ng-container *ngIf="draft$$ | async as draftApp">
  <mat-card appearance="outlined" *ngIf="isSuspended" class="app-suspension-container">
    <mat-card-content>
      <div class="description">
        <mat-icon class="suspension-icon">block</mat-icon>
        <span>Activations are suspended for this product</span>
      </div>
      <div *ngIf="isSuperAdmin$ | async">
        <button
          data-action="clicked-resume-new-activations-product"
          mat-flat-button
          color="primary"
          (click)="unSuspendApp(draftApp.appId, draftApp.name)"
        >
          Resume new activations
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <mat-card
    appearance="outlined"
    *ngIf="(isSuperAdmin$ | async) && !isSuspended && draftApp.type !== AppType.APP_TYPE_ADDON"
    class="app-suspension-container"
  >
    <mat-card-content>
      <div class="description">
        <mat-icon class="suspension-icon">done</mat-icon>
        <span>This product can be activated</span>
      </div>
      <div>
        <button
          data-action="clicked-suspend-new-activations-product"
          mat-flat-button
          color="warn"
          (click)="suspendApp(draftApp.appId, draftApp.name)"
        >
          Suspend new activations
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <div
    *ngIf="(isSuperAdmin$ | async) !== false && (isOpen$ | async) === false && draftApp.type !== AppType.APP_TYPE_ADDON"
    class="open-app-container"
  >
    <div>
      <span class="open-app-message">Open up this product (and all of it's add-ons) to the marketplace.</span>
    </div>
    <div>
      <button data-action="clicked-open-product-to-marketplace" mat-flat-button color="primary" (click)="openApp()">
        Open
      </button>
    </div>
  </div>

  <div
    *ngIf="(isSuperAdmin$ | async) && (areChanges$ | async) && draftApp.type !== AppType.APP_TYPE_ADDON"
    class="header-layout"
  >
    <div class="unpublished-banner">This product has unpublished changes</div>
  </div>
  <div *ngIf="draftApp.type !== AppType.APP_TYPE_ADDON">
    <ng-container *ngIf="(unpublishedAddonNames$ | async)?.length > 0">
      <glxy-alert type="warning">
        There are unpublished changes to add-ons ({{ unpublishedAddonNames$ | async }}). Go to each add-on to see the
        changes.
      </glxy-alert>
    </ng-container>
  </div>

  @if ((isSuperAdmin$ | async) && (isMarketingServicesVendor$ | async)) {
    <mat-card appearance="outlined" class="app-reporting-code-override-container">
      <mat-card-content
        [ngClass]="{
          'has-reporting-code': (currentReportingCode$ | async),
        }"
      >
        @if (currentReportingCode$ | async; as reportingCode) {
          <div>
            <div class="reporting-code-label">Reporting code override</div>
            <span>{{ getReportingCodeDisplayName(reportingCode.reportingCodeType) }}</span>
          </div>
        }
        <button mat-stroked-button (click)="setReportingCode()">Set reporting code override</button>
      </mat-card-content>
    </mat-card>
  }

  <mat-card appearance="outlined" class="app-diff" *ngIf="areChanges$ | async as areChanges; else areNoChanges">
    <mat-card-header>
      <mat-card-subtitle>
        Changes submitted on {{ dateChanged$ | async }}
        <span *ngIf="isSuperAdmin$ | async">
          <button
            data-action="clicked-publish-changes-product"
            (click)="publishAppChanges()"
            class="mat-flat-button mat-primary button-float"
            mat-flat-button
            type="button"
          >
            Publish Changes
          </button>
        </span>
        <span
          *ngIf="
            (isSuperAdmin$ | async) === false &&
            draftApp.integration.appApprovalProcess === ApprovalProcess.APPROVAL_PROCESS_VENDASTA
          "
        >
          <button
            data-action="clicked-request-product-review"
            [disabled]="requestedReview"
            (click)="requestReview(draftApp.appId)"
            class="mat-primary button-float"
            mat-button
            type="button"
          >
            Request Review
          </button>
        </span>
      </mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <span *ngIf="generalSetting.length">
        <h2>General Settings</h2>
        <app-marketplace-diff-table [diff]="generalSetting"></app-marketplace-diff-table>
      </span>

      <span *ngIf="integration.length">
        <h2>Integration</h2>
        <app-marketplace-diff-table [diff]="integration"></app-marketplace-diff-table>
      </span>

      <span *ngIf="resellerMarketing.length">
        <h2>Reseller Marketing</h2>
        <app-marketplace-diff-table [diff]="resellerMarketing"></app-marketplace-diff-table>
      </span>

      <span *ngIf="endUserMarketing.length">
        <h2>End User Marketing</h2>
        <app-marketplace-diff-table [diff]="endUserMarketing"></app-marketplace-diff-table>
      </span>

      <span *ngIf="orderForm.length">
        <h2>Order Form</h2>
        <app-marketplace-diff-table [diff]="orderForm"></app-marketplace-diff-table>
      </span>

      <ng-container *ngIf="fulfillmentConfig$ | async as fConfig">
        <span *ngIf="fConfig.length">
          <h2>Order Fulfillment</h2>
          <app-marketplace-diff-table
            [diff]="fConfig"
            [displayNameOverrides]="fulfillmentDisplayNameOverrides"
          ></app-marketplace-diff-table>
        </span>
      </ng-container>

      <ng-container *ngIf="editionDiffs$ | async as editionDiffs">
        <ng-container *ngIf="editionDiffs.length">
          <div *ngFor="let diff of editionDiffs">
            <h2>Edition - {{ diff.editionName }}</h2>
            <span *ngIf="diff.topLevelDataChanges.length">
              <app-marketplace-diff-table [diff]="diff.topLevelDataChanges"></app-marketplace-diff-table>
            </span>
          </div>
        </ng-container>
      </ng-container>

      <ng-container *ngIf="editionMarketingdiffs$ | async as emDiffs">
        <ng-container *ngIf="emDiffs.length">
          <div *ngFor="let diff of emDiffs">
            <h2>Edition Marketing - {{ diff.editionName }}</h2>
            <span *ngIf="diff.topLevelDataChanges.length">
              <h3>General Settings</h3>
              <app-marketplace-diff-table [diff]="diff.topLevelDataChanges"></app-marketplace-diff-table>
            </span>

            <span *ngIf="diff.endUserMarketingChanges.length">
              <h3>End User Marketing</h3>
              <app-marketplace-diff-table [diff]="diff.endUserMarketingChanges"></app-marketplace-diff-table>
            </span>

            <span *ngIf="diff.resellerMarketingChanges.length">
              <h3>Reseller Marketing</h3>
              <app-marketplace-diff-table [diff]="diff.resellerMarketingChanges"></app-marketplace-diff-table>
            </span>
          </div>
        </ng-container>
      </ng-container>
    </mat-card-content>
  </mat-card>
</ng-container>
<ng-container *ngIf="hasError()">
  <mat-card appearance="outlined">
    <div class="empty">
      <h2 class="empty-title">An error occurred</h2>
      <div class="empty-message">Could not determine the changes in the product</div>
    </div>
  </mat-card>
</ng-container>
<ng-template #areNoChanges>
  <mat-card appearance="outlined">
    <mat-card-content>
      <div class="empty">
        <h2 class="empty-title">There are no changes</h2>
        <div class="empty-message">
          Edit this product to see a comparison of the published product versus your draft
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</ng-template>
