import { Component, Inject } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { AppReportingCodeOverrideType } from '@vendasta/marketplace-apps';
import { getReportingCodeOverrideOptions } from '../utils/reporting-code-override';

export interface SetReportingCodeDialogData {
  currentReportingCode?: AppReportingCodeOverrideType;
}

@Component({
  templateUrl: './set-reporting-code-dialog.component.html',
  standalone: false,
})
export class SetReportingCodeDialogComponent {
  reportingCodeOptions = getReportingCodeOverrideOptions();
  reportingCodeControl: FormControl<AppReportingCodeOverrideType | null>;

  constructor(
    public dialogRef: MatDialogRef<SetReportingCodeDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: SetReportingCodeDialogData,
  ) {
    this.reportingCodeControl = new FormControl(data?.currentReportingCode || null);
  }

  save(): void {
    this.dialogRef.close(null);
  }

  cancel(): void {
    this.dialogRef.close(null);
  }
}
