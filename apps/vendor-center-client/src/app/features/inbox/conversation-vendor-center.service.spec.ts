// eslint-disable-next-line @nx/enforce-module-boundaries
import { ConversationDetail, ParticipantService } from '@galaxy/conversation/core';
import { SpectatorService, createServiceFactory } from '@ngneat/spectator/jest';
import { Conversation, Message, Participant, ParticipantType } from '@vendasta/conversation';
import { of } from 'rxjs';
import { ConversationVendorCenterService } from './conversation-vendor-center.service';
import { PARTNER_ID } from '../../tokens';

describe('ConversationVendorService', () => {
  let spectator: SpectatorService<ConversationVendorCenterService>;
  const createService = createServiceFactory({
    service: ConversationVendorCenterService,
    providers: [
      { provide: PARTNER_ID, useValue: of('VUNI') },
      {
        provide: ParticipantService,
        useValue: new ParticipantService(null, null, null, null, null, null, null, null, null),
      },
    ],
  });

  beforeEach(() => {
    spectator = createService();
  });

  it('should give deleted contact as title if contact deleted', () => {
    const conversationDetailDeleted = {
      conversation: {
        conversationId: 'vstore-123',
      } as Conversation,
      participants: [
        new Participant({
          internalParticipantId: 'Contact-123',
          participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
          isSubjectParticipant: true,
          isParticipantInternalInfoDeleted: true,
        }),
        new Participant({
          internalParticipantId: 'VUNI',
          participantType: ParticipantType.PARTICIPANT_TYPE_PARTNER,
          isSubjectParticipant: true,
        }),
      ],
      latestMessage: {} as Message,
    } as ConversationDetail;

    const get = spectator.service.getConversationTitleInfo(conversationDetailDeleted);
    expect(get).toEqual({ title: 'INBOX.ERROR.DELETED_CUSTOMER', secondarySubtitle: '' });
  });
});
