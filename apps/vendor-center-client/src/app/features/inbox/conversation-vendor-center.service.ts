import { Inject, Injectable } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  AppOptionsKeys,
  ConversationDetail,
  ConversationMessage,
  ConversationTitleInfo,
  CreateContactState,
  HostAppInterface,
  InboxNamespace,
  KabobDynamicButton,
  ParticipantService,
  RouteConfig,
  SendMessage,
  SubjectParticipant,
} from '@galaxy/conversation/core';
import {
  ConversationChannel,
  GlobalParticipantType,
  Participant,
  ParticipantType,
  PlatformLocation,
} from '@vendasta/conversation';
import { forkJoin, map, Observable, of } from 'rxjs';
import { PARTNER_ID } from '../../tokens';
import { AssociatedUserInterface } from '@vendasta/business-center/lib/_internal/interfaces/api.interface';

@Injectable()
export class ConversationVendorCenterService implements HostAppInterface {
  private partnerId = toSignal(this.partnerId$);

  constructor(
    private participantService: ParticipantService,
    @Inject(PARTNER_ID) private partnerId$: Observable<string>,
  ) {}

  getConversationTitleInfo(conversationDetail: ConversationDetail): ConversationTitleInfo {
    const recipients = this.getRecipient(conversationDetail);
    const conversationTitleInfo = {} as ConversationTitleInfo;
    const title: string[] = [];
    const secondarySubtitle: string[] = [];

    recipients.map((recipient) => {
      if (
        recipient.participantType == ParticipantType.PARTICIPANT_TYPE_CUSTOMER &&
        recipient.isParticipantInternalInfoDeleted
      ) {
        title.push('INBOX.ERROR.DELETED_CUSTOMER');
      } else {
        title.push(recipient.name || recipient.internalParticipantId);
        secondarySubtitle.push(recipient.internalParticipantId);
      }
    });
    conversationTitleInfo.title = title.filter((item) => item).join(', ');
    conversationTitleInfo.secondarySubtitle = secondarySubtitle.filter((item) => item).join(', ');

    return conversationTitleInfo;
  }

  getRecipient(conversationDetail: ConversationDetail): Participant[] | null {
    const subjectParticipants = conversationDetail?.participants.filter(
      (participant) => participant.isSubjectParticipant,
    );

    let recipientResult = subjectParticipants.filter(
      (recipient) => recipient.internalParticipantId !== this.partnerId(),
    );

    if (!recipientResult?.length) {
      recipientResult = subjectParticipants.filter((recipient) => recipient.internalParticipantId === this.partnerId());
    }

    return recipientResult;
  }

  isSenderFromOrganization(message: ConversationMessage): boolean {
    if (
      message.sender?.participantType === ParticipantType.PARTICIPANT_TYPE_IAM_USER ||
      message.sender?.participantType === ParticipantType.PARTICIPANT_TYPE_PARTNER
    ) {
      return message.sender?.partnerId === this.partnerId() && !message.sender?.accountGroupId;
    }
    return false;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  isYourExpert(_conversationDetail: ConversationDetail): boolean {
    return false;
  }

  isRecipientInternalInfoDeleted(conversationDetail: ConversationDetail): boolean {
    return this.getRecipient(conversationDetail).filter(
      (recipient) => recipient.participantType === ParticipantType.PARTICIPANT_TYPE_PARTNER,
    )[0]?.isParticipantInternalInfoDeleted;
  }

  buildParticipants(currentIAMParticipant: Participant, participants: SubjectParticipant[]): Observable<Participant[]> {
    const participantList$: Observable<Participant>[] = [];

    participants.forEach((participant) => {
      participantList$.push(this.buildParticipant(currentIAMParticipant?.partnerId, participant));
    });
    return forkJoin(participantList$).pipe(
      map((participants) => {
        participants.push(currentIAMParticipant);
        return participants;
      }),
    );
  }

  buildParticipant(partnerId: string, recipient: SubjectParticipant): Observable<Participant> {
    return of(this.buildSubjectParticipant(partnerId, recipient));
  }

  buildSubjectParticipant(_partnerId: string, subjectParticipant: SubjectParticipant): Participant {
    switch (subjectParticipant?.participantType) {
      case GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER:
        return this.participantService.buildPartner(subjectParticipant?.internalParticipantId);
      case GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_VENDOR:
        return this.participantService.buildVendor(subjectParticipant?.internalParticipantId);
      default:
        return {} as Participant;
    }
  }

  buildSendMessageParams(
    currentIAMParticipant: Participant,
    subjectParticipants: SubjectParticipant[],
    channel: ConversationChannel,
  ): Observable<SendMessage> {
    return this.buildParticipants(currentIAMParticipant, subjectParticipants).pipe(
      map((participants) => ({
        participants: participants,
        location: PlatformLocation.PLATFORM_LOCATION_VENDOR_CENTER,
        channel: channel,
      })),
    );
  }

  redirectToInternalConversation(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _currentIAMParticipant: Participant,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _recipientSubjectParticipant: SubjectParticipant[],
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _channel: ConversationChannel,
  ): void {
    throw new Error('Not implemented');
  }

  createPaymentLink(): Observable<string> {
    throw new Error('Not implemented');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  shortenLink(_link: string): Observable<string> {
    throw new Error('Not implemented');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  calculatePaymentLinkTax(_conversationDetail: ConversationDetail, _amount: number): Observable<number> {
    throw new Error('Not implemented');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getKabobAvailableActions(_conversationDetail: ConversationDetail): Observable<KabobDynamicButton[]> {
    return of([]);
  }

  getAppOptions(): { [key in AppOptionsKeys]?: boolean } {
    return {};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getUsers(accountGroupId: string): Observable<AssociatedUserInterface[]> {
    return;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  navigateToCreateContact(state: CreateContactState, routes: RouteConfig) {
    throw new Error('not implemented');
  }

  getNamespace(): Observable<InboxNamespace> {
    return this.partnerId$.pipe(
      map(
        (partnerId): InboxNamespace => ({
          id: partnerId,
          namespaceType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_VENDOR,
        }),
      ),
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  viewContact(_conversationDetail: ConversationDetail): void {
    // Vendor Center does not support viewing contacts
    return;
  }
}
