import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { inject, NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorIntl, MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule, Routes } from '@angular/router';

import { RouteParamsService } from '@vendasta/route-params';
import { UIKitModule } from '@vendasta/uikit';

import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AuthenticatedPageComponent } from './app-pages/authenticated-page/authenticated-page.component';
import { ComposerModule } from './composer/composer.module';
import { CoreModule } from './core/core.module';
import { PerformanceIntroComponent } from './pages/analytics/link-performance/performance-intro/performance-intro.component';
import { MatPaginatorPerformanceIntl } from './pages/analytics/link-performance/performance-paginator.service';
import { UrlShortenerApiService } from './pages/analytics/link-performance/performance.api.service';
import { PerformanceComponent } from './pages/analytics/link-performance/performance.component';
import { ContentComponent } from './pages/content/content.component';
import { ContentService } from './pages/content/content.service';
import { CustomersComponent } from './pages/customers/customers.component';
import { LeadsComponent } from './pages/leads/leads.component';
import { LimitedComponent } from './pages/limited/limited.component';
import { PostsCalendarComponent } from './pages/posts/calendar/posts-calendar.component';
import { DraftsPostsComponent } from './pages/posts/drafts/drafts.component';
import { PostLandingComponent } from './pages/posts/landing/landing-post.component';
import { PostsModule } from './pages/posts/posts.module';
import { RecentPostsComponent } from './pages/posts/recent/recent-posts.component';
import { ScheduledPostsComponent } from './pages/posts/scheduled/scheduled-posts.component';
import { AddSocialPagesComponent } from './pages/settings/add-social-pages/add-social-pages.component';
import { ConnectionSettingsComponent } from './pages/settings/connection-settings/connection-settings.component';
import { InterestingContentSettingsComponent } from './pages/settings/interesting-content/interesting-content.component';
import { LeadResponsesSettingsComponent } from './pages/settings/lead-responses/lead-responses.component';
import { LeadSearchesSettingsComponent } from './pages/settings/lead-searches/lead-searches.component';
import { SettingsModule } from './pages/settings/settings.module';
import { CarouselDialogComponent } from './shared/carousel-dialog/carousel-dialog.component';
import { GMBWelcomeDialogComponent } from './shared/gmb-welcome/gmb-welcome.component';
import { InstagramWelcomeDialogComponent } from './shared/instagram-welcome/instagram-welcome.component';
import { SharedModule } from './shared/shared.module';

import { LexiconModule } from '@galaxy/lexicon';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyInputModule } from '@vendasta/galaxy/input';
import { GalaxyTableModule } from '@vendasta/galaxy/table';
import { UrlShortenerService } from '@vendasta/url-shortener';
import baseTranslation from '../assets/i18n/en_devel.json';
import { AnalyticsModule } from './core/analytics';
import { EntryGuard } from './embed/entry.guard';
import { UpgradeCTAGuard } from './embed/pro.guard';
import { LocalLoginModule } from './local-login';
import { LinkMetricsDataSource } from './pages/analytics/link-performance/performance.service';
import { PostPerformanceComponent } from './pages/analytics/post-performance/post-performance.component';
import { PostPerformanceModule } from './pages/analytics/post-performance/post-performance.module';
import { OverviewComponent } from './pages/overview/overview.component';
import { OverviewModule } from './pages/overview/overview.module';
import { AiBundlePostsComponent } from './pages/posts/ai-bundle-posts/ai-bundle-posts.component';
import { ClickableBioParentComponent } from './pages/posts/link-directory-page/clickable-bio-parent.component';
import { EditSettingsComponent } from './pages/posts/link-directory-page/edit-settings.component';
import { LinkDirectoryPageComponent } from './pages/posts/link-directory-page/link-directory-page.component';
import { SettingsPageComponent } from './pages/posts/link-directory-page/settings-page.component';
import { SetupSummaryPageComponent } from './pages/posts/link-directory-page/setup-summary-page.component';
import { TemplatePostsComponent } from './pages/posts/template-posts/template-posts.component';
import { CuratedContentChannelComponent } from './pages/settings/curated-content-channel/curated-content-channel.component';
import { CustomInstructionsComponent } from './pages/settings/AI-settings/custom-instructions/custom-instructions.component';
import { DebugService } from './shared/debug/debug.service';
import { PolicyComponent } from './shared/policy/policy.component';
import { CanComponentDeactivate, CanDeactivateGuard } from './shared/route-alert/can-deactivate-guard.service';
import { AiSettingsPageComponent } from './pages/settings/AI-settings/ai-settings-page.component';
import { PostsV2Component } from './pages/posts/posts-v2/posts-v2.component';
import { ToolsManagerComponent } from './pages/tools/tools-manager.component';
import { WordpressBlogChannelComponent } from './pages/settings/wordpress-blog-channel/wordpress-blog-channel.component';

export const routes: Routes = [
  {
    path: '',
    component: AuthenticatedPageComponent,
    canActivate: [EntryGuard],
    children: [
      {
        path: 'account/:agid',
        children: [
          { path: 'overview', component: OverviewComponent, canActivate: [EntryGuard] },
          {
            path: 'posts',
            children: [
              {
                path: '',
                component: PostsV2Component,
              },
              { path: 'recent', component: RecentPostsComponent },
              { path: 'scheduled', component: ScheduledPostsComponent },
              { path: 'calendar', component: PostsCalendarComponent },
              { path: 'draft', component: DraftsPostsComponent },
              { path: 'templates', component: TemplatePostsComponent },
              { path: 'clickable-bio', component: ClickableBioParentComponent, canActivate: [UpgradeCTAGuard] },
              { path: 'settings-page', component: SettingsPageComponent },
              { path: 'link-directory-page', component: LinkDirectoryPageComponent },
              { path: 'setup-summary-page', component: SetupSummaryPageComponent },
              { path: 'edit-settings', component: EditSettingsComponent },
              {
                path: 'ai-bundle-posts',
                component: AiBundlePostsComponent,
                canDeactivate: [
                  (component: CanComponentDeactivate) => inject(CanDeactivateGuard).canDeactivate(component),
                ],
                canActivate: [UpgradeCTAGuard],
              },
            ],
          },
          {
            path: 'calendar',
            component: PostsCalendarComponent,
          },
          {
            path: 'post/details/:postId/:accountId/locations/:locationId/:referralType',
            component: PostLandingComponent,
          },
          { path: 'post/details/:postId/:referralType', component: PostLandingComponent },
          { path: 'post/details/:postId', component: PostLandingComponent },
          { path: 'post/details', component: PostLandingComponent },
          { path: 'customers', component: CustomersComponent, canActivate: [UpgradeCTAGuard] },
          { path: 'leads', component: LeadsComponent, canActivate: [UpgradeCTAGuard] },
          { path: 'content', component: ContentComponent, canActivate: [UpgradeCTAGuard] },
          {
            path: 'tools',
            children: [
              { path: '', component: ToolsManagerComponent },
              { path: 'templates', component: TemplatePostsComponent },
              { path: 'clickable-bio', component: ClickableBioParentComponent, canActivate: [UpgradeCTAGuard] },
              { path: 'comments', component: CustomersComponent, canActivate: [UpgradeCTAGuard] },
              { path: 'content', component: ContentComponent, canActivate: [UpgradeCTAGuard] },
            ],
          },
          {
            path: 'analytics',
            children: [
              { path: '', component: PostPerformanceComponent },
              { path: 'link-performance', component: PerformanceComponent, canActivate: [UpgradeCTAGuard] },
              { path: 'post-performance', component: PostPerformanceComponent },
            ],
          },
          {
            path: 'settings',
            children: [
              { path: '', component: ConnectionSettingsComponent },
              { path: 'leads', component: LeadSearchesSettingsComponent },
              { path: 'responses', component: LeadResponsesSettingsComponent },
              { path: 'articles', component: InterestingContentSettingsComponent, canActivate: [UpgradeCTAGuard] },
              { path: 'social-pages/add', component: AddSocialPagesComponent },
              { path: 'curated-content', component: CuratedContentChannelComponent },
              { path: 'wordpress-blog', component: WordpressBlogChannelComponent },
              { path: 'ai-settings', component: AiSettingsPageComponent },
              { path: 'custom-instructions', component: CustomInstructionsComponent },
            ],
          },
          { path: 'limited', component: LimitedComponent },
          { path: 'mention/details/:mentionId/:referralType', component: PostLandingComponent },
        ],
        resolve: { routeParams: RouteParamsService },
        runGuardsAndResolvers: 'paramsOrQueryParamsChange',
      },
    ],
  },
];

@NgModule({
  imports: [
    RouterModule.forChild(routes),
    CommonModule,
    CoreModule,

    SharedModule,
    UIKitModule,
    OverlayModule,
    PostsModule,
    SettingsModule,
    ComposerModule,
    MatIconModule,
    MatToolbarModule,
    MatTooltipModule,
    MatButtonModule,
    MatCardModule,
    MatSidenavModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatProgressBarModule,
    MatMenuModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatTabsModule,
    TranslateModule,
    LexiconModule.forRoot({
      componentName: 'social-marketing/social-marketing-client',
      baseTranslation: baseTranslation,
    }),
    AnalyticsModule,
    OverviewModule,
    LocalLoginModule,
    PostPerformanceModule,
    GalaxyFormFieldModule,
    GalaxyInputModule,
    GalaxyTableModule,
    PolicyComponent,
  ],
  declarations: [
    AuthenticatedPageComponent,
    PerformanceComponent,
    PerformanceIntroComponent,
    GMBWelcomeDialogComponent,
    InstagramWelcomeDialogComponent,
    ContentComponent,
    LimitedComponent,
    CarouselDialogComponent,
  ],
  providers: [
    UrlShortenerApiService,
    UrlShortenerService,
    LinkMetricsDataSource,
    ContentService,
    {
      provide: MatPaginatorIntl,
      useFactory: (translate: TranslateService) => {
        const service = new MatPaginatorPerformanceIntl();
        service.injectTranslateService(translate);
        return service;
      },
      deps: [TranslateService],
    },
    DebugService,
    UpgradeCTAGuard,
    EntryGuard,
    CanDeactivateGuard,
  ],
})
export class AuthenticatedAppModule {}
