// tslint:disable-next-line
export const URL_REGEX =
  /(?=https?:\/\/)[-a-zA-Z0-9@:%_+~#?&/=]+(\.[-a-zA-Z0-9@:%_+~#?&//=]{2,256})*(\.[a-zA-Z]{2,9}\b)(\/[-a-zA-Z0-9@:%_.+~#?&//=]*)?|[a-zA-Z0-9-]{2,256}(?!https?\b)(\.[-a-zA-Z0-9@:%_+~#?&//=]{2,256})*(\.[a-zA-Z]{2,9}\b)(\/[-a-zA-Z0-9@:%_.+~#?&//=]*)?/;

export const SUB_DOMAIN_REGEX = /^[a-zA-Z0-9]+[a-zA-Z0-9-_]*[a-zA-Z0-9]+$/;

export const SHORT_CODE_REGEX = /(1l\.ink|url-shortener-demo.apigateway.co)\/([A-Za-z0-9]+)/;

export const HASHTAG_REGEX = /(#\S*)/g;

export const MENTIONS_REGEX = /(@\S*)/g;

export const GMB_SSID_REGEX = /^accounts\/\d*\/locations\/\d*/;

export const DATE_GMB_FORMAT = 'YYYY-MM-DDTHH:mm:ssZ';

export const DEFAULT_PROFILE_IMAGE =
  'https://vstatic-prod.apigateway.co/social-marketing-client/assets/social-icons/default.png';
export const DEFAULT_POST_IMAGE =
  'https://vstatic-prod.apigateway.co/social-marketing-client/assets/social-icons/default.png';
export const INTERESTING_CONTENT_EMPTY_STATE_IMG =
  'https://vstatic-prod.apigateway.co/social-marketing-client/assets/empty-states/interesting-content-empty-state.svg';

export const CALENDAR_MONTH_IMG =
  'https://vstatic-prod.apigateway.co/social-marketing-client/assets/icons/calendar_month.svg';

export const MY_LISTING_LOGO = 'https://vstatic-prod.apigateway.co/social-marketing-client/assets/icons/myListing.svg';

export const BROKEN_IMAGE_PLACEHOLDER_SQUARE =
  'https://vstatic-prod.apigateway.co/social-marketing-client/assets/empty-states/error_preview_square.svg';
export const BROKEN_IMAGE_PLACEHOLDER_SMALL =
  'https://vstatic-prod.apigateway.co/social-marketing-client/assets/empty-states/error_preview_small.svg';

export const BULK_UPLOAD_CSV_TEMPLATE =
  'https://vstatic-prod.apigateway.co/social-marketing-client/assets/csv-upload/bulk_upload_sample_template.csv';

export const SERVICE_MAX_FILE_SIZES = {
  FB_PAGE: 10 * 1000 * 1000,
  TW_USER: 5 * 1024 * 1024,
  IG_USER: 8 * 1000 * 1000,
  LI_USER: 10 * 1000 * 1000,
  LI_COMPANY: 10 * 1000 * 1000,
  GMB: 10 * 1000 * 1000,
  GMB_LOCATION: 10 * 1000 * 1000,
};

// === VIDEO ========================================

export const SERVICE_MAX_VIDEO_SIZES = {
  FB_PAGE: 1000 * 1000 * 1000, // 1 GB
  // https://developers.facebook.com/docs/instagram-api/reference/ig-user/media/
  IG_USER: 1000 * 1000 * 500, // 500 MB
  TW_USER: 1000 * 1000 * 512, // 512 MB
  // While the limit is 512 MB, only 200 MB is supported until we implement Multi-Part Uploading.
  LI_COMPANY: 1000 * 1000 * 200,
  LI_USER: 1000 * 1000 * 200,
};

export const SERVICE_MIN_FILE_SIZES = {
  GMB: 10 * 1024,
  GMB_LOCATION: 10 * 1024,
};

export const SERVICE_MIN_IMAGE_SIZE_PX = {
  GMB: 250,
  GMB_LOCATION: 250,
  IG_USER: 150,
  TW_USER: 4,
};

export const SERVICE_MAX_IMAGE_SIZE_PX = {
  GMB: 5000,
  GMB_LOCATION: 5000,
  TW_USER: 2600,
};

export const STORY_MAX_IMAGE_WIDTH = {
  IG_USER: 1080,
};

export const STORY_MAX_IMAGE_HIGHT = {
  IG_USER: 1920,
};

export const SERVICE_MIN_IMAGE_ASPECT_RATIO = {
  IG_USER: 0.8,
};

export const SERVICE_MAX_IMAGE_ASPECT_RATIO = {
  IG_USER: 1.91,
};

export const DEFAULT_SERVICE_MAX_FILE_SIZES = {
  limitingSource: 'FB_PAGE',
  size: 10 * 1000 * 1000,
};

export const DEFAULT_SERVICE_MIN_FILE_SIZES = {
  limitingSource: 'GMB',
  size: 10 * 1024,
};

export const DEFAULT_SERVICE_MIN_IMAGE_SIZE_PX = {
  limitingSource: 'TW_USER',
  size: 4,
};

export const DEFAULT_SERVICE_MAX_IMAGE_SIZE_PX = {
  limitingSource: 'TW_USER',
  size: 8192,
};

export const DEFAULT_SERVICE_MIN_IMAGE_ASPECT_RATIO = {
  limitingSource: 'IG_USER',
  size: 0.8,
};

export const DEFAULT_SERVICE_MAX_IMAGE_ASPECT_RATIO = {
  limitingSource: 'IG_USER',
  size: 1.91,
};

export const SERVICE_MAX_HASHTAGS = {
  IG_USER: 30,
};

export const SERVICE_MAX_VIDEO_WIDTHS = {
  IG_USER: 1920,
  TW_USER: 1920,
};

export const SERVICE_MAX_VIDEO_HEIGHTS = {
  TW_USER: 1024,
};

export const SERVICE_MAX_VIDEO_DURATIONS = {
  FB_PAGE: 60 * 60, // 1 hour
  IG_USER: 60 * 60, // 1 hour
  TW_USER: 140, // 2 minutes 20 seconds
  LI_COMPANY: 600, // 10 minutes
};

export const SERVICE_MAX_VIDEO_ASPECT_RATIOS = {
  IG_USER: 16 / 9,
  TW_USER: 3,
  FB_PAGE: 16 / 9,
  LI_COMPANY: 2.4,
};

export const SERVICE_MAX_BIT_RATES = {
  // Mbps
  IG_USER: 25,
  TW_USER: 25,
  LI_COMPANY: 30,
};

export const SERVICE_MIN_VIDEO_DURATIONS = {
  IG_USER: 3, // 3 seconds
  TW_USER: 0.5, // 0.5 seconds
  LI_COMPANY: 3, // 3 seconds
};

export const SERVICE_MIN_VIDEO_ASPECT_RATIOS = {
  IG_USER: 0.01 / 1,
  TW_USER: 1 / 3,
  FB_PAGE: 9 / 16,
  LI_COMPANY: 1 / 2.4,
};

export const NEW_POST_ID = 'NEW_POST';

export const GMB_LABEL_ACTION_MAP = Object.freeze({
  LEARN_MORE: 'COMPOSER.GOOGLE_MY_BUSINESS.LEARN_MORE',
  BOOK: 'COMPOSER.GOOGLE_MY_BUSINESS.BOOK',
  ORDER: 'COMPOSER.GOOGLE_MY_BUSINESS.ORDER',
  SHOP: 'COMPOSER.GOOGLE_MY_BUSINESS.SHOP',
  SIGN_UP: 'COMPOSER.GOOGLE_MY_BUSINESS.SIGN_UP',
  GET_OFFER: 'COMPOSER.GOOGLE_MY_BUSINESS.GET_OFFER',
  CALL: 'COMPOSER.GOOGLE_MY_BUSINESS.CALL',
});

// Do not change the order of this object unless the enum in vendastaapis change
export const GMB_ACTION_TYPES = Object.freeze(['LEARN_MORE', 'BOOK', 'ORDER', 'SHOP', 'SIGN_UP', 'GET_OFFER', 'CALL']);

export const LINK_OWNER_SOCIAL_MARKETING = 'SocialMarketing';
export const UWM_PID = 'USFS';

export const EXCCLUDE_NO_BUSINESS_PARTNERS = Object.freeze(['FREN', 'HMSI', 'NCHP']);
export const AcceptedImageMimeTypes = Object.freeze(['image/gif', 'image/jpeg', 'image/pjpeg', 'image/png']);
export const AcceptedVideoMimeTypes = Object.freeze(['video/mp4', 'video/quicktime']);
export const AcceptedMediaMimeTypes = AcceptedImageMimeTypes.concat(AcceptedVideoMimeTypes);

export const EXPRESS_EDITION_IDS = Object.freeze(['EDITION-SWVF3WH8', 'EDITION-FVGBNLVZ']);
export const PRO_EDITION_IDS = Object.freeze(['EDITION-5HNRPJW8', 'EDITION-2GG4WC8P']);

export const TWITTER_IMAGE_LIMIT = 4;
export const LINKEDIN_IMAGE_LIMIT = 9;
export const INTAGRAM_CAROUSEL_LIMIT = 10;
export const DEFAULT_MULTI_IMAGE_LIMIT = 10;

export const MENTIONS_COLOR = '#1CA1F3';

export const DEFAULT_MAX_SIZE_AI_CONTENT = 600;

export const DEFAULT_MAX_BULK_UPLOAD_POSTS = 25;

export const MENTION_PROMPT_OPTION_MESSAGES = Object.freeze({
  0: 'UPGRADE_CTA.SOCIAL_MENTIONS.TWITTER_UPGRADE_QUESTION',
  1: 'UPGRADE_CTA.SOCIAL_MENTIONS.FACEBOOK_UPGRADE_QUESTION',
  2: 'COMPOSER.SOCIAL_MENTIONS.EMPTY_RESULT',
  3: 'COMPOSER.SOCIAL_MENTIONS.PROMPT_QUESTION',
  4: 'COMPOSER.SOCIAL_MENTIONS.CONNECT_TWITTER',
  5: 'COMPOSER.SOCIAL_MENTIONS.CONNECT_FB',
});

export const SERVICE_MAX_CHAR_COUNT = Object.freeze({
  MAX_HASHTAGS_LIMIT: 280,
  INSTAGRAM_CHARS_LIMIT: 2200,
  FACEBOOK_CHARS_LIMIT: 63206,
  LINKEDIN_CHARS_LIMIT: 3000,
  GMB_CHARS_LIMIT: 1500,
  TIKTOK_CHAR_LIMIT: 2200,
  TWITTER_CHAR_LIMIT: 280,
  TWITTER_EXTENDED_CHAR_LIMIT: 25000,
  VALID_X_SUBSCRIPTIONS: ['basic', 'premium', 'premium+'],
});

export const MULTILOCATION_SERVICE_KEYS = {
  FB_PAGE: 'facebook_info',
  GMB: 'gmb_info',
  GMB_LOCATION: 'gmb_info',
  IG_USER: 'ig_info',
  LI_USER: 'linkedin_info',
  LI_COMPANY: 'linkedin_info',
};

export const MAX_ALLOWED_POSTS = 19;

export const POSTHOG_KEYS = {
  COMPOSER_SUGGEST_AI_IMAGE_CONTENT: 'ComposerSuggestImageAndContent',
  COMPOSER_OPENED_AI_MENU: 'ComposerWriteWithAI',
  COMPOSER_SUGGEST_AI_TEXT: 'ComposerSuggestText',
  COMPOSER_AI_REGENERATE_IMAGE: 'ComposerRegenerateImage',
  COMPOSER_SUGGEST_AI_IMAGE: 'ComposerSuggestImage',
  COMPOSER_USED_AI_CONTENT: 'ComposerUseSuggestContent',
  COMPOSER_AI_REGENERATE_TEXT: 'ComposerSuggestRegenerateText',

  COMPOSER_AI_CHATBOT: 'ComposerAIChatbot',
  ********************************: 'ComposerChatbotUsedAIContent',
  COMPOSER_CHATBOT_SEND_MESSAGE: 'ComposerChatbotSendMessage',

  COMPOSER_POST_WORKFLOW: 'ComposerPostWorkflow',
  COMPOSER_STORY_WORKFLOW: 'ComposerStoryWorkflow',
  COMPOSER_LONG_VIDEO_WORKFLOW: 'ComposerLongVideoWorkflow',

  BUNDLE_AI_SAVE_GENERATE: 'BundleAiPostGenerate',
  BUNDLE_AI_SAVE_SELECT: 'BundleAiPostSelect',
  BUNDLE_AI_SAVE_POST: 'BundleAiPostSave',
  BUNDLE_AI_ADD_ANOTHER: 'BundleAiAddAnotherPost',

  GENERATE_TITLE: 'TitleGenerated',
  GENERATE_OUTLINE: 'OutlineGenerated',
  GENERATE_BLOG: 'BlogGenerated',
  BLOG_POST_SUCESS: 'BlogPostSuccess',
  BLOG_POST_DRAFTED: 'BlogPostDrafted',
  BLOG_POST_FAILED: 'BlogPostFailed',
  BLOG_POST_UPDATE_SCHEDULED: 'BlogPostScheduleUpdated',

  BULK_UPLOAD_WORKFLOW_FAILED: 'BulkUploadWorkflowFailed',
  BULK_UPLOAD_WORKFLOW_SUCCESS: 'BulkUploadWorkflowSuccess',
  BULK_UPLOAD_CLICK: 'BulkUploadClick',
  CSV_UPLOAD_FAILED: 'CsvUploadFailed',
  CSV_UPLOAD_SUCCESS: 'CsvUploadSuccess',
  BULK_UPLOAD_PREVIEW: 'BulkUploadPreview',
  BULK_UPLOAD_PREVIEW_EDIT_CLICKED: 'BulkUploadPreviewEditClicked',
  BULK_UPLOAD_PREVIEW_DELETE_CLICKED: 'BulkUploadPreviewDeleteClicked',
  BULK_UPLOAD_PREVIEW_POST_EDITED: 'BulkUploadPreviewPostEdited',

  SOCIAL_CAMPAIGN_ADD_ANOTHER_POST: 'SocialCampaignAddAnotherPost',
  SOCIAL_CAMPAIGN_SAVE_POSTS: 'SocialCampaignSavePosts',
  SOCIAL_CAMPAIGN_DELETE_POST: 'SocialCampaignDeletePost',
  SOCIAL_CAMPAIGN_EDIT_POST: 'SocialCampaignEditPost',
};

export const LOCATION_KEYS = {
  SINGLE: 'SingleLocation',
  MULTI: 'MultiLocation',
};

export const MAX_POST_CAMPAIGN_PROMPT_LENGTH = 30000;
