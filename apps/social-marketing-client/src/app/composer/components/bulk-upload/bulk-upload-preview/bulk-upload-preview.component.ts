import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { PostType } from '@vendasta/composer';
import { ParsedPost, PreviewUploadedPosts } from '../../../../shared/csv/csv-model';
import { BulkUploadPostsService } from '../bulk-upload-posts.service';
import { Steps, StepStatus } from '../bulk-upload.enum';
import { MediaType, PostContent } from './../../../../shared/dynamic-posts/dynamic-posts-models';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { POSTHOG_KEYS } from '../../../constants';

@Component({
  selector: 'app-bulk-upload-preview',
  standalone: false,
  templateUrl: './bulk-upload-preview.component.html',
  styleUrl: './bulk-upload-preview.component.scss',
})
export class BulkUploadPreviewComponent implements OnInit {
  @Input() set selectedIndex(step: number) {
    if (step === Steps.EDIT) {
      this.nextStep.emit({
        stepName: Steps.EDIT,
        formGroupValue: this.previewForm,
      });
    }
  }
  @Input() posts: PreviewUploadedPosts;
  @Output() nextStep = new EventEmitter<any>();
  @Output() postEdited = new EventEmitter();
  @Output() postDeleted = new EventEmitter();
  @Input() brandId: string;

  convertedPosts: PostContent[];
  previewForm: UntypedFormGroup;

  constructor(
    private bulkUploadPostsService: BulkUploadPostsService,
    private productAnalyticsService: ProductAnalyticsService,
  ) {
    this.previewForm = new UntypedFormGroup({});
  }
  ngOnInit() {
    this.convertedPosts = this.convertToDynamicFeed(this.posts?.posts);
    this.convertedPosts.forEach((n) => {
      const isML = this.posts?.isML;
      n.isML = isML;
    });
    this.previewForm?.valueChanges?.subscribe(() => {
      this.bulkUploadPostsService.setFormStatus(Steps.EDIT, this.previewForm.status === StepStatus.VALID);
      this.nextStep.emit({
        stepName: Steps.EDIT,
        formGroupValue: this.previewForm,
      });
    });
  }

  private convertToDynamicFeed(posts: ParsedPost[]): PostContent[] {
    return posts.map((post) => ({
      postText: post?.postText,
      scheduleDate: post?.scheduleDate,
      Medias: post?.Medias?.map((media) => ({
        mediaUrl: media.mediaUrl,
        mediaType: media.mediaType.toUpperCase() === 'VIDEO' ? MediaType.VIDEO : MediaType.IMAGE,
      })),
      mlSocialNetworkType: post?.selectedMLNetworks,
      locations: post?.locations,
      gmbCustomization: post.gmbCustomization,
      postType: post?.isIGStory ? PostType.POST_TYPE_STORIES : PostType.POST_TYPE_IMAGE,
      mlPostTypes: post?.postTypes,
      filter: post?.filter,
    }));
  }

  onPostEdited(event) {
    if (!event?.post) return;

    if (event?.type === 'date') {
      // If the event type is date, we only update the schedule date
      this.posts.posts[event.index].scheduleDate = event.post.scheduleDate;
    } else {
      this.posts.posts[event.index].postText = event.post.postText;
      this.posts.posts[event.index].scheduleDate = event.post.scheduleDate;
      this.posts.posts[event.index].Medias = event.post?.Medias?.map((media) => ({
        mediaUrl: media.mediaUrl,
        mediaType: media.mediaType,
      }));
      this.posts.posts[event.index].locations = event.post?.locations;
      this.posts.posts[event.index].selectedMLNetworks = event.post?.mlSocialNetworkType;
      this.posts.posts[event.index].gmbCustomization = event.post?.gmbCustomization;
      this.posts.posts[event.index].postTypes = event.post.postType;
      this.productAnalyticsService.trackEvent(POSTHOG_KEYS.BULK_UPLOAD_PREVIEW_POST_EDITED, 'user', 'click', 0, {
        brandId: this.brandId,
      });
      this.posts.posts[event.index].filter = event.post?.filter;
    }
    this.postEdited.emit({ post: this.posts.posts[event.index], index: event.index, type: event?.type });
  }

  onPostDeleted(event: { index: number; isLastPost: boolean }) {
    this.productAnalyticsService.trackEvent(POSTHOG_KEYS.BULK_UPLOAD_PREVIEW_DELETE_CLICKED, 'user', 'click', 0, {
      brandId: this.brandId,
    });
    this.convertedPosts.splice(event.index, 1);
    this.postDeleted.emit(event);
  }

  onEditClicked() {
    this.productAnalyticsService.trackEvent(POSTHOG_KEYS.BULK_UPLOAD_PREVIEW_EDIT_CLICKED, 'user', 'click', 0, {
      brandId: this.brandId,
    });
  }
}
