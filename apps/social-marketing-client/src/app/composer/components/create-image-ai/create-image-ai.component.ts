import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyPopoverModule } from '@vendasta/galaxy/popover';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { PipesModule } from '../../shared/pipes/pipes.module';
import { TranslateModule } from '@ngx-translate/core';
import { SuggestionsService } from '../suggestions/suggestions.service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { PostTextActionsService } from '../post-text-actions/post-text-actions.service';
import { ImageResultsComponent } from '../create-image/results/image-results.component';
import { GenerateType } from '../suggestions/generate-type';
import { CreateImageRequestInterface } from '@vendasta/social-posts';
import { ImageOptionComponent } from './image-option.component';
import { MatDividerModule } from '@angular/material/divider';
import { MatMenuModule } from '@angular/material/menu';

@Component({
  selector: 'app-create-image-ai',
  imports: [
    CommonModule,
    FormsModule,
    GalaxyButtonLoadingIndicatorModule,
    GalaxyFormFieldModule,
    GalaxyLoadingSpinnerModule,
    GalaxyPopoverModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatProgressSpinnerModule,
    PipesModule,
    TranslateModule,
    ImageResultsComponent,
    ImageOptionComponent,
    MatDividerModule,
    MatMenuModule,
  ],
  templateUrl: './create-image-ai.component.html',
  styleUrl: './create-image-ai.component.scss',
})
export class CreateImageAiComponent implements OnInit, OnDestroy {
  topicText = '';
  promptText = '';
  imageSettings: CreateImageRequestInterface = {
    imageAmount: 1,
    size: '1024x1024',
    model: 'dall-e-3',
    style: 'vivid',
    quality: 'standard',
    prompt: '',
  };

  popoverOpen = false;
  @Output() imageSelected = new EventEmitter<boolean>();

  constructor(
    public suggestionsService: SuggestionsService,
    private productAnalyticsService: ProductAnalyticsService,
    private postTextActionsService: PostTextActionsService,
  ) {}

  ngOnInit() {
    this.topicText = this.suggestionsService.topic();
    this.promptText = this.suggestionsService.imagePrompt();
  }

  suggestImages(topic: string) {
    this.suggestionsService.generate(GenerateType.IMAGES, topic);
    this.promptText = this.suggestionsService.imagePrompt();
  }

  updateTopic(topic: string) {
    this.updateImageSettings({ prompt: topic });
  }

  updateSelectedOptions(options) {
    this.updateImageSettings({ style: options.style, size: options.orientation });
  }

  updateImageSettings(options: { prompt?: string; style?: string; size?: string }) {
    this.imageSettings = {
      ...this.imageSettings,
      ...options,
    };

    this.suggestionsService.updateImageSettings(this.imageSettings);
  }

  onImageSelected(imageSelected: boolean): void {
    this.imageSelected.emit(imageSelected);
  }

  ngOnDestroy() {
    this.suggestionsService.setDefaultImageSettings();
  }
}
