<div class="image-form-container" [formGroup]="form">
  <glxy-form-field>
    <glxy-label> {{ 'COMPOSER.CREATE_IMAGE.STYLE_SELECTOR' | translate }}</glxy-label>
    <mat-select formControlName="style">
      <mat-option *ngFor="let style of styles" [value]="style.value">{{ style.name }}</mat-option>
    </mat-select>
  </glxy-form-field>

  <glxy-form-field>
    <glxy-label> {{ 'COMPOSER.CREATE_IMAGE.ORIENTATION_SELECTOR' | translate }}</glxy-label>
    <mat-select formControlName="orientation">
      <mat-option *ngFor="let orientation of orientations" [value]="orientation.value">{{
        orientation.name
      }}</mat-option>
    </mat-select>
  </glxy-form-field>
</div>
