import { Component, DestroyRef, EventEmitter, OnInit, Output, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-image-option',
  imports: [
    CommonModule,
    GalaxyButtonLoadingIndicatorModule,
    GalaxyFormFieldModule,
    MatButtonModule,
    MatInputModule,
    MatRadioModule,
    MatSelectModule,
    ReactiveFormsModule,
    TranslateModule,
  ],
  templateUrl: './image-option.component.html',
  styleUrl: './image-option.component.scss',
})
export class ImageOptionComponent implements OnInit {
  @Output() selectionChange = new EventEmitter<any>();
  private destroyRef = inject(DestroyRef);
  form: FormGroup;

  styles = [
    { name: 'Default', value: 'vivid' },
    { name: 'Natural', value: 'natural' },
  ];

  orientations = [
    { name: 'Square', value: '1024x1024' },
    { name: 'Landscape', value: '1792x1024' },
    { name: 'Portrait', value: '1024x1792' },
  ];

  constructor(private fb: FormBuilder) {}

  ngOnInit() {
    this.form = this.fb.group({
      style: ['vivid'],
      orientation: ['1024x1024'],
    });

    this.form.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => this.selectionChange?.emit(this.form.value));
  }
}
