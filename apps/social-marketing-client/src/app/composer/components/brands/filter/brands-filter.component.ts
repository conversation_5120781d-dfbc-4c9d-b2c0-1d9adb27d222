import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { combineLatest, Observable, Subscription } from 'rxjs';
import { BrandsService } from '../brands.service';
import { map, startWith, switchMap, withLatestFrom } from 'rxjs/operators';
import { MatDialog } from '@angular/material/dialog';
import { LocationDialogComponent } from '../location-dialog/location-dialog.component';
import { GroupDialogComponent, GroupWithDisplayName } from '../group-dialog/group-dialog.component';
import { ConfigService } from '../../../../core';
import { GeographyDialogComponent } from '../geography-dialog/geography-dialog.component';
import {
  CITIES_LABEL,
  COUNTRIES_LABEL,
  DisplayFilter,
  FilterDisplaySettings,
  Location,
  LocationGroup,
  STATEPROV_LABEL,
} from '../brand-filter-container.service';
import { AddressAPIService, CountryConfiguration } from '@vendasta/address';
import { LanguageService } from '../../../../core/language-service.service';
import {
  AnalyticsApiService,
  CompositeFilterOperator,
  FieldFilterOperator,
  Filter,
  PropertyType,
} from '@vendasta/multi-location-analytics';

export interface FilterDisplay {
  filterName: string;
  filterValue: number;
}

@Component({
  selector: 'app-brands-filter',
  templateUrl: './brands-filter.component.html',
  styleUrls: ['./brands-filter.component.scss'],
  standalone: false,
})
export class BrandsFilterComponent implements OnInit, OnDestroy {
  @Input() isEditing = false;
  totalLocations$: Observable<number>;
  selectedLocations$: Observable<number>;
  locationsWithoutServices$: Observable<number>;
  missingService = 'Facebook';

  filterItems: DisplayFilter[] = [];
  availableLocations: LocationGroup[];
  selectedCountries: Location[] = [];
  selectedStateProvinces: Location[] = [];
  selectedCities: Location[] = [];

  filterSettings: FilterDisplaySettings = {
    textColor: '',
    backgroundColor: '',
  };
  selectedFilters$: Observable<FilterDisplay[]>;

  private _subscriptions: Subscription[] = [];

  constructor(
    private brandsService: BrandsService,
    private analyticsApiService: AnalyticsApiService,
    private dialog: MatDialog,
    private configService: ConfigService,
    private languageService: LanguageService,
    private addressService: AddressAPIService,
  ) {
    this.totalLocations$ = this.brandsService.totalLocationsInBrand$;
    this.selectedLocations$ = this.brandsService.numOfSelectedLocations$;
    this.locationsWithoutServices$ = combineLatest([
      this.brandsService.numOfSelectedLocations$,
      this.brandsService.numFBPages$,
    ]).pipe(
      map(([totalLocations, pagesFound]) => {
        return totalLocations - pagesFound;
      }),
    );
  }

  ngOnInit(): void {
    this._subscriptions.push(this.brandsService.fullBrandToAgidServicesMap$().subscribe());

    this.selectedFilters$ = this.brandsService.filters$.pipe(
      map((filtersMap) => {
        const filtersDisplay = [];
        if (filtersMap) {
          filtersMap.forEach((value: string[], key: string) => {
            filtersDisplay.push({
              filterName: key,
              filterValue: value.length,
            });
          });
        }
        return filtersDisplay;
      }),
      startWith([]),
    );

    const geographicBreakdown$ = this.brandsService.getGeographicBreakdown$();
    const currentLocale = this.languageService.getCurrentLang();
    geographicBreakdown$
      .pipe(
        switchMap((geographicBreakdown) => {
          const countryCodes = geographicBreakdown.reduce((codes, location) => {
            if (location.countryCode && codes.indexOf(location.countryCode) === -1) {
              codes.push(location.countryCode);
            }
            return codes;
          }, []);
          return combineLatest(
            countryCodes.map((code) => this.addressService.getCountryConfiguration(code, currentLocale)),
          );
        }),
        map((countryConfigs: CountryConfiguration[]) => {
          return countryConfigs.reduce(
            (options: Record<string, CountryConfiguration>, country: CountryConfiguration) => {
              options[country.code] = country;
              return options;
            },
            {},
          );
        }),
        withLatestFrom(geographicBreakdown$),
      )
      .subscribe(([countryOptions, geographicBreakdown]) => {
        if (typeof geographicBreakdown === 'undefined' || geographicBreakdown === null) {
          return;
        }
        this.clearAvailableLocations();
        geographicBreakdown.forEach((location) => {
          if (location.country && !location.state) {
            this.availableLocations[0].options.push({
              id: location.countryCode,
              name: countryOptions[location.countryCode].name,
              parent: [],
              locationCount: location.numLocations,
            });
          }
          if (location.country && location.state && !location.city) {
            const state = countryOptions[location.countryCode].zones.find((zone) => {
              return zone.code === location.stateCode;
            });
            this.availableLocations[1].options.push({
              id: location.stateCode,
              name: state.name,
              parent: [location.countryCode],
              locationCount: location.numLocations,
            });
          }
          if (location.country && location.state && location.city) {
            this.availableLocations[2].options.push({
              id: location.city,
              name: `${location.city}, ${location.stateCode}`,
              parent: [location.countryCode, location.stateCode],
              locationCount: location.numLocations,
            });
          }
        });
        this.availableLocations[0].options.sort(this.sortName);
        this.availableLocations[1].options.sort(this.sortName);
        this.availableLocations[2].options.sort(this.sortName);
      });
  }

  private clearAvailableLocations(): void {
    this.availableLocations = [
      {
        name: COUNTRIES_LABEL,
        options: [],
      },
      {
        name: STATEPROV_LABEL,
        options: [],
      },
      {
        name: CITIES_LABEL,
        options: [],
      },
    ];
  }

  private sortName(a: Location, b: Location): any {
    return a.name.localeCompare(b.name);
  }

  onRemoveFilter(filter: FilterDisplay): void {
    const existingFilters = this.brandsService.filters$$.getValue();
    existingFilters.delete(filter.filterName);
    this.brandsService.filters$$.next(existingFilters);
    this.brandsService.updateAgids();
    if (filter.filterName === 'Geography') {
      this.selectedCountries = [];
      this.selectedStateProvinces = [];
      this.selectedCities = [];
    }
  }

  openGroupDialog(): void {
    const dialogRef = this.dialog.open(GroupDialogComponent);
    this._subscriptions.push(
      dialogRef.afterClosed().subscribe((results) => {
        if (results) {
          this.consolidateGroupFilters(results.selectedGroups).forEach((displayGroup) => {
            this._subscriptions.push(
              this.brandsService
                .accountGroupIdsForPath$(displayGroup.group.pathNodes, this.configService.partnerId)
                .subscribe((selectedAccountGroups) => {
                  const existingFilters = this.brandsService.filters$$.getValue() || new Map<string, string[]>();
                  const selectedAccountGroupIds = selectedAccountGroups.map((ag) => ag.accountGroupId);
                  existingFilters.set('Group/' + displayGroup.name, selectedAccountGroupIds);
                  this.brandsService.filters$$.next(existingFilters);
                  this.brandsService.updateAgids();
                }),
            );
          });
        }
      }),
    );
  }

  openGeographyDialog(): void {
    const dialogRef = this.dialog.open(GeographyDialogComponent, {
      data: {
        availableLocations: this.availableLocations,
        selectedCountries: this.selectedCountries,
        selectedStateProvinces: this.selectedStateProvinces,
        selectedCities: this.selectedCities,
        filterSettings: this.filterSettings,
      },
      panelClass: 'ml-filter-dialog',
    });
    this._subscriptions.push(
      dialogRef.afterClosed().subscribe((results) => {
        if (typeof results !== 'undefined') {
          this.selectedCountries = results.selectedCountries;
          this.selectedStateProvinces = results.selectedStateProvinces;
          this.selectedCities = results.selectedCities;
        }
        if (
          this.selectedCountries.length !== 0 ||
          this.selectedStateProvinces.length !== 0 ||
          this.selectedCities.length !== 0
        ) {
          const brandContext$ = this.brandsService.buildBrandContext$([this.calculateGeographyFilters()]);
          this._subscriptions.push(
            brandContext$.subscribe((brandContext) => {
              this._subscriptions.push(
                this.analyticsApiService
                  .queryMetrics(this.brandsService.buildAccountGroupRequest(brandContext))
                  .subscribe((queryMetricsResponse) => {
                    const selectedAccountGroups = this.brandsService._getAgidsFromQueryResponse(queryMetricsResponse);
                    const selectedAccountGroupIds = selectedAccountGroups.map((ag) => ag.accountGroupId);
                    const existingFilters = this.brandsService.filters$$.getValue() || new Map<string, string[]>();
                    existingFilters.set('Geography', selectedAccountGroupIds);
                    this.brandsService.filters$$.next(existingFilters);
                    this.brandsService.updateAgids();
                  }),
              );
            }),
          );
        }
      }),
    );
  }

  private calculateGeographyFilters(): Filter {
    if (
      this.selectedCountries.length === 0 &&
      this.selectedStateProvinces.length === 0 &&
      this.selectedCities.length === 0
    ) {
      return null;
    }
    const filterObject = new Filter({
      compositeFilter: {
        op: CompositeFilterOperator.OR,
        filters: [],
      },
    });
    [...this.selectedCountries, ...this.selectedStateProvinces, ...this.selectedCities].forEach((location) => {
      let dimension = 'country';
      if (location.parent.length === 1) {
        dimension = 'state';
      } else if (location.parent.length === 2) {
        dimension = 'city';
      }
      filterObject.compositeFilter.filters.push(
        new Filter({
          fieldFilter: {
            operator: FieldFilterOperator.EQUAL,
            dimension: `account_group__${dimension}`,
            value: {
              value: location.id,
              valueType: PropertyType.PROPERTY_TYPE_STRING,
            },
          },
        }),
      );
    });
    return filterObject;
  }

  addOrUpdateFilterItem(
    filterId: string,
    filterTypeTranslateKey: string,
    value: string,
    value$?: Observable<string>,
  ): void {
    const foundFilter = this.filterItems.filter((item) => item.filterId === filterId);
    if (foundFilter.length > 0) {
      foundFilter[0].displayName = value;
      this.filterItems[this.filterItems.findIndex((el) => el.filterId === filterId)] = foundFilter[0];
      return;
    }
    this.filterItems.push({
      filterId: filterId,
      filterTypeTranslateKey: filterTypeTranslateKey,
      displayName: value,
      displayName$: value$,
    });
  }

  consolidateGroupFilters(selectedGroups: GroupWithDisplayName[]): GroupWithDisplayName[] {
    const newFilters = new Map<string, string[]>();
    const existingFilters = this.brandsService.filters$$.getValue();

    if (existingFilters) {
      existingFilters.forEach((value: string[], key: string) => {
        // If an existing Group filter is still part of the selectedGroups there's no
        // need to redo the api call to get the accountGroupIdsForPath
        if (key.startsWith('Group')) {
          const name = key.substring(6);
          const found = selectedGroups.find((g) => g.name === name);
          if (found) {
            selectedGroups = selectedGroups.filter((obj) => obj !== found);
            newFilters.set(key, value);
          }
        } else {
          // If it's not a Group filter keep it
          newFilters.set(key, value);
        }
      });
      this.brandsService.filters$$.next(newFilters);
      this.brandsService.updateAgids();
    }
    return selectedGroups;
  }

  openLocationDialog(): void {
    const dialogRef = this.dialog.open(LocationDialogComponent);
    this._subscriptions.push(
      dialogRef.afterClosed().subscribe((results) => {
        if (results) {
          if (results && results.selectedAccountGroupIds) {
            const existingFilters = this.brandsService.filters$$.getValue() || new Map<string, string[]>();
            existingFilters.set('Location', results.selectedAccountGroupIds);
            this.brandsService.filters$$.next(existingFilters);
            this.brandsService.updateAgids();
          }
        }
      }),
    );
  }

  ngOnDestroy(): void {
    this._subscriptions.forEach((a) => a.unsubscribe());
  }
}
