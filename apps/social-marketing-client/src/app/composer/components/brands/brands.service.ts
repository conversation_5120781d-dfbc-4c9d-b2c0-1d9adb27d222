import { Injectable } from '@angular/core';
import { BehaviorSubject, combineLatest, forkJoin, Observable, of } from 'rxjs';
import { PostableService } from '../../../shared/social-service/social-service.service';
import { SocialService } from '../../post';
import { ComposerStoreService } from '../../composer-store.service';
import {
  AnalyticsApiService,
  CompositeFilterOperator,
  Filter,
  GroupBy,
  GroupByOperator,
  GroupResourceId,
  Measure,
  MeasureAggregate,
  MeasureAggregateOperator,
  QueryMetricsRequest,
  QueryMetricsResponse,
  ResourceId,
} from '@vendasta/multi-location-analytics';
import { map, shareReplay, startWith, switchMap, tap } from 'rxjs/operators';
import { ConfigService } from '../../../core';
import { RepcoreApiService } from '../../../shared/repcore-api/repcore-api.service';
import { BrandsApiService, Group } from './brand-api.service';
import { MULTI_LOCATION_INSTAGRAM_PAGE_SIZE, Row, rowify } from './helpers';
import { HttpClient } from '@angular/common/http';
import { InstagramService } from '@vendasta/instagram';

class GeoWithCount {
  constructor(
    public countryCode: string,
    public country: string,
    public state: string,
    public stateCode: string,
    public city: string,
    public numLocations: number,
  ) {}
}

class Country {
  constructor(
    public countryCode: string,
    public countryName: string,
    public stateCodeToName: Map<string, string>,
  ) {}

  stateName(stateCode: string): string {
    const val = this.stateCodeToName.get(stateCode);
    if (!val) {
      return stateCode;
    }
    return val;
  }
}

class CountryDetails {
  constructor(public countryCodeToCountry: Map<string, Country>) {}

  public countryName(countryCode: string): string {
    const val = this.countryCodeToCountry.get(countryCode);
    if (!val) {
      return countryCode;
    }
    return val.countryName;
  }

  public stateName(countryCode: string, stateCode: string): string {
    const val = this.countryCodeToCountry.get(countryCode);
    if (!val) {
      return stateCode;
    }
    return val.stateName(stateCode);
  }
}

const allAvailableNetworks = [
  {
    serviceType: SocialService.FACEBOOK,
    name: 'Location Facebook',
    profileUrl: '',
    profileImageUrl: '',
    spid: '',
    ssid: 'FBPID',
    socialTokenBroken: false,
  } as PostableService,
  {
    serviceType: SocialService.GOOGLE_MY_BUSINESS,
    name: 'Location Google My Business',
    profileUrl: '',
    profileImageUrl: '',
    spid: '',
    ssid: 'GMBSID',
    socialTokenBroken: false,
  } as PostableService,
  {
    serviceType: SocialService.GMB,
    name: 'Location Google My Business',
    profileUrl: '',
    profileImageUrl: '',
    spid: '',
    ssid: 'GMBSID-2',
    socialTokenBroken: false,
  } as PostableService,
  {
    serviceType: SocialService.INSTAGRAM,
    name: 'Location Instagram',
    profileUrl: 'https://instagram.com/location_instagram',
    profileImageUrl: '',
    spid: '',
    ssid: 'IGU',
    socialTokenBroken: false,
  } as PostableService,
  {
    serviceType: SocialService.LINKEDIN_COMPANY,
    name: 'Location LinkedIn',
    profileUrl: '',
    profileImageUrl: '',
    spid: '',
    ssid: 'LIC',
    socialTokenBroken: false,
  } as PostableService,
  {
    serviceType: SocialService.LINKEDIN,
    name: 'Location LinkedIn',
    profileUrl: '',
    profileImageUrl: '',
    spid: '',
    ssid: 'LIU',
    socialTokenBroken: false,
  } as PostableService,
];

export class BrandContext {
  public readonly sourceIdsSet: Set<string>;

  constructor(
    public path: string[],
    public partnerId: string,
    public filters: Filter[],
    public groupBy: string[],
  ) {}

  public buildFilter(filters?: Filter[]): Filter {
    if ((!this.filters || this.filters.length === 0) && (!filters || filters.length === 0)) {
      return null;
    }

    if (!filters) {
      filters = [];
    }
    if (this.filters && this.filters.length > 0) {
      filters = filters.concat(this.filters);
    }

    return new Filter({
      compositeFilter: {
        op: CompositeFilterOperator.AND,
        filters: filters,
      },
    });
  }
}

export interface AccountGroupWithEdition {
  accountGroupId: string;
  isPro: boolean;
}

@Injectable()
export class BrandsService {
  partnerId: string;
  availableNetworks$$: BehaviorSubject<PostableService[]> = new BehaviorSubject<PostableService[]>([]);

  networkGroupSelectedSubject = new BehaviorSubject<Partial<Record<SocialService, boolean>>>({
    // Default to true for these services
    [SocialService.FACEBOOK]: true,
    [SocialService.GMB]: true,
    [SocialService.INSTAGRAM]: true,
    [SocialService.LINKEDIN_COMPANY]: true,
    [SocialService.LINKEDIN]: true,
  });

  networkGroupSelected$ = this.networkGroupSelectedSubject.asObservable();

  // Map of filter value to list of agids caught by it
  filters$$: BehaviorSubject<Map<string, string[]>> = new BehaviorSubject<Map<string, string[]>>(null);

  fullBrandServiceMap$$: BehaviorSubject<Map<string, unknown>> = new BehaviorSubject<Map<string, unknown>>(null);
  numOfSelectedLocations$: Observable<number>;
  totalLocations$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  numLocationsWithFBService$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  numLocationsWithGMBService$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  numLocationsWithIGService$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  numLocationsWithLIService$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);

  disableSubmit$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  isSelected$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);

  // Counts by Geo, for geo filter options
  readonly accountGroupGeography$: Observable<QueryMetricsResponse>;
  public allGroups$: Observable<Group[]>;
  private groupMapByID: { [key: string]: Group } = {};
  private readonly selectedAccountGroupIds$$ = new BehaviorSubject<string[]>([]);
  public readonly selectedAccountGroupIds$ = this.selectedAccountGroupIds$$.asObservable();

  constructor(
    private composerStore: ComposerStoreService,
    private configService: ConfigService,
    private analyticsApiService: AnalyticsApiService,
    private brandsApiService: BrandsApiService,
    private repcoreApiService: RepcoreApiService,
    private httpClient: HttpClient,
    private instagramService: InstagramService,
  ) {
    this.partnerId = this.configService.partnerId;
    this.availableNetworks$$.next(allAvailableNetworks);
    this.numOfSelectedLocations$ = combineLatest([
      this.composerStore.brandAgids$,
      this.totalLocations$$.asObservable(),
    ]).pipe(
      map(([agids, totalLocations]) => {
        if (!!agids && !!agids.size) {
          return agids.size;
        } else {
          return totalLocations;
        }
      }),
    );

    // All the Groups/SubGroups for the Groups filter
    this.allGroups$ = combineLatest([of([this.configService.brandId]), of(this.partnerId)]).pipe(
      switchMap(([path, partnerId]) => {
        return this.brandsApiService.listChildGroups(partnerId, path.join('|')).pipe(startWith(null as Group[]));
      }),
      tap((groups) => {
        if (!groups || !groups.length) {
          return;
        }
        groups.forEach((group) => {
          const groupID = group.pathNodes[group.pathNodes.length - 1];
          group.hasAccess = true;
          this.groupMapByID[groupID] = group;
        });
      }),
      shareReplay(1),
    );

    // Don't pass any filters to buildBrandContext$() in constructor. Initially, we want to get all locations
    //   from this.analyticsApiService.queryMetrics(), unfiltered.
    this.accountGroupGeography$ = this.buildBrandContext$().pipe(
      switchMap((brandsContext: BrandContext) =>
        this.analyticsApiService
          .queryMetrics(this.buildAccountGroupGeographyRequest(brandsContext))
          .pipe(startWith(null as QueryMetricsResponse)),
      ),
      shareReplay(1),
    );
  }

  setNetworkState(service: SocialService, isSelected: boolean): void {
    const currentState = this.networkGroupSelectedSubject.value;
    this.networkGroupSelectedSubject.next({ ...currentState, [service]: isSelected });
  }

  getNetworkState(service: SocialService): boolean {
    const currentState = this.networkGroupSelectedSubject.value;
    if (!(service in currentState)) {
      this.setNetworkState(service, true);
    }
    return currentState[service];
  }

  public buildBrandContext$(filters: Filter[] = null): Observable<BrandContext> {
    const partnerId = this.configService.partnerId;
    const brandId = this.configService.brandId;
    return of(new BrandContext([brandId], partnerId, filters, []));
  }

  public getGeographicBreakdown$(): Observable<GeoWithCount[]> {
    const rows$ = this.accountGroupGeography$.pipe(
      map((r: QueryMetricsResponse) => {
        if (r == null) {
          return null;
        }
        return rowify(r.metricResults[0]);
      }),
    );
    const countyInfo$ = rows$.pipe(
      switchMap((i) => {
        if (!i) {
          return of(null);
        }
        const countries = new Set<string>();
        i.forEach((c) => {
          if (!c.values[0]) {
            return;
          }
          countries.add(c.values[0]);
        });
        return this.getCountryAndStateInformation(Array.from(countries.keys()));
      }),
    );

    return combineLatest([rows$, countyInfo$]).pipe(
      map(([rows, countryInfo]: [Row[], CountryDetails]) => {
        if (!rows || !countryInfo) {
          return [];
        }
        return rows.map(function (dataRow: Row): GeoWithCount {
          return new GeoWithCount(
            dataRow.values[0],
            countryInfo.countryName(dataRow.values[0]),
            countryInfo.stateName(dataRow.values[0], dataRow.values[1]),
            dataRow.values[1],
            dataRow.values[2],
            Number(dataRow.measures[0][0]),
          );
        });
      }),
    );
  }

  private getCountryAndStateInformation(countries: string[]): Observable<CountryDetails> {
    return forkJoin(
      countries.map((c) => {
        return this.httpClient
          .get('https://www.gstatic.com/chrome/autofill/libaddressinput/chromium-i18n/ssl-address/data/' + c)
          .pipe(
            map((r) => {
              const subKeys = String(r['sub_keys']).split('~');
              const subNames = String(r['sub_names']).split('~');

              const states = new Map<string, string>();
              subKeys.forEach(function (key, i): void {
                states.set(key, subNames[i]);
              });

              return new Country(String(r['key']), this.capitalizeFirstLetters(String(r['name'])), states);
            }),
          );
      }),
    ).pipe(
      map((r) => {
        const m = new Map<string, Country>();
        r.forEach((c) => {
          m.set(c.countryCode, c);
        });
        return new CountryDetails(m);
      }),
    );
  }

  deselectNetwork(type: SocialService): void {
    const selected = allAvailableNetworks.filter((network) => network.serviceType === type);

    this.composerStore.deselectPostableService(selected);

    this.composerStore.toggleMLServiceGroup(type, false);

    this.disableSubmit$$.next(this.noNetworkSelected());
  }

  selectNetwork(type: SocialService): void {
    const selected = allAvailableNetworks.filter((network) => network.serviceType === type);

    this.composerStore.selectServices(selected);
    this.composerStore.toggleMLServiceGroup(type, true);
    this.disableSubmit$$.next(this.noNetworkSelected());
    this.updateAgids();
  }

  noNetworkSelected(): boolean {
    const networkGroupSelected = this.composerStore.typeConnectionMlSelected$$.getValue();
    return (
      !networkGroupSelected.facebook &&
      !networkGroupSelected.gmb &&
      !networkGroupSelected.instagram &&
      !networkGroupSelected.linkedin
    );
  }

  updateAgids(): void {
    // Note: We should probably be using pipes here instead of using getValue like this...
    const filtersMap = this.filters$$.getValue();
    let filters = [];
    if (filtersMap) {
      filters = Array.from(filtersMap.values());
    }
    const brandAgids = this.fullBrandServiceMap$$.getValue();
    let numServiceFoundFB = 0;
    let numServiceFoundGMB = 0;
    let numServiceFoundIG = 0;
    let numServiceFoundLI = 0;

    // neat check to see if all arrays in an array are empty.
    const isEmpty = (a) => Array.isArray(a) && a.every(isEmpty);
    const emptyFilters = isEmpty(filters);

    if (this.composerStore.isEditing$$.getValue()) {
      [numServiceFoundFB, numServiceFoundGMB, numServiceFoundIG, numServiceFoundLI] =
        this.getCountersFromActiveLocations(this.composerStore.activeLocations);
      const activeLocationIds = this.composerStore.activeLocations.map((location) => location.accountGroupId);

      const filteredBrandAgids = new Map<string, any>(
        Array.from(brandAgids).filter(([agid]) => activeLocationIds.includes(agid)),
      );

      this.composerStore.brandAgids$$.next(filteredBrandAgids);

      // It is not necessary to set the brandAgids from the activeLocations
      // here as Editing ML posts is a mainly backend operation now
    } else {
      if (emptyFilters) {
        [numServiceFoundFB, numServiceFoundGMB, numServiceFoundIG, numServiceFoundLI] =
          this.countMLServicesFromKey(brandAgids);
        this.composerStore.brandAgids$$.next(brandAgids);
      } else {
        const filteredAgids = [...new Set([].concat(...filters))];
        const filteredMap: Map<string, unknown> = new Map(
          filteredAgids.map((agid: string) => {
            const mappedAgid = brandAgids.get(agid);
            return [agid, mappedAgid];
          }),
        );
        [numServiceFoundFB, numServiceFoundGMB, numServiceFoundIG, numServiceFoundLI] =
          this.countMLServicesFromKey(filteredMap);
        this.composerStore.brandAgids$$.next(filteredMap);
      }
    }

    this.numLocationsWithFBService$$.next(numServiceFoundFB);
    this.numLocationsWithGMBService$$.next(numServiceFoundGMB);
    this.numLocationsWithIGService$$.next(numServiceFoundIG);
    this.numLocationsWithLIService$$.next(numServiceFoundLI);
    // Edge case: If service is already selected, but btn disabled(cause there were initially no pages)
    // then the filter is changed to add pages the btn needs to be manually re-enabled
    if (!!numServiceFoundFB || !!numServiceFoundGMB || !!numServiceFoundIG) {
      this.disableSubmit$$.next(false);
    }
  }

  // All account group ids for a path
  accountGroupIdsForPath$(path: string[], partnerId: string): Observable<AccountGroupWithEdition[]> {
    const brandsContext = new BrandContext(path, partnerId, null, []);
    return this.analyticsApiService
      .queryMetrics(this.buildAccountGroupRequest(brandsContext))
      .pipe(map((response) => this._getAgidsFromQueryResponse(response)));
  }

  getCountersFromActiveLocations(activeLocation: Array<any>) {
    const counters = {}; // Object to hold the counters for each pattern

    const patterns = {
      FBP: /^FBP-/,
      GMB: /^accounts\/.*\/locations\/.*$/,
      IG: /^IGU-/,
      LI: /^LI[UC]-/,
      // Add more patterns here if needed
    };

    Object.keys(patterns).forEach((pattern) => {
      counters[pattern] = 0;
    });

    activeLocation.forEach((obj) => {
      obj?.socialServiceIds?.forEach((socialServiceId) => {
        // Check each pattern against the socialServiceId and increment the corresponding counter
        Object.entries(patterns).forEach(([pattern, regex]) => {
          if (regex.test(socialServiceId)) {
            counters[pattern]++;
          }
        });
      });
    });

    return [counters['FBP'], counters['GMB'], counters['IG'], counters['LI']];
  }

  // All the account groups in a brand mapped to their available services
  fullBrandToAgidServicesMap$(): Observable<Map<string, unknown>> {
    return this.accountGroupIdsForPath$([this.configService.brandId], this.partnerId).pipe(
      switchMap((accountGroups) => {
        const accountGroupIds = accountGroups.map((accountGroup) => accountGroup.accountGroupId);
        this.totalLocations$$.next(accountGroupIds.length);

        return forkJoin([
          this.repcoreApiService.getServicesForBrand(accountGroupIds),
          this.instagramService.listUsersByMultiBusinesses(accountGroupIds, null, MULTI_LOCATION_INSTAGRAM_PAGE_SIZE),
        ]).pipe(
          map(([response, responseIG]) => {
            const agidMap = new Map<string, unknown>();
            Object.keys(response).forEach((agid) => {
              const accountGroup = accountGroups.find((accountGroup) => accountGroup.accountGroupId === agid);
              if (accountGroup && Object.keys(response[agid]).length > 0) {
                const igAccount = responseIG?.results?.find((u) => u.businessId == agid);
                const inflatedResponse = {
                  ...response[agid],
                  ig_info: !igAccount
                    ? {}
                    : {
                        name: igAccount.fullName,
                        ssid: igAccount.userId,
                        tokeIsBroken: !!igAccount.tokenBroken,
                      },
                  agid_info: { ...response[agid].agid_info, isPro: accountGroup.isPro },
                };
                agidMap.set(agid, inflatedResponse);
              }
            });
            return agidMap;
          }),
        );
      }),
      tap((agidToServiceMap) => {
        const [serviceFoundFB, serviceFoundGMB, serviceFoundIG, serviceFoundLI] =
          this.countMLServicesFromKey(agidToServiceMap);
        this.composerStore.setSocialProfile(agidToServiceMap);
        this.numLocationsWithFBService$$.next(serviceFoundFB);
        this.numLocationsWithGMBService$$.next(serviceFoundGMB);
        this.numLocationsWithIGService$$.next(serviceFoundIG);
        this.numLocationsWithLIService$$.next(serviceFoundLI);
        this.fullBrandServiceMap$$.next(agidToServiceMap);
      }),
    );
  }

  countMLServicesFromKey(agidToServiceMap: Map<string, unknown>) {
    let numServiceFoundFB = 0;
    let numServiceFoundGMB = 0;
    let numServiceFoundIG = 0;
    let numServiceFoundLI = 0;

    const keys = Array.from(agidToServiceMap.keys());
    for (const location of keys) {
      const entry = agidToServiceMap.get(location);
      if (!entry) {
        continue;
      }
      if (Object.keys(entry['facebook_info']).length > 0) {
        numServiceFoundFB += 1;
      }
      if (Object.keys(entry['gmb_info']).length > 0) {
        numServiceFoundGMB += 1;
      }
      if (Object.keys(entry['ig_info']).length > 0) {
        numServiceFoundIG += 1;
      }
      if (Object.keys(entry['linkedin_info']).length > 0) {
        numServiceFoundLI += 1;
      }
    }
    return [numServiceFoundFB, numServiceFoundGMB, numServiceFoundIG, numServiceFoundLI];
  }

  _getAgidsFromQueryResponse(resp: QueryMetricsResponse): AccountGroupWithEdition[] {
    const metricRes = resp?.metricResults?.[0];
    const metrics = metricRes?.metrics?.metrics ?? [];

    return metrics
      .filter((metric) => metric.measures != null && metric.measures.length >= 2)
      .map((metric) => ({ accountGroupId: metric.measures[0], isPro: metric.measures[1] }));
  }

  get fullBrandServiceMap$(): Observable<Map<string, unknown>> {
    return this.fullBrandServiceMap$$.asObservable();
  }

  get filters$(): Observable<Map<string, string[]>> {
    return this.filters$$.asObservable();
  }

  get disableSubmit$(): Observable<boolean> {
    return this.disableSubmit$$.asObservable();
  }

  get numFBPages$(): Observable<number> {
    return this.numLocationsWithFBService$$.asObservable();
  }

  get numGMBPages$(): Observable<number> {
    return this.numLocationsWithGMBService$$.asObservable();
  }

  get numIGPages$(): Observable<number> {
    return this.numLocationsWithIGService$$.asObservable();
  }

  get numLIPages$(): Observable<number> {
    return this.numLocationsWithLIService$$.asObservable();
  }

  get totalLocationsInBrand$(): Observable<number> {
    return this.totalLocations$$.asObservable();
  }

  clearAccountGroupIds(): void {
    this.selectedAccountGroupIds$$.next([]);
  }

  getAccountGroupIds(): string[] {
    return this.selectedAccountGroupIds$$.getValue();
  }

  setAccountGroupIds(accountGroupIds: string[]): void {
    this.selectedAccountGroupIds$$.next(accountGroupIds);
  }

  private capitalizeFirstLetters(a: string): string {
    return a
      .split(' ')
      .map((c) => c.charAt(0).toUpperCase() + c.slice(1).toLowerCase())
      .join(' ');
  }

  //----------------------ML MultiService Type--------------------------------//

  ToggleService(serviceType: SocialService) {
    this.composerStore.toggleMLServiceGroup(serviceType);
  }

  // ------------- MLA Queries ---------------------------- //

  public buildAccountGroupRequest(brandsContext: BrandContext, filters: Filter[] = null): QueryMetricsRequest {
    return new QueryMetricsRequest({
      partnerId: brandsContext.partnerId,
      metricName: 'sm_account_group_editions',
      resourceIds: [
        new ResourceId({
          groupId: new GroupResourceId({ groupPathNodes: brandsContext.path }),
        }),
      ],
      measures: [
        new Measure({
          measure: 'account_group_id',
        }),
        new Measure({
          measure: 'is_pro',
        }),
      ],
      filter: brandsContext.buildFilter(filters),
    });
  }

  private buildAccountGroupGeographyRequest(brandContext: BrandContext): QueryMetricsRequest {
    return new QueryMetricsRequest({
      partnerId: brandContext.partnerId,
      metricName: 'account_group',
      resourceIds: [
        new ResourceId({
          groupId: new GroupResourceId({ groupPathNodes: brandContext.path }),
        }),
      ],
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: '*',
            aggOp: MeasureAggregateOperator.COUNT,
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          { dimension: 'account_group__country' },
          { dimension: 'account_group__state' },
          { dimension: 'account_group__city' },
        ],
        groupByOperator: GroupByOperator.OPERATOR_ROLLUP,
      }),
    });
  }

  clearBrandService(): void {
    this.fullBrandServiceMap$$.next(new Map<string, unknown>());
    this.composerStore.brandAgids$$.next(new Map<string, unknown>());
    this.composerStore.setSocialProfile(new Map<string, unknown>());
    this.numLocationsWithFBService$$.next(0);
    this.numLocationsWithGMBService$$.next(0);
    this.numLocationsWithIGService$$.next(0);
    this.numLocationsWithLIService$$.next(0);
    this.clearAccountGroupIds();
    this.networkGroupSelectedSubject.next({
      [SocialService.FACEBOOK]: true,
      [SocialService.GMB]: true,
      [SocialService.INSTAGRAM]: true,
      [SocialService.LINKEDIN_COMPANY]: true,
      [SocialService.LINKEDIN]: true,
    });
    this.filters$$.next(new Map<string, string[]>());
  }
}
