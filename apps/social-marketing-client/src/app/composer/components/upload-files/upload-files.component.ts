import {
  Component,
  EventEmitter,
  HostListener,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { ComposerStoreService } from '../../composer-store.service';
import { catchError, distinctUntilChanged, filter, finalize, map, share, startWith } from 'rxjs/operators';
import { combineLatest, EMPTY as empty, Observable } from 'rxjs';
import { UploadedFile } from '../../interfaces';
import { MediaLibraryService } from '../media-library/media-library.service';
import { SnackBarService } from '../../../shared/snack-bar/snack-bar.service';
import { TranslateService } from '@ngx-translate/core';
import { AcceptedImageMimeTypes, AcceptedMediaMimeTypes } from '../../constants';
import { VideoUploadService } from '../video-upload/video-upload.service';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-upload-files',
  templateUrl: './upload-files.component.html',
  styleUrls: ['./upload-files.component.scss'],
  standalone: false,
})
export class UploadFilesComponent implements OnInit, OnChanges {
  @Input() saveToAccount = false;
  @Output() mediaSelected = new EventEmitter();

  @ViewChild('mediaUploader', { static: true }) mediaUploader;

  dragOver = false;
  acceptedMediaMimeTypes = AcceptedImageMimeTypes;
  mediaUploading$: Observable<boolean>;
  uploadProgress$: Observable<number>;
  readableUploadProgress$: Observable<string>;
  spinnerMode$: Observable<string>;

  toolTipText = this.translateService.instant('COMPOSER.UPLOAD_MEDIA.UPLOAD_IMAGE');
  selectMediaText = this.translateService.instant('COMPOSER.UPLOAD_MEDIA.DRAG_AND_DROP_MEDIA');
  helperText = this.translateService.instant('COMPOSER.UPLOAD_MEDIA.UPLOAD_LIMIT_LABEL');

  constructor(
    private composerStore: ComposerStoreService,
    private mediaLibraryService: MediaLibraryService,
    private translateService: TranslateService,
    private snackBarService: SnackBarService,
    private videoUploadService: VideoUploadService,
    private snackBar: MatSnackBar,
  ) {}

  ngOnInit(): void {
    this.mediaUploading$ = this.composerStore.mediaUploading$.pipe(
      map((uploading) => !!uploading),
      startWith(false),
      share(),
    );

    this.uploadProgress$ = this.videoUploadService.uploadProgress$.pipe(
      filter((uploadProgress) => !!uploadProgress),
      share(),
    );

    this.readableUploadProgress$ = this.videoUploadService.uploadProgress$.pipe(
      map((progress: number) => (progress ? `${(progress * 100).toFixed(2)}%` : '')),
      share(),
    );

    this.spinnerMode$ = combineLatest([
      this.composerStore.mediaUploading$,
      this.videoUploadService.uploadProgress$,
    ]).pipe(
      map(([mediaUploading, progress]) => (mediaUploading && progress ? 'determinate' : 'indeterminate')),
      distinctUntilChanged(),
      startWith('indeterminate'),
    );
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.acceptedMediaMimeTypes = AcceptedMediaMimeTypes;
    this.toolTipText = this.translateService.instant('COMPOSER.UPLOAD_MEDIA.UPLOAD_MEDIA');
    this.selectMediaText = this.translateService.instant('COMPOSER.UPLOAD_MEDIA.DRAG_AND_DROP_MEDIA');

    if (changes.saveToAccount && changes.saveToAccount.currentValue) {
      this.toolTipText = this.translateService.instant('COMPOSER.UPLOAD_MEDIA.UPLOAD_IMAGE');
      this.selectMediaText = this.translateService.instant('COMPOSER.UPLOAD_MEDIA.CHOOSE_IMAGE');
    }
  }

  uploadMedia(element: any): void {
    this.composerStore.uploadMedia(element).subscribe(() => {
      this.mediaSelected.emit();
      this.clearPictureAttachment();
    });
  }

  saveImage(uploadedImage: UploadedFile): void {
    this.mediaLibraryService
      .saveImage(uploadedImage.url)
      .pipe(
        finalize(() => this.mediaSelected.emit()),
        catchError(() => {
          this.snackBarService.errorSnack(this.translateService.instant('SNACKBAR.SAVE_IMAGE_ERROR'));
          return empty;
        }),
      )
      .subscribe(() => this.snackBarService.successSnack(this.translateService.instant('SNACKBAR.SAVE_IMAGE_SUCCEED')));
  }

  @HostListener('dragover', ['$event'])
  onDragOver(event: Event): void {
    this.preventAndStop(event);
    this.dragOver = true;
  }

  @HostListener('drop', ['$event'])
  onDrop(event: any): void {
    this.preventAndStop(event);
    this.dragOver = false;
    this.handleMediaUpload(event.dataTransfer);
  }

  @HostListener('paste', ['$event'])
  onPaste(event: ClipboardEvent): void {
    this.preventAndStop(event);
    this.handleMediaUpload(event.clipboardData);
  }

  private preventAndStop(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
  }

  private handleMediaUpload(dataContainer: { files: FileList }): void {
    dataContainer
      ? this.uploadMedia(dataContainer)
      : this.snackBar.open('Your browser does not support drag and drop. Click the "Camera" icon instead.');
  }

  private clearPictureAttachment(): void {
    this.mediaUploader.nativeElement.value = '';
  }
}
