<input
  #mediaUploader
  type="file"
  class="media-upload-input"
  (change)="uploadMedia(mediaUploader)"
  [multiple]="true"
  [accept]="acceptedMediaMimeTypes"
/>
<div
  [ngClass]="{
    dragover: dragOver
  }"
  class="image-upload image-collection-item"
  (click)="mediaUploader.click()"
>
  <span class="button" [matTooltip]="toolTipText" matTooltipPosition="above">
    <mat-icon class="icon" *ngIf="(mediaUploading$ | async) === false"> add_a_photo </mat-icon>
    <mat-progress-spinner
      *ngIf="mediaUploading$ | async"
      [mode]="spinnerMode$ | async"
      [diameter]="24"
      [value]="(uploadProgress$ | async) * 100"
    ></mat-progress-spinner>
    <span *ngIf="(uploadProgress$ | async) > 0">
      {{ readableUploadProgress$ | async }}
    </span>
    <div class="text-center">
      <div class="description">{{ selectMediaText }}</div>
      <div class="helper-text">{{ helperText }}</div>
    </div>
    <button class="choose-image" mat-stroked-button>{{ 'COMPOSER.UPLOAD_MEDIA.CHOOSE_MEDIA' | translate }}</button>
  </span>
  <ng-container *ngIf="saveToAccount">
    <span class="description">
      {{ 'COMPOSER.UPLOAD_MEDIA.AND' | translate }}
    </span>
    <span class="description">
      {{ 'COMPOSER.UPLOAD_MEDIA.SAVE_TO_LIBRARY' | translate }}
    </span>
  </ng-container>
</div>
