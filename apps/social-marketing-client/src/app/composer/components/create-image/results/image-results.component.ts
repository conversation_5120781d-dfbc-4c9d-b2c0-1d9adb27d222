import { Component, EventEmitter, Output, signal } from '@angular/core';
import { CommonModule } from '@angular/common';

import { map } from 'rxjs/operators';
import { from, Observable, switchMap, take } from 'rxjs';

import { ProductAnalyticsService } from '@vendasta/product-analytics';

import { ComposerStoreService } from '../../../composer-store.service';
import { PipesModule } from '../../../../shared/pipes/pipes.module';

import { ImageComponent } from './image.component';
import { AcceptedImageMimeTypes } from '../../../constants';
import { SuggestionsService } from '../../suggestions/suggestions.service';
import { SMComposerApiService } from '../../../composer-api.service';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { MatButtonModule } from '@angular/material/button';
import { SMFeaturesService } from '../../../../core/features.service';

@Component({
  selector: 'app-v-composer-ai-image-results',
  imports: [CommonModule, ImageComponent, PipesModule, GalaxyButtonLoadingIndicatorModule, MatButtonModule],
  templateUrl: './image-results.component.html',
  styleUrls: ['./image-results.component.scss'],
})
export class ImageResultsComponent {
  @Output() imageSelected = new EventEmitter<boolean>();
  readonly addingImageIndexes = signal([]);

  constructor(
    private composerService: SMComposerApiService,
    private composerStore: ComposerStoreService,
    public suggestionsService: SuggestionsService,
    private productAnalyticsService: ProductAnalyticsService,
    public featuresService: SMFeaturesService,
  ) {}

  handleSelectImage(url: string, index: number): void {
    this.setImageAsAdding(index);

    this.productAnalyticsService.trackEvent('GeneratedImageClicked', 'user', 'click');
    let fileName = '';
    this.composerService
      .getBlobFromUrl(url)
      .pipe(
        take(1),
        map((response) => {
          fileName = `${response.blob.slice(0, 30)}.png`;
          return `data:image/png;base64,${response.blob}`;
        }),
        switchMap((file64) => this.toBlob(file64)),
        map((blobResponse) => {
          const contentType = AcceptedImageMimeTypes.includes(blobResponse.type) ? blobResponse.type : 'image/jpeg';
          const d = new Date();
          return new File([blobResponse], fileName, { type: contentType, lastModified: d.valueOf() });
        }),
        switchMap((file: File) => this.composerStore.uploadMedia({ files: [file] }, 'dalle')),
      )
      .subscribe(() => {
        this.imageSelected.emit(true);
        this.removeImageFromAdding(index);
      });
  }

  private toBlob(file: string): Observable<Blob> {
    return from(fetch(file).then((res) => res.blob()));
  }

  private setImageAsAdding(index: number): void {
    this.addingImageIndexes.update((indexes) => [...indexes, index]);
  }

  private removeImageFromAdding(index: number): void {
    this.addingImageIndexes.update((indexes) => indexes.filter((i) => i !== index));
  }
}
