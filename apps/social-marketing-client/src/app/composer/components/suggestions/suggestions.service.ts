import { Injectable, signal } from '@angular/core';

import { finalize, map, switchMap, take } from 'rxjs/operators';

import { TranslateService } from '@ngx-translate/core';
import { MessageLength, TemplateType, ImageCreated, CreateImageRequestInterface } from '@vendasta/social-posts';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

import { GenerateType } from './generate-type';
import { LOCATION_KEYS, POSTHOG_KEYS } from '../../constants';
import { ComposerStoreService } from '../../composer-store.service';
import { SnackBarService } from '../../../shared/snack-bar/snack-bar.service';
import { SMComposerApiService } from '../../composer-api.service';
import { ConfigService } from '../../../core';
import { TechnicalPromptModel } from '../../../core/ai/technical-prompt.model';

const generatedTypeEvent = {
  [GenerateType.CONTENT]: POSTHOG_KEYS.COMPOSER_SUGGEST_AI_TEXT,
  [GenerateType.IMAGES]: POSTHOG_KEYS.COMPOSER_SUGGEST_AI_IMAGE,
  [GenerateType.CONTENT_AND_IMAGES]: POSTHOG_KEYS.COMPOSER_SUGGEST_AI_IMAGE_CONTENT,
};

/**
 * Remove wrapping quotes from text. If text is not wrapped in quotes, it is returned as is.
 *
 * For example: Hello -> Hello
 *              "Hello -> "Hello
 *              Hello" -> Hello"
 *              "Hello" -> Hello
 */
export const removeWrappingQuotes = (text: string): string =>
  text.startsWith('"') && text.endsWith('"') ? text.slice(1, -1) : text;

@Injectable({ providedIn: 'root' })
export class SuggestionsService {
  readonly isGeneratingText = signal(false);
  readonly isRegeneratingText = signal(false);
  readonly isGeneratingImages = signal(false);
  readonly isGeneratingMoreImages = signal(false);
  readonly isRegeneratingImages = signal(false);
  readonly showGeneratedContentArea = signal(false);

  readonly topic = signal('');
  readonly generatedType = signal(GenerateType.CONTENT_AND_IMAGES);
  readonly postLength = signal(MessageLength.SHORT_FORM);
  readonly tone = signal<string | null>(null);
  readonly generatedText = signal('');
  readonly textPrompt = signal('');
  readonly textPromptVisible = signal(false);
  readonly imagePrompt = signal('');
  readonly imagePromptVisible = signal(false);
  readonly generatedImages = signal<ImageCreated[]>([]);
  readonly customInstructions = signal<string | null>(null);
  readonly useCustomInstructions = signal<boolean>(true);
  private imageSettings = signal<CreateImageRequestInterface>({ size: '512x512', model: 'dall-e-2', imageAmount: 3 });
  private readonly isMultilocation$ = this.composerStore.brandAgids$.pipe(map((agids) => agids?.size > 0));

  constructor(
    private composerService: SMComposerApiService,
    private configService: ConfigService,
    private composerStore: ComposerStoreService,
    private snackBarService: SnackBarService,
    private translateService: TranslateService,
    private productAnalyticsService: ProductAnalyticsService,
  ) {}

  generate(
    generateType: GenerateType,
    topic: string,
    postLength: MessageLength = MessageLength.SHORT_FORM,
    tone?: string,
    customInstructions?: string,
    useCustomInstructions = true,
  ): void {
    this.showGeneratedContentArea.set(true);
    this.isRegeneratingText.set(false);
    this.isRegeneratingImages.set(false);
    this.topic.set(topic);
    this.generatedType.set(generateType);
    this.postLength.set(postLength);
    this.tone.set(tone || null);
    this.trackEvent(generatedTypeEvent[generateType]);
    this.useCustomInstructions.set(useCustomInstructions);
    this.customInstructions.set(customInstructions || null);
    if (generateType === GenerateType.CONTENT || generateType === GenerateType.CONTENT_AND_IMAGES) {
      this.generateContentForTopic(topic, postLength, tone, customInstructions, useCustomInstructions);
    }
    if (generateType === GenerateType.IMAGES || generateType === GenerateType.CONTENT_AND_IMAGES) {
      this.generateImageForTopic(topic);
    }
  }

  clear(): void {
    this.isGeneratingText.set(false);
    this.isGeneratingImages.set(false);
    this.showGeneratedContentArea.set(false);
    this.topic.set('');
    this.generatedType.set(GenerateType.CONTENT_AND_IMAGES);
    this.postLength.set(MessageLength.SHORT_FORM);
    this.tone.set(null);
    this.generatedText.set('');
    this.textPrompt.set('');
    this.textPromptVisible.set(false);
    this.imagePrompt.set('');
    this.imagePromptVisible.set(false);
    this.generatedImages.set([]);
  }

  useText(text: string): void {
    this.trackEvent(POSTHOG_KEYS.COMPOSER_USED_AI_CONTENT);
    this.composerStore.updatePostText(text);
  }

  regenerateText(technicalPrompt: TechnicalPromptModel): void {
    this.trackEvent(POSTHOG_KEYS.COMPOSER_AI_REGENERATE_TEXT);
    this.isRegeneratingText.set(true);
    this.customInstructions.set(technicalPrompt.customInstructions);
    this.generateContentForPrompt(
      technicalPrompt.prompt,
      this.postLength(),
      TemplateType.TEMPLATE_CUSTOM,
      technicalPrompt.customInstructions,
      this.useCustomInstructions(),
    );
  }

  regenerateImage(prompt: string): void {
    this.trackEvent(POSTHOG_KEYS.COMPOSER_AI_REGENERATE_IMAGE);
    this.isRegeneratingImages.set(true);
    this.generateImagesForPrompt(prompt);
  }

  generateImageVariationsFromUrl(url: string): void {
    this.trackEvent('ImageVariationClicked');
    this.isRegeneratingImages.set(true);
    this.isGeneratingImages.set(true);
    this.composerService
      .createVariations(url)
      .pipe(
        take(1),
        map((resp) => resp.generatedImages),
        finalize(() => {
          this.isGeneratingImages.set(false);
          this.isRegeneratingImages.set(false);
        }),
      )
      .subscribe({
        next: (resp) => this.generatedImages.set(resp),
        error: () => this.snackBarService.errorSnack(this.translateService.instant('SNACKBAR.IMAGE_GENERATION')),
      });
  }

  private generateContentForTopic(
    topic: string,
    length: MessageLength,
    tone?: string,
    customInstructions?: string,
    useCustomInstructions = true,
  ): void {
    this.generateContentForPrompt(
      this.getTextPrompt(topic, length, tone),
      length,
      TemplateType.TEMPLATE_CUSTOM,
      customInstructions,
      useCustomInstructions,
    );
  }

  private generateImageForTopic(topic: string): void {
    this.generateImagesForPrompt(this.getImagePrompt(topic));
  }

  private generateContentForPrompt(
    prompt: string,
    length: MessageLength,
    templateType: TemplateType,
    customInstructions?: string,
    useCustomInstructions = true,
  ): void {
    this.isGeneratingText.set(true);
    this.textPrompt.set(prompt);
    this.composerStore
      .suggestMessage(prompt, length, templateType, customInstructions, useCustomInstructions)
      .pipe(
        take(1),
        map((suggestedMessage) => removeWrappingQuotes(suggestedMessage)),
        finalize(() => {
          this.isGeneratingText.set(false);
          this.isRegeneratingText.set(false);
        }),
      )
      .subscribe({
        next: (suggestedMessage) => {
          this.generatedText.set(suggestedMessage);
        },
        error: () =>
          this.snackBarService.errorSnack(this.translateService.instant('COMPOSER.SUGGESTIONS.ERROR_MESSAGE')),
      });
  }

  private generateImagesForPrompt(prompt: string): void {
    this.isGeneratingImages.set(true);
    this.imagePrompt.set(prompt);
    this.isMultilocation$
      .pipe(
        take(1),
        switchMap((isMultilocation) =>
          this.composerService.createImage(prompt, this.configService.accountGroupId, this.imageSettings(), {
            sm_context: isMultilocation ? 'multi-location' : 'single-location',
          }),
        ),
        map((resp) => resp.generatedImages),
        finalize(() => {
          this.isGeneratingImages.set(false);
          this.isRegeneratingImages.set(false);
        }),
      )
      .subscribe({
        next: (resp) => this.generatedImages.set(resp),
        error: () => this.snackBarService.errorSnack(this.translateService.instant('SNACKBAR.IMAGE_GENERATION')),
      });
  }

  generateMoreImages(): void {
    this.isGeneratingMoreImages.set(true);
    this.isMultilocation$
      .pipe(
        take(1),
        switchMap((isMultilocation) =>
          this.composerService.createImage('', this.configService.accountGroupId, this.imageSettings(), {
            sm_context: isMultilocation ? 'multi-location' : 'single-location',
          }),
        ),
        map((resp) => resp.generatedImages),
        finalize(() => {
          this.isGeneratingMoreImages.set(false);
          this.isRegeneratingImages.set(false);
        }),
      )
      .subscribe({
        next: (resp) => this.addMoreImages(resp),
        error: () => this.snackBarService.errorSnack(this.translateService.instant('SNACKBAR.IMAGE_GENERATION')),
      });
  }

  private addMoreImages(images: ImageCreated[]) {
    const previousImages = this.generatedImages();
    this.generatedImages.set([...previousImages, ...images]);
  }

  updateImageSettings(settings: CreateImageRequestInterface): void {
    this.imageSettings.set(settings);
  }

  setDefaultImageSettings() {
    this.imageSettings.set({ size: '512x512', model: 'dall-e-2', imageAmount: 3 });
  }

  private getTextPrompt(topic: string, length: MessageLength, tone?: string) {
    const promptParts: string[] = [];
    if (length === MessageLength.LONG_FORM) {
      promptParts.push('Write a long post for a social network.');
    } else {
      promptParts.push('Write a very short post for a social network.');
    }
    if (tone) {
      promptParts.push(`The post should be in the tone "${tone}".`);
    }
    promptParts.push(`The post should be about the provided prompt:\n\n${topic}`);
    return promptParts.join(' ');
  }

  private getImagePrompt(topic: string) {
    return `Generate a flawless, high-quality and pixel-perfect image depicting about '${topic}', with no distortion by using the best blend of camera type, lens, lighting, camera settings, vibes, resolution, and style. Capture the scene with optimal sharpness, clarity, engaging atmosphere, and minimal post-processing for a professional and visually appealing result. Ensure that the resolution is optimal for online purposes and there will be no text in the image .`;
  }

  private trackEvent(eventName: string): void {
    this.isMultilocation$.pipe(take(1)).subscribe((isMultilocation) =>
      this.productAnalyticsService.trackEvent(eventName, 'user', 'click', 0, {
        location: isMultilocation ? LOCATION_KEYS.MULTI : LOCATION_KEYS.SINGLE,
      }),
    );
  }
}
