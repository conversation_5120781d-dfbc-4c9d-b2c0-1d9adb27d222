import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  HostListener,
  Inject,
  Input,
  NgZone,
  OnDestroy,
  OnInit,
  Output,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { Mat<PERSON>atepicker, MatDatepickerInputEvent } from '@angular/material/datepicker';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { EmojiData } from '@ctrl/ngx-emoji-mart/ngx-emoji';
import { BehaviorSubject, combineLatest, firstValueFrom, fromEvent, iif, Observable, of, Subscription } from 'rxjs';
import {
  auditTime,
  debounceTime,
  filter,
  finalize,
  map,
  mergeMap,
  shareReplay,
  startWith,
  switchMap,
  take,
  tap,
  throttleTime,
  withLatestFrom,
} from 'rxjs/operators';

import moment, { Moment } from 'moment';
import { ComposerStoreService } from './composer-store.service';
import {
  ComposerSettings,
  Coupon,
  InterestingContentItem,
  LinkHistory,
  MentionPromptOptions,
  PanelType,
  SocialMentionInterface,
  typeServiceMlSelected,
} from './interfaces';
import { serviceIconPath, SocialService, WorkflowType } from './post';

import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { DOCUMENT } from '@angular/common';
import { UntypedFormControl } from '@angular/forms';
import { MatSidenav } from '@angular/material/sidenav';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { MentionConfig } from '@vendasta/angular-social-mentions';
import { GalaxyAiIconService } from '@vendasta/galaxy/ai-icon';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import * as twitterText from 'twitter-text';
import { ConfigService } from '../core';
import { SMFeaturesService } from '../core/features.service';
import { Customization } from '../core/post/customization';
import { RpcClass } from '../rpc-class';
import { PostableService } from '../shared/social-service/social-service.service';
import { UpdateRequest } from './components/ai-menu/ai-menu-settings';
import { BrandsService } from './components/brands/brands.service';
import { ImageCropperModalComponent } from './components/image-cropper-modal/image-cropper-modal.component';
import { ImageUploadModalComponent } from './components/image-upload-modal/image-upload-modal.component';
import { PostTextActionsService } from './components/post-text-actions/post-text-actions.service';
import { PostModeEnum } from './components/post-type/config';
import { SuggestionsService } from './components/suggestions/suggestions.service';
import { ValidatorFailure } from './composer-validator.service';
import * as CONSTANTS from './constants';
import { HASHTAG_REGEX, MENTION_PROMPT_OPTION_MESSAGES, POSTHOG_KEYS } from './constants';
import { CookieService } from './cookie.service';
import { ConfirmationDialogComponent } from './shared/dialogs/confirmation-dialog/confirmation-dialog.component';
import {
  CreateTemplateDialogComponent,
  CreateTemplateDialogResponse,
} from './shared/dialogs/create-template-dialog/create-template-dialog.component';
import { MessagePromptComponent } from './shared/dialogs/message-prompt';
import { PostResultComponent } from './shared/dialogs/post-result/post-result.component';
import { ValidationCollectorService } from './validation-collector.service';
import { PostType } from '@vendasta/social-posts';
import { DateUsageModes } from './components/date-time-selector/date-time-selector.component';

export enum Display {
  BOTH = 'both',
  COMPOSER_ONLY = 'composer',
  PREVIEW_ONLY = 'preview',
}

export enum TriggeredFrom {
  CALENDAR = 'Calendar',
}

const TEMPLATES_HINT_COOKIE = 'templatesHintCookie';

@RpcClass('ComposerComponent', [
  'displayComposerOnly',
  'displayPreviewOnly',
  'selectServices',
  'removeToolbar',
  'setScheduledDate',
  'addMedia',
  'hidePostButton',
  'submitPost',
  'clearMedia',
  'setPostText',
])
@Component({
  selector: 'app-v-composer',
  templateUrl: './composer.component.html',
  styleUrls: ['./composer.component.scss'],
  standalone: false,
})
export class ComposerComponent implements OnInit, OnDestroy, AfterViewInit {
  Display = Display;
  TriggeredFrom = TriggeredFrom;
  postService = SocialService;
  panelType = PanelType;
  POST_TYPE = PostType;

  POST_MODE = PostModeEnum;
  showBackBtn = false;
  showFooter = true;

  @Input() visible = false;
  @Input() accountGroupId: string = null;
  @Input() brandId: string = null;
  @Input() showTopNavBar = false;
  @Input() showClose = true;
  @Input() showDraft = true;
  @Input() showTemplate = true;

  @Output() visibleEvent = new EventEmitter<boolean>();

  @Output() dataInfo = new EventEmitter<any>();

  @ViewChild('sidePanel') sidePanel: MatSidenav;
  @ViewChild('composePost') composePost;
  @ViewChild('imageCropper') imageCropper;
  @ViewChild('serviceSelector') serviceSelector;
  //Uncomment it later if custom overlay is needed.
  // @ViewChild('textBox') textBox: ElementRef;
  @ViewChild('picker') datePicker: MatDatepicker<Date>;
  getEditedPost: typeServiceMlSelected;
  selectedPanel$$ = new BehaviorSubject<PanelType>(PanelType.MEDIA);
  selectedPanel$ = this.selectedPanel$$.asObservable();
  sidePanelTitle$: Observable<string>;
  existingPostIds: string[] = [];
  textArea: HTMLTextAreaElement;
  templateContent: string;

  subscriptions: Subscription[] = [];

  mediaTabGroupSelectedIndex = 0;

  highlightErrors: boolean;
  isUWM = false;
  highlightNoAccountError: boolean;

  previewLinkHistory$: Observable<LinkHistory>;
  customHint$: Observable<string>;

  showShortenLinks$: Observable<boolean>;

  tweetLength$: Observable<number>;
  tweetValid$: Observable<boolean>;

  hashtagCount$: Observable<number>;
  igHashtagCountValid$: Observable<boolean>;
  postLength$: Observable<number>;

  instagramSelected$: Observable<boolean>;
  tiktokSelected$: Observable<boolean>;
  youtubeSelected$: Observable<boolean>;
  gmbSelected$: Observable<boolean>;

  instagramValid$: Observable<boolean>;
  linkedInValid$: Observable<boolean>;
  gmbValid$: Observable<boolean>;
  tiktokValid$: Observable<boolean>;
  availablePostableServices$: Observable<PostableService[]>;

  isTemplate$: Observable<boolean>;
  templateTitle$: Observable<string>;
  showTemplate$: Observable<boolean>;
  showDraft$: Observable<boolean>;
  showPostButton = true;

  WorkflowType = WorkflowType;
  workFlowType$: Observable<WorkflowType> = of(WorkflowType.POST_WORKFLOW);

  scheduledPostCount$: Observable<number> = of(null);
  readonly SCHEDULED_POST_LIMIT: number;

  mediaUploading$: Observable<boolean>;
  mediaLibraryEnabled$: Observable<boolean> = of(false);
  proFlag$: Observable<boolean>;

  defaultServiceLoading$: Observable<boolean>;
  previewLinkLoading$: Observable<boolean>;
  requirePreviewLink$: Observable<boolean>;
  postButtonText$: Observable<string>;
  postModeButtonText$: Observable<string>; // Post Text for the Post Mode Button
  draftButtonText$: Observable<string>;
  templateButtonText$: Observable<string>;

  private emojiIndex = -1;
  topNavBarOffset = 0;
  coupons$: Observable<Coupon[]>;

  rootCustomizationsFailingValidators$: Observable<ValidatorFailure[]>;
  composerFailingValidators$: Observable<ValidatorFailure[]>;

  serviceSelectorValidator$: Observable<ValidatorFailure>;
  mediaUploadValidator$: Observable<ValidatorFailure>;
  imageUsedPreviouslyValidator$: Observable<ValidatorFailure>;
  invalidInstagramImage$: Observable<ValidatorFailure>;
  gifsOnInstagramValidator$: Observable<ValidatorFailure>;
  scheduledPostValidator$: Observable<ValidatorFailure>;
  schedulerValidator$: Observable<ValidatorFailure>;
  youtubeMediaValidator$: Observable<ValidatorFailure>;
  brandsServiceLimitValidator$: Observable<ValidatorFailure>;

  facebookSelected$: Observable<boolean>;
  twitterSelected$: Observable<boolean>;
  linkedinSelected$: Observable<boolean>;

  shouldDisableDraftButton$: Observable<boolean>;
  shouldDisableSubmitButton$: Observable<boolean>;
  customizationsToPreview$: Observable<Customization[]>;

  displaying: Display = Display.BOTH;
  previousDisplaying: Display = Display.BOTH;
  displayToolbar = true;
  isEditing$: Observable<boolean>;
  draftId$: Observable<string>;
  hasTwoStageFlag$: Observable<boolean>;
  isMobile$: Observable<boolean>;
  displayTwoStage$: Observable<boolean>;
  isNotPc$: Observable<boolean>;

  displayTemplateHint$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  //Uncomment it later if custom overlay is needed.
  // textBoxDimensions$$: BehaviorSubject<any> = new BehaviorSubject<any>(null);

  hashtagConfig: MentionConfig = { useNestedConfig: true };
  hashtags$: Observable<string[]>;
  triggerChar$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  fbMentionsList$$: BehaviorSubject<SocialMentionInterface[]> = new BehaviorSubject<SocialMentionInterface[]>([]);
  twitterMentionsList$$: BehaviorSubject<SocialMentionInterface[]> = new BehaviorSubject<SocialMentionInterface[]>([]);
  selectedSocialTab$$: BehaviorSubject<string> = new BehaviorSubject<SocialService>(SocialService.TWITTER);
  filteredOptions$: Observable<any>;
  hashtagSearchTerm$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  mentionsTerm$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  showMentionTemplate$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  mentionPromptOption$$: BehaviorSubject<number> = new BehaviorSubject<number>(-1);
  mentionPromptMessages = MENTION_PROMPT_OPTION_MESSAGES;
  mentionPromptOptions = MentionPromptOptions;
  generatingContent$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  aiChatbotIsOpen$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  twPromptCode: number = MentionPromptOptions.NONE;
  fbPromptCode: number = MentionPromptOptions.NONE;
  imageDragOver = false;
  isMobile = false;

  // Here for display purposes will be handled by the radio buttons afterward
  postType$ = this.composerStore.postModeSelected$;
  currSelectedCustomization$ = this.composerStore.currSelectedCustomization$;
  composerLoaded = false;

  controls = {
    scheduleDate: new UntypedFormControl(),
  };

  isProContext$ = combineLatest([
    this.configService.proFlag$.pipe(startWith(false)),
    this.composerStore.isProBrand$,
  ]).pipe(map(([proFlag, isProBrand]) => proFlag || isProBrand));

  @HostListener('dragenter') onDragEnter(): void {
    this.imageDragOver = true;
  }

  @HostListener('dragleave') onDragLeave(): void {
    this.imageDragOver = false;
  }

  @HostListener('dragend') onDragEnd(): void {
    this.imageDragOver = false;
  }

  @HostListener('window:scroll', []) scrollHandler(): void {
    if (!this.showTopNavBar) {
      return;
    }
    const navBarHeight = 40;
    if (window.scrollY < navBarHeight) {
      this.topNavBarOffset = navBarHeight - window.scrollY;
    } else {
      this.topNavBarOffset = 0;
    }
  }

  constructor(
    public composerStore: ComposerStoreService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    public featuresService: SMFeaturesService,
    private router: Router,
    public configService: ConfigService,
    private translateService: TranslateService,
    private ref: ChangeDetectorRef,
    private postTextActionsService: PostTextActionsService,
    private validationCollectorService: ValidationCollectorService,
    private activatedRoute: ActivatedRoute,
    private breakpointObserver: BreakpointObserver,
    private cookieService: CookieService,
    private brandsService: BrandsService,
    private productAnalyticsService: ProductAnalyticsService,
    private _: GalaxyAiIconService, // This ensures mat-icon has access to the AI icon.
    private suggestionsService: SuggestionsService,
    public confirmationModal: OpenConfirmationModalService,
    private zone: NgZone,
    private renderer: Renderer2,
    @Inject(DOCUMENT) private document: Document,
  ) {
    this.SCHEDULED_POST_LIMIT = this.composerStore.SCHEDULED_POST_LIMIT;
  }

  ngOnInit(): void {
    this.instagramSelected$ = this.composerStore.instagramSelected$.pipe(startWith(false));
    this.tiktokSelected$ = this.composerStore.tiktokSelected$.pipe(startWith(false));
    this.youtubeSelected$ = this.composerStore.youtubeSelected$.pipe(startWith(false));
    this.gmbSelected$ = this.composerStore.gmbSelected$.pipe(startWith(false));

    this.hasTwoStageFlag$ = this.activatedRoute.queryParams.pipe(
      map((queryParams: { twostage: string }) => queryParams.twostage === 'true'),
      startWith(false),
    );

    this.isMobile$ = this.breakpointObserver
      .observe('(max-width: 994px)')
      .pipe(map((state: BreakpointState) => state.matches));
    this.displayTwoStage$ = combineLatest([this.hasTwoStageFlag$, this.isMobile$]).pipe(map(([a, b]) => a && b));
    this.isNotPc$ = combineLatest([this.displayTwoStage$, this.isMobile$]).pipe(map(([a, b]) => a || b));

    this.tweetLength$ = this.composerStore.tweetLength$;
    this.previewLinkHistory$ = this.composerStore.previewLinkHistory$;

    this.availablePostableServices$ = this.composerStore.availablePostableServices$;
    this.templateTitle$ = this.composerStore.templateTitle$;
    this.customizationsToPreview$ = this.composerStore.customizations$;

    this.workFlowType$ = this.composerStore.workFlowType$$;

    this.linkedinSelected$ = this.composerStore.linkedinSelected$.pipe(startWith(false));

    this.isTemplate$ = combineLatest([this.featuresService.postTemplatesEnabled$, this.composerStore.templateId$]).pipe(
      map(([flag, templateId]) => {
        if (flag) {
          return templateId !== undefined && templateId != null;
        }
        return false;
      }),
    );

    this.showTemplate$ = this.featuresService.postTemplatesEnabled$.pipe(
      map((templatesEnabled) => {
        return this.showTemplate && templatesEnabled;
      }),
    );

    this.showDraft$ = combineLatest([this.composerStore.draftId$, this.isTemplate$]).pipe(
      map(([draftId, isTemplate]) => {
        if (!this.showDraft) {
          return false;
        } else if (draftId) {
          return true;
        } else {
          return !isTemplate;
        }
      }),
    );

    this.mediaUploading$ = this.composerStore.mediaUploading$.pipe(map((uploading) => !!uploading));
    this.scheduledPostCount$ = this.composerStore.scheduledPostCount$;

    this.defaultServiceLoading$ = this.composerStore.defaultServiceLoading$;

    this.coupons$ = this.composerStore.coupons$;
    this.subscriptions.push(
      this.configService.partnerId$.pipe(take(1)).subscribe((pid: string) => {
        this.isUWM = pid === 'USFS';
        this.highlightErrors = this.setHighlightErrors(false, this.isUWM);
        this.showFooter = this.isUWM
          ? this.composerStore?.triggeredFrom === TriggeredFrom.CALENDAR.toString()
            ? true
            : false
          : true;
        top.postMessage('composer_loaded', '*');
        this.composerLoaded = true;
      }),
    );

    this.isEditing$ = this.composerStore.isEditing$;
    this.draftId$ = this.composerStore.draftId$;

    this.topNavBarOffset = this.showTopNavBar ? 40 : 0;

    this.showShortenLinks$ = this.composerStore.urlMatches$.pipe(
      startWith([]),
      map((urlMatches) => {
        if (!urlMatches.length) {
          return false;
        }
        return (
          urlMatches.filter((match) => {
            return match.indexOf('1l.ink') < 0 && match.indexOf('url-shortener-demo.apigateway.co') < 0;
          }).length > 0
        );
      }),
    );

    this.subscriptions.push(
      this.composerStore.failedServices$
        .pipe(withLatestFrom(this.composerStore.succeededServices$))
        .subscribe(([failedServices, succeededServices]) => {
          this.showPostResult(failedServices, succeededServices);
        }),
    );

    this.mediaLibraryEnabled$ = of(false);

    this.postModeButtonText$ = combineLatest([
      this.composerStore.submitting$,
      this.composerStore.isBundleAIPost$,
      this.composerStore.postModeSelected$,
      this.composerStore.workFlowType$$,
      this.composerStore.isCSVUploadedPost$,
    ]).pipe(
      switchMap(
        ([submitting, isBundleAIPost, postModeSelected, workFlowType, isCSVUploadedPost]: [
          boolean,
          boolean,
          PostModeEnum,
          WorkflowType,
          boolean,
        ]) => {
          const stringKey = isBundleAIPost
            ? 'COMPOSER.AI_BUNDLE_POST'
            : postModeSelected === PostModeEnum.SCHEDULED
              ? isCSVUploadedPost
                ? 'COMPOSER.SAVE_POST'
                : `COMPOSER.${
                    submitting
                      ? 'SCHEDULING'
                      : workFlowType === WorkflowType.STORY_WORKFLOW
                        ? 'SCHEDULE_STORY'
                        : workFlowType === WorkflowType.LONG_VIDEO_WORKFLOW
                          ? 'SCHEDULE_LONG_VIDEO'
                          : 'SCHEDULE_POST'
                  }`
              : `COMPOSER.${submitting ? 'POSTING' : 'POST_NOW'}`;

          return this.translateService.stream(stringKey);
        },
      ),
    );

    this.draftButtonText$ = combineLatest([this.composerStore.draftId$, this.composerStore.submitting$]).pipe(
      switchMap(([draftId, submitting]) => {
        return draftId
          ? this.translateService.stream(submitting ? `COMPOSER.DRAFTING` : `COMPOSER.UPDATE_DRAFT`)
          : this.translateService.stream(submitting ? `COMPOSER.DRAFTING` : `COMPOSER.SAVE_DRAFT`);
      }),
    );

    this.templateButtonText$ = this.composerStore.templateId$.pipe(
      switchMap((templateId) => {
        return templateId
          ? this.translateService.stream(`COMPOSER.UPDATE_TEMPLATE`)
          : this.translateService.stream(`COMPOSER.SAVE_AS_TEMPLATE`);
      }),
    );

    this.sidePanelTitle$ = this.selectedPanel$.pipe(
      map((selectedPanel) => {
        switch (selectedPanel) {
          case PanelType.MEDIA:
            return 'COMPOSER.ADD_MEDIA';
          case PanelType.LINK:
            return 'COMPOSER.SHORTEN_LINK';
          case PanelType.CONTENT:
            return 'COMPOSER.INTERESTING_CONTENT.DIALOG_TITLE';
          case PanelType.COUPON:
            return 'COMPOSER.COUPONS.DIALOG_TITLE';
          case PanelType.EMOJI:
            return 'COMPOSER.EMOJI_PICKER.CHOOSE_EMOJI';
          case PanelType.PREVIEW:
            return 'COMPOSER.PREVIEWS';
          case PanelType.CROP:
            return 'COMPOSER.EDIT_IMAGE';
          case PanelType.TEMPLATES:
            return 'COMPOSER.TEMPLATES';
          case PanelType.DYNAMIC_CONTENT:
            return 'COMPOSER.DYNAMIC_CONTENT.TITLE';
          case PanelType.SUGGESTIONS:
            return 'COMPOSER.SUGGESTIONS.AI_TITLE';
          default:
            return '';
        }
      }),
    );
    this.rootCustomizationsFailingValidators$ = iif(
      () => !!this.brandId,
      this.validationCollectorService.brandsFailingValidators$,
      this.validationCollectorService.rootCustomizationFailingValidators$,
    );
    this.composerFailingValidators$ = this.validationCollectorService.composerFailingValidators$;

    this.proFlag$ = this.configService.proFlag$;
    this.serviceSelectorValidator$ = combineLatest([
      this.composerFailingValidators$,
      this.rootCustomizationsFailingValidators$,
    ]).pipe(
      map(([composerValidators, rootValidators]) => {
        return composerValidators.concat(rootValidators).find((v) => v.class === 'service-selector-error');
      }),
    );
    this.brandsServiceLimitValidator$ = this.rootCustomizationsFailingValidators$.pipe(
      map((validators) => validators.find((v) => v.class === 'brands-service-limit-error')),
    );

    this.mediaUploadValidator$ = this.rootCustomizationsFailingValidators$.pipe(
      map((validators) => validators.find((v) => v.class === 'media-upload-error')),
    );
    this.imageUsedPreviouslyValidator$ = this.rootCustomizationsFailingValidators$.pipe(
      map((validators) => validators.find((v) => v.class === 'images-used-previously-warning')),
    );
    this.invalidInstagramImage$ = this.rootCustomizationsFailingValidators$.pipe(
      map((validators) => validators.find((v) => v.class === 'invalid-story-image-warning')),
    );
    this.gifsOnInstagramValidator$ = this.rootCustomizationsFailingValidators$.pipe(
      map((validators) => validators.find((v) => v.class === 'instagram-gifs-warning')),
    );
    this.scheduledPostValidator$ = this.composerFailingValidators$.pipe(
      map((validators) => validators.find((v) => v.class === 'scheduled-post-error')),
    );
    this.schedulerValidator$ = combineLatest([
      this.rootCustomizationsFailingValidators$,
      this.composerFailingValidators$,
    ]).pipe(
      map(([rootValidators, composerValidators]) => {
        const validator = rootValidators.find((v) => v.class === 'scheduler-error');
        return validator ? validator : composerValidators.find((v) => v.class === 'scheduler-error');
      }),
    );

    this.subscriptions.push(
      this.postTextActionsService.openLinkShortener$.subscribe(() => this.openLinkShortenerPanel()),
      this.postTextActionsService.openContent$.subscribe(() => this.openContentPanel()),
      this.postTextActionsService.openCoupons$.subscribe(() => this.openCouponPanel()),
      this.postTextActionsService.openEmojis$.subscribe(() => this.openEmojiPanel()),
      this.postTextActionsService.openTemplates$.subscribe(() => this.openTemplatesPanel()),
      this.postTextActionsService.openDynamicContent$.subscribe(() => this.openDynamicContentPanel()),
      this.postTextActionsService.openSuggestions$.subscribe(() => this.openSuggestionsPanel()),
    );

    this.shouldDisableSubmitButton$ = combineLatest([
      this.composerStore.submitting$,
      this.brandsService.disableSubmit$,
    ]).pipe(
      map(([isSubmitting, disableBrandsSubmit]) => {
        if (this.brandId) {
          return disableBrandsSubmit;
        }
        return isSubmitting;
      }),
    );

    this.shouldDisableDraftButton$ = combineLatest([this.composerStore.submitting$, this.customizeByAccounts$]).pipe(
      map(([submitting, customizeByAccounts]) => submitting || customizeByAccounts),
      shareReplay(),
    );

    this.visibleEvent.emit(this.visible);
    this.cookieService.deleteCookie(TEMPLATES_HINT_COOKIE);

    this.subscriptions.push(
      this.getFirstValidatorFailureFeed$().subscribe((x) => {
        if (!x) {
          top.postMessage('Valid Post', '*');
        } else {
          top.postMessage('Invalid Post', '*');
        }
      }),
    );

    /* A string date directly on the form without TZ helps to display the local date as UTC date and not trigger the UTC date on the
         input field .We work with more than one locale , if we pass just the date the component will display the international conversion.
      */
    const sub = this.scheduledDate$.pipe(filter((date) => !!date)).subscribe((scheduleDate) => {
      const mon = scheduleDate?.getMonth() + 1;
      const formattedDate = `${scheduleDate?.getFullYear().toString()}-${mon.toString().padStart(2, '0')}-${scheduleDate
        ?.getDate()
        .toString()
        .padStart(2, '0')}T01:00:00`;
      this.controls.scheduleDate = new UntypedFormControl(formattedDate || new Date().toISOString());
    });

    this.subscriptions.push(sub);

    this.suggestionsService.clear();

    this.updateInitialPostLength();
    this.composerStore.setUpButtonClickIg$$?.subscribe((composerClose) => {
      if (composerClose) {
        this.onInstagramOptionsSetUpButtonClick();
      }
    });
    if (this.brandId) {
      this.composerLoaded = true;
    }
  }

  ngAfterViewInit(): void {
    this.checkContentForReels();
    this.windowResize();
    fromEvent(window, 'resize')
      .pipe(debounceTime(100))
      .subscribe((_) => {
        this.windowResize();
      });
  }

  windowResize() {
    this.zone.run((_) => {
      const initialSize: any = this.document.querySelectorAll('.mat-tab-group-container .mat-mdc-tab-body');
      const getFooterHeight: HTMLElement = this.document.querySelector('.composer-footer');
      const composerDOM: HTMLElement = this.document.querySelector('.compose-post-container');
      this.isNotPc$.subscribe((val: boolean) => {
        this.isMobile = val;
        if (!val) {
          this.renderer.setStyle(composerDOM, 'padding-bottom', `${getFooterHeight?.clientHeight}px`);
        } else {
          initialSize.forEach((itemList: HTMLElement) => {
            this.renderer.setStyle(composerDOM, 'padding-bottom', `0px`);
            this.renderer.setStyle(itemList, 'height', `${this.document.body.clientHeight - 148}px`);
            this.renderer.setStyle(itemList, 'padding-bottom', `${getFooterHeight?.clientHeight}px`);
          });
        }
        this.ref.detectChanges();
      });
    });
  }

  sidePanelOpened(value) {
    const panelId = this.selectedPanel$$.getValue();
    if (panelId !== this.panelType.PREVIEW || !value) return;
    //Necessary to send a resize event in order to let know carousel it need to re-calc size as mat-sidenav is flex element
    window.dispatchEvent(new Event('resize'));
  }

  initialize(ssids?: string[]): void {
    if (this.accountGroupId) {
      this.composerStore.initializeStore(this.accountGroupId, ssids);
    }
  }

  setAgidOnStore(): void {
    this.composerStore.getAgidFromConfig();
  }

  setHighlightErrors(value: boolean, isUWM: boolean): boolean {
    return value || isUWM;
  }

  selectedMLNetworks(networks): void {
    this.brandsService.networkGroupSelectedSubject.next(networks);
    this.brandsService.updateAgids();
  }

  updateBrandFilter(filter: Map<string, string[]>): void {
    this.brandsService.filters$$.next(filter);
  }

  @HostListener('dragover', ['$event'])
  onDragOver(e: Event): void {
    e.preventDefault();
    e.stopPropagation();
    this.imageDragOver = true;
  }

  @HostListener('drop', ['$event'])
  onDrop(event: any): void {
    event.preventDefault();
    event.stopPropagation();
    this.imageDragOver = false;
    this.handleMediaUpload(event.dataTransfer);
  }

  @HostListener('paste', ['$event'])
  onPaste(e: any): void {
    if (e.clipboardData.files && e.clipboardData.files.length > 0) {
      e.preventDefault();
      e.stopPropagation();
      this.handleMediaUpload(e.clipboardData);
    }
  }

  clearMedia(): void {
    this.composerStore.deleteMedia();
  }

  addMedia(url: string): void {
    fetch(url)
      .then((response) => response.blob())
      .then((response: Blob) => {
        const d = new Date();
        const fileName = url.split('/').slice(-1)[0] + '.' + response.type.split('/')[1];
        const file: File = new File([response], fileName, { type: response.type, lastModified: d.valueOf() });
        this.composerStore.uploadMedia({ files: [file] }).subscribe();
      });
  }

  private handleMediaUpload(dataContainer: { files: FileList }): void {
    if (dataContainer) {
      this.subscriptions.push(this.composerStore.uploadMedia(dataContainer).subscribe());
    } else {
      const message = this.translateService.instant('COMPOSER.DRAG_DROP_ERROR');
      this.snackBar.open(message);
    }
  }

  showPostResult(failedServices: PostableService[], succeededServices: PostableService[]): void {
    if (failedServices.length > 0) {
      const data = {
        accountGroupId: this.accountGroupId,
        title: 'COMPOSER.POST_SUMMARY',
        isErrorMessage: true,
        succeedServices: succeededServices,
        failedServices: failedServices,
        errorMessage: this.composerStore.failedServiceReason,
        component: PostResultComponent,
      };
      this.subscriptions.push(
        this.dialog
          .open(ConfirmationDialogComponent, {
            data: data,
            autoFocus: false,
          } as MatDialogConfig)
          .afterClosed()
          .subscribe(() => {
            this.composerStore.failedServices$$.next([]);
          }),
      );
    }
  }

  get postLoading(): boolean {
    return this.composerStore.isLoading;
  }

  @HostListener('document:keydown.escape', ['$event'])
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onKeydownHandler(event: KeyboardEvent): void {
    if (!this.visible) {
      return;
    }
    this.closeComposer(true);
  }

  public hideComposer(): void {
    this.visible = false;
    this.visibleEvent.emit(false);
  }

  public closeComposer(checkChanges = false, connectAccount = false, fromSave = false): void {
    this.subscriptions.push(
      combineLatest([
        this.composerStore.changesMade$(),
        this.composerStore.draftId$,
        this.composerStore.templateId$,
        this.isEditing$,
        this.customizeByAccounts$,
        this.configService.embedded$,
        this.composerStore.isBundleAIPost$,
        this.scheduledDate$,
        this.composerStore.isCSVUploadedPost$,
      ])
        .pipe(take(1))
        .subscribe(
          ([
            changesMade,
            draftId,
            templateId,
            isEditing,
            isCustomizing,
            isEmbedded,
            isBundleAIPost,
            scheduledDate,
            isCSVUploadedPost,
          ]) => {
            const postDateTime = scheduledDate ?? null;
            const selectedPostMode = this.composerStore?.postModeSelected();
            const triggerFromnCalender = this.composerStore?.triggeredFrom === TriggeredFrom.CALENDAR;
            if (!isEmbedded && !isEditing && !draftId && !templateId && checkChanges && changesMade && !isCustomizing) {
              if (isBundleAIPost) {
                this.bulkCreateEditFlowDiscardModal(connectAccount);
              } else {
                this.showSaveDraftPrompt(connectAccount);
              }
            } else if (checkChanges && changesMade && isCustomizing) {
              this.showCloseCustomizeConfirmation();
            } else if (isCSVUploadedPost) {
              this.resetMLComposer();
            } else {
              this.resetComposer();
            }

            if (connectAccount) {
              let url = `/account/${this.configService.accountGroupId}/settings`;
              if (isEmbedded) {
                url = '/embed' + url;
                this.hideComposer();
              }
              this.router.navigateByUrl(url);
              return;
            }

            if (fromSave && !this.isUWM) {
              this.navigateToPostView(selectedPostMode, triggerFromnCalender, postDateTime);
            }
          },
        ),
    );
  }

  private navigateToPostView(selectedPostMode: PostModeEnum, triggerFromCalendar: boolean, postDateTime: Date): void {
    let url = `/account/${this.configService.accountGroupId}/posts`;
    const queryParams: Record<string, string> = {};

    const statusMapping: Record<PostModeEnum, string> = {
      [PostModeEnum.POST_NOW]: 'published',
      [PostModeEnum.SCHEDULED]: 'scheduled',
      [PostModeEnum.DRAFT]: 'drafts',
      [PostModeEnum.HIDDEN_DRAFT]: 'drafts',
    };

    if (triggerFromCalendar) {
      url = `/account/${this.configService.accountGroupId}/calendar`;

      if (postDateTime) {
        const date = new Date(postDateTime);
        const month = date.getMonth();
        url += `?month=${month}`;
      }

      this.router.navigateByUrl(url);
      return;
    }

    if (selectedPostMode && statusMapping[selectedPostMode]) {
      queryParams.view = statusMapping[selectedPostMode];
    }

    this.router.navigate([url], { queryParams });
  }

  private showCloseCustomizeConfirmation(): void {
    this.confirmationModal
      .openModal({
        type: 'confirm',
        title: this.translateService.instant('COMPOSER.CLOSE_CUSTOMIZE_MODAL.TITLE'),
        message: this.translateService.instant('COMPOSER.CLOSE_CUSTOMIZE_MODAL.MESSAGE'),
        hideCancel: false,
        confirmButtonText: this.translateService.instant('COMPOSER.CLOSE_CUSTOMIZE_MODAL.CLOSE'),
        cancelButtonText: this.translateService.instant('COMPOSER.CLOSE_CUSTOMIZE_MODAL.CANCEL'),
        actionOnEnterKey: false,
        cancelOnEscapeKeyOrBackgroundClick: true,
      })
      .subscribe((response: boolean) => {
        if (response) {
          this.composerStore.clearMediaStorage();
          this.resetComposer();
        }
      });
  }

  private showSaveDraftPrompt(connectAccount = false): void {
    const data = {
      title: 'COMPOSER.DRAFT_MODAL.TITLE',
      message: 'COMPOSER.DRAFT_MODAL.MESSAGE',
      primaryAction: 'COMPOSER.DRAFT_MODAL.SAVE',
      secondaryAction: 'COMPOSER.DRAFT_MODAL.DISCARD',
      component: MessagePromptComponent,
    };
    this.subscriptions.push(
      this.dialog
        .open(ConfirmationDialogComponent, {
          data: data,
          autoFocus: false,
        } as MatDialogConfig)
        .afterClosed()
        .subscribe((response: string) => {
          if (!response) {
            return;
          } else if (response === data.primaryAction) {
            this.saveDraft();
          } else {
            this.composerStore.clearMediaStorage();
            this.resetComposer();
          }
          if (connectAccount) {
            const url = `/account/${this.configService.accountGroupId}/settings`;
            this.router.navigateByUrl(url);
          }
        }),
    );
  }
  private bulkCreateEditFlowDiscardModal(connectAccount = false): void {
    this.confirmationModal
      .openModal({
        type: 'confirm',
        title: this.translateService.instant('COMPOSER.CUSTOMIZE_MODAL.TITLE'),
        message: this.translateService.instant('COMPOSER.CUSTOMIZE_MODAL.MESSAGE'),
        hideCancel: false,
        confirmButtonText: this.translateService.instant('COMPOSER.CUSTOMIZE_MODAL.GO_BACK'),
        cancelButtonText: this.translateService.instant('COMPOSER.CUSTOMIZE_MODAL.KEEP_EDITING'),
        actionOnEnterKey: false,
        cancelOnEscapeKeyOrBackgroundClick: true,
      })
      .subscribe((userDidAction: boolean) => {
        if (userDidAction) {
          this.resetComposer();
        }
        if (connectAccount) {
          const url = `/account/${this.configService.accountGroupId}/settings`;
          this.router.navigateByUrl(url);
        }
      });
  }

  showNewTemplatePrompt(): void {
    this.subscriptions.push(
      combineLatest([this.isTemplate$, this.composerStore.templateTitle$])
        .pipe(
          take(1),
          switchMap(([isTemplate, templateTitle]) => {
            return this.dialog
              .open(CreateTemplateDialogComponent, {
                data: {
                  isUpdate: isTemplate,
                  templateTitle: templateTitle || '',
                } as MatDialogConfig,
                autoFocus: false,
                minWidth: '25vw',
                maxWidth: '350px',
              })
              .afterClosed();
          }),
        )
        .pipe(
          filter((res) => !!res?.createTemplate),
          mergeMap((response: CreateTemplateDialogResponse) => {
            this.composerStore.updateTemplateTitle(response.templateTitle);
            return this.composerStore.submitTemplate();
          }),
        )
        .subscribe(() => this.redirectToTemplates()),
    );
  }

  private redirectToTemplates(): void {
    this.resetComposer();
    firstValueFrom(this.featuresService.displayToolsOption$).then((displayTools) => {
      if (displayTools) {
        this.router.navigate(['/account', this.configService.accountGroupId, 'tools', 'templates'], {
          queryParams: { redirect: 'auto' },
        });
      } else {
        this.router.navigateByUrl(`/account/${this.configService.accountGroupId}/posts/templates`);
      }
    });
  }

  private resetComposer(): void {
    this.highlightErrors = this.setHighlightErrors(false, this.isUWM);
    this.highlightNoAccountError = false;
    top.postMessage('Composer Closed', '*');
    this.hideComposer();
    this.composerStore.resetStore();
  }

  private resetMLComposer(): void {
    this.highlightErrors = this.setHighlightErrors(false, this.isUWM);
    this.highlightNoAccountError = false;
    top.postMessage('Preview Edited', '*');
    this.hideComposer();
    this.composerStore.resetStore();
  }

  public showComposer(composerSettings?: ComposerSettings): void {
    if (!!composerSettings && composerSettings.groupedCustomization) {
      const currentServices = composerSettings.groupedCustomization.services$$.getValue();
      if (currentServices && currentServices.values()) {
        this.existingPostIds = Array.from(currentServices.values());
      }
    }
    if (composerSettings) {
      this.composerStore.applySettings(composerSettings);
    } else {
      this.composerStore.resetStore();
    }
    if (!this.brandId) {
      this.composerStore.fetchCoupons();
    }
    this.visible = true;
    this.visibleEvent.emit(true);
  }

  openLinkShortenerPanel(): void {
    this.selectedPanel$$.next(PanelType.LINK);
    this.sidePanel.open();
  }

  closeSidePanel(): void {
    this.sidePanel.close();
  }

  setPostType(): string {
    let postMode;
    this.postType$.subscribe((type: any) => {
      postMode = type;
    });
    if (postMode === PostModeEnum.SCHEDULED) return this.translateService.instant('COMPOSER.POST_SCHEDULE_TITLE');
    return this.translateService.instant('COMPOSER.POST_DRAFT_TITLE');
  }

  openContentPanel(): void {
    this.selectedPanel$$.next(PanelType.CONTENT);
    this.sidePanel.open();
  }

  contentSelected(content?: InterestingContentItem): void {
    if (content) {
      const contentHint: string = this.translateService.instant('COMPOSER.CONTENT_HINT');
      this.composerStore.updateCustomHint(contentHint);
      if (content.url || content.permalink) {
        this.composerStore.shortenPreviewLink(content.url || content.permalink);
      }
    }
    this.sidePanel.close();
  }

  openCouponPanel(): void {
    this.selectedPanel$$.next(PanelType.COUPON);
    this.sidePanel.open();
  }

  couponSelected(coupon: Coupon): void {
    this.composerStore.insertCoupon(coupon);
    this.sidePanel.close();
  }

  openEmojiPanel(): void {
    this.textArea = document.querySelector('.textarea textarea');
    if (this.textArea && 'selectionStart' in this.textArea) {
      this.emojiIndex = this.textArea.selectionStart;
    }
    this.selectedPanel$$.next(PanelType.EMOJI);
    this.sidePanel.open();
  }

  handleEmoji(emoji: EmojiData): void {
    if (this.textArea && 'selectionStart' in this.textArea) {
      if (this.emojiIndex < 0) {
        this.emojiIndex = this.textArea.selectionStart;
      }
      this.composerStore.insertEmoji(emoji, this.emojiIndex);
      this.emojiIndex += emoji.native.length;
    } else {
      this.composerStore.insertEmoji(emoji, -1);
    }
  }

  openTemplatesPanel(): void {
    this.selectedPanel$$.next(PanelType.TEMPLATES);
    this.sidePanel.open();
  }

  openDynamicContentPanel(): void {
    this.selectedPanel$$.next(PanelType.DYNAMIC_CONTENT);
    this.sidePanel.open();
  }

  openSuggestionsPanel(): void {
    this.selectedPanel$$.next(PanelType.SUGGESTIONS);
    this.sidePanel.open();
  }

  templateSelected(templateText: any): void {
    this.templateContent = templateText?.content;
    const templatesHintOverlaySet: string = this.cookieService.getCookie(TEMPLATES_HINT_COOKIE) || '';
    if (templatesHintOverlaySet !== 'true') {
      // Display the overlay and message
      this.displayTemplateHint$$.next(true);

      //Uncomment it later if custom overlay is needed.
      // this.textBoxDimensions$$.next(this.textBox.nativeElement.getBoundingClientRect());
      // Set the cookie
      this.cookieService.setCookie(TEMPLATES_HINT_COOKIE, 'true', 60 * 60 * 24 * 1000);
    }

    // Close
    window.setTimeout(() => this.sidePanel.close(), 500);
  }

  onSidePanelClose(): void {
    this.subscriptions.push(
      this.selectedPanel$.pipe(take(1)).subscribe((selectedPanel: PanelType) => {
        if (selectedPanel === PanelType.EMOJI) {
          this.emojiIndex = -1;
        }
      }),
    );
  }

  changePublishTime(event: { value: boolean }): void {
    this.composerStore.changePublishTime(event);
  }

  setScheduledDateFromDatepickerEvent(event: MatDatepickerInputEvent<Moment | Date>): void {
    this.subscriptions.push(
      this.scheduledDate$.pipe(take(1)).subscribe((scheduledDate) => {
        const date: Date = moment(event.value).toDate();
        const temp = scheduledDate || new Date();
        date.setHours(temp.getHours(), temp.getMinutes(), temp.getSeconds());
        this.composerStore.setScheduledDate(date);
        this.handleScroll(); // SOC-2610: Get the datetime element into view
      }),
    );
  }

  submitPost(): void {
    this.trackWorkflowEvent();
    this.subscriptions.push(
      this.getFirstValidatorFailure$()
        .pipe(
          withLatestFrom(
            this.isEditing$,
            this.draftId$,
            this.composerStore.isBundleAIPost$,
            this.composerStore.isCSVUploadedPost$,
          ),
          switchMap(([validators, isEditing, draftId, isBundleAIPost, isCSVUploadedPost]) => {
            return !validators
              ? isEditing && !isCSVUploadedPost
                ? this.handleEdit(draftId)
                : this.handleCreate(isBundleAIPost, isCSVUploadedPost)
              : this.highlightErrorsAfterSubmitting();
          }),
        )
        .subscribe(),
    );
  }

  handleSubmitPost(): void {
    const postFunctions = {
      [PostModeEnum.HIDDEN_DRAFT]: () => this.saveDraft(),
      [PostModeEnum.DRAFT]: () => this.saveDraft(),
      [PostModeEnum.SCHEDULED]: () => this.submitPost(),
      [PostModeEnum.POST_NOW]: () => this.submitPost(),
    };

    this.composerStore.handleDateChangeWithPostMode();
    postFunctions[this.composerStore.postModeSelected()]();
  }

  getButtonText(): Observable<string> {
    const buttonTextObservables = {
      [PostModeEnum.HIDDEN_DRAFT]: this.draftButtonText$,
      [PostModeEnum.DRAFT]: this.draftButtonText$,
      [PostModeEnum.SCHEDULED]: this.postModeButtonText$,
      [PostModeEnum.POST_NOW]: this.postModeButtonText$,
    };

    return buttonTextObservables[this.composerStore.postModeSelected()] || this.postModeButtonText$;
  }

  handleEdit(draftId: string) {
    return this.brandId
      ? this.composerStore.editPostToBrand().pipe(finalize(() => this.closeComposer(false, false, true)))
      : this.existingPostIds?.length === 0
        ? this.composerStore.submitPost().pipe(
            tap({
              // only close the composer if the post was successfully submitted
              complete: () => {
                this.closeComposer(false, false, true);
              },
            }),
          )
        : this.composerStore.editPosts('', true, this.existingPostIds, draftId).pipe(
            tap({
              // only close the composer if the post was successfully submitted
              complete: () => {
                this.closeComposer(false, false, true);
              },
            }),
          );
  }

  handleCreate(isBundleAIPost = false, isCSVUploadedPost = false) {
    if (this.brandId && !isCSVUploadedPost) {
      return this.composerStore.submitPostToBrand().pipe(finalize(() => this.closeComposer()));
    }

    if (isBundleAIPost) {
      return this.composerStore.submitAiBundle().pipe(tap(() => this.closeComposer()));
    }

    if (isCSVUploadedPost) {
      return this.composerStore.submitCSVUploadedPost().pipe(tap(() => this.closeComposer()));
    }

    return this.composerStore.submitPost().pipe(
      tap({
        // only close the composer if the post was successfully submitted
        complete: () => {
          this.closeComposer(false, false, true);
        },
      }),
    );
  }

  getFirstValidatorFailure$(): Observable<ValidatorFailure> {
    return combineLatest([
      this.validationCollectorService.rootCustomizationFailingValidators$,
      this.validationCollectorService.composerFailingValidators$,
      this.validationCollectorService.customizationsFailingValidators$,
      this.validationCollectorService.brandsFailingValidators$,
      this.isCustomizing$,
      this.composerStore.isCSVUploadedPost$,
    ]).pipe(
      take(1),
      map(
        ([
          rootCustomizationValidators,
          composerValidators,
          customizationValidators,
          brandsValidators,
          isCustomizing,
          isCSVUploadedPost,
        ]) => {
          return isCustomizing || isCSVUploadedPost
            ? customizationValidators.concat(composerValidators)
            : this.brandId
              ? brandsValidators
              : rootCustomizationValidators.concat(composerValidators);
        },
      ),
      map((validators) => validators.filter((v) => v.type === 'error')),
      map((validators) => (validators.length ? validators[0] : null)),
    );
  }

  getFirstValidatorFailureFeed$(): Observable<ValidatorFailure> {
    return combineLatest([
      this.validationCollectorService.rootCustomizationFailingValidators$,
      this.validationCollectorService.composerFailingValidators$,
      this.validationCollectorService.customizationsFailingValidators$,
      this.validationCollectorService.brandsFailingValidators$,
      this.isCustomizing$,
    ]).pipe(
      // Throttle this feed sanely so we don't spam the other end with messages.
      auditTime(250),
      map(
        ([
          rootCustomizationValidators,
          composerValidators,
          customizationValidators,
          brandsValidators,
          isCustomizing,
        ]) => {
          if (isCustomizing) {
            return customizationValidators.concat(composerValidators);
          } else if (this.brandId) {
            return brandsValidators;
          }
          return rootCustomizationValidators.concat(composerValidators);
        },
      ),
      map((validators) => validators.filter((v) => v.type === 'error')),
      map((validators) => (validators.length ? validators[0] : null)),
    );
  }

  highlightErrorsAfterSubmitting(): Observable<any> {
    this.highlightErrors = this.setHighlightErrors(true, this.isUWM);
    return this.availablePostableServices$.pipe(
      take(1),
      tap((services) => {
        if (services.length === 0) {
          this.highlightNoAccountError = true;
          this.composePost.nativeElement.scrollTop = 0;
        }
      }),
    );
  }

  handleScroll(): void {
    const selector = document.querySelector('#scheduled-date-time');
    selector.scrollIntoView({ behavior: 'smooth' });
  }

  saveDraft(): void {
    this.subscriptions.push(
      this.getFirstValidatorFailure$()
        .pipe(
          switchMap((validators) => {
            // Allow drafts to be saved even if they don't have a connected social network
            const shouldSaveDraft = !validators || validators['class'] === 'service-selector-error';

            return shouldSaveDraft
              ? of(
                  this.composerStore.submitDraft(
                    this.closeComposer.bind(this, false, false, true),
                    this.existingPostIds,
                  ),
                )
              : this.highlightErrorsAfterSubmitting();
          }),
        )
        .subscribe(),
    );
  }

  serviceIcon(service: string): string {
    return serviceIconPath(service);
  }

  now(): Date {
    return new Date();
  }

  openPreviewPanel(): void {
    this.selectedPanel$$.next(PanelType.PREVIEW);
    this.sidePanel.open();
  }

  openImageCropperPanel(data: any): void {
    this.dialog.open(ImageCropperModalComponent, {
      data: data,
      autoFocus: false,
      width: '60%',
      height: 'auto',
      panelClass: 'sm-image-cropper-dialog',
    } as MatDialogConfig);
  }

  croppedImageSelected(data: any): void {
    this.composerStore.replaceUploadedImage(data.croppedImage, data.index);
    this.sidePanel.close();
  }

  openMediaPanelFromCustomize(settings: any): void {
    if (settings.isCrop) {
      this.openImageCropperPanel(settings.data);
      return;
    }
    this.openMediaPanel(settings.data);
  }

  openMediaPanel(mediaTabGroupSelectedIndex: number): void {
    this.mediaTabGroupSelectedIndex = mediaTabGroupSelectedIndex;
    this.dialog.open(ImageUploadModalComponent, {
      data: {
        mediaTabGroupSelectedIndex: mediaTabGroupSelectedIndex,
        brandId: this.brandId,
      },
      autoFocus: false,
      width: '50%',
      height: 'auto',
      panelClass: 'sm-image-upload-dialog',
    } as MatDialogConfig);
  }

  ngOnDestroy(): void {
    this.composerStore.resetStore();
    this.composerStore.clearPostSettings();
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  connectAccounts(): void {
    this.closeComposer(true, true);
  }

  setDisplaying(value: Display): void {
    this.displaying = value;
    this.ref.detectChanges();
  }

  displayComposerOnly(): void {
    this.setDisplaying(Display.COMPOSER_ONLY);
  }

  displayPreviewOnly(): void {
    if (this.datePicker) {
      this.datePicker.close();
    }
    this.setDisplaying(Display.PREVIEW_ONLY);
    window.dispatchEvent(new Event('resize'));
  }

  selectServices(...types: string[]): void {
    const services: SocialService[] = [];
    for (const type of types) {
      if (type === 'facebook') {
        services.push(SocialService.FACEBOOK);
      } else if (type === 'twitter') {
        services.push(SocialService.TWITTER);
      } else if (type === 'linkedin') {
        services.push(SocialService.LINKEDIN_COMPANY);
        services.push(SocialService.LINKEDIN);
      } else if (type === 'instagram') {
        services.push(SocialService.INSTAGRAM);
      } else if (type === 'gmb') {
        services.push(SocialService.GMB);
      }
    }
    this.serviceSelector.selectServiceTypes(services);
    this.ref.detectChanges();
  }

  removeToolbar(): void {
    this.displayToolbar = false;
    this.ref.detectChanges();
  }

  setScheduledDate(d: Date): void {
    this.composerStore.setScheduledDate(d);
    this.ref.detectChanges();
  }

  hidePostButton(): void {
    this.showPostButton = false;
    this.ref.detectChanges();
  }

  goToStageOne(): void {
    this.setDisplaying(this.previousDisplaying);
  }

  goToStageTwo(): void {
    this.subscriptions.push(
      this.getFirstValidatorFailure$()
        .pipe(
          switchMap((validators) => {
            if (!validators) {
              this.previousDisplaying = this.displaying;
              this.setDisplaying(Display.PREVIEW_ONLY);
              return of(null);
            } else {
              return this.highlightErrorsAfterSubmitting();
            }
          }),
          finalize(() => {
            window.dispatchEvent(new Event('resize'));
          }),
        )
        .subscribe(),
    );
  }

  onDateChange(date: Date): void {
    this.composerStore.setScheduledDate(date);
  }

  get customizeByAccounts$(): Observable<boolean> {
    return this.composerStore.isCustomizing$;
  }

  get scheduledDate$(): Observable<Date> {
    return this.composerStore.rootCustomization.scheduledDate$;
  }

  get isCustomizing$(): Observable<boolean> {
    return this.composerStore.isCustomizing$;
  }

  //Uncomment it later if custom overlay is needed.
  // get textBoxDimensions$(): Observable<any> {
  //   return this.textBoxDimensions$$.asObservable();
  // }

  get displayTemplateHint$(): Observable<boolean> {
    return this.displayTemplateHint$$.asObservable();
  }

  onOverlayClicked(): void {
    this.displayTemplateHint$$.next(false);
  }

  onInstagramOptionsSetUpButtonClick(): void {
    this.closeComposer(true);
  }

  checkContentForReels() {
    const sub = this.composerStore.initCheckReels().subscribe();
    this.subscriptions.push(sub);
  }

  toggleReels(): void {
    this.composerStore.updateIsReels();
    const sub = this.composerStore.handleMediaForReels().subscribe();
    this.subscriptions.push(sub);
  }

  getTooltipText(): string {
    return this.translateService.instant('POSTS.POST_BANNERS.INSTAGRAM_REELS_INFO');
  }

  openAISidePanel(): void {
    this.productAnalyticsService.trackEvent(POSTHOG_KEYS.COMPOSER_OPENED_AI_MENU, 'user', 'click');
    this.postTextActionsService.openSuggestions();
  }

  aiMenuSuggestcontent(event: UpdateRequest) {
    this.generatingContent$$.next(true);
    this.productAnalyticsService.trackEvent('AIContentButtonSuggestContent', 'user', 'click');
    this.composerStore
      .suggestContentPrompt(event.existingValue)
      .pipe(take(1))
      .subscribe(() => this.generatingContent$$.next(false));
  }

  updateInitialPostLength() {
    combineLatest([this.composerStore.rootCustomization.postText$, this.composerStore.selectedPostableServices$])
      .pipe(throttleTime(200), debounceTime(200))
      .subscribe(([postText, selectedServices]: [string, PostableService[]]) => {
        if (this.containsServiceType(selectedServices, SocialService.TWITTER)) {
          this.parseTweet(postText);
        }
        if (this.containsServiceType(selectedServices, SocialService.INSTAGRAM)) {
          this.parseHashtags(postText);
        }
        if (
          this.containsServiceType(selectedServices, SocialService.INSTAGRAM) ||
          this.containsServiceType(selectedServices, SocialService.LINKEDIN_COMPANY) ||
          this.containsServiceType(selectedServices, SocialService.LINKEDIN) ||
          this.containsServiceType(selectedServices, SocialService.GMB)
        ) {
          this.parsePostText(postText);
        }
      });
  }

  containsServiceType(services: PostableService[], type: SocialService): boolean {
    return (
      services.filter((svc: PostableService) => {
        if (svc.serviceType === SocialService.LINKEDIN) {
          return SocialService.LINKEDIN_COMPANY === type;
        }
        return svc.serviceType === type;
      }).length > 0
    );
  }

  parseTweet(postText?: string): void {
    postText = postText || this.composerStore.rootCustomization.postText$$.getValue();
    const parsedTweet = twitterText.parseTweet(postText);
    this.composerStore.tweetLength$$.next(parsedTweet.weightedLength);
    this.composerStore.tweetValid$$.next(parsedTweet.valid);
  }

  parseHashtags(postText?: string): void {
    postText = postText || this.composerStore.rootCustomization.postText$$.getValue();
    const hashtags = postText.match(HASHTAG_REGEX);
    const hashtagCount = hashtags ? hashtags.length : 0;
    this.composerStore.hashtagCount$$.next(hashtagCount);
    const valid = hashtagCount === 0 || hashtagCount <= CONSTANTS.SERVICE_MAX_HASHTAGS.IG_USER;
    this.composerStore.igHashtagCountValid$$.next(valid);
  }

  parsePostText(postText?: string): void {
    const postTextParsed = postText || this.composerStore.rootCustomization.postText$$.getValue();
    this.composerStore.postLength$$.next(postTextParsed.length);
    const liTextValid =
      postTextParsed.length <= CONSTANTS.SERVICE_MAX_CHAR_COUNT.LINKEDIN_CHARS_LIMIT && !!postTextParsed?.length;
    const gmbTextValid =
      postTextParsed.length <= CONSTANTS.SERVICE_MAX_CHAR_COUNT.GMB_CHARS_LIMIT && !!postTextParsed?.length;
    const igTextValid =
      postTextParsed.length <= CONSTANTS.SERVICE_MAX_CHAR_COUNT.INSTAGRAM_CHARS_LIMIT && !!postTextParsed?.length;
    const tiktokTextValid =
      postTextParsed.length <= CONSTANTS.SERVICE_MAX_CHAR_COUNT.TIKTOK_CHAR_LIMIT && !!postTextParsed?.length;

    this.composerStore.linkedInValid$$.next(liTextValid);
    this.composerStore.gmbValid$$.next(gmbTextValid);
    this.composerStore.instagramValid$$.next(igTextValid);
    this.composerStore.tiktokValid$$.next(tiktokTextValid);
  }

  customizeBackEvent(event: boolean) {
    if (event) {
      this.showBackBtn = event;
    }
  }

  goBack() {
    this.showResetCustomizationPrompt();
  }

  getWorkFlowTypeText(): string {
    const workFlowType = this.composerStore.workFlowType$$.getValue(); // Assuming workFlowType$ is a BehaviorSubject
    if (workFlowType === WorkflowType.STORY_WORKFLOW) {
      return 'COMPOSER.WORKFLOW_TYPE.CREATE_STORY';
    } else if (workFlowType === WorkflowType.LONG_VIDEO_WORKFLOW) {
      return 'COMPOSER.WORKFLOW_TYPE.CREATE_LONG_VIDEO';
    } else {
      return 'COMPOSER.WORKFLOW_TYPE.CREATE_POST';
    }
  }

  private showResetCustomizationPrompt() {
    this.confirmationModal
      .openModal({
        type: 'confirm',
        title: this.translateService.instant('COMPOSER.CUSTOMIZE_MODAL.TITLE'),
        message: this.translateService.instant('COMPOSER.CUSTOMIZE_MODAL.MESSAGE'),
        hideCancel: false,
        confirmButtonText: this.translateService.instant('COMPOSER.CUSTOMIZE_MODAL.GO_BACK'),
        cancelButtonText: this.translateService.instant('COMPOSER.CUSTOMIZE_MODAL.KEEP_EDITING'),
        actionOnEnterKey: false,
        cancelOnEscapeKeyOrBackgroundClick: true,
      })
      .subscribe((response: boolean) => {
        if (response) {
          this.showBackBtn = false;
          this.composerStore.stopCustomizingByAccount();
        }
      });
  }

  trackWorkflowEvent() {
    if (this.composerStore.workFlowType$$.getValue() === WorkflowType.STORY_WORKFLOW) {
      this.productAnalyticsService.trackEvent(POSTHOG_KEYS.COMPOSER_STORY_WORKFLOW, 'user', 'click');
      return;
    } else if (this.composerStore.workFlowType$$.getValue() === WorkflowType.LONG_VIDEO_WORKFLOW) {
      this.productAnalyticsService.trackEvent(POSTHOG_KEYS.COMPOSER_LONG_VIDEO_WORKFLOW, 'user', 'click');
      return;
    }
    this.productAnalyticsService.trackEvent(POSTHOG_KEYS.COMPOSER_POST_WORKFLOW, 'user', 'click');
  }

  setPostText(content: string): void {
    if (!content) return;

    this.composerStore.updatePostText(content);
    this.ref.detectChanges();
  }

  protected readonly DateUsageModes = DateUsageModes;
}
