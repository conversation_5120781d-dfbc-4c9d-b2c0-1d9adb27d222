import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, inject, NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { HammerGestureConfig } from '@angular/platform-browser';
import { RouterModule } from '@angular/router';

import { UIKitModule, VaProductNavService } from '@vendasta/uikit';

import { TranslateModule } from '@ngx-translate/core';
import { NgxChartsModule } from '@swimlane/ngx-charts';

import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatTimepicker, MatTimepickerModule, MatTimepickerToggle } from '@angular/material/timepicker';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GALAXY_PAGE_OPTIONS, GalaxyPageModule, GalaxyPageOptions } from '@vendasta/galaxy/page';
import { GalaxyUploaderModule } from '@vendasta/galaxy/uploader';
import { CoreSharedModule } from '@vendasta/shared';
import { ComposerModule } from '../composer/composer.module';
import { CreatePostsSplitFlowBannerComponent } from '../pages/overview/compose-banner/split-flow-banner/create-posts-split-flow-banner.component';
import { SplitFlowButtonComponent } from '../pages/overview/compose-banner/split-flow-banner/split-flow-button/splitFlowButton.component';
import { ActiveOrdersService } from './active-orders/active-orders.service';
import { AppPageComponent } from './app-page/app-page.component';
import { BrokenTokenComponent } from './broken-token/broken-token.component';
import { BrokenTokenService } from './broken-token/broken-token.service';
import { CarouselComponent } from './carousel/carousel.component';
import { WistiaVideoComponent } from './carousel/wistia-video/wistia-video.component';
import { ChartComponent } from './chart/chart.component';
import { AutoscheduledContentCardComponent } from './content-card/autoscheduled-content-card.component';
import { ContentCardComponent } from './content-card/content-card.component';
import { AccountDebuggerComponent } from './debug/account-debug.component';
import { DebugService } from './debug/debug.service';
import { DialogsModule } from './dialogs/dialogs.module';
import { GlobalWarningsService } from './global-warnings/global-warnings.service';
import { GoogleTagManagerService } from './google-tag-manager/google-tag-manager.service';
import { GmbPostStatsService } from './location-analytics/gmb-post-stats.service';
import { SingleLocationService } from './location-analytics/single-location.service';
import { MarketingMaterialComponent } from './marketing-material/marketing-material.component';
import { CurrencyService } from './marketing-services/currency.service';
import { GetProService } from './marketing-services/get-pro.service';
import { NoIGAccounConnectedComponent } from './no-ig-account-connected/no-ig-account-connected.component';
import { OverlayOriginDirective } from './overlay/overlay-origin.directive';
import { OverlayComponent } from './overlay/overlay.component';
import { PipesModule } from './pipes/pipes.module';
import { SocialPostModule } from './post';
import { PostsTogglerComponent } from './posts-toggle/posts-toggler.component';
import { ProductNavbarComponent } from './product-navbar/product-navbar.component';
import { ReconnectFacebookBannerComponent } from './reconnect-facebook-banner/reconnect-facebook-banner.component';
import { ReconnectInstagramBannerComponent } from './reconnect-instagram-banner/reconnect-instagram-banner.component';
import { RepcoreApiService } from './repcore-api/repcore-api.service';
import { AiSettingButtonComponent } from './settings-button/ai-setting-button.component';
import { SnackBarService } from './snack-bar/snack-bar.service';
import { SocialProfileService } from './social-profile/social-profile.service';
import { SocialServiceNamesService } from './social-service/social-service-names.service';
import { SocialServiceService } from './social-service/social-service.service';
import { StickyFooterComponent } from './sticky-footer/sticky-footer.component';
import { ContentSearchesInfoDialogComponent } from './twitter-searches/dialogs/content-searches-info.component';
import { LeadsSearchesInfoDialogComponent } from './twitter-searches/dialogs/leads-searches-info.component';
import { TwitterSearchTooGeneralDialogComponent } from './twitter-searches/dialogs/twitter-search-too-general.component';
import { TweetComponent } from './twitter-searches/tweet.component';
import { TwitterSearchComponent } from './twitter-searches/twitter-search.component';
import { TwitterSearchesComponent } from './twitter-searches/twitter-searches.component';
import { TwitterSearchesService } from './twitter-searches/twitter-searches.service';
import { UpgradeCTADialogService } from './upgrade-cta-dialog/upgrade-cta-dialog.service';
import { ImageModule } from './va-image/image.module';
import { WhitelabelToolbarComponent } from './whitelabel-toolbar/whitelabel-toolbar.component';

@NgModule({
  providers: [
    {
      provide: GALAXY_PAGE_OPTIONS,
      useFactory: galaxySMNavOptionsFactory,
      deps: [VaProductNavService],
    },
    SnackBarService,
    SocialProfileService,
    SocialServiceService,
    SocialServiceNamesService,
    GlobalWarningsService,
    TwitterSearchesService,
    GoogleTagManagerService,
    HammerGestureConfig,
    DebugService,
    UpgradeCTADialogService,
    GetProService,
    CurrencyService,
    ActiveOrdersService,
    RepcoreApiService,
    SingleLocationService,
    GmbPostStatsService,
    BrokenTokenService,
  ],
  declarations: [
    OverlayComponent,
    OverlayOriginDirective,
    CarouselComponent,
    OverlayOriginDirective,
    BrokenTokenComponent,
    NoIGAccounConnectedComponent,
    ChartComponent,
    AppPageComponent,
    TwitterSearchesComponent,
    TwitterSearchComponent,
    TweetComponent,
    TwitterSearchTooGeneralDialogComponent,
    ContentSearchesInfoDialogComponent,
    LeadsSearchesInfoDialogComponent,
    WistiaVideoComponent,
    WhitelabelToolbarComponent,
    AccountDebuggerComponent,
    ProductNavbarComponent,
    MarketingMaterialComponent,
    ContentCardComponent,
    AutoscheduledContentCardComponent,
  ],
  imports: [
    CommonModule,
    ComposerModule,
    MatSidenavModule,
    OverlayModule,
    UIKitModule,
    NgxChartsModule,
    DialogsModule,
    PipesModule,
    FormsModule,
    MatIconModule,
    MatCardModule,
    MatButtonModule,
    MatDialogModule,
    MatTooltipModule,
    MatExpansionModule,
    MatListModule,
    MatFormFieldModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatChipsModule,
    MatToolbarModule,
    MatInputModule,
    GalaxyAlertModule,
    GalaxyFormFieldModule,
    GalaxyUploaderModule,
    TranslateModule,
    RouterModule,
    CoreSharedModule,
    MatDatepickerModule,
    MatTimepicker,
    MatTimepickerToggle,
    MatTimepickerModule,
    ReconnectInstagramBannerComponent,
    ReconnectFacebookBannerComponent,
    GalaxyPageModule,
    CreatePostsSplitFlowBannerComponent,
    SplitFlowButtonComponent,
    StickyFooterComponent,
    AiSettingButtonComponent,
    PostsTogglerComponent,
  ],
  exports: [
    SocialPostModule,
    OverlayComponent,
    OverlayOriginDirective,
    PipesModule,
    CarouselComponent,
    PipesModule,
    BrokenTokenComponent,
    NoIGAccounConnectedComponent,
    ChartComponent,
    DialogsModule,
    GalaxyAlertModule,
    GalaxyFormFieldModule,
    GalaxyUploaderModule,
    AppPageComponent,
    ProductNavbarComponent,
    TwitterSearchesComponent,
    WhitelabelToolbarComponent,
    MarketingMaterialComponent,
    ImageModule,
    ContentCardComponent,
    AutoscheduledContentCardComponent,
    StickyFooterComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SharedModule {}

export function galaxySMNavOptionsFactory(): GalaxyPageOptions {
  const navControlService = inject(VaProductNavService);
  return {
    showToggle: true,
    toggleNav: () => navControlService.toggleNav(),
  };
}
