import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { FileInfo, FileUploadError, FileUploadStatus, GalaxyUploaderModule } from '@vendasta/galaxy/uploader';
import { CsvParserService } from '../csv-parser.service';
import { MatDialog, MatDialogContent, MatDialogTitle } from '@angular/material/dialog';
import { CsvErrorsContainerComponent } from '../csv-errors-container/csv-errors-container.component';
import { ValidationService } from '../csv-validation.service';
import { Subscription } from 'rxjs';
import { CsvParserStore } from '../csv-parser.store';
import { CommonModule } from '@angular/common';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { TranslateModule } from '@ngx-translate/core';
import { POSTHOG_KEYS } from '../../../composer/constants';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { SocialService } from '../../../composer/post';

@Component({
  selector: 'app-csv-upload',
  standalone: true,
  imports: [CommonModule, GalaxyUploaderModule, GalaxyAlertModule, MatDialogContent, MatDialogTitle, TranslateModule],
  templateUrl: './csv-upload.component.html',
  styleUrl: './csv-upload.component.scss',
})
export class CsvUploadComponent implements OnInit, OnDestroy {
  @Input() selectedSocialService: SocialService;

  validationErrors = [];
  files: FileInfo[] = [];
  private subscriptions: Subscription = new Subscription();
  @Input() brandId: string;

  constructor(
    private snack: SnackbarService,
    private csvParserService: CsvParserService,
    public modal: MatDialog,
    private validationService: ValidationService,
    private csvParserStore: CsvParserStore,
    private productAnalyticsService: ProductAnalyticsService,
  ) {}

  get submitText(): string {
    return 'COMPOSER.UPLOAD_MEDIA.UPLOAD_FILE';
  }

  ngOnInit(): void {
    this.subscriptions.add(
      this.validationService.errors$.subscribe((errors) => {
        this.validationErrors = errors;
      }),
    );
  }

  private clearAllErrors(): void {
    this.validationService.clearErrors();
    this.csvParserStore.clear();
  }

  handleFileUploadError(event: FileUploadError): void {
    this.snack.errorSnack(event.error.message);
  }

  async onUpload(event: FileInfo[]): Promise<void> {
    // Clearing previous errors and posts as we are supporting only single file upload for now.
    // For multi file upload we may need to remove this.
    this.clearAllErrors();

    for (const file of event) {
      file.status = FileUploadStatus.InProgress;
      const hasError = await this.csvParserService.parseCSVFile(file?.file);
      if (hasError) {
        file.status = FileUploadStatus.Fail;
        this.productAnalyticsService.trackEvent(POSTHOG_KEYS.CSV_UPLOAD_FAILED, 'user', 'click', 0, {
          brandId: this.brandId,
        });
      } else {
        file.status = FileUploadStatus.Success;
        this.productAnalyticsService.trackEvent(POSTHOG_KEYS.CSV_UPLOAD_SUCCESS, 'user', 'click', 0, {
          brandId: this.brandId,
        });
      }
    }
  }

  onFilesChanged(files: FileInfo[]): void {
    this.files = files;
  }

  removeFile(fileInfo: FileInfo): void {
    this.clearAllErrors();
    const index = this.files.indexOf(fileInfo);
    this.files.splice(index, 1);
  }

  clearErrors(_: FileInfo[]): void {
    this.clearAllErrors();
  }

  showErrors() {
    const data = {
      errorsList: this.validationErrors.slice().sort((a, b) => a.lineNumber - b.lineNumber),
    };
    this.modal.open(CsvErrorsContainerComponent, {
      data: data,
      width: '660px',
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
}
