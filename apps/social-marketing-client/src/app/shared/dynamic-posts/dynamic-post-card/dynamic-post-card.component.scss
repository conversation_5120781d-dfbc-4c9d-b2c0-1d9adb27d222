@use 'design-tokens' as *;
@import '../../../core/breaks.scss';

.avatar-group {
  &-container {
    display: inline-flex;
    padding-right: 6px;
    margin-left: 12px;
  }
  &-avatar {
    position: relative;
    border: 2px solid $card-background-color;
    border-radius: 50%;
  }
  &-avatar:not(:last-child) {
    margin-left: -8px;
  }
}
:host ::ng-deep .post-card .mat-mdc-card-header {
  padding: 12px 16px !important;
  background-color: $glxy-grey-50;
}
:host ::ng-deep {
  .hide-tab-header mat-tab-header {
    display: none;
  }
}
:host ::ng-deep .post-card-workflow {
  display: flex;
  gap: 10px;
  mat-card-title {
    align-content: center !important;
  }
}

.menu-container {
  gap: 5px;
  margin-left: auto;
  display: flex;
  align-items: center;
}

mat-tab-group {
  position: relative;
  width: 100%;
  margin: 0px auto 20px;
  ::ng-deep {
    .mdc-tab {
      min-width: 50px;
      padding: 0px 4px;
      border: 1px $glxy-grey-300 solid;
      background-color: white;
      border-radius: 5px 5px 0 0;
    }
    mat-tab-header {
      position: unset;
    }
  }
}

.text-box {
  width: 100%;
}

.mat-mdc-card-avatar-list {
  display: flex;
  background-color: #cccccc;

  img {
    border: 1px solid #cccccc;
  }

  img.social-icon {
    position: relative;
    right: 12px;
    align-self: flex-end;
    border-radius: 16px;
    font-size: 8px;
    height: 16px;
    width: 16px;
    top: 9px;
  }
}

.editable-textarea {
  width: 100%;
  height: 100px; /* Adjust height as needed */
  padding: 10px;
  font-family: inherit;
  font-weight: inherit;
  line-height: inherit;
  resize: none;
  outline: none;
  border: none;
  background: transparent;
}
.text-see-more {
  color: $primary-color;
  cursor: pointer;
  padding-left: 10px;
}

.non-editable-text {
  display: block;
  width: 100%;
  min-height: 100px;
  padding: 10px;
  font-family: inherit;
  font-weight: inherit;
  line-height: inherit;
  cursor: pointer;
  word-break: break-word;
}

mat-card.post-card {
  max-width: 800px;
}

.post-content-box {
  margin-bottom: 16px;
}

mat-card-content {
  min-height: 120px;
}

mat-card-title {
  font-weight: 700;
}

glxy-badge {
  height: 24px;
}

mat-card-title {
  font-size: 14px;
}

.post-container {
  display: flex;
  word-break: break-word;
  gap: 25px;
}

.card-details {
  gap: 5px;
  display: grid;
}

app-post-card-content {
  flex: 2;
}

/* Responsive behavior */
@media (max-width: 768px) {
  :host ::ng-deep .mat-mdc-card-header {
    padding: 12px 4px 12px 16px !important;
  }
}

.card-action-icon {
  display: inline-block;
  position: relative;
  vertical-align: bottom;
  font-size: 20px;
  height: 20px;
  right: 1px;
}

.card-action-name {
  font-family: Roboto;
  font-size: 14px;
  line-height: 16px;
  text-align: center;
}

.card-action-btn {
  height: 36px;
  width: 78px;
  border: 1px solid $border-color;
  border-radius: 4px;
  background-color: $glxy-grey-50;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mat-mdc-card-subtitle {
  color: $glxy-grey-900 !important;
}

.edit-button:hover {
  background-color: $glxy-grey-100;
}

:host ::ng-deep .no-events {
  cursor: default !important;
  pointer-events: none !important;
}

:host ::ng-deep .post-card mat-card-header.mat-mdc-card-header mat-card-title {
  margin-bottom: 0px;
}

.border-below {
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.post-error {
  word-break: break-word;
  padding-top: 10px;
}

.multi-media {
  gap: 40px;
}

.card-view {
  width: 43%;
}

.mobile-card-view {
  width: 100%;
}

.box-card {
  flex-direction: column;
}

.box-card-image {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.box-card-image-section {
  display: block;
  width: 100%;
  max-width: 100%;
}

.ai-schedule-type {
  width: 100%;
}

.scheduler-card {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.radio-buttons {
  width: 100%;
}

.ai-schedule-kinds {
  font-family: 'Roboto', serif;
  font-weight: 500;
  font-size: smaller;
  padding: 8px;
  border-radius: 18px;
  height: 24px;
}

.ai-schedule-SCHEDULE {
  margin-left: $negative-2;
  color: $glxy-blue-700;
  background: $glxy-blue-50;
}

.ai-scheduler-card {
  padding: 8px 8px 8px 8px;
  gap: 8px;
  border: 1px;
  radius: 0px 0px 4px 4px;
}

.ai-schedule-DRAFT {
  color: #6d4c41;
  background: $warn-background-color;
  font-weight: 700;
}

.ai-schedule-HIDDEN_DRAFT {
  color: $secondary-text-color;
  background: #e0e0e0;
  width: 100%;
}

.ai-schedule-POST_NOW {
  color: $success-text-color;
  background: $success-background-color;
}

.ai-post-schedule-card {
  padding: 5px 12px 0 12px;
  border-bottom: none;
  border-right: none;
  border-left: none;
  border-radius: 4px;
}

.card-time-picker {
  display: flex;
  gap: 15px;
  mat-form-field {
    width: 276px;
  }
}

.tools-container {
  gap: 10px;
  display: flex;
  flex-direction: column;
}

.z-0 {
  z-index: 0;
}
.z-1 {
  z-index: 1;
}
.z-2 {
  z-index: 2;
}
.z-3 {
  z-index: 3;
}

$size: 24px;

.mat-mdc-card-avatar {
  height: $size !important;
  width: $size !important;
  margin-bottom: 5px !important;
  position: relative !important;
  margin-left: -5px !important;
  background: transparent;
}

:host ::ng-deep .mat-mdc-tab .mdc-tab__content {
  height: 44px !important;
}
