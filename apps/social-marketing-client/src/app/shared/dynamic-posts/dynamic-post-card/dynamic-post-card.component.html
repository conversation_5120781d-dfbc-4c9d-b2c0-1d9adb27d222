<div *ngIf="postContent.length > 0">
  <div *ngIf="isMLPost; else tabView">
    <ng-container *ngTemplateOutlet="singleView"></ng-container>
  </div>
  <ng-template #tabView>
    <mat-tab-group [(selectedIndex)]="selectedIndex" (selectedIndexChange)="onTabChange($event)">
      <mat-tab *ngFor="let post of postContent; let i = index">
        <ng-container>
          <ng-template matTabLabel>
            <div class="avatar-group-container">
              <app-avatar class="avatar-group-avatar" [service]="post.socialConnection"> </app-avatar>
            </div>
          </ng-template>
        </ng-container>

        <mat-card appearance="outlined" class="post-card">
          <mat-card-header class="card-header">
            <div class="card-details">
              <div class="post-card-workflow">
                <mat-card-title>{{ 'POSTS_CAMPAIGN.SOCIAL_POST' | translate }}</mat-card-title>
              </div>
            </div>

            <div class="menu-container">
              <button
                mat-stroked-button
                class="card-action-btn"
                attr.data-action="clicked-delete-post"
                (click)="showDeleteConfirmationDialog(parentIndex)"
              >
                {{ 'POSTS.CTA.DELETE' | translate }}
              </button>

              <button mat-stroked-button class="card-action-btn" (click)="editPosts(post, i)">
                <span class="card-action-name">
                  {{ 'POSTS.CTA.EDIT' | translate }}
                </span>
              </button>
            </div>
          </mat-card-header>

          <mat-card-content class="post-content-box">
            <div class="post-container">
              @if (post?.Medias?.length) {
                <app-dynamic-card-image-holder
                  *ngIf="post?.Medias"
                  [mediaEntries]="post?.Medias"
                ></app-dynamic-card-image-holder>
              }
              <div class="text-box">
                <span class="non-editable-text">
                  {{
                    isExpanded
                      ? post?.postText
                      : (post?.postText | slice: 0 : MAX_CONTENT_LENGTH) +
                        (post?.postText.length > MAX_CONTENT_LENGTH ? '...' : '')
                  }}
                </span>
                <span
                  *ngIf="post?.postText.length > MAX_CONTENT_LENGTH"
                  class="text-see-more"
                  (click)="isExpanded = !isExpanded"
                >
                  <ng-container *ngIf="isExpanded; else seeMore">
                    <span>{{ 'POSTS_CAMPAIGN.SEE_LESS' | translate }}</span>
                  </ng-container>
                  <ng-template #seeMore>
                    <span>{{ 'POSTS_CAMPAIGN.SEE_MORE' | translate }}</span>
                  </ng-template>
                </span>
              </div>
            </div>
          </mat-card-content>
          <mat-card class="ai-post-schedule-card">
            <div class="tools-container">
              <div class="scheduler-card">
                <div class="save-row-container">
                  <mat-radio-group [(ngModel)]="post.postMode" class="ai-schedule-type">
                    <ng-container *ngFor="let scheduleType of postStatusOptions">
                      <mat-radio-button [value]="scheduleType.value" class="radio-buttons">
                        <span [ngClass]="'ai-schedule-' + scheduleType.key" [className]="'ai-schedule-kinds'">
                          {{ scheduleType?.value }}
                        </span>
                      </mat-radio-button>
                    </ng-container>
                  </mat-radio-group>
                </div>
              </div>
              <div class="card-time-picker">
                <mat-form-field>
                  <mat-label>{{ 'POSTS_CAMPAIGN.DATE' | translate }}</mat-label>
                  <input matInput [matDatepicker]="datepicker" [min]="now()" [(ngModel)]="post.scheduleDate" />
                  <mat-datepicker #datepicker />
                  <mat-datepicker-toggle [for]="datepicker" matSuffix />
                </mat-form-field>

                <mat-form-field>
                  <mat-label>{{ 'POSTS_CAMPAIGN.TIME' | translate }}</mat-label>
                  <input
                    matInput
                    [min]="now()"
                    [matTimepicker]="timepicker"
                    [(ngModel)]="post.scheduleDate"
                    [ngModelOptions]="{ updateOn: 'blur' }"
                    (ngModelChange)="validateDateTime(i)"
                  />
                  <mat-timepicker #timepicker />
                  <mat-timepicker-toggle [for]="timepicker" matSuffix />
                </mat-form-field>
              </div>
            </div>
          </mat-card>
        </mat-card>
      </mat-tab>
    </mat-tab-group>
  </ng-template>
  <ng-template #singleView>
    <mat-tab-group *ngFor="let post of postContent; let i = index">
      <mat-tab>
        <ng-container>
          <ng-template matTabLabel>
            <div class="avatar-group-container">
              @for (network of post?.mlSocialNetworkType; track network) {
                <div mat-card-avatar class="mat-mdc-card-avatar-list">
                  @switch (network) {
                    <!--Show FACEBOOK icons-->
                    @case (networkType.FACEBOOK) {
                      <img mat-card-avatar [src]="defaultProfileImageUrl" class="z-3" />
                      <img class="social-icon z-3" [src]="fbIcon" />
                    }
                    <!--Show Instagram icons-->
                    @case (networkType.INSTAGRAM) {
                      <img mat-card-avatar [src]="defaultProfileImageUrl" class="z-2" />
                      <img class="social-icon z-2" [src]="igIcon" />
                    }
                    <!--Show LinkedIn icons-->
                    @case (networkType.LINKEDIN) {
                      <img mat-card-avatar [src]="defaultProfileImageUrl" class="z-1" />
                      <img class="social-icon z-1" [src]="liIcon" />
                    }
                    <!--Show GMB icons-->
                    @case (networkType.GMB) {
                      <img mat-card-avatar [src]="defaultProfileImageUrl" class="z-0" />
                      <img class="social-icon z-0" [src]="gmbIcon" />
                    }
                  }
                </div>
              }
            </div>
          </ng-template>
        </ng-container>

        <mat-card appearance="outlined" class="post-card">
          <mat-card-header class="card-header">
            <div class="card-details">
              <div class="post-card-workflow">
                <mat-card-title>{{ 'POSTS_CAMPAIGN.SOCIAL_POST' | translate }}</mat-card-title>
              </div>
            </div>

            <div class="menu-container">
              <button
                mat-stroked-button
                class="card-action-btn"
                attr.data-action="clicked-delete-post"
                (click)="showDeleteConfirmationDialog(i)"
              >
                {{ 'POSTS.CTA.DELETE' | translate }}
              </button>

              <button mat-stroked-button class="card-action-btn" (click)="editPosts(post, i)">
                <span class="card-action-name">
                  {{ 'POSTS.CTA.EDIT' | translate }}
                </span>
              </button>
            </div>
          </mat-card-header>

          <mat-card-content class="post-content-box">
            <div class="post-container">
              @if (post?.Medias?.length) {
                <app-dynamic-card-image-holder
                  *ngIf="post?.Medias"
                  [mediaEntries]="post?.Medias"
                ></app-dynamic-card-image-holder>
              }
              <div class="text-box">
                <span class="non-editable-text">
                  {{
                    isExpanded
                      ? post?.postText
                      : (post?.postText | slice: 0 : MAX_CONTENT_LENGTH) +
                        (post?.postText.length > MAX_CONTENT_LENGTH ? '...' : '')
                  }}
                </span>
                <span
                  *ngIf="post?.postText.length > MAX_CONTENT_LENGTH"
                  class="text-see-more"
                  (click)="isExpanded = !isExpanded"
                >
                  <ng-container *ngIf="isExpanded; else seeMore">
                    <span>{{ 'POSTS_CAMPAIGN.SEE_LESS' | translate }}</span>
                  </ng-container>
                  <ng-template #seeMore>
                    <span>{{ 'POSTS_CAMPAIGN.SEE_MORE' | translate }}</span>
                  </ng-template>
                </span>
              </div>
            </div>
          </mat-card-content>
          <mat-card class="ai-post-schedule-card">
            <div class="tools-container">
              <div class="scheduler-card">
                <div class="save-row-container">
                  <mat-radio-group [(ngModel)]="selectedOption" class="ai-schedule-type">
                    <ng-container *ngFor="let scheduleType of postStatusOptions; index as i">
                      <mat-radio-button [value]="scheduleType.value" class="radio-buttons">
                        <span [ngClass]="'ai-schedule-' + scheduleType.key" [className]="'ai-schedule-kinds'">
                          {{ scheduleType?.value }}
                        </span>
                      </mat-radio-button>
                    </ng-container>
                  </mat-radio-group>
                </div>
              </div>
              <div class="card-time-picker">
                <mat-form-field>
                  <mat-label>{{ 'POSTS_CAMPAIGN.DATE' | translate }}</mat-label>
                  <input
                    matInput
                    [matDatepicker]="datepicker"
                    [min]="now()"
                    [(ngModel)]="post.scheduleDate"
                    (dateChange)="validateDateTime(i)"
                  />
                  <mat-datepicker #datepicker />
                  <mat-datepicker-toggle [for]="datepicker" matSuffix />
                </mat-form-field>

                <mat-form-field>
                  <mat-label>{{ 'POSTS_CAMPAIGN.TIME' | translate }}</mat-label>
                  <input
                    matInput
                    [min]="now()"
                    [matTimepicker]="timepicker"
                    [(ngModel)]="post.scheduleDate"
                    [ngModelOptions]="{ updateOn: 'blur' }"
                    (ngModelChange)="validateDateTime(i)"
                  />
                  <mat-timepicker #timepicker />
                  <mat-timepicker-toggle [for]="timepicker" matSuffix />
                </mat-form-field>
              </div>
            </div>
          </mat-card>
        </mat-card>
      </mat-tab>
    </mat-tab-group>
  </ng-template>
</div>
