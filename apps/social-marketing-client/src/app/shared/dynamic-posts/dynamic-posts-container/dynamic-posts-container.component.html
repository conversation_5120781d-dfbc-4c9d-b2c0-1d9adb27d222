<div class="optimized-container">
  <div class="campaign-cards-container">
    <div class="title-container">
      <div class="editable-container" *ngIf="isEditing">
        <input
          type="text"
          [(ngModel)]="generatedCampaignTitle"
          class="editable-input"
          [ngClass]="{ 'input-error': !isTitleValid }"
          required
          (blur)="validateTitle()"
        />

        <button mat-icon-button (click)="toggleEdit()" [disabled]="!generatedCampaignTitle?.trim()">
          <mat-icon>done</mat-icon>
        </button>
      </div>

      <div class="error-text" *ngIf="!isTitleValid">
        {{ 'POSTS_CAMPAIGN_VALIDATION.POSTS_CAMPAIGN_NAME_REQUIRED' | translate }}
      </div>

      <div class="non-edit-title" *ngIf="!isEditing">
        <h2>{{ generatedCampaignTitle }}</h2>
        <button mat-icon-button (click)="toggleEdit()">
          <mat-icon>edit</mat-icon>
        </button>
      </div>
    </div>

    <div>
      @for (post of posts; track post; let parentIndex = $index) {
        <app-dynamic-post-card
          [parentIndex]="parentIndex"
          [postContent]="post.postContent"
          [socialNetworks]="services"
          (postDeleted)="postDeleted($event)"
        ></app-dynamic-post-card>
      }
    </div>
    @if (posts?.length <= maxPost) {
      <div class="add-post-box">
        <button
          mat-flat-button
          class="add-another-button"
          (click)="addPost()"
          mat-button
          color="primary"
          [disabled]="isContentGenerating"
        >
          <glxy-button-loading-indicator [isLoading]="isContentGenerating">
            <span class="button-content">
              <mat-icon class="add-another-icon">add</mat-icon> {{ 'POSTS_CAMPAIGN.ADD_POST' | translate }}
            </span>
          </glxy-button-loading-indicator>
        </button>
      </div>
    }
  </div>
  @if (posts?.length !== 0) {
    <mat-card class="action-toolbar">
      <div class="button-container">
        <button
          mat-flat-button
          color="primary"
          [disabled]="!isTitleValid || isContentGenerating || showSpinner"
          (click)="saveCampaign()"
        >
          <span *ngIf="!isContentGenerating && !showSpinner">
            {{ 'POSTS_CAMPAIGN.SAVE' | translate }}
          </span>
          <mat-spinner *ngIf="showSpinner" [diameter]="24" strokeWidth="3"></mat-spinner>
        </button>
      </div>
    </mat-card>
  }
</div>
