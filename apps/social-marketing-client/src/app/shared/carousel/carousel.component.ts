import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { combineLatest, Observable, Subscription } from 'rxjs';
import { WistiaVideoComponent } from './wistia-video/wistia-video.component';
import { HammerGestureConfig } from '@angular/platform-browser';
import { map } from 'rxjs/operators';
import { ConfigService } from '../../core';
import { ComposerSettingsService } from '../../core/composer-settings/composer-settings.service';
import { SMFeaturesService } from '../../core/features.service';

export const SlideIds = {
  welcome: 'welcome',
  leads: 'leads',
  customerPosts: 'customerPosts',
  draftPosts: 'draftPosts',
  templatePosts: 'templatePosts',
  interestingContent: 'interestingContent',
  analytics: 'analytics',
  compose: 'compose',
  getStarted: 'getStarted',
  leadsCreate: 'leadsCreate',
  leadsRespond: 'leadsRespond',
  newUser: 'newUser',
  clickableBio: 'clickableBio',
  recentPosts: 'recentPosts',
};

class CarouselInfo {
  title: string;
  text: string;
  imageUrl: string;
  videoId: string;
  buttonText: string;
  slideId: string;
  isPhoneGif: boolean;

  constructor(
    title: string,
    text: string,
    imageUrl: string,
    buttonText: string,
    videoId: string,
    slideId: string,
    isPhoneGif?: boolean,
  ) {
    this.title = title;
    this.text = text;
    this.imageUrl = imageUrl;
    this.buttonText = buttonText;
    this.videoId = videoId;
    this.slideId = slideId;
    this.isPhoneGif = isPhoneGif;
  }
}

export interface CarouselConfig {
  slideIdList: string[];
  shouldShowAction: boolean;
  actionText?: string;
  actionUrl?: string;
  hasAccountConnected: boolean;
}

interface UpdatedAiEmptyState {
  imageUrl: string;
  title: string;
  subtext: string;
}

@Component({
  selector: 'app-info-carousel',
  templateUrl: './carousel.component.html',
  styleUrls: ['./carousel.component.scss'],
  standalone: false,
})
export class CarouselComponent implements OnInit, OnDestroy {
  carouselList = [
    new CarouselInfo(
      'COMMON.CAROUSEL.WELCOME.TITLE',
      'COMMON.CAROUSEL.WELCOME.TEXT',
      'https://vstatic-prod.apigateway.co/social-marketing-client/assets/onboarding-slides/post-anywhere.png',
      null,
      null,
      SlideIds.welcome,
    ),
    new CarouselInfo(
      'COMMON.CAROUSEL.LEADS.TITLE',
      'COMMON.CAROUSEL.LEADS.TEXT',
      'https://vstatic-prod.apigateway.co/social-marketing-client/assets/onboarding-carousel/Generate-social-leads.png',
      'COMMON.CAROUSEL.SEE_HOW_IT_WORKS',
      '3uhmvg4crr',
      SlideIds.leads,
    ),
    new CarouselInfo(
      'COMMON.CAROUSEL.COMMENTS.TITLE',
      'COMMON.CAROUSEL.COMMENTS.TEXT',
      'https://vstatic-prod.apigateway.co/social-marketing-client/assets/onboarding-carousel/Desktop-Customer-Posts.png',
      'COMMON.CAROUSEL.SEE_HOW_IT_WORKS',
      'djfl9m16w2',
      SlideIds.customerPosts,
    ),
    new CarouselInfo(
      'COMMON.CAROUSEL.INTERESTING_CONTENT.TITLE',
      'COMMON.CAROUSEL.INTERESTING_CONTENT.TEXT',
      'https://vstatic-prod.apigateway.co/social-marketing-client/assets/onboarding-carousel/Mobile-Content.png',
      'COMMON.CAROUSEL.SEE_HOW_IT_WORKS',
      'ti4r4u8vwv',
      SlideIds.interestingContent,
    ),
    new CarouselInfo(
      'COMMON.CAROUSEL.ANALYTICS.TITLE',
      'COMMON.CAROUSEL.ANALYTICS.TEXT',
      'https://vstatic-prod.apigateway.co/social-marketing-client/assets/onboarding-carousel/Desktop-Analytics.png',
      'COMMON.CAROUSEL.SEE_HOW_IT_WORKS',
      'j3lmykzzls',
      SlideIds.analytics,
    ),
    new CarouselInfo(
      'COMMON.CAROUSEL.COMPOSE.TITLE',
      'COMMON.CAROUSEL.COMPOSE.TEXT',
      'https://vstatic-prod.apigateway.co/social-marketing-client/assets/onboarding-carousel/Mobile-Composer.png',
      'COMMON.CAROUSEL.COMPOSE_A_POST',
      'open composer',
      SlideIds.compose,
    ),
    new CarouselInfo(
      'COMMON.CAROUSEL.WELCOME.TITLE',
      'COMMON.CAROUSEL.WELCOME.TEXT',
      'https://vstatic-prod.apigateway.co/social-marketing-client/assets/onboarding-slides/post-anywhere.png',
      null,
      null,
      SlideIds.newUser,
    ),
    new CarouselInfo(
      'COMMON.CAROUSEL.GET_STARTED.TITLE',
      'COMMON.CAROUSEL.GET_STARTED.TEXT',
      'https://vstatic-prod.apigateway.co/social-marketing-client/assets/onboarding-carousel/social-marketing-press-release.png',
      'COMMON.CAROUSEL.SEE_HOW_IT_WORKS',
      'yhwgunks40',
      SlideIds.getStarted,
    ),
    new CarouselInfo(
      'COMMON.CAROUSEL.LEADS_CREATE.TITLE',
      'COMMON.CAROUSEL.LEADS_CREATE.TEXT',
      'https://vstatic-prod.apigateway.co/social-marketing-client/assets/onboarding-carousel/Generate-social-leads.png',
      null,
      null,
      SlideIds.leadsCreate,
    ),
    new CarouselInfo(
      'COMMON.CAROUSEL.LEADS_RESPOND.TITLE',
      'COMMON.CAROUSEL.LEADS_RESPOND.TEXT',
      'https://vstatic-prod.apigateway.co/social-marketing-client/assets/onboarding-carousel/Generate-social-leads.png',
      null,
      null,
      SlideIds.leadsRespond,
    ),
    new CarouselInfo(
      'COMMON.CAROUSEL.DRAFT_POSTS.TITLE',
      'COMMON.CAROUSEL.DRAFT_POSTS.TEXT',
      'https://vstatic-prod.apigateway.co/social-marketing-client/assets/onboarding-carousel/Mobile-Composer.png',
      'COMMON.CAROUSEL.COMPOSE_A_POST',
      'open composer',
      SlideIds.draftPosts,
    ),
    new CarouselInfo(
      'COMMON.CAROUSEL.TEMPLATES.TITLE',
      'COMMON.CAROUSEL.TEMPLATES.TEXT',
      'https://vstatic-prod.apigateway.co/social-marketing-client/assets/onboarding-carousel/Mobile-Composer.png',
      'COMMON.CAROUSEL.COMPOSE_A_POST',
      'open composer',
      SlideIds.templatePosts,
    ),
    new CarouselInfo(
      'COMMON.CAROUSEL.CLICKABLE_BIO.TITLE',
      'COMMON.CAROUSEL.CLICKABLE_BIO.TEXT',
      'https://vstatic-prod.apigateway.co/social-marketing-client/assets/link-directory-page/step-walkthrough.gif',
      null,
      null,
      SlideIds.clickableBio,
      true,
    ),
    new CarouselInfo(
      'COMMON.CAROUSEL.COMPOSE.TITLE',
      'COMMON.CAROUSEL.COMPOSE.TEXT',
      'https://vstatic-prod.apigateway.co/social-marketing-client/assets/onboarding-carousel/Mobile-Composer.png',
      'COMMON.CAROUSEL.COMPOSE_A_POST',
      'open composer',
      SlideIds.recentPosts,
    ),
  ];

  title: string;
  imageUrl: string;
  text: string;
  videoId: string;
  isPhoneGif: boolean;
  buttonText$: Observable<string>;
  sliceIndex = 0;
  rightButtonAction$: Observable<string> = new Observable<string>();
  productName$: Observable<string>;

  @Input() carouselConfig: CarouselConfig;
  @Output() closeDialog = new EventEmitter();

  slideIdList: string[];
  shouldShowAction: boolean;
  actionText: string;
  actionUrl: string;
  hasAccountConnected: boolean;
  noUpgardePath: boolean;

  emptyState: UpdatedAiEmptyState;

  displayUpdatedAiEmptyState$: Observable<boolean>;

  private _subscriptions: Subscription[] = [];

  constructor(
    private translateService: TranslateService,
    private dialogService: MatDialog,
    private configService: ConfigService,
    private router: Router,
    private hammerConfig: HammerGestureConfig,
    private composerSettingsService: ComposerSettingsService,
    private featuresService: SMFeaturesService,
  ) {}

  ngOnInit(): void {
    this.productName$ = this.configService.config$.pipe(map((config) => config.whitelabel_info.product_name));

    this.slideIdList = this.carouselConfig.slideIdList;
    this.shouldShowAction = this.carouselConfig.shouldShowAction;
    this.actionText = this.carouselConfig.actionText;
    this.actionUrl = this.carouselConfig.actionUrl;
    this.hasAccountConnected = this.carouselConfig.hasAccountConnected;

    this.displayUpdatedAiEmptyState$ = combineLatest([
      this.configService.proFlag$,
      this.featuresService.aiBundleCreate$,
    ]).pipe(
      map(([proFlag, aiBundleCreate]) => {
        return (
          proFlag &&
          (this.slideIdList.includes(SlideIds.compose) ||
            this.slideIdList.includes(SlideIds.draftPosts) ||
            this.slideIdList.includes(SlideIds.recentPosts)) &&
          aiBundleCreate
        );
      }),
    );

    this._subscriptions.push(
      this.displayUpdatedAiEmptyState$.pipe().subscribe(() => {
        if (this.slideIdList.includes(SlideIds.compose)) {
          // case of scheduled
          this.emptyState = {
            title: this.translateService.instant('COMMON.CAROUSEL.EMPTY_AI_STATE.SCHEDULE.SCHEDULE_TITLE'),
            subtext: this.translateService.instant('COMMON.CAROUSEL.EMPTY_AI_STATE.SCHEDULE.SCHEDULE_SUBTEXT'),
            imageUrl:
              'https://vstatic-prod.apigateway.co/social-marketing-client/assets/onboarding-carousel/EmptyCalendar.png',
          };
        }
        if (this.slideIdList.includes(SlideIds.draftPosts)) {
          // case of draft
          this.emptyState = {
            title: this.translateService.instant('COMMON.CAROUSEL.EMPTY_AI_STATE.DRAFTS.DRAFTS_TITLE'),
            subtext: this.translateService.instant('COMMON.CAROUSEL.EMPTY_AI_STATE.DRAFTS.DRAFTS_SUBTEXT'),
            imageUrl: 'https://vstatic-prod.apigateway.co/social-marketing-client/assets/onboarding-carousel/Post.png',
          };
        }

        if (this.slideIdList.includes(SlideIds.recentPosts)) {
          // case of recent
          this.emptyState = {
            title: this.translateService.instant('COMMON.CAROUSEL.EMPTY_AI_STATE.RECENT.RECENT_TITLE'),
            subtext: this.translateService.instant('COMMON.CAROUSEL.EMPTY_AI_STATE.RECENT.RECENT_SUBTEXT'),
            imageUrl:
              'https://vstatic-prod.apigateway.co/social-marketing-client/assets/onboarding-carousel/EmptyCalendar.png',
          };
        }
      }),
    );

    this.setCarousel();
    this.hammerConfig.buildHammer(document.getElementById('carousel-card'));

    this._subscriptions.push(
      this.featuresService.noUpgradePath$.subscribe((noUpgradePath) => {
        this.noUpgardePath = noUpgradePath;
      }),
    );
  }

  setCarousel(index?: number): void {
    if (index >= 0) {
      this.sliceIndex = index;
    }
    const carouselInfo = this.carouselList.filter(
      (carousel) => carousel.slideId === this.slideIdList[this.sliceIndex],
    )[0];
    this.title = carouselInfo.title;
    this.imageUrl = carouselInfo.imageUrl;
    this.text = carouselInfo.text;
    this.videoId = carouselInfo.videoId;
    this.isPhoneGif = carouselInfo.isPhoneGif;
    if (carouselInfo.buttonText && !this.noUpgardePath) {
      this.buttonText$ = this.translateService.stream(carouselInfo.buttonText);
    }

    switch (this.sliceIndex) {
      case 0:
        this.rightButtonAction$ = this.translateService.stream('COMMON.CAROUSEL.TAKE_A_TOUR');
        break;
      case this.slideIdList.length - 1:
        this.rightButtonAction$ = this.translateService.stream('COMMON.CAROUSEL.CONNECT_ACCOUNTS');
        break;
      default:
        this.rightButtonAction$ = this.translateService.stream('COMMON.NEXT');
        break;
    }
  }

  previous(): void {
    if (this.sliceIndex > 0) {
      this.sliceIndex -= 1;
    }
    this.setCarousel();
  }

  next(): void {
    if (this.sliceIndex < this.slideIdList.length - 1) {
      this.sliceIndex += 1;
    }
    this.setCarousel();
  }

  openVideoModalOrComposer(videoId: string): void {
    if (videoId === 'open composer') {
      return this.composerSettingsService.showComposer();
    }
    const dialogConfig = { data: { videoId: videoId } } as MatDialogConfig;
    this.dialogService.open(WistiaVideoComponent, dialogConfig);
  }

  goToLeads(): void {
    this.router.navigateByUrl(`/account/${this.configService.accountGroupId}/settings/leads`);
    this.closeDialog.emit();
  }

  goToSettings(): void {
    this.router.navigateByUrl(`/account/${this.configService.accountGroupId}/settings`);
    this.closeDialog.emit();
  }

  ngOnDestroy(): void {
    this._subscriptions.forEach((s) => s.unsubscribe());
  }
}
