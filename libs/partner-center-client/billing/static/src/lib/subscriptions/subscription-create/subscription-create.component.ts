import { HttpResponse } from '@angular/common/http';
import { Component, computed, inject, Inject, OnInit, signal, ViewChild } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { AbstractControl, FormArray, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import {
  BillingStrategy,
  Consumer,
  Frequency,
  MerchantService,
  ProductPricing,
  ProductPricingRules,
  ProductPricingType,
  RenewalState,
  RetailCustomerConfigurationService,
  RetailPricingForSubscriptionId,
  RetailPricingService,
} from '@galaxy/billing';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { RetryConfig, retryer } from '@vendasta/rx-utils';
import { firstValueFrom, forkJoin, from, map, Observable } from 'rxjs';
import { delay, filter, finalize, switchMap, take } from 'rxjs/operators';
import { convertDollarsToCents } from '../utils';
import { CreateSubscription, SubscriptionsService } from '../subscriptions.service';
import { CreateSubscriptionsService } from '../create-subscriptions.service';
import { SubscriptionCreateItemDialogComponent } from './subscription-create-item-dialog/subscription-create-item-dialog.component';
import { Frequency as GlxyFrequency } from '@vendasta/galaxy/frequency';
import {
  ItemSelectorDialogComponent,
  SubscriptionItem,
} from '../../billing-line-item/item-selector-dialog/item-selector-dialog.component';
import { ItemSelectorAdapterService } from '../../billing-line-item/item-selector-dialog/item-selector-adapter.service';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { MatButton } from '@angular/material/button';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { MatCard, MatCardContent } from '@angular/material/card';
import { MatGridList, MatGridTile } from '@angular/material/grid-list';
import { CommonModule, DatePipe } from '@angular/common';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatIcon } from '@angular/material/icon';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { BillingUiModule } from '@vendasta/billing-ui';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { MatSelectModule } from '@angular/material/select';
import { PARTNER_CENTER_BILLING_CONFIG_TOKEN, PartnerCenterBillingConfig } from '../../core/config';
import { SubscriptionCreateTableFormComponent } from './subscription-create-table-form/subscription-create-table-form.component';

@Component({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GalaxyFormFieldModule,
    GalaxyPageModule,
    MatButton,
    GalaxyButtonLoadingIndicatorModule,
    MatCard,
    MatGridList,
    MatCardContent,
    MatGridTile,
    MatSelectModule,
    MatIcon,
    GalaxyTooltipModule,
    BillingUiModule,
    GalaxyAlertModule,
    GalaxyPipesModule,
    SubscriptionCreateTableFormComponent,
  ],
  providers: [SubscriptionsService, ItemSelectorAdapterService, DatePipe],
  templateUrl: './subscription-create.component.html',
  selector: 'billing-subscription-create',
  styleUrls: ['./subscription-create.component.scss'],
})
export class SubscriptionCreateComponent implements OnInit {
  loading = signal(true);
  isSubmitting = signal(false);
  currency = signal('USD');
  merchantID = toSignal(this.config.merchantId$);
  marketID = signal<string>('');
  customerID = toSignal(this.route.params.pipe(map((params) => params['businessId'])));
  previousPageUrl = computed(() => `/businesses/accounts/${this.customerID()}/subscriptions`);
  subscriptionItems = signal<SubscriptionItem[]>([]);
  invoiceDay = signal(0);

  customerService = inject(PARTNER_CENTER_BILLING_CONFIG_TOKEN).customerService;

  @ViewChild(SubscriptionCreateTableFormComponent) tableForm!: SubscriptionCreateTableFormComponent;

  today = new Date();

  retryConfig: RetryConfig = {
    maxAttempts: 5,
    retryDelay: 100,
    timeoutMilliseconds: 10000,
  };

  protected readonly GlxyFrequency = GlxyFrequency;

  constructor(
    private readonly subscriptionsService: SubscriptionsService,
    private readonly retailPricingService: RetailPricingService,
    private route: ActivatedRoute,
    protected dialog: MatDialog,
    private snackbarService: SnackbarService,
    private translateService: TranslateService,
    @Inject(PARTNER_CENTER_BILLING_CONFIG_TOKEN) private readonly config: PartnerCenterBillingConfig,
    private router: Router,
    private customerConfigService: RetailCustomerConfigurationService,
    private merchantService: MerchantService,
    private createSubscriptionsService: CreateSubscriptionsService,
    private itemAdapterService: ItemSelectorAdapterService,
  ) {}

  async ngOnInit(): Promise<void> {
    const merchantId = await this.waitForMerchantId();
    try {
      const retailConfig = await firstValueFrom(this.customerConfigService.get(merchantId, this.customerID()));
      this.invoiceDay.set(retailConfig && retailConfig.autoGenerate ? retailConfig.invoiceDay || 0 : 0);
    } catch (error) {
      this.invoiceDay.set(0);
    }

    const marketId = await firstValueFrom(
      this.customerService.getCustomer(this.customerID()).pipe(map((customer) => customer?.marketId)),
    );
    this.marketID.set(marketId);
    const currency = await firstValueFrom(
      this.merchantService.getMultiRetailConfigurations(merchantId, [marketId]).pipe(
        map((retailConfigResp) => {
          const [{ currencyCode }] = retailConfigResp.values();
          return currencyCode;
        }),
      ),
    );
    this.currency.set(currency);

    await this.prePopulateForm(merchantId, marketId, currency);

    this.loading.set(false);
  }

  onSubmit(): void {
    if (this.tableForm.form.valid) {
      this.isSubmitting.set(true);

      this.executeCreateSubscriptions()
        .pipe(
          switchMap(() => this.executeUpsertPricing()),
          delay(3000), // give some time for the subscriptions to be synced
          take(1),
          finalize(() => this.isSubmitting.set(false)),
        )
        .subscribe({
          next: (_) => {
            this.router.navigateByUrl(this.previousPageUrl());
          },
          error: (err) => {
            this.snackbarService.openErrorSnack(
              this.translateService.instant('BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.ERROR', {
                error: err?.error?.message ?? err.error,
              }),
            );
          },
        });
    } else {
      const invalidExpiryAndStart = this.retailSubscriptions.controls.filter(
        (control) => control.errors && control.errors?.['invalidExpiryAndStart'],
      );
      const invalidExpiryAndRenewal = this.retailSubscriptions.controls.filter(
        (control) => control.errors && control.errors?.['invalidExpiryAndRenewal'],
      );
      this.tableForm.form.markAllAsTouched();
      if (invalidExpiryAndStart.length > 0) {
        this.snackbarService.openErrorSnack(
          this.translateService.instant('BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.INVALID_EXPIRY_AND_START'),
        );
      } else if (invalidExpiryAndRenewal.length > 0) {
        this.snackbarService.openErrorSnack(
          this.translateService.instant('BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.INVALID_EXPIRY_AND_RENEWAL'),
        );
      } else {
        this.snackbarService.openErrorSnack(
          this.translateService.instant('BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.INVALID_FORM'),
        );
      }
    }
  }

  addSubscriptions(items: SubscriptionItem[]): void {
    this.subscriptionItems.set(items || []);
  }

  get retailSubscriptions() {
    return this.tableForm?.form?.get('retailSubscriptions') as FormArray;
  }

  selectItems(): void {
    this.dialog
      .open(ItemSelectorDialogComponent, {
        width: '60%',
        minHeight: '572px',
        data: {
          partnerId: this.merchantID(),
          marketId: this.marketID(),
          customerId: this.customerID(),
          title: this.translateService.instant('BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.TITLE'),
        },
      })
      .afterClosed()
      .subscribe((subscriptionItems) => {
        if (subscriptionItems) {
          this.addSubscriptions(subscriptionItems);
        }
      });
  }

  createItem(): void {
    this.dialog
      .open(SubscriptionCreateItemDialogComponent, {
        width: '60%',
        maxWidth: '464px',
        data: {
          partnerId: this.merchantID(),
          marketId: this.marketID(),
          customerId: this.customerID(),
          currencyCode: this.currency(),
          title: this.translateService.instant(
            'BILLING_SUBSCRIPTIONS.CREATE_SUBSCRIPTIONS.CREATE_ITEM_DIALOG.CREATE_ITEM',
          ),
        },
      })
      .afterClosed()
      .pipe(take(1))
      .subscribe((subscription: SubscriptionItem) => {
        if (subscription) {
          this.addSubscriptions([subscription]);
        }
      });
  }

  executeUpsertPricing(): Observable<HttpResponse<null>[]> {
    const pricingReqs: Observable<HttpResponse<null>>[] = [];
    this.retailSubscriptions.controls.forEach((subscription) => {
      const frequency = subscription.get('frequency')?.value;

      const subscriptionIds: string[] = subscription.get('subscriptionIds')?.value || [];

      subscriptionIds.forEach((subscriptionId) => {
        const pricing = <ProductPricing>{
          strategy: BillingStrategy.Instantly,
          pricingType: ProductPricingType.Standard,
          currency: subscription.get('currency')?.value,
          frequency: frequency,
          pricingRules: [
            <ProductPricingRules>{
              price: convertDollarsToCents(subscription.get('price')?.value),
              minUnits: 1,
              maxUnits: -1,
            },
          ],
          commitment: {
            initial: 0,
            recurring: 0,
          },
          volumeCommitment: 0,
          isStartingPrice: false,
          fees: [],
          setupFee: convertDollarsToCents(subscription.get('setupFee')?.value),
        };

        pricingReqs.push(
          this.retailPricingService
            .upsertPricing(
              this.merchantID() || '',
              subscription.get('sku')?.value,
              frequency,
              pricing,
              new RetailPricingForSubscriptionId(subscriptionId),
            )
            .pipe(retryer(this.retryConfig)),
        );
      });
    });

    return forkJoin(pricingReqs);
  }

  executeCreateSubscriptions(): Observable<string[][]> {
    const subscriptionReqs: Observable<string>[] = [];
    const subscriptionIdsMap: Map<AbstractControl, string[]> = new Map();

    this.retailSubscriptions.controls.forEach((subscription) => {
      const currentSubscriptionIds: string[] = subscription.get('subscriptionIds')?.value || [];
      if (!subscriptionIdsMap.has(subscription)) {
        subscriptionIdsMap.set(subscription, [...currentSubscriptionIds]);
      }

      const quantity = subscription.get('quantity')?.value || 1;
      const remainingSubscriptionsToCreate = quantity - currentSubscriptionIds.length;

      if (remainingSubscriptionsToCreate <= 0) {
        return;
      }

      let renewalStart = new Date(subscription.get('renewalStart')?.value);
      let billingStart = this.today;
      let billingCycleAnchor: Date | null = null;
      if (subscription.get('billingStart')?.value) {
        // if the billingStart field exists on the form, that means the user has access to the billing start feature flag
        billingStart = new Date(subscription.get('billingStart')?.value);
        renewalStart = billingStart;
        billingCycleAnchor = billingStart;
      } else {
        if (subscription.get('frequency')?.value === Frequency.OneTime) {
          renewalStart = billingStart;
        }
      }

      if (subscription.get('servicePeriod')?.value) {
        // if the servicePeriod field exists on the form, that means the user has access to the service period feature flag.
        // renewalStart is responsible for updating the renewalStart and servicePeriodStart field of a subscription.
        renewalStart = new Date(subscription.get('servicePeriod')?.value);
        if (billingCycleAnchor === null) {
          // billing start did not previously set the anchor, therefore it should align to the billing day specified by renewalStart
          billingCycleAnchor = new Date(subscription.get('renewalStart')?.value);
        }
      }

      const expiryValue = subscription.get('expiry')?.value;
      const expiry = expiryValue ? new Date(expiryValue) : null;

      // ensure dates selected are set to noon to avoid timezone conversion discrepancies; also ensures renewalStart >= billingStart
      billingStart.setUTCHours(12, 0, 0, 0);
      renewalStart.setUTCHours(12, 0, 0, 0);
      for (let i = 0; i < remainingSubscriptionsToCreate; i++) {
        subscriptionReqs.push(
          from(
            this.subscriptionsService.createSubscription(<CreateSubscription>{
              merchantId: this.merchantID(),
              sku: subscription.get('sku')?.value,
              customerId: this.customerID(),
              billingStart: billingStart,
              renewalStart: renewalStart,
              expiry: expiry,
              frequency: subscription.get('frequency')?.value,
              consumer: Consumer.CONSUMER_RETAIL,
              renewalState: RenewalState.Delayed,
              billingCycleAnchor: billingCycleAnchor,
            }),
          ).pipe(
            retryer(this.retryConfig),
            map((subscriptionId) => {
              const ids = subscriptionIdsMap.get(subscription) || [];
              ids.push(subscriptionId);
              subscriptionIdsMap.set(subscription, ids);
              return subscriptionId;
            }),
          ),
        );
      }
    });

    return forkJoin(subscriptionReqs).pipe(
      map(() => {
        subscriptionIdsMap.forEach((ids, subscription) => {
          subscription.get('subscriptionIds')?.setValue(ids);
        });
        return Array.from(subscriptionIdsMap.values());
      }),
    );
  }

  private async waitForMerchantId(): Promise<string> {
    return await firstValueFrom(this.config.merchantId$.pipe(filter(Boolean)));
  }

  private async prePopulateForm(partnerId: string, marketId: string, currency: string): Promise<void> {
    const preSelectedSubscriptions = await firstValueFrom(this.createSubscriptionsService.preSelectedSubscriptions);
    if (preSelectedSubscriptions?.length > 0) {
      this.createSubscriptionsService.clearPreSelectedSubscriptions();
      const subscriptionItems = await this.itemAdapterService.convertSubscriptionsToSubscriptionItems(
        preSelectedSubscriptions,
        partnerId,
        marketId,
        currency,
      );
      this.addSubscriptions(subscriptionItems);
    }
  }
}
