import { Component, DestroyRef, inject, Input, OnInit } from '@angular/core';
import { BillingUiModule } from '@vendasta/billing-ui';
import { Mat<PERSON><PERSON>, MatCardHeader, MatCardModule } from '@angular/material/card';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { Invoice, InvoiceStatus } from '@galaxy/billing';
import { ReplaySubject } from 'rxjs';
import { MatTableModule } from '@angular/material/table';
import { PadInvoiceNumberPipe, StatusBadgePipe } from '@vendasta/smb-invoicing';
import { DatePipe, NgClass } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RouterLink } from '@angular/router';
import { getAmountCredited, getAmountRemaining } from '../../credit-note/credit-note.service';

@Component({
  selector: 'billing-credited-invoice',
  imports: [
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ardHeader,
    GalaxyBadgeModule,
    BillingUiModule,
    MatTableModule,
    NgClass,
    DatePipe,
    MatCardModule,
    RouterLink,
    PadInvoiceNumberPipe,
    StatusBadgePipe,
  ],
  templateUrl: './credited-invoice.component.html',
  styleUrl: './credited-invoice.component.scss',
})
export class CreditedInvoiceComponent implements OnInit {
  private readonly destroyRef = inject(DestroyRef);
  invoiceSubject$$ = new ReplaySubject<Invoice>(1);

  @Input() set invoice(value: Invoice) {
    this.invoiceSubject$$.next(value);
  }

  columns = ['number', 'status', 'dueDate', 'amountCredited', 'amountRemaining'];
  dataSource: {
    id?: string;
    number?: string;
    status?: InvoiceStatus;
    due?: Date;
    amountCredited?: number;
    amountRemaining?: number;
  }[] = [];
  amountCredited = 0;
  amountRemaining = 0;

  ngOnInit() {
    this.invoiceSubject$$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((invoice) => {
      this.amountCredited = getAmountCredited(invoice);
      this.amountRemaining = getAmountRemaining(invoice);
      this.dataSource = [
        {
          id: invoice?.id,
          number: invoice?.number,
          status: invoice?.status,
          due: invoice?.due,
          amountCredited: this.amountCredited,
          amountRemaining: this.amountRemaining,
        },
      ];
    });
  }
}
