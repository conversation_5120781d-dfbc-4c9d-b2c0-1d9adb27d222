import { AssistantInterface, ConnectionInterface, Namespace } from '@vendasta/ai-assistants';

export const MY_LISTING_CONNECTION_NAME = 'My Listing Chat Widget';
export const WEBCHAT_CONNECTION_NAME = 'Web Chat';

export enum ConnectionType {
  WebchatWidget = 'WebchatWidget',
  SMS = 'SMS',
  SocialMarketing = 'SocialMarketing',
  System = 'System',
  Unspecified = 'Unspecified',
  Voice = 'Voice',
  WhatsApp = 'WhatsApp',
}

export interface NamespaceConfig {
  namespace: Namespace;
  root?: string;
}

export interface AiAssistant {
  assistant: AssistantInterface;
  subtitleKey?: string;
  descriptionKey?: string;
  decoration?: AiAssistantDecoration;
  connections?: AiConnection[];
  isDefault?: boolean;
  showConfigButtonOnDefault?: boolean;
  isDisabled?: boolean;
  isComingSoon?: boolean;
  cta?: CTAction;
  computeCTA?: (assistant: AiAssistant) => CTAction;
  alertInformation?: string;
  hideConnections?: boolean;
  goalIDsToHide?: string[];
  autonomyRating?: number;
  order?: number;
}

export interface AiConnection {
  connection: ConnectionInterface;
  isDefault?: boolean;
  cta?: CTAction;
}

export interface AiAssistantDecoration {
  defaultAvatarIcon?: string;
  gradientColor?: string;
  avatarIconUrl?: string;
}

export type Action = ActionWithUrl | ActionWithPath | ActionWithCallback;

export interface CTAction {
  label?: string;
  action?: Action;
}

interface ActionBase {
  showButton?: boolean;
  disabled?: boolean;
}

export interface ActionWithUrl extends ActionBase {
  url: string;
  relativeRoute?: boolean;
  newTab?: boolean;
  pathCommands?: never;
  callback?: never;
}

export interface ActionWithPath extends ActionBase {
  url?: never;
  relativeRoute?: boolean;
  newTab?: never;
  pathCommands: unknown[];
  callback?: never;
}

export interface ActionWithCallback extends ActionBase {
  url?: never;
  relativeRoute?: never;
  newTab?: never;
  pathCommands?: never;
  callback: () => void;
}
