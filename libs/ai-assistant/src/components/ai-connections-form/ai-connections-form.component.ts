import { Component, inject, input } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatCardModule } from '@angular/material/card';
import { AiAssistantI18nModule } from '../../assets/i18n/ai-assistant-i18n.module';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { AiConnectionsForm, ConnectionMetadata } from '../../core/forms';

@Component({
  selector: 'ai-connections-form',
  imports: [
    AiAssistantI18nModule,
    MatCardModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    FormsModule,
    MatCheckboxModule,
    GalaxyLoadingSpinnerModule,
  ],
  templateUrl: './ai-connections-form.component.html',
  styleUrl: './ai-connections-form.component.scss',
})
export class AiConnectionsFormComponent {
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);

  connectionForms = input.required<AiConnectionsForm>();

  protected triggerAction(metadata: ConnectionMetadata): void {
    if (metadata.cta?.action) {
      this.buildConnectionActionCallback(metadata)?.();
    }
  }

  protected buildConnectionActionCallback(metadata: ConnectionMetadata): (() => void) | undefined {
    if (!metadata?.cta?.action?.showButton) {
      return undefined;
    }

    const action = metadata.cta.action;

    if (action?.url) {
      if (action.newTab && !action.relativeRoute) {
        return () => window.open(action.url, '_blank');
      }
      return () => this.router.navigate([action.url], action.relativeRoute ? { relativeTo: this.route } : undefined);
    }

    if (action?.pathCommands) {
      return () => this.router.navigate(action.pathCommands);
    }

    if (action?.callback) {
      return action.callback;
    }
    return undefined;
  }
}
