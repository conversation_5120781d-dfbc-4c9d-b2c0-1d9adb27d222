import { Component, inject, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CONFIG_MANAGER_CONFIG_TOKEN } from '../tokens';
import { ConfigEditorDialogComponent } from '../config-editor/dialog/config-editor-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { ConfigManagerI18nModule } from '../assets/i18n/config-manager-i18n.module';
import { ConfigListComponent } from '../config-list/config-list.component';

@Component({
  selector: 'config-manager',
  standalone: true,
  imports: [CommonModule, TranslateModule, ConfigManagerI18nModule, ConfigListComponent, MatButtonModule],
  templateUrl: './config-manager.component.html',
  styleUrls: ['./config-manager.component.scss'],
})
export class ConfigManagerComponent {
  @ViewChild(ConfigListComponent) configList!: ConfigListComponent;

  title = 'Configuration Manager';
  private config = inject(CONFIG_MANAGER_CONFIG_TOKEN);
  partnerId$ = this.config.partnerId$;
  dialog = inject(MatDialog);

  openConfigDialog(): void {
    const dialogRef = this.dialog.open(ConfigEditorDialogComponent);

    dialogRef.afterClosed().subscribe((result) => {
      if (result?.success) {
        this.configList.loadListData();
      }
    });
  }
}
