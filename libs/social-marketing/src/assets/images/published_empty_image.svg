<svg width="291" height="257" viewBox="0 0 291 257" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M251.82 66.4129C266.763 107.086 239.492 152.459 186.464 191.839C154.851 215.315 75.5784 228.62 45.1006 194.803C18.2258 164.985 12.5362 119.728 61.6405 93.0855C136.701 52.3609 238.023 28.8588 251.82 66.4129Z" fill="#E3F2FD"/>
<rect x="1" y="-1" width="172" height="162" rx="9" transform="matrix(1 0 0 -1 48 199)" fill="#FAFAFA" stroke="#1976D2" stroke-width="2"/>
<g filter="url(#filter0_d_362_1105)">
<rect x="37.7998" y="63" width="194.027" height="56.8577" rx="8" fill="white"/>
<rect x="38.7998" y="64" width="192.027" height="54.8577" rx="7" stroke="#1976D2" stroke-width="2"/>
</g>
<g clip-path="url(#clip0_362_1105)">
<rect x="47.6582" y="72" width="41.2219" height="39.0897" rx="6" fill="#E3F2FD"/>
<mask id="mask0_362_1105" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="67" y="105" width="22" height="27">
<rect width="18.9615" height="18.9615" rx="4" transform="matrix(-0.6348 0.772677 -0.626142 -0.779709 89.5625 118.45)" fill="#90CAF9"/>
</mask>
<g mask="url(#mask0_362_1105)">
<rect x="89.6675" y="121.424" width="24.1525" height="20.8144" rx="4" transform="rotate(-179.68 89.6675 121.424)" fill="#90CAF9"/>
</g>
<mask id="mask1_362_1105" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="51" y="97" width="26" height="36">
<rect width="24.0871" height="24.0871" rx="4" transform="matrix(-0.588712 0.808343 -0.57966 -0.814859 78.252 115.097)" fill="#64B5F6"/>
</mask>
<g mask="url(#mask1_362_1105)">
<rect x="78.373" y="119.047" width="28.4289" height="27.6471" rx="4" transform="rotate(-179.68 78.373 119.047)" fill="#64B5F6"/>
</g>
<circle cx="77.0072" cy="93.5536" r="3.55361" fill="#64B5F6"/>
</g>
<rect x="95" y="83" width="64" height="6" rx="3" fill="#D9D9D9" fill-opacity="0.57"/>
<rect x="95" y="93" width="113" height="6" rx="3" fill="#D9D9D9" fill-opacity="0.57"/>
<rect x="76.1211" y="128" width="116.672" height="26.3387" rx="6" fill="white"/>
<rect x="76.1211" y="160" width="116.672" height="26.3387" rx="6" fill="white"/>
<rect x="80.3896" y="132" width="27.7453" height="19.2202" rx="4" fill="#D9D9D9" fill-opacity="0.57"/>
<rect x="112" y="135" width="26" height="4" rx="2" fill="#D9D9D9" fill-opacity="0.57"/>
<rect x="112" y="143" width="73" height="4" rx="2" fill="#D9D9D9" fill-opacity="0.57"/>
<rect x="80.3896" y="164" width="27.7453" height="18.5083" rx="4" fill="#D9D9D9" fill-opacity="0.57"/>
<rect x="112" y="168" width="26" height="4" rx="2" fill="#D9D9D9" fill-opacity="0.57"/>
<rect x="112" y="176" width="73" height="4" rx="2" fill="#D9D9D9" fill-opacity="0.57"/>
<path d="M224.529 30.797C224.665 30.4046 224.752 30.0839 224.889 29.7759C226.258 26.6625 228.257 23.9587 230.486 21.4184C230.748 21.1215 231.067 20.8465 231.411 20.6747C231.596 20.5816 231.945 20.6294 232.136 20.7558C232.279 20.8527 232.359 21.1883 232.31 21.3692C232.22 21.715 232.063 22.0633 231.859 22.3654C230.521 24.3612 229.175 26.3479 227.813 28.3248C227.302 29.0629 226.766 29.7904 226.238 30.5186C226.065 30.7644 225.876 31.0004 225.68 31.2189C225.464 31.4693 225.157 31.6022 224.88 31.4008C224.698 31.2668 224.497 31.1647 224.529 30.797Z" fill="#1976D2"/>
<path d="M235.266 44.2901C234.871 44.1878 234.408 44.0962 233.965 43.9559C232.22 43.3972 230.475 42.8385 228.74 42.2637C228.364 42.1378 228.002 41.9457 227.676 41.7315C227.237 41.4484 227.109 41.0917 227.217 40.7222C227.325 40.3528 227.647 40.1454 228.174 40.1836C228.534 40.2071 228.918 40.2495 229.253 40.3634C231.451 41.1307 233.648 41.9147 235.836 42.7064C236.081 42.7954 236.329 42.9354 236.507 43.1196C236.955 43.572 236.748 44.2023 236.116 44.3063C235.868 44.3434 235.595 44.2939 235.266 44.2901Z" fill="#1976D2"/>
<path d="M213.757 19.8034C213.825 19.4135 213.943 19.0281 214.077 18.6525C214.2 18.3179 214.455 18.0962 214.834 18.0961C215.154 18.0991 215.469 18.4471 215.482 18.8694C215.499 19.4353 215.508 20.0088 215.433 20.5666C215.209 22.2483 214.951 23.9353 214.685 25.6133C214.594 26.1696 214.469 26.7229 214.328 27.2748C214.278 27.4641 214.193 27.6587 214.071 27.7996C213.741 28.1999 213.209 28.1192 213.041 27.641C212.947 27.3715 212.924 27.0495 212.949 26.7653C212.99 25.8255 213.526 21.1812 213.757 19.8034Z" fill="#1976D2"/>
<path d="M29.2273 130.029C29.0459 130.403 28.9211 130.711 28.7488 131C27.0187 133.929 24.7124 136.376 22.1974 138.633C21.9019 138.896 21.5524 139.131 21.1908 139.261C20.9959 139.332 20.6545 139.242 20.4799 139.094C20.3494 138.981 20.3099 138.638 20.3808 138.465C20.5107 138.132 20.7081 137.805 20.9461 137.529C22.5124 135.707 24.0851 133.894 25.6726 132.093C26.2682 131.421 26.8868 130.763 27.4972 130.102C27.6985 129.879 27.9145 129.667 28.1352 129.474C28.3793 129.251 28.6994 129.155 28.9506 129.388C29.1152 129.543 29.3032 129.668 29.2273 130.029Z" fill="#1976D2"/>
<path d="M20.1713 115.355C20.5515 115.503 21.001 115.65 21.4236 115.841C23.0896 116.604 24.7556 117.366 26.4099 118.143C26.7686 118.313 27.1053 118.547 27.4039 118.798C27.8055 119.131 27.8907 119.501 27.7391 119.855C27.5876 120.209 27.2437 120.377 26.7247 120.276C26.3699 120.21 25.9939 120.122 25.6746 119.969C23.5833 118.946 21.4955 117.906 19.4177 116.86C19.1852 116.742 18.9548 116.573 18.8005 116.369C18.4089 115.867 18.6896 115.266 19.3299 115.238C19.5801 115.23 19.8459 115.312 20.1713 115.355Z" fill="#1976D2"/>
<path d="M38.6156 142.226C38.5016 142.605 38.3384 142.974 38.1605 143.331C37.9994 143.649 37.7195 143.838 37.3431 143.793C37.0259 143.752 36.7549 143.369 36.7919 142.949C36.8419 142.385 36.9017 141.814 37.042 141.269C37.4646 139.626 37.9217 137.982 38.3853 136.347C38.542 135.806 38.7315 135.271 38.9375 134.74C39.01 134.558 39.1171 134.375 39.2549 134.25C39.6303 133.891 40.1489 134.035 40.2592 134.53C40.3206 134.808 40.3043 135.131 40.2456 135.41C40.0934 136.338 39.0088 140.886 38.6156 142.226Z" fill="#1976D2"/>
<path d="M19 200H188.862" stroke="#1976D2" stroke-width="2" stroke-linecap="round"/>
<path d="M71.2744 207H222.658" stroke="#1976D2" stroke-width="2" stroke-linecap="round"/>
<path d="M53 207H62.9501" stroke="#1976D2" stroke-width="2" stroke-linecap="round"/>
<g clip-path="url(#clip1_362_1105)">
<circle cx="207" cy="51.0001" r="7.57576" fill="white"/>
<path d="M207 42.6665C202.4 42.6665 198.667 46.3998 198.667 50.9998C198.667 55.5998 202.4 59.3332 207 59.3332C211.6 59.3332 215.333 55.5998 215.333 50.9998C215.333 46.3998 211.6 42.6665 207 42.6665ZM205.333 55.1665L201.167 50.9998L202.342 49.8248L205.333 52.8082L211.658 46.4832L212.833 47.6665L205.333 55.1665Z" fill="#4CAF50"/>
</g>
<defs>
<filter id="filter0_d_362_1105" x="33.7998" y="63" width="202.027" height="64.8579" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_362_1105"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_362_1105" result="shape"/>
</filter>
<clipPath id="clip0_362_1105">
<rect width="41.2219" height="39.0897" fill="white" transform="translate(47.6582 72)"/>
</clipPath>
<clipPath id="clip1_362_1105">
<rect width="20" height="20" fill="white" transform="translate(197 41)"/>
</clipPath>
</defs>
</svg>
