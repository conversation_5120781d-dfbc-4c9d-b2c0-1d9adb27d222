/*

To use these you need to import the file in your scss like:
    @import '../../core/breaks.scss';

And to use the points, you can use them like this:
    @include respond-to(mobile) {
        ... Your styles for that breakdpoint ...
    }

*/

$mobile-small: 350px;
$mobile-small-min-height: 500px;
$mobile-small-max-height: 800px;
$mobile: 767px;
$tablet: 995px;
$small-desktop: 1200px;
$small-desktop-max-height: 730px;
$desktop-min-width: 850px;

@mixin respond-to($media) {
  @if $media == mobile-small {
    @media only screen and (min-height: $mobile-small-min-height - 1) and (max-height: $mobile-small-max-height - 1) {
      @content;
    }
  } @else if $media == mobile {
    @media only screen and (max-width: $mobile - 1) {
      @content;
    }
  } @else if $media == tablet {
    @media only screen and (min-width: $mobile) and (max-width: $tablet - 1) {
      @content;
    }
  } @else if $media == small-desktop {
    @media only screen and (min-width: $tablet) and (max-width: $small-desktop) {
      @content;
    }
  } @else if $media == desktop-with-low-hight {
    @media only screen and (max-height: $small-desktop-max-height) and (min-width: $desktop-min-width) {
      @content;
    }
  }
}
