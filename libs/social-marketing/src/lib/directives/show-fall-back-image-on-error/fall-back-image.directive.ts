import { Directive, ElementRef, HostListener, Input } from '@angular/core';

@Directive({
  selector: '[socialMarketingAppFallbackImg]',
})
export class FallbackImgDirective {
  @Input() appFallbackImg!: string;

  constructor(private el: ElementRef) {}

  @HostListener('error')
  onError() {
    if (!this.appFallbackImg) {
      this.appFallbackImg =
        'https://vstatic-prod.apigateway.co/social-marketing-client/assets/empty-states/error_preview_square.svg';
    }
    this.el.nativeElement.src = this.appFallbackImg;
  }
}
