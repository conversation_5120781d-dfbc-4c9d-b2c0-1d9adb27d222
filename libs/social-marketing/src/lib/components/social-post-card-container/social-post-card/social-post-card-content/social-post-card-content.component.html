<div class="text-preview">
  @if (post().isDraft) {
    @if (post().postText?.trim()) {
      @if (post().blogPostCustomization?.title || post().service === serviceName.WORDPRESS_BLOG) {
        <div class="post-title">
          {{ getTitle(post()) }}
        </div>
      }
      @if (
        parsePostText(
          post().postText | spaceLinky | linky: { stripPrefix: false } | postPreview: post().service,
          post().service,
          post().draftId,
          post().ssid
        ) | async;
        as currentText
      ) {
        <span [innerHTML]="postTextToDisplay(post(), currentText)"></span>
      }
      @if (
        sanitizeHtmlContent(
          parsePostText(
            post().postText || post().post_text
              | spaceLinky
              | linky: { stripPrefix: false, email: false }
              | postPreview: post().service,
            post().service,
            post().postId,
            post().ssid
          ) | async
        ).length > readMoreMaxLength && !isBlogPost
      ) {
        <p class="text-see-more">
          <span (click)="post().readMoreExpanded = !post().readMoreExpanded">
            {{ (post().readMoreExpanded ? 'POSTS.ACTIONS.VIEW_LESS' : 'POSTS.ACTIONS.VIEW_MORE') | translate }}
          </span>
        </p>
      }
      <!--<span class="text-see-more" (click)="handleViewMore()" *ngIf="isBlogPost">
        {{ 'POSTS.ACTIONS.VIEW_MORE' | translate }}
      </span>-->
    } @else {
      <app-empty-posts-content [postType]="workFlowTypeReadable()"></app-empty-posts-content>
    }
  } @else {
    @if (post().postText || post().post_text?.trim()) {
      @if (post().blogPostCustomization?.title || post().service === serviceName.WORDPRESS_BLOG) {
        <div class="post-title">
          {{ getTitle(post()) }}
        </div>
      }
      @if (
        parsePostText(
          post().postText || post().post_text
            | spaceLinky
            | linky: { stripPrefix: false }
            | postPreview: post().service,
          post().service,
          post().postId,
          post().ssid
        ) | async;
        as currentText
      ) {
        <span [innerHTML]="postTextToDisplay(post(), currentText)"></span>
      }
      @if (
        sanitizeHtmlContent(
          parsePostText(
            post().postText || post().post_text
              | spaceLinky
              | linky: { stripPrefix: false, email: false }
              | postPreview: post().service,
            post().service,
            post().postId,
            post().ssid
          ) | async
        ).length > readMoreMaxLength && !isBlogPost
      ) {
        <p class="text-see-more">
          <span (click)="post().readMoreExpanded = !post().readMoreExpanded">
            {{ (post().readMoreExpanded ? 'POSTS.ACTIONS.VIEW_LESS' : 'POSTS.ACTIONS.VIEW_MORE') | translate }}
          </span>
        </p>
      }
      <!-- <span class="text-see-more" (click)="handleViewMore()" *ngIf="isBlogPost">
        {{ 'POSTS.ACTIONS.VIEW_MORE' | translate }}
      </span>-->
      @if (
        (post().service === serviceName.GMB || post().service === serviceName.GOOGLE_MY_BUSINESS) && post().callToAction
      ) {
        <div class="gmb-cta">
          @if (post().callToAction.actionType === 6 || post().callToAction.type === 'CALL') {
            <div class="gmb-cta-phony-link">
              {{ post().gmbCallToActionLabel | translate }}
            </div>
          } @else {
            <a [attr.href]="post().callToAction.url" target="_blank">
              {{ post().gmbCallToActionLabel | translate }}
            </a>
          }
        </div>
      }
      @if (shouldShowDraftGMBCtaOptions(post())) {
        <div class="gmb-cta">
          @if (post().gmb_post_customization?.ctaOptions?.action === 'CALL') {
            <div class="gmb-cta-phony-link">
              {{ 'COMPOSER.GOOGLE_MY_BUSINESS.CALL' | translate }}
            </div>
          } @else {
            <a [attr.href]="post().gmb_post_customization?.ctaOptions?.ctaUrl" target="_blank">
              {{ post().gmb_post_customization.ctaOptions.label | translate }}
            </a>
          }
        </div>
      }
    } @else {
      <div class="no-content">
        <social-marketing-social-empty-post-content
          [postType]="workFlowTypeReadable()"
        ></social-marketing-social-empty-post-content>
      </div>
    }
  }
</div>
