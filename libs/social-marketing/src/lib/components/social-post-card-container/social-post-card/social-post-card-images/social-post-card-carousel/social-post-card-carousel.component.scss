@use 'design-tokens' as *;
:host {
  display: block;
  button {
    background: rgba(0, 0, 0, 0.5);
    color: $white;
    border: none;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
    cursor: pointer;
    height: 30px !important;
    width: 30px !important;
    padding: 4px !important;
  }
}

.carousel {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-image {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.carousel-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 2%;
}

button:hover {
  background: rgba(0, 0, 0, 0.7);
}

.prev-button {
  left: 10px;
}

.next-button {
  right: 10px;
}

.left-button {
  left: 1%;
}
.right-button {
  right: 1%;
}
