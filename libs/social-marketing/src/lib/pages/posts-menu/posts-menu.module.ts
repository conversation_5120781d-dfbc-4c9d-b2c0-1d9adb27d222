import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButton, MatIconButton } from '@angular/material/button';
import { Mat<PERSON><PERSON>onToggle, MatButtonToggleGroup } from '@angular/material/button-toggle';
import { MatCardModule } from '@angular/material/card';
import { MatChip } from '@angular/material/chips';
import { MatIcon } from '@angular/material/icon';
import { MatMenu, MatMenuItem, MatMenuTrigger } from '@angular/material/menu';
import { MatTab, MatTabGroup, MatTabLabel } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { LinkyPipe } from '@vendasta/galaxy/pipes';
import { DraftsService } from '@vendasta/social-drafts';
import { SocialPostsService } from '@vendasta/social-posts';
import { BlogConnectionService } from '@vendasta/social-posts';
import { SocialPostCardModule } from '../../components/social-post-card-container/social-post-card.module';
import { ConfigService, PublishPostEventService, SocialDraftsAPIService, SocialPostsAPIService } from '../../services';
import { PipesModule } from '../../Utils';
import { SocialDraftsViewComponent } from './drafts/social-drafts-view.component';
import { SocialPublishedViewComponent } from './published/social-published-view.component';
import { SocialScheduledViewComponent } from './scheduled/social-scheduled-view.component';
import { SocialPostsMenuComponent } from './social-posts-menu.component';

@NgModule({
  imports: [
    SocialPostCardModule,
    CommonModule,
    MatCardModule,
    MatIcon,
    MatIconButton,
    MatMenu,
    MatMenuTrigger,
    MatMenuItem,
    MatTabGroup,
    MatTab,
    MatTabLabel,
    TranslateModule,
    GalaxyBadgeModule,
    GalaxyAlertModule,
    MatButton,
    MatChip,
    LinkyPipe,
    PipesModule,
    MatButtonToggleGroup,
    MatButtonToggle,
    FormsModule,
    // Import standalone components instead of declaring them
    SocialScheduledViewComponent,
    SocialPublishedViewComponent,
    SocialDraftsViewComponent,
  ],
  declarations: [SocialPostsMenuComponent],
  providers: [
    ConfigService,
    SocialDraftsAPIService,
    SocialPostsAPIService,
    PublishPostEventService,
    DraftsService,
    SocialPostsService,
    BlogConnectionService,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  exports: [
    SocialScheduledViewComponent,
    SocialPublishedViewComponent,
    SocialDraftsViewComponent,
    SocialPostsMenuComponent,
  ],
})
export class PostMenuModule {}
