import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { LexiconModule } from '@galaxy/lexicon';
import En from './en_devel.json';

@NgModule({
  imports: [
    TranslateModule,
    LexiconModule.forChild({
      componentName: 'common/social-marketing',
      baseTranslation: En,
    }),
  ],
  exports: [TranslateModule],
})
export class SmI18nModule {}
