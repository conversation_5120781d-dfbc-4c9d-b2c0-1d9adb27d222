import { Type } from '@angular/core';
import {
  ABBranchTaskDefinitionId,
  associateComapyWithContactTaskDefinitionId,
  CloseAllOpportunitiesDefinitionId,
  CloseOpportunityDefinitionId,
  CompanyFilterTaskDefinitionId,
  ContactFilterTaskDefinitionId,
  CreateCRMOpportunity,
  CustomAccountFilterTaskDefinitionId,
  CustomObjectFilterTaskDefinitionId,
  CustomOrderFilterTaskDefinitionId,
  CustomProductFilterTaskDefinitionId,
  CustomUserFilterTaskDefinitionId,
  DelayUntilEventTaskDefinitionId,
  EndTaskDefinitionId,
  GetCompanyFromCrmCustomObject,
  GetConnectionDataVendorTaskDefinitionId,
  GetContactFromCrmCustomObject,
  GetContactFromCrmOpportunity,
  GetCrmOpportunityFromCompany,
  GetCrmOpportunityFromContact,
  GetOpportunityFromCrmCustomObject,
  IfElseBranchAccountDataTaskDefinitionId,
  IfElseBranchAccountInternalDataTaskDefinitionId,
  IfElseBranchBusinessAppDataTaskDefinitionId,
  IfElseBranchCrmCallDataTaskDefinitionId,
  IfElseBranchCrmEmailDataTaskDefinitionId,
  IfElseBranchCrmMeetingDataTaskDefinitionId,
  IfElseBranchCrmNoteDataTaskDefinitionId,
  IfElseBranchCrmTaskDataTaskDefinitionId,
  IfElseBranchOpportunityDataTaskDefinitionId,
  IfElseBranchOpportunityFilterTaskDefinitionId,
  IfElseBranchOrderDataTaskDefinitionId,
  IfElseBranchPartnerDataTaskDefinitionId,
  IfElseBranchShoppingCartDataTaskDefinitionId,
  IfElseBranchTaskDefinitionId,
  IfElseBranchUserDataTaskDefinitionId,
  IfElseBranchUserInternalDataTaskDefinitionId,
  IfElseBranchWorkflowStepTaskDefinitionId,
  IfElseIntegrationConnectionFilterTaskDefinitionId,
  InvoiceFilterTaskDefinitionId,
  JumpTaskDefinitionId,
  ModifyCustomAccountDataTaskDefinitionId,
  ModifyCustomProductDataTaskDefinitionId,
  ModifyCustomSalesOrderTaskDefinitionId,
  ModifyCustomUserDataTaskDefinitionId,
  OpportunityFilterTaskDefinitionId,
  pauseCampaignForCompanyTaskDefinitionId,
  pauseCampaignForContactTaskDefinitionId,
  PauseOrCancelTaskManagerAccountId,
  ProductPriceFilterTaskDefinitionId,
  RateLimitTaskDefinitionId,
  SendAiPromptId,
  SendEmailToCompanyTaskDefinitionId,
  SendEmailToContactTaskDefinitionId,
  SendEmailToUserTaskDefinitionId,
  SendEmailViaInboxTaskDefinitionId,
  SendEventViaInboxTaskDefinitionId,
  SendSMSViaInboxTaskDefinitionId,
  StartCampaignAction,
  startCampaignForCompanyTaskDefinitionId,
  UpdateCrmOpportunity,
  WorkflowTaskCategory,
} from './constants';
import { CommonActionPanelComponent } from '../components/common/common-action-panel-component.directive';
import { CommonDisplayActionService } from '../components/common/common-display-action.service';
import { CommonDisplayActivityService } from '../components/common/common-display-activity.service';
import {
  ABBranchPanelComponent,
  ABBranchService,
  ActivateLimitedProductsPanelComponent,
  ActivateLimitedProductsService,
  ActivateProductPanelComponent,
  ActivateProductService,
  ActivateSalesOrderPanelComponent,
  ActivateSalesOrderService,
  AddTagPanelComponent,
  AddTagService,
  AddTagsToOrderPanelComponent,
  AddTagsToOrderService,
  AddToListPanelComponent,
  AddToListService,
  ApproveSalesOrderPanelComponent,
  ApproveSalesOrderService,
  ArchiveSalesOrderPanelComponent,
  ArchiveSalesOrderService,
  AssignOwnerToContactPanelComponent,
  AssignOwnerToContactService,
  AssignOwnerToCompanyPanelComponent,
  AssignOwnerToCompanyService,
  AssignSalespersonPanelComponent,
  AssignSalespersonService,
  AssignSalesTeamPanelComponent,
  AssignSalesTeamService,
  AssociateCompanyContactPanelComponent,
  AssociateCompanyContactService,
  CallEndpointPanelComponent,
  CallEndpointService,
  ChangeOpportunityPipelineStagePanelComponent,
  ChangeOpportunityPipelineStageService,
  ChargeInvoicePanelComponent,
  ChargeInvoiceService,
  CloseAllOpportunitiesPanelComponent,
  CloseAllOpportunitiesService,
  CloseOpportunityPanelComponent,
  CloseOpportunityService,
  CreateBusinessActivityPanelComponent,
  CreateBusinessActivityService,
  CreateCompanyPanelComponent,
  CreateCompanyService,
  CreateContactPanelComponent,
  CreateContactService,
  CreateCrmCallActivityPanelComponent,
  CreateCrmCallActivityService,
  CreateCrmNotePanelComponent,
  CreateCrmNoteService,
  CreateCrmOpportunityPanelComponent,
  CreateCrmOpportunityService,
  CreateFulfillmentTaskPanelComponent,
  CreateFulfillmentTaskService,
  CreateOpportunityPanelComponent,
  CreateOpportunityService,
  CreateProjectInTaskManagerForOrderPanelComponent,
  CreateProjectInTaskManagerForOrderService,
  CreateProjectInTaskManagerPanelComponent,
  CreateProjectInTaskManagerService,
  CreateProposalPanelComponent,
  CreateProposalService,
  CreateSalesTaskPanelComponent,
  CreateSalesTaskService,
  CreateSnapshotReportPanelComponent,
  CreateSnapshotReportService,
  CreateTaskManagerAccountPanelComponent,
  CreateTaskManagerAccountService,
  CreateUserFromContactService,
  CreateUserPanelComponent,
  CreateUserService,
  CrmTaskCreatePanelComponent,
  CrmTaskCreateService,
  DeactivateProductPanelComponent,
  DeactivateProductService,
  DeclineSalesOrderPanelComponent,
  DeclineSalesOrderService,
  DelayPanelComponent,
  DelayService,
  DelayUntilEventPanelComponent,
  DelayUntilEventService,
  EndActionPanelComponent,
  EndActionService,
  GetAccountFromCompanyService,
  GetAssociatedCrmObjectPanelComponent,
  GetAssociatedCrmObjectService,
  GetCompanyFromAccountGroupService,
  GetConnectionBusinessDataService,
  GetConnectionDataVendorPanelComponent,
  GetConnectionDataVendorService,
  GetMyTeamMemberPanelComponent,
  GetMyTeamMemberService,
  GetOrderDataService,
  GetProductsCustomFieldDataPanelComponent,
  GetProductsCustomFieldDataService,
  IfElseBranchPanelComponent,
  IfElseBranchService,
  JumpPanelComponent,
  JumpService,
  LogSalesActivityPanelComponent,
  LogSalesActivityService,
  LookupContactPanelComponent,
  LookupContactService,
  ModifyAuxiliaryAccountDataPanelComponent,
  ModifyAuxiliaryDataService,
  ModifyAuxiliaryProductDataPanelComponent,
  ModifyAuxiliarySalesOrderDataPanelComponent,
  ModifyAuxiliaryUserDataPanelComponent,
  ModifyCrmCallActivityPanelComponent,
  ModifyCrmCallActivityService,
  NoUserInputPanelComponent,
  OutgoingWebhookPanelComponent,
  OutgoingWebhookService,
  PauseCampaignForUserPanelComponent,
  PauseCampaignForUserService,
  PauseCampaignPanelComponent,
  PauseCampaignService,
  PauseOrCancelTaskManagerAccountPanelComponent,
  PauseOrCancelTaskManagerAccountService,
  QueryOpportunitiesPanelComponent,
  QueryOpportunitiesService,
  RateFilterPanelComponent,
  RateFilterService,
  RemoveFromListPanelComponent,
  RemoveFromListService,
  RemoveTagPanelComponent,
  RemoveTagService,
  RemoveTagsFromOrderPanelComponent,
  RemoveTagsFromOrderService,
  ReopenOpportunityPanelComponent,
  ReopenOpportunityService,
  SendAiPromptPanelComponent,
  SendAiPromptService,
  SendEmailNotificationPanelComponent,
  SendEmailNotificationService,
  SendHtmlEmailToUserComponent,
  SendHtmlEmailToUserService,
  SendInAppNotificationPanelComponent,
  SendInAppNotificationService,
  SendInboxMessageInternalPanelComponent,
  SendInboxMessageInternalService,
  SendInboxMessagePanelComponent,
  SendInboxMessageService,
  SendInboxOnChannelPanelComponent,
  SendInboxOnChannelService,
  SendNotificationCustomersPanelComponent,
  SendNotificationCustomersService,
  SendNotificationFulfillmentAgentPanelComponent,
  SendNotificationFulfillmentAgentService,
  SendNotificationPartnerAdminPanelComponent,
  SendNotificationPartnerAdminService,
  SendNotificationSalespersonPanelComponent,
  SendNotificationSalespersonService,
  SendNotificationsToSmbsPanelComponent,
  SendNotificationsToSmbsService,
  SendNotificationToTheUserPanelComponent,
  SendNotificationToTheUserService,
  SendNotificationUserPanelComponent,
  SendNotificationUserService,
  SendPublicSystemMessageInternalPanelComponent,
  SendPublicSystemMessageInternalService,
  SendPublicSystemMessagePanelComponent,
  SendPublicSystemMessageService,
  SendReviewRequestPanelComponent,
  SendReviewRequestService,
  SetAccountLifecycleStagePanelComponent,
  SetAccountLifecycleStageService,
  StartCampaignForCompanyPanelComponent,
  StartCampaignForCompanyService,
  StartCampaignForUserPanelComponent,
  StartCampaignForUserService,
  StartCampaignPanelComponent,
  StartCampaignService,
  StringOperationsPanelComponent,
  StringOperationsService,
  SubmitOrderForAdminApprovalPanelComponent,
  SubmitOrderForAdminApprovalService,
  UnassignSalespeoplePanelComponent,
  UnassignSalespeopleService,
  UpdateBillingContractPanelComponent,
  UpdateBillingContractService,
  UpdateCompanyPanelComponent,
  UpdateCompanyService,
  UpdateContactPanelComponent,
  UpdateContactService,
  UpdateCrmOpportunityPanelComponent,
  UpdateCrmOpportunityService,
} from '../components/actions';
import {
  AUTOMATION_AI_ASSISTANT_FEATURE_FLAG,
  BCC_CRM_CUSTOM_OBJECT_FEATURE_FLAG,
  BUILDING_BLOCK_ACTION_COLOR,
  CRM_CUSTOM_OBJECT_FEATURE_FLAG,
  DEFAULT_ACTION_COLOR,
  INTERNAL_ACTION_COLOR,
} from '@galaxy/automata/shared';
import { ModifyCRMContactDataPanelComponent } from '../components/actions/modify-auxiliary-data/modify-crm-contact-data-panel.component';
import { AiAssistantActionPanelComponent, AiAssistantActionService } from '../components/actions/ai-assistant-action';
import { StartYeswareCampaignPanelComponent } from '../components/actions/start-yesware-campaign/start-yesware-campaign-panel/start-yesware-campaign-panel.component';
import { StartYeswareCampaignService } from '../components/actions/start-yesware-campaign';
import { GetMyBusinessInfoPanelComponent, GetMyBusinessInfoService } from '../components/actions/get-my-business-info';
import { ModifyCRMCompanyDataPanelComponent } from '../components/actions/modify-auxiliary-data/modify-crm-company-data-panel.component';
import {
  AddContactToKixiePowerlistPanelComponent,
  AddContactToKixiePowerlistService,
} from '../components/actions/add-contact-to-kixie-powerlist';

export interface WorkflowActionInterface {
  panel: Type<CommonActionPanelComponent>;
  actionDisplayService: Type<CommonDisplayActionService>;
  activityDisplayService: Type<CommonDisplayActivityService>;
  icon: string;
  iconColor: string;
  taskDefinitionId: string;
  name: string;
  nameTranslationParams?: unknown;
  categories: WorkflowTaskCategory[];
  featureFlag?: string;
  hasSleep?: boolean;
  upgradeSubscriptionTitle?: string;
  upgradeSubscriptionDescription?: string;
  supportsCustomOutputParameters?: boolean;
  // Whether or not to use `getMultiBranchNodeData` for the node display instead of `getActionSubtitle`. `getActionSubtitle` will still be used
  // for the activity table.
  useMultiBranchingForNodeDisplay?: boolean;
  // Used to provide a description when using common panels (e.g. "noUserInput" panel).
  panelDescription?: string;
}

export const IF_ELSE_BRANCH_PANEL: WorkflowActionInterface = {
  // If/else branch
  panel: IfElseBranchPanelComponent,
  activityDisplayService: IfElseBranchService,
  actionDisplayService: IfElseBranchService,
  icon: 'device_hub',
  iconColor: BUILDING_BLOCK_ACTION_COLOR,
  taskDefinitionId: 'TaskDefinition-25932478-3111-4462-b342-ee94628245df',
  name: 'AUTOMATIONS.EDITOR.TASKS.IF_ELSE_BRANCH.IF_ELSE_BRANCH',
  categories: [WorkflowTaskCategory.Conditions],
  useMultiBranchingForNodeDisplay: true,
};
export const WORKFLOW_ACTIONS: { [taskKey: string]: WorkflowActionInterface } = {
  [SendEmailToUserTaskDefinitionId]: {
    panel: SendHtmlEmailToUserComponent,
    activityDisplayService: SendHtmlEmailToUserService,
    actionDisplayService: SendHtmlEmailToUserService,
    icon: 'email',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: SendEmailToUserTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_HTML_EMAIL_TO_USER.SEND_HTML_EMAIL_TO_USER',
    categories: [WorkflowTaskCategory.CampaignsAndEmails],
  },
  [SendEmailToContactTaskDefinitionId]: {
    panel: SendHtmlEmailToUserComponent,
    activityDisplayService: SendHtmlEmailToUserService,
    actionDisplayService: SendHtmlEmailToUserService,
    icon: 'email',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: SendEmailToContactTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_HTML_EMAIL_TO_USER.SEND_HTML_EMAIL_TO_CONTACT',
    categories: [WorkflowTaskCategory.CampaignsAndEmails],
  },
  [SendEmailToCompanyTaskDefinitionId]: {
    panel: SendHtmlEmailToUserComponent,
    activityDisplayService: SendHtmlEmailToUserService,
    actionDisplayService: SendHtmlEmailToUserService,
    icon: 'email',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: SendEmailToCompanyTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_HTML_EMAIL_TO_USER.SEND_HTML_EMAIL_TO_COMPANY',
    categories: [WorkflowTaskCategory.CampaignsAndEmails],
  },
  'TaskDefinition-641620ec-f9ac-42a2-abf8-bbdee57e8dc7': {
    panel: SendInAppNotificationPanelComponent,
    activityDisplayService: SendInAppNotificationService,
    actionDisplayService: SendInAppNotificationService,
    icon: 'notifications',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-641620ec-f9ac-42a2-abf8-bbdee57e8dc7',
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_IN_APP_NOTIFICATION.SEND_A_NOTIFICATION',
    categories: [WorkflowTaskCategory.Notifications],
  },
  // 'send-notification-to-user'
  'TaskDefinition-send-notification-to-user': {
    panel: SendNotificationToTheUserPanelComponent,
    activityDisplayService: SendNotificationToTheUserService,
    actionDisplayService: SendNotificationToTheUserService,
    icon: 'notifications',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-send-notification-to-user',
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_NOTIFICATION_TO_THE_USER.TITLE',
    categories: [WorkflowTaskCategory.Notifications],
  },
  // 'start-campaign'
  [StartCampaignAction]: {
    panel: StartCampaignPanelComponent,
    activityDisplayService: StartCampaignService,
    actionDisplayService: StartCampaignService,
    icon: 'email',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: StartCampaignAction,
    name: 'AUTOMATIONS.EDITOR.TASKS.CAMPAIGNS.START_GENERAL.SEND_A_CAMPAIGN',
    categories: [WorkflowTaskCategory.CampaignsAndEmails],
  },
  // 'start-campaign-for-user'
  'TaskDefinition-2b2c79c7-11c5-4830-b7ea-66f06032304f': {
    panel: StartCampaignForUserPanelComponent,
    activityDisplayService: StartCampaignForUserService,
    actionDisplayService: StartCampaignForUserService,
    icon: 'email',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-2b2c79c7-11c5-4830-b7ea-66f06032304f',
    name: 'AUTOMATIONS.EDITOR.TASKS.CAMPAIGNS.START_FOR_USER.SEND_A_CAMPAIGN',
    categories: [WorkflowTaskCategory.CampaignsAndEmails],
  },
  'TaskDefinition-add-contact-campaign': {
    panel: StartCampaignPanelComponent,
    activityDisplayService: StartCampaignService,
    actionDisplayService: StartCampaignService,
    icon: 'email',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-add-contact-campaign',
    name: 'AUTOMATIONS.EDITOR.TASKS.CAMPAIGNS.START_GENERAL.SEND_A_CAMPAIGN_CONTACT',
    categories: [WorkflowTaskCategory.CampaignsAndEmails],
  },
  'TaskDefinition-add-contact-to-yesware-campaign': {
    panel: StartYeswareCampaignPanelComponent,
    activityDisplayService: StartYeswareCampaignService,
    actionDisplayService: StartYeswareCampaignService,
    icon: 'email',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-add-contact-to-yesware-campaign',
    name: 'AUTOMATIONS.EDITOR.TASKS.CAMPAIGNS.START_GENERAL.SEND_A_YESWARE_CAMPAIGN_CONTACT',
    categories: [WorkflowTaskCategory.CampaignsAndEmails],
  },
  [pauseCampaignForContactTaskDefinitionId]: {
    panel: StartCampaignPanelComponent,
    activityDisplayService: StartCampaignService,
    actionDisplayService: StartCampaignService,
    icon: 'unsubscribe',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: pauseCampaignForContactTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.CAMPAIGNS.PAUSE_CAMPAIGN_FOR_CONTACT.PAUSE_A_CAMPAIGN',
    categories: [WorkflowTaskCategory.CampaignsAndEmails],
  },
  [pauseCampaignForCompanyTaskDefinitionId]: {
    panel: StartCampaignPanelComponent,
    activityDisplayService: StartCampaignService,
    actionDisplayService: StartCampaignService,
    icon: 'unsubscribe',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: pauseCampaignForCompanyTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.CAMPAIGNS.PAUSE_CAMPAIGN_FOR_COMPANY.PAUSE_A_CAMPAIGN',
    categories: [WorkflowTaskCategory.CampaignsAndEmails],
  },
  // 'pause-campaign'
  'TaskDefinition-7ad4424b-e88a-44b7-bab2-c5fb111ab026': {
    panel: PauseCampaignPanelComponent,
    activityDisplayService: PauseCampaignService,
    actionDisplayService: PauseCampaignService,
    icon: 'unsubscribe',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-7ad4424b-e88a-44b7-bab2-c5fb111ab026',
    name: 'AUTOMATIONS.EDITOR.TASKS.CAMPAIGNS.PAUSE_CAMPAIGN.PAUSE_A_CAMPAIGN',
    categories: [WorkflowTaskCategory.CampaignsAndEmails],
  },
  // 'pause-campaign-for-user'
  'TaskDefinition-pause-campaign-for-user': {
    panel: PauseCampaignForUserPanelComponent,
    activityDisplayService: PauseCampaignForUserService,
    actionDisplayService: PauseCampaignForUserService,
    icon: 'unsubscribe',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-pause-campaign-for-user',
    name: 'AUTOMATIONS.EDITOR.TASKS.CAMPAIGNS.PAUSE_CAMPAIGN_FOR_USER.PAUSE_A_CAMPAIGN',
    categories: [WorkflowTaskCategory.CampaignsAndEmails],
  },
  [startCampaignForCompanyTaskDefinitionId]: {
    panel: StartCampaignForCompanyPanelComponent,
    activityDisplayService: StartCampaignForCompanyService,
    actionDisplayService: StartCampaignForCompanyService,
    icon: 'email',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: startCampaignForCompanyTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.START_CAMPAIGN_FOR_COMPANY.TITLE',
    categories: [WorkflowTaskCategory.CampaignsAndEmails],
  },
  // 'create-sales-task'
  'TaskDefinition-a314fe31-9f19-4167-b02b-7ae991cda21d': {
    panel: CreateSalesTaskPanelComponent,
    activityDisplayService: CreateSalesTaskService,
    actionDisplayService: CreateSalesTaskService,
    icon: 'assignment_turned_in',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-a314fe31-9f19-4167-b02b-7ae991cda21d',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_SALES_TASK.CREATE_SALES_TASK_V2',
    categories: [WorkflowTaskCategory.Sales],
  },
  // 'create-fulfillment-task'
  'TaskDefinition-create_fulfillment_task': {
    panel: CreateFulfillmentTaskPanelComponent,
    activityDisplayService: CreateFulfillmentTaskService,
    actionDisplayService: CreateFulfillmentTaskService,
    icon: 'assignment_turned_in',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-create_fulfillment_task',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_FULFILLMENT_TASK.CREATE_FULFILLMENT_TASK',
    categories: [WorkflowTaskCategory.Fulfillment],
  },
  'TaskDefinition-Create-CRM-Task-Contact': {
    panel: CrmTaskCreatePanelComponent,
    activityDisplayService: CrmTaskCreateService,
    actionDisplayService: CrmTaskCreateService,
    icon: 'assignment_turned_in',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-Create-CRM-Task-Contact',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_TASK.CONTACT_NAME_V2',
    categories: [WorkflowTaskCategory.Advanced],
  },
  'TaskDefinition-Create-CRM-Task-Company': {
    panel: CrmTaskCreatePanelComponent,
    activityDisplayService: CrmTaskCreateService,
    actionDisplayService: CrmTaskCreateService,
    icon: 'assignment_turned_in',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-Create-CRM-Task-Company',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_TASK.COMPANY_NAME_V2',
    categories: [WorkflowTaskCategory.Advanced],
  },
  // 'create-business-activity'
  'TaskDefinition-dc9de50c-9489-4e5f-9248-936089111706': {
    panel: CreateBusinessActivityPanelComponent,
    activityDisplayService: CreateBusinessActivityService,
    actionDisplayService: CreateBusinessActivityService,
    icon: 'hotness',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-dc9de50c-9489-4e5f-9248-936089111706',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_BUSINESS_ACTIVITY.CREATE_BUSINESS_ACTIVITY',
    categories: [WorkflowTaskCategory.Sales],
  },
  // 'create-user'
  'TaskDefinition-4fe18b59-ec50-49dc-bacf-f491d0f24a29': {
    panel: CreateUserPanelComponent,
    activityDisplayService: CreateUserService,
    actionDisplayService: CreateUserService,
    icon: 'person_add',
    iconColor: INTERNAL_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-4fe18b59-ec50-49dc-bacf-f491d0f24a29',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_USER.CREATE_USER',
    categories: [WorkflowTaskCategory.Internal],
  },
  // 'activate-express-product'
  'TaskDefinition-activate-product': {
    panel: ActivateProductPanelComponent,
    activityDisplayService: ActivateProductService,
    actionDisplayService: ActivateProductService,
    icon: 'shopping_cart',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-activate-product',
    name: 'AUTOMATIONS.EDITOR.TASKS.ACTIVATE_PRODUCT.ACTIVATE_EXPRESS_PRODUCT',
    categories: [WorkflowTaskCategory.Products],
  },
  // 'activate-limited-products'
  'TaskDefinition-activate-limited-products': {
    panel: ActivateLimitedProductsPanelComponent,
    activityDisplayService: ActivateLimitedProductsService,
    actionDisplayService: ActivateLimitedProductsService,
    icon: 'shopping_cart',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-activate-limited-product',
    name: 'AUTOMATIONS.EDITOR.TASKS.ACTIVATE_PRODUCT.ACTIVATE_PRODUCT',
    categories: [WorkflowTaskCategory.Products],
  },
  // 'assign-salesperson-to-account-group'
  'TaskDefinition-assign-salesperson-to-account-group': {
    panel: AssignSalespersonPanelComponent,
    activityDisplayService: AssignSalespersonService,
    actionDisplayService: AssignSalespersonService,
    icon: 'person_add',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-assign-salesperson-to-account-group',
    name: 'AUTOMATIONS.EDITOR.TASKS.ASSIGN_SALESPERSON.ASSIGN_SALESPERSON',
    categories: [WorkflowTaskCategory.Sales],
  },
  'TaskDefinition-assign-sales-team-to-account-group': {
    panel: AssignSalesTeamPanelComponent,
    activityDisplayService: AssignSalesTeamService,
    actionDisplayService: AssignSalesTeamService,
    icon: 'group_add',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-assign-sales-team-to-account-group',
    name: 'AUTOMATIONS.EDITOR.TASKS.ASSIGN_SALES_TEAM.ASSIGN_SALES_TEAM',
    categories: [WorkflowTaskCategory.Sales],
  },
  'TaskDefinition-deactivate-product': {
    panel: DeactivateProductPanelComponent,
    activityDisplayService: DeactivateProductService,
    actionDisplayService: DeactivateProductService,
    icon: 'remove_shopping_cart',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-deactivate-product',
    name: 'AUTOMATIONS.EDITOR.TASKS.DEACTIVATE_PRODUCT.DEACTIVATE_PRODUCT',
    categories: [WorkflowTaskCategory.Products],
  },
  'TaskDefinition-add-tag': {
    panel: AddTagPanelComponent,
    activityDisplayService: AddTagService,
    actionDisplayService: AddTagService,
    icon: 'label',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-add-tag',
    name: 'AUTOMATIONS.EDITOR.TASKS.ADD_TAG.ADD_TAG',
    categories: [WorkflowTaskCategory.Tags],
  },
  'TaskDefinition-remove-tag': {
    panel: RemoveTagPanelComponent,
    activityDisplayService: RemoveTagService,
    actionDisplayService: RemoveTagService,
    icon: 'label_off',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-remove-tag',
    name: 'AUTOMATIONS.EDITOR.TASKS.REMOVE_TAG.REMOVE_TAG',
    categories: [WorkflowTaskCategory.Tags],
  },
  'TaskDefinition-set-account-lifecycle-stage': {
    panel: SetAccountLifecycleStagePanelComponent,
    activityDisplayService: SetAccountLifecycleStageService,
    actionDisplayService: SetAccountLifecycleStageService,
    icon: 'assessment',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-set-account-lifecycle-stage',
    name: 'AUTOMATIONS.EDITOR.TASKS.SET_ACCOUNT_LIFECYCLE_STAGE.LIFECYCLE_STAGE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
  },
  'TaskDefinition-add-tags-to-order': {
    panel: AddTagsToOrderPanelComponent,
    activityDisplayService: AddTagsToOrderService,
    actionDisplayService: AddTagsToOrderService,
    icon: 'label',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-add-tags-to-order',
    name: 'AUTOMATIONS.EDITOR.TASKS.ADD_TAGS_TO_ORDER.ADD_TAGS',
    categories: [WorkflowTaskCategory.SalesOrders, WorkflowTaskCategory.Tags],
  },
  'TaskDefinition-remove-tags-from-order': {
    panel: RemoveTagsFromOrderPanelComponent,
    activityDisplayService: RemoveTagsFromOrderService,
    actionDisplayService: RemoveTagsFromOrderService,
    icon: 'label_off',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-remove-tags-from-order',
    name: 'AUTOMATIONS.EDITOR.TASKS.REMOVE_TAGS_FROM_ORDER.REMOVE_TAG',
    categories: [WorkflowTaskCategory.SalesOrders, WorkflowTaskCategory.Tags],
  },
  'TaskDefinition-decline-sales-order': {
    panel: DeclineSalesOrderPanelComponent,
    activityDisplayService: DeclineSalesOrderService,
    actionDisplayService: DeclineSalesOrderService,
    icon: 'cancel',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-decline-sales-order',
    name: 'AUTOMATIONS.EDITOR.TASKS.DECLINE_SALES_ORDER.DECLINE_ORDER',
    categories: [WorkflowTaskCategory.SalesOrders],
  },
  'TaskDefinition-activate-sales-order': {
    panel: ActivateSalesOrderPanelComponent,
    activityDisplayService: ActivateSalesOrderService,
    actionDisplayService: ActivateSalesOrderService,
    icon: 'shopping_cart',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-activate-sales-order',
    name: 'AUTOMATIONS.EDITOR.TASKS.ACTIVATE_SALES_ORDER.ACTIVATE_ORDER',
    categories: [WorkflowTaskCategory.SalesOrders],
  },
  'TaskDefinition-archive-sales-order': {
    panel: ArchiveSalesOrderPanelComponent,
    activityDisplayService: ArchiveSalesOrderService,
    actionDisplayService: ArchiveSalesOrderService,
    icon: 'archive',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-archive-sales-order',
    name: 'AUTOMATIONS.EDITOR.TASKS.ARCHIVE_SALES_ORDER.ARCHIVE_ORDER',
    categories: [WorkflowTaskCategory.SalesOrders],
  },
  'TaskDefinition-approve-sales-order': {
    panel: ApproveSalesOrderPanelComponent,
    activityDisplayService: ApproveSalesOrderService,
    actionDisplayService: ApproveSalesOrderService,
    icon: 'done',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-approve-sales-order',
    name: 'AUTOMATIONS.EDITOR.TASKS.APPROVE_SALES_ORDER.APPROVE_ORDER',
    categories: [WorkflowTaskCategory.SalesOrders],
  },
  'TaskDefinition-submit-order-for-admin-approval': {
    panel: SubmitOrderForAdminApprovalPanelComponent,
    activityDisplayService: SubmitOrderForAdminApprovalService,
    actionDisplayService: SubmitOrderForAdminApprovalService,
    icon: 'send',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-submit-order-for-admin-approval',
    name: 'AUTOMATIONS.EDITOR.TASKS.SUBMIT_ORDER_FOR_ADMIN_APPROVAL.SUBMIT_ORDER_FOR_ADMIN_APPROVAL',
    categories: [WorkflowTaskCategory.SalesOrders],
  },
  'TaskDefinition-send-email-notification': {
    panel: SendEmailNotificationPanelComponent,
    activityDisplayService: SendEmailNotificationService,
    actionDisplayService: SendEmailNotificationService,
    icon: 'email',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-send-email-notification',
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_EMAIL_NOTIFICATION.SEND_A_NOTIFICATION',
    categories: [WorkflowTaskCategory.Notifications],
  },
  'TaskDefinition-send-notifications-partner-admin': {
    panel: SendNotificationPartnerAdminPanelComponent,
    activityDisplayService: SendNotificationPartnerAdminService,
    actionDisplayService: SendNotificationPartnerAdminService,
    icon: 'notifications',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-send-notifications-partner-admin',
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_NOTIFICATIONS.TO_PARTNER_ADMIN',
    categories: [WorkflowTaskCategory.Notifications],
  },
  'TaskDefinition-send-notifications-fulfillment-agents': {
    panel: SendNotificationFulfillmentAgentPanelComponent,
    activityDisplayService: SendNotificationFulfillmentAgentService,
    actionDisplayService: SendNotificationFulfillmentAgentService,
    icon: 'notifications',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-send-notifications-fulfillment-agents',
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_NOTIFICATIONS.TO_FULFILLMENT_AGENT',
    categories: [WorkflowTaskCategory.Notifications],
  },
  'TaskDefinition-send-notifications-salespersons': {
    panel: SendNotificationSalespersonPanelComponent,
    activityDisplayService: SendNotificationSalespersonService,
    actionDisplayService: SendNotificationSalespersonService,
    icon: 'notifications',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-send-notifications-salespersons',
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_NOTIFICATIONS.TO_SALESPERSON',
    categories: [WorkflowTaskCategory.Notifications],
  },
  'TaskDefinition-send-notifications-customers': {
    panel: SendNotificationCustomersPanelComponent,
    activityDisplayService: SendNotificationCustomersService,
    actionDisplayService: SendNotificationCustomersService,
    icon: 'notifications',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-send-notifications-customers',
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_NOTIFICATIONS.ACTIONS.TO_ALL_ACCOUNT_USERS',
    categories: [WorkflowTaskCategory.Notifications],
  },
  'TaskDefinition-send-notifications-user': {
    panel: SendNotificationUserPanelComponent,
    activityDisplayService: SendNotificationUserService,
    actionDisplayService: SendNotificationUserService,
    icon: 'notifications',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-send-notifications-user',
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_NOTIFICATIONS.TO_USER',
    categories: [WorkflowTaskCategory.Notifications],
  },
  'TaskDefinition-send-notifications-to-smbs': {
    panel: SendNotificationsToSmbsPanelComponent,
    activityDisplayService: SendNotificationsToSmbsService,
    actionDisplayService: SendNotificationsToSmbsService,
    icon: 'notifications',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-send-notifications-to-smbs',
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_NOTIFICATIONS.SMB.NOTIFY_A_USER',
    categories: [WorkflowTaskCategory.Notifications],
  },
  'TaskDefinition-add-account-group-to-list': {
    panel: AddToListPanelComponent,
    activityDisplayService: AddToListService,
    actionDisplayService: AddToListService,
    icon: 'list',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-add-account-group-to-list',
    name: 'AUTOMATIONS.EDITOR.TASKS.ADD_TO_LIST.ADD_TO_LIST',
    categories: [WorkflowTaskCategory.Lists],
  },
  'TaskDefinition-update-company': {
    panel: UpdateCompanyPanelComponent,
    activityDisplayService: UpdateCompanyService,
    actionDisplayService: UpdateCompanyService,
    icon: 'location_city',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-update-company',
    name: 'AUTOMATIONS.EDITOR.TASKS.UPDATE_COMPANY.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
  },
  'TaskDefinition-update-contact': {
    panel: UpdateContactPanelComponent,
    activityDisplayService: UpdateContactService,
    actionDisplayService: UpdateContactService,
    icon: 'person',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-update-contact',
    name: 'AUTOMATIONS.EDITOR.TASKS.UPDATE_CONTACT.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
  },
  'TaskDefinition-assign-owner-to-contact': {
    panel: AssignOwnerToContactPanelComponent,
    activityDisplayService: AssignOwnerToContactService,
    actionDisplayService: AssignOwnerToContactService,
    icon: 'person',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-assign-owner-to-contact',
    name: 'AUTOMATIONS.EDITOR.TASKS.ASSIGN_OWNER_CONTACT.TITLE',
    categories: [WorkflowTaskCategory.Contacts],
  },
  'TaskDefinition-assign-owner-to-company': {
    panel: AssignOwnerToCompanyPanelComponent,
    activityDisplayService: AssignOwnerToCompanyService,
    actionDisplayService: AssignOwnerToCompanyService,
    icon: 'location_city',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-assign-owner-to-company',
    name: 'AUTOMATIONS.EDITOR.TASKS.ASSIGN_OWNER_COMPANY.TITLE',
    categories: [WorkflowTaskCategory.Companies],
  },
  'TaskDefinition-update-contact-v2': {
    panel: ModifyCRMContactDataPanelComponent,
    activityDisplayService: UpdateContactService,
    actionDisplayService: UpdateContactService,
    icon: 'person',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-update-contact-v2',
    name: 'AUTOMATIONS.EDITOR.TASKS.UPDATE_CONTACT.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
  },
  'TaskDefinition-update-company-v2': {
    panel: ModifyCRMCompanyDataPanelComponent,
    activityDisplayService: UpdateCompanyService,
    actionDisplayService: UpdateCompanyService,
    icon: 'business',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-update-company-v2',
    name: 'AUTOMATIONS.EDITOR.TASKS.UPDATE_COMPANY.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
  },
  [UpdateCrmOpportunity]: {
    panel: UpdateCrmOpportunityPanelComponent,
    activityDisplayService: UpdateCrmOpportunityService,
    actionDisplayService: UpdateCrmOpportunityService,
    icon: 'monetization_on',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: UpdateCrmOpportunity,
    name: 'AUTOMATIONS.EDITOR.TASKS.UPDATE_OPPORTUNITY.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
  },
  'TaskDefinition-remove-account-group-from-list': {
    panel: RemoveFromListPanelComponent,
    activityDisplayService: RemoveFromListService,
    actionDisplayService: RemoveFromListService,
    icon: 'block',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-remove-account-group-from-list',
    name: 'AUTOMATIONS.EDITOR.TASKS.REMOVE_FROM_LIST.REMOVE_FROM_LIST',
    categories: [WorkflowTaskCategory.Lists],
  },
  'TaskDefinition-unassign-salespeople': {
    panel: UnassignSalespeoplePanelComponent,
    activityDisplayService: UnassignSalespeopleService,
    actionDisplayService: UnassignSalespeopleService,
    icon: 'person_add_disabled',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-unassign-salespeople',
    name: 'AUTOMATIONS.EDITOR.TASKS.UNASSIGN_SALESPEOPLE.UNASSIGN_SALESPEOPLE',
    categories: [WorkflowTaskCategory.Sales],
  },
  'TaskDefinition-create-project-in-task-manager': {
    panel: CreateProjectInTaskManagerPanelComponent,
    activityDisplayService: CreateProjectInTaskManagerService,
    actionDisplayService: CreateProjectInTaskManagerService,
    icon: 'group_work',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-create-project-in-task-manager',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_PROJECT_IN_TASK_MANAGER.CREATE_PROJECT_IN_TASK_MANAGER',
    categories: [WorkflowTaskCategory.Fulfillment],
  },
  'TaskDefinition-create-project-in-task-manager-for-order': {
    panel: CreateProjectInTaskManagerForOrderPanelComponent,
    activityDisplayService: CreateProjectInTaskManagerForOrderService,
    actionDisplayService: CreateProjectInTaskManagerForOrderService,
    icon: 'group_work',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-create-project-in-task-manager-for-order',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_PROJECT_IN_TASK_MANAGER_FOR_ORDER.CREATE_PROJECT_IN_TASK_MANAGER_FOR_ORDER',
    categories: [WorkflowTaskCategory.Fulfillment],
  },
  'TaskDefinition-create-opportunity': {
    panel: CreateOpportunityPanelComponent,
    activityDisplayService: CreateOpportunityService,
    actionDisplayService: CreateOpportunityService,
    icon: 'local_atm',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-create-opportunity',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_OPPORTUNITY.TITLE',
    categories: [WorkflowTaskCategory.Sales],
  },
  'TaskDefinition-create-proposal': {
    panel: CreateProposalPanelComponent,
    activityDisplayService: CreateProposalService,
    actionDisplayService: CreateProposalService,
    icon: 'request_page',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-create-proposal',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_PROPOSAL.TITLE',
    categories: [WorkflowTaskCategory.Sales],
  },
  'TaskDefinition-log-sales-activity': {
    panel: LogSalesActivityPanelComponent,
    activityDisplayService: LogSalesActivityService,
    actionDisplayService: LogSalesActivityService,
    icon: 'local_atm',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-log-sales-activity',
    name: 'AUTOMATIONS.EDITOR.TASKS.LOG_SALES_ACTIVITY.TITLE',
    categories: [WorkflowTaskCategory.Sales],
  },
  'TaskDefinition-send-public-system-message': {
    panel: SendPublicSystemMessagePanelComponent,
    activityDisplayService: SendPublicSystemMessageService,
    actionDisplayService: SendPublicSystemMessageService,
    icon: 'question_answer',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-send-public-system-message',
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_PUBLIC_SYSTEM_MESSAGE.ACTION',
    categories: [WorkflowTaskCategory.Inbox],
  },
  'TaskDefinition-send-public-system-message-internal': {
    panel: SendPublicSystemMessageInternalPanelComponent,
    activityDisplayService: SendPublicSystemMessageInternalService,
    actionDisplayService: SendPublicSystemMessageInternalService,
    icon: 'question_answer',
    iconColor: INTERNAL_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-send-public-system-message-internal',
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_PUBLIC_SYSTEM_MESSAGE_INTERNAL.ACTION',
    categories: [WorkflowTaskCategory.Internal],
  },
  'TaskDefinition-send-inbox-message': {
    panel: SendInboxMessagePanelComponent,
    activityDisplayService: SendInboxMessageService,
    actionDisplayService: SendInboxMessageService,
    icon: 'question_answer',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-send-inbox-message',
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_INBOX_MESSAGE.ACTION',
    categories: [WorkflowTaskCategory.Inbox],
  },
  'TaskDefinition-send-inbox-message-internal': {
    panel: SendInboxMessageInternalPanelComponent,
    activityDisplayService: SendInboxMessageInternalService,
    actionDisplayService: SendInboxMessageInternalService,
    icon: 'question_answer',
    iconColor: INTERNAL_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-send-inbox-message-internal',
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_INBOX_MESSAGE_INTERNAL.ACTION',
    categories: [WorkflowTaskCategory.Internal],
  },
  'TaskDefinition-update-billing-contract-internal': {
    panel: UpdateBillingContractPanelComponent,
    activityDisplayService: UpdateBillingContractService,
    actionDisplayService: UpdateBillingContractService,
    icon: 'api',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-update-billing-contract-internal',
    name: 'AUTOMATIONS.EDITOR.TASKS.UPDATE_BILLING_CONTRACT.ACTION',
    categories: [WorkflowTaskCategory.Internal],
  },
  'TaskDefinition-call-endpoint': {
    panel: CallEndpointPanelComponent,
    activityDisplayService: CallEndpointService,
    actionDisplayService: CallEndpointService,
    icon: 'api',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-call-endpoint',
    name: 'AUTOMATIONS.EDITOR.TASKS.TRIGGER_WEBHOOK.TRIGGER_WEBHOOK',
    categories: [WorkflowTaskCategory.Advanced],
    upgradeSubscriptionTitle: 'AUTOMATIONS.EDITOR.TASKS.TRIGGER_WEBHOOK.UPGRADE_SUBSCRIPTION_TITLE',
    upgradeSubscriptionDescription: 'AUTOMATIONS.EDITOR.TASKS.TRIGGER_WEBHOOK.UPGRADE_SUBSCRIPTION_DESCRIPTION',
    supportsCustomOutputParameters: true,
  },
  [ModifyCustomAccountDataTaskDefinitionId]: {
    panel: ModifyAuxiliaryAccountDataPanelComponent,
    activityDisplayService: ModifyAuxiliaryDataService,
    actionDisplayService: ModifyAuxiliaryDataService,
    icon: 'text_snippet',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: ModifyCustomAccountDataTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.MODIFY_CUSTOM_FIELD_DATA.ACCOUNT_DATA.ACTION_TITLE',
    categories: [WorkflowTaskCategory.Advanced],
  },
  [ModifyCustomProductDataTaskDefinitionId]: {
    panel: ModifyAuxiliaryProductDataPanelComponent,
    activityDisplayService: ModifyAuxiliaryDataService,
    actionDisplayService: ModifyAuxiliaryDataService,
    icon: 'text_snippet',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: ModifyCustomProductDataTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.MODIFY_CUSTOM_FIELD_DATA.PRODUCT_DATA.ACTION_TITLE',
    categories: [WorkflowTaskCategory.Advanced],
  },
  [ModifyCustomSalesOrderTaskDefinitionId]: {
    panel: ModifyAuxiliarySalesOrderDataPanelComponent,
    activityDisplayService: ModifyAuxiliaryDataService,
    actionDisplayService: ModifyAuxiliaryDataService,
    icon: 'text_snippet',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: ModifyCustomSalesOrderTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.MODIFY_CUSTOM_FIELD_DATA.SALES_ORDER.ACTION_TITLE',
    categories: [WorkflowTaskCategory.Advanced],
  },
  [ModifyCustomUserDataTaskDefinitionId]: {
    panel: ModifyAuxiliaryUserDataPanelComponent,
    activityDisplayService: ModifyAuxiliaryDataService,
    actionDisplayService: ModifyAuxiliaryDataService,
    icon: 'text_snippet',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: ModifyCustomUserDataTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.MODIFY_CUSTOM_FIELD_DATA.USER_DATA.ACTION_TITLE',
    categories: [WorkflowTaskCategory.Advanced],
  },
  'TaskDefinition-write-note-contact': {
    panel: CreateCrmNotePanelComponent,
    activityDisplayService: CreateCrmNoteService,
    actionDisplayService: CreateCrmNoteService,
    icon: 'notes',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-write-note-contact',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_NOTE.CONTACT_NAME',
    categories: [WorkflowTaskCategory.Advanced],
  },
  'TaskDefinition-write-note-company': {
    panel: CreateCrmNotePanelComponent,
    activityDisplayService: CreateCrmNoteService,
    actionDisplayService: CreateCrmNoteService,
    icon: 'notes',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-write-note-company',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_NOTE.COMPANY_NAME',
    categories: [WorkflowTaskCategory.Advanced],
  },
  'TaskDefinition-create-call-activity-company': {
    panel: CreateCrmCallActivityPanelComponent,
    activityDisplayService: CreateCrmCallActivityService,
    actionDisplayService: CreateCrmCallActivityService,
    icon: 'local_phone',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-create-call-activity-company',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_CALL_ACTIVITY.COMPANY_NAME',
    categories: [WorkflowTaskCategory.Advanced],
  },
  'TaskDefinition-create-call-activity-contact': {
    panel: CreateCrmCallActivityPanelComponent,
    activityDisplayService: CreateCrmCallActivityService,
    actionDisplayService: CreateCrmCallActivityService,
    icon: 'local_phone',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-create-call-activity-contact',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_CALL_ACTIVITY.CONTACT_NAME',
    categories: [WorkflowTaskCategory.Advanced],
  },
  'TaskDefinition-modify-call-activity-company': {
    panel: ModifyCrmCallActivityPanelComponent,
    activityDisplayService: ModifyCrmCallActivityService,
    actionDisplayService: ModifyCrmCallActivityService,
    icon: 'local_phone',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-modify-call-activity-company',
    name: 'AUTOMATIONS.EDITOR.TASKS.MODIFY_CALL_ACTIVITY.COMPANY_NAME',
    categories: [WorkflowTaskCategory.Advanced],
  },
  // Building blocks below
  // 'delay'
  'TaskDefinition-25c199e4-0c25-4680-9a05-1c9e838c5653': {
    panel: DelayPanelComponent,
    activityDisplayService: DelayService,
    actionDisplayService: DelayService,
    icon: 'schedule',
    iconColor: BUILDING_BLOCK_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-25c199e4-0c25-4680-9a05-1c9e838c5653',
    name: 'AUTOMATIONS.EDITOR.TASKS.DELAY.DELAY',
    categories: [WorkflowTaskCategory.Delays],
    hasSleep: true,
  },
  // 'delay-until-event'
  [DelayUntilEventTaskDefinitionId]: {
    panel: DelayUntilEventPanelComponent,
    activityDisplayService: DelayUntilEventService,
    actionDisplayService: DelayUntilEventService,
    icon: 'update',
    iconColor: BUILDING_BLOCK_ACTION_COLOR,
    taskDefinitionId: DelayUntilEventTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.DELAY_UNTIL_EVENT.DELAY_UNTIL_EVENT',
    categories: [WorkflowTaskCategory.Delays],
    hasSleep: true,
  },
  // 'a-b branch'
  [ABBranchTaskDefinitionId]: {
    panel: ABBranchPanelComponent,
    activityDisplayService: ABBranchService,
    actionDisplayService: ABBranchService,
    icon: 'call_split',
    iconColor: BUILDING_BLOCK_ACTION_COLOR,
    taskDefinitionId: ABBranchTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.AB_BRANCH.TITLE',
    categories: [WorkflowTaskCategory.Workflow],
  },
  // 'Rate filter'
  [RateLimitTaskDefinitionId]: {
    panel: RateFilterPanelComponent,
    activityDisplayService: RateFilterService,
    actionDisplayService: RateFilterService,
    icon: 'device_hub',
    iconColor: BUILDING_BLOCK_ACTION_COLOR,
    taskDefinitionId: RateLimitTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.RATE_FILTER.TITLE',
    categories: [WorkflowTaskCategory.Conditions],
  },
  [EndTaskDefinitionId]: {
    panel: EndActionPanelComponent,
    activityDisplayService: EndActionService,
    actionDisplayService: EndActionService,
    icon: 'done',
    iconColor: BUILDING_BLOCK_ACTION_COLOR,
    taskDefinitionId: EndTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.END_ACTION.TITLE',
    categories: [WorkflowTaskCategory.Workflow],
  },
  // 'Jump to step'
  [JumpTaskDefinitionId]: {
    panel: JumpPanelComponent,
    activityDisplayService: JumpService,
    actionDisplayService: JumpService,
    icon: 'redo',
    iconColor: BUILDING_BLOCK_ACTION_COLOR,
    taskDefinitionId: JumpTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.JUMP.TITLE',
    categories: [WorkflowTaskCategory.Conditions],
  },
  // 'Close all Opportunities'
  [CloseAllOpportunitiesDefinitionId]: {
    panel: CloseAllOpportunitiesPanelComponent,
    activityDisplayService: CloseAllOpportunitiesService,
    actionDisplayService: CloseAllOpportunitiesService,
    icon: 'money_off',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: CloseAllOpportunitiesDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.CLOSE_ALL_OPPORTUNITIES.TITLE',
    categories: [WorkflowTaskCategory.Sales],
  },
  // 'Close Opportunity'
  [CloseOpportunityDefinitionId]: {
    panel: CloseOpportunityPanelComponent,
    activityDisplayService: CloseOpportunityService,
    actionDisplayService: CloseOpportunityService,
    icon: 'money_off',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: CloseOpportunityDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.CLOSE_OPPORTUNITY.TITLE',
    categories: [WorkflowTaskCategory.Sales],
  },
  // 'Send AI prompt'
  [SendAiPromptId]: {
    panel: SendAiPromptPanelComponent,
    activityDisplayService: SendAiPromptService,
    actionDisplayService: SendAiPromptService,
    icon: 'chat',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: SendAiPromptId,
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_AI_PROMPT.TITLE',
    categories: [WorkflowTaskCategory.Advanced],
  },
  // Query Opportunities
  'TaskDefinition-query-opportunities': {
    panel: QueryOpportunitiesPanelComponent,
    activityDisplayService: QueryOpportunitiesService,
    actionDisplayService: QueryOpportunitiesService,
    icon: 'input',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-query-opportunities',
    name: 'AUTOMATIONS.EDITOR.TASKS.QUERY_OPPORTUNITIES.GET_OPPORTUNITY',
    categories: [WorkflowTaskCategory.Advanced],
  },
  'TaskDefinition-change-opportunity-pipeline-stage': {
    panel: ChangeOpportunityPipelineStagePanelComponent,
    activityDisplayService: ChangeOpportunityPipelineStageService,
    actionDisplayService: ChangeOpportunityPipelineStageService,
    icon: 'local_atm',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-change-opportunity-pipeline-stage',
    name: 'AUTOMATIONS.EDITOR.TASKS.CHANGE_OPPORTUNITY_PIPELINE_STAGE.UPDATE_OPPORTUNITY_STAGE',
    categories: [WorkflowTaskCategory.Advanced],
  },
  'TaskDefinition-reopen-opportunity': {
    panel: ReopenOpportunityPanelComponent,
    activityDisplayService: ReopenOpportunityService,
    actionDisplayService: ReopenOpportunityService,
    icon: 'local_atm',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-reopen-opportunity',
    name: 'AUTOMATIONS.EDITOR.TASKS.REOPEN_OPPORTUNITY.REOPEN_OPPORTUNITY',
    categories: [WorkflowTaskCategory.Sales],
  },
  'TaskDefinition-create-snapshot-report': {
    panel: CreateSnapshotReportPanelComponent,
    activityDisplayService: CreateSnapshotReportService,
    actionDisplayService: CreateSnapshotReportService,
    icon: 'note_add',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-create-snapshot-report',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_SNAPSHOT_REPORT.TITLE',
    categories: [WorkflowTaskCategory.Sales],
  },
  [PauseOrCancelTaskManagerAccountId]: {
    panel: PauseOrCancelTaskManagerAccountPanelComponent,
    activityDisplayService: PauseOrCancelTaskManagerAccountService,
    actionDisplayService: PauseOrCancelTaskManagerAccountService,
    icon: 'pause_circle_outline',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: PauseOrCancelTaskManagerAccountId,
    name: 'AUTOMATIONS.EDITOR.TASKS.PAUSE_OR_CANCEL_TASK_MANAGER_ACCOUNT.GENERAL.TITLE',
    categories: [WorkflowTaskCategory.Fulfillment],
  },
  'TaskDefinition-provision-task-manager-account': {
    panel: CreateTaskManagerAccountPanelComponent,
    activityDisplayService: CreateTaskManagerAccountService,
    actionDisplayService: CreateTaskManagerAccountService,
    icon: 'domain_add',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-provision-task-manager-account',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_TASK_MANAGER_ACCOUNT.GENERAL.TITLE',
    categories: [WorkflowTaskCategory.Fulfillment],
  },
  'TaskDefinition-send-review-request': {
    panel: SendReviewRequestPanelComponent,
    activityDisplayService: SendReviewRequestService,
    actionDisplayService: SendReviewRequestService,
    icon: 'grade',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-send-review-request',
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.TITLE',
    categories: [WorkflowTaskCategory.CampaignsAndEmails],
    panelDescription: 'AUTOMATIONS.EDITOR.TASKS.SEND_REVIEW_REQUEST.DESCRIPTION',
  },
  'TaskDefinition-get-company-from-account-group': {
    panel: NoUserInputPanelComponent,
    activityDisplayService: GetCompanyFromAccountGroupService,
    actionDisplayService: GetCompanyFromAccountGroupService,
    icon: 'input',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-get-company-from-account-group',
    name: 'AUTOMATIONS.EDITOR.TASKS.GET_COMPANY_FROM_ACCOUNT_GROUP.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
    panelDescription: 'AUTOMATIONS.EDITOR.TASKS.GET_COMPANY_FROM_ACCOUNT_GROUP.DESCRIPTION',
  },
  'TaskDefinition-get-account-from-company': {
    panel: NoUserInputPanelComponent,
    activityDisplayService: GetAccountFromCompanyService,
    actionDisplayService: GetAccountFromCompanyService,
    icon: 'input',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-get-account-from-company',
    name: 'AUTOMATIONS.EDITOR.TASKS.GET_ACCOUNT_FROM_COMPANY.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
    panelDescription: 'AUTOMATIONS.EDITOR.TASKS.GET_ACCOUNT_FROM_COMPANY.DESCRIPTION',
  },
  'TaskDefinition-create-company': {
    panel: CreateCompanyPanelComponent,
    activityDisplayService: CreateCompanyService,
    actionDisplayService: CreateCompanyService,
    icon: 'location_city',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-create-company',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_COMPANY.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
  },
  'TaskDefinition-create-contact': {
    panel: CreateContactPanelComponent,
    activityDisplayService: CreateContactService,
    actionDisplayService: CreateContactService,
    icon: 'person',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-create-contact',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_CONTACT.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
  },
  'TaskDefinition-lookup-contact': {
    panel: LookupContactPanelComponent,
    activityDisplayService: LookupContactService,
    actionDisplayService: LookupContactService,
    icon: 'person_search',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-lookup-contact',
    name: 'AUTOMATIONS.EDITOR.TASKS.LOOKUP_CONTACT.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
  },
  'TaskDefinition-outgoing-webhook': {
    panel: OutgoingWebhookPanelComponent,
    activityDisplayService: OutgoingWebhookService,
    actionDisplayService: OutgoingWebhookService,
    icon: 'webhook',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-outgoing-webhook',
    name: 'AUTOMATIONS.EDITOR.TASKS.OUTGOING_WEBHOOK.OUTGOING_WEBHOOK',
    categories: [WorkflowTaskCategory.Advanced],
    upgradeSubscriptionTitle: 'AUTOMATIONS.EDITOR.TASKS.TRIGGER_WEBHOOK.UPGRADE_SUBSCRIPTION_TITLE',
    upgradeSubscriptionDescription: 'AUTOMATIONS.EDITOR.TASKS.TRIGGER_WEBHOOK.UPGRADE_SUBSCRIPTION_DESCRIPTION',
    supportsCustomOutputParameters: true,
  },
  [SendSMSViaInboxTaskDefinitionId]: {
    panel: SendInboxOnChannelPanelComponent,
    activityDisplayService: SendInboxOnChannelService,
    actionDisplayService: SendInboxOnChannelService,
    icon: 'sms',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: SendSMSViaInboxTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_SMS_INBOX.TITLE',
    categories: [WorkflowTaskCategory.Inbox],
  },
  [SendEventViaInboxTaskDefinitionId]: {
    panel: SendInboxOnChannelPanelComponent,
    activityDisplayService: SendInboxOnChannelService,
    actionDisplayService: SendInboxOnChannelService,
    icon: 'sms',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: SendEventViaInboxTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_EVENT_INBOX.TITLE',
    categories: [WorkflowTaskCategory.Inbox],
  },
  [SendEmailViaInboxTaskDefinitionId]: {
    panel: SendInboxOnChannelPanelComponent,
    activityDisplayService: SendInboxOnChannelService,
    actionDisplayService: SendInboxOnChannelService,
    icon: 'email',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: SendEmailViaInboxTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.SEND_EMAIL_INBOX.TITLE',
    categories: [WorkflowTaskCategory.Inbox],
  },
  'TaskDefinition-find-user': {
    panel: GetMyTeamMemberPanelComponent,
    activityDisplayService: GetMyTeamMemberService,
    actionDisplayService: GetMyTeamMemberService,
    icon: 'input',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-find-user',
    name: 'AUTOMATIONS.EDITOR.TASKS.GET_MY_TEAM_MEMBER.TITLE',
    categories: [WorkflowTaskCategory.Users],
  },
  'TaskDefinition-get-my-business-info': {
    panel: GetMyBusinessInfoPanelComponent,
    activityDisplayService: GetMyBusinessInfoService,
    actionDisplayService: GetMyBusinessInfoService,
    icon: 'input',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-get-my-business-info',
    name: 'AUTOMATIONS.EDITOR.TASKS.GET_MY_BUSINESS_INFO.TITLE',
    categories: [WorkflowTaskCategory.Advanced],
  },
  'TaskDefinition-create-user-from-contact': {
    panel: NoUserInputPanelComponent,
    activityDisplayService: CreateUserFromContactService,
    actionDisplayService: CreateUserFromContactService,
    icon: 'list',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-create-user-from-contact',
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_USER_FROM_CONTACT.TITLE',
    categories: [WorkflowTaskCategory.Users],
  },
  'TaskDefinition-string-operations': {
    panel: StringOperationsPanelComponent,
    activityDisplayService: StringOperationsService,
    actionDisplayService: StringOperationsService,
    icon: 'text_format',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-string-operations',
    name: 'AUTOMATIONS.EDITOR.TASKS.STRING_FORMATTER.TEXT_TITLE',
    categories: [WorkflowTaskCategory.Advanced],
  },
  'TaskDefinition-get-connection-business-data': {
    panel: NoUserInputPanelComponent,
    activityDisplayService: GetConnectionBusinessDataService,
    actionDisplayService: GetConnectionBusinessDataService,
    icon: 'list',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-get-connection-business-data',
    name: 'AUTOMATIONS.EDITOR.TASKS.GET_CONNECTION_BUSINESS_DATA.TITLE',
    categories: [WorkflowTaskCategory.Integrations],
    panelDescription: 'AUTOMATIONS.EDITOR.TASKS.GET_CONNECTION_BUSINESS_DATA.DESCRIPTION',
  },
  [associateComapyWithContactTaskDefinitionId]: {
    panel: AssociateCompanyContactPanelComponent,
    activityDisplayService: AssociateCompanyContactService,
    actionDisplayService: AssociateCompanyContactService,
    icon: 'link',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: associateComapyWithContactTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.ASSOCIATE_COMPANY_WITH_CONTACT.TITLE',
    categories: [WorkflowTaskCategory.Advanced],
  },
  'TaskDefinition-get-order-data': {
    panel: NoUserInputPanelComponent,
    activityDisplayService: GetOrderDataService,
    actionDisplayService: GetOrderDataService,
    icon: 'input',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-get-order-data',
    name: 'AUTOMATIONS.EDITOR.TASKS.GET_ORDER_DATA.TITLE',
    panelDescription: 'AUTOMATIONS.EDITOR.TASKS.GET_ORDER_DATA.DESCRIPTION',
    categories: [WorkflowTaskCategory.SalesOrders],
  },
  'TaskDefinition-get-multi-auxiliary-product-data': {
    panel: GetProductsCustomFieldDataPanelComponent,
    activityDisplayService: GetProductsCustomFieldDataService,
    actionDisplayService: GetProductsCustomFieldDataService,
    icon: 'input',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-get-multi-auxiliary-product-data',
    name: 'AUTOMATIONS.EDITOR.TASKS.GET_MULTI_PRODUCT_DATA.TITLE',
    categories: [WorkflowTaskCategory.Products],
  },
  [GetConnectionDataVendorTaskDefinitionId]: {
    panel: GetConnectionDataVendorPanelComponent,
    activityDisplayService: GetConnectionDataVendorService,
    actionDisplayService: GetConnectionDataVendorService,
    icon: 'list',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: GetConnectionDataVendorTaskDefinitionId,
    name: 'AUTOMATIONS.EDITOR.TASKS.GET_CONNECTION_DATA_VENDOR.TITLE',
    categories: [WorkflowTaskCategory.Integrations],
  },
  [GetContactFromCrmOpportunity]: {
    panel: GetAssociatedCrmObjectPanelComponent,
    activityDisplayService: GetAssociatedCrmObjectService,
    actionDisplayService: GetAssociatedCrmObjectService,
    icon: 'input',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: GetContactFromCrmOpportunity,
    name: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.CONTACT_OPPORTUNITY.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
  },
  [GetCrmOpportunityFromCompany]: {
    panel: GetAssociatedCrmObjectPanelComponent,
    activityDisplayService: GetAssociatedCrmObjectService,
    actionDisplayService: GetAssociatedCrmObjectService,
    icon: 'input',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: GetCrmOpportunityFromCompany,
    name: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.OPPORTUNITY_COMPANY.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
  },
  [GetCrmOpportunityFromContact]: {
    panel: GetAssociatedCrmObjectPanelComponent,
    activityDisplayService: GetAssociatedCrmObjectService,
    actionDisplayService: GetAssociatedCrmObjectService,
    icon: 'input',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: GetCrmOpportunityFromContact,
    name: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.OPPORTUNITY_CONTACT.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
  },
  [GetContactFromCrmCustomObject]: {
    panel: GetAssociatedCrmObjectPanelComponent,
    activityDisplayService: GetAssociatedCrmObjectService,
    actionDisplayService: GetAssociatedCrmObjectService,
    icon: 'input',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: GetContactFromCrmCustomObject,
    name: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.CONTACT_CUSTOM_OBJECT.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
    featureFlag: CRM_CUSTOM_OBJECT_FEATURE_FLAG,
  },
  [GetCompanyFromCrmCustomObject]: {
    panel: GetAssociatedCrmObjectPanelComponent,
    activityDisplayService: GetAssociatedCrmObjectService,
    actionDisplayService: GetAssociatedCrmObjectService,
    icon: 'input',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: GetCompanyFromCrmCustomObject,
    name: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.COMPANY_CUSTOM_OBJECT.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
    featureFlag: CRM_CUSTOM_OBJECT_FEATURE_FLAG,
  },
  [GetOpportunityFromCrmCustomObject]: {
    panel: GetAssociatedCrmObjectPanelComponent,
    activityDisplayService: GetAssociatedCrmObjectService,
    actionDisplayService: GetAssociatedCrmObjectService,
    icon: 'input',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: GetOpportunityFromCrmCustomObject,
    name: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.OPPORTUNITY_CUSTOM_OBJECT.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
    featureFlag: BCC_CRM_CUSTOM_OBJECT_FEATURE_FLAG,
  },
  [CreateCRMOpportunity]: {
    panel: CreateCrmOpportunityPanelComponent,
    activityDisplayService: CreateCrmOpportunityService,
    actionDisplayService: CreateCrmOpportunityService,
    icon: 'monetization_on',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: CreateCRMOpportunity,
    name: 'AUTOMATIONS.EDITOR.TASKS.CREATE_CRM_OPPORTUNITY.TITLE',
    categories: [WorkflowTaskCategory.AccountsAndUsers],
  },
  'TaskDefinition-charge-invoice': {
    panel: ChargeInvoicePanelComponent,
    activityDisplayService: ChargeInvoiceService,
    actionDisplayService: ChargeInvoiceService,
    icon: 'monetization_on',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-charge-invoice',
    name: 'AUTOMATIONS.EDITOR.TASKS.CHARGE_INVOICE.TITLE',
    categories: [WorkflowTaskCategory.Sales],
  },
  'TaskDefinition-ai-assistant-action': {
    panel: AiAssistantActionPanelComponent,
    activityDisplayService: AiAssistantActionService,
    actionDisplayService: AiAssistantActionService,
    icon: 'galaxy-ai-icon-monochrome',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-ai-assistant-action',
    name: 'AUTOMATIONS.EDITOR.TASKS.AI_ASSISTANT_ACTION.TITLE',
    categories: [WorkflowTaskCategory.Advanced],
    featureFlag: AUTOMATION_AI_ASSISTANT_FEATURE_FLAG,
  },
  'TaskDefinition-add-contact-to-kixie-powerlist': {
    panel: AddContactToKixiePowerlistPanelComponent,
    activityDisplayService: AddContactToKixiePowerlistService,
    actionDisplayService: AddContactToKixiePowerlistService,
    icon: 'list',
    iconColor: DEFAULT_ACTION_COLOR,
    taskDefinitionId: 'TaskDefinition-add-contact-to-kixie-powerlist',
    name: 'AUTOMATIONS.EDITOR.TASKS.ADD_CONTACT_TO_KIXIE_POWERLIST.TITLE',
    categories: [WorkflowTaskCategory.Integrations],
  },
  /*
   * When adding more If-else-branch task definitions, don't forget to also add the branch options in the branch-options.ts file the same
   * as adding the task definition below
   */
  // if-else-branch
  [IfElseBranchTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,

  // The following task panels need to exist to be able to open specific filters since the if-else branch panel will always save steps in terms of their specific task definition ids
  // account-data
  [IfElseBranchAccountDataTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  // user-data
  [IfElseBranchUserDataTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  // partner-data
  [IfElseBranchPartnerDataTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  // user-internal-data
  [IfElseBranchUserInternalDataTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  // shopping-cart-data
  [IfElseBranchShoppingCartDataTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  [IfElseBranchBusinessAppDataTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  [IfElseBranchAccountInternalDataTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  // order data
  [IfElseBranchOrderDataTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  // opportunities data
  [IfElseBranchOpportunityFilterTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  [IfElseBranchOpportunityDataTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  // CRM Task data
  [IfElseBranchCrmTaskDataTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  // CRM Note data
  [IfElseBranchCrmNoteDataTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  // CRM Email data
  [IfElseBranchCrmEmailDataTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  // CRM Meeting data
  [IfElseBranchCrmMeetingDataTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  // CRM Call data
  [IfElseBranchCrmCallDataTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  // Product price
  [ProductPriceFilterTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  [ContactFilterTaskDefinitionId]: {
    ...IF_ELSE_BRANCH_PANEL,
  },
  [CompanyFilterTaskDefinitionId]: {
    ...IF_ELSE_BRANCH_PANEL,
  },
  [CustomObjectFilterTaskDefinitionId]: {
    ...IF_ELSE_BRANCH_PANEL,
  },
  [OpportunityFilterTaskDefinitionId]: {
    ...IF_ELSE_BRANCH_PANEL,
  },
  [CustomAccountFilterTaskDefinitionId]: {
    ...IF_ELSE_BRANCH_PANEL,
  },
  [CustomUserFilterTaskDefinitionId]: {
    ...IF_ELSE_BRANCH_PANEL,
  },
  [CustomOrderFilterTaskDefinitionId]: {
    ...IF_ELSE_BRANCH_PANEL,
  },
  [CustomProductFilterTaskDefinitionId]: {
    ...IF_ELSE_BRANCH_PANEL,
  },
  [InvoiceFilterTaskDefinitionId]: {
    ...IF_ELSE_BRANCH_PANEL,
  },
  // workflow data (parameter passing)
  [IfElseBranchWorkflowStepTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
  [IfElseIntegrationConnectionFilterTaskDefinitionId]: IF_ELSE_BRANCH_PANEL,
};
