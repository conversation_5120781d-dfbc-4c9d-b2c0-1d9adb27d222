import { TestScheduler } from 'rxjs/testing';
import { MockTranslateService, MockTranslateServiceEnglishValueReturn } from '../../../../translate.mock';
import { InboxMessageSentService } from './inbox-message-sent.service';

import { TestBed } from '@angular/core/testing';
import { DomSanitizer } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { WorkflowStepInterface } from '@vendasta/automata';
import { AutomationErrorHandlerService } from '../../../services';

let scheduler: TestScheduler;
let mockTranslateService: any;
let errorHandlerService: AutomationErrorHandlerService;
let svc: InboxMessageSentService;

describe('InboxMessageSentService', () => {
  const activity = {
    data: `{"trigger_starts_option":"inbox message is sent or received"}`,
  } as WorkflowStepInterface;

  beforeEach(() => {
    scheduler = new TestScheduler((a, b) => expect(a).toEqual(b));
    errorHandlerService = {
      setValidationError: jest.fn(() => null),
    } as unknown as AutomationErrorHandlerService;
    TestBed.configureTestingModule({
      providers: [
        {
          provide: TranslateService,
          useClass: MockTranslateService,
        },
        DomSanitizer,
        {
          provide: AutomationErrorHandlerService,
          useValue: errorHandlerService,
        },
      ],
    }).compileComponents();
    mockTranslateService = new MockTranslateServiceEnglishValueReturn();
    TestBed.runInInjectionContext(() => {
      svc = new InboxMessageSentService(mockTranslateService);
    });
  });

  afterEach(() => {
    scheduler.flush();
  });

  describe('getActivityTitle', () => {
    it('should return entered the automation string', () => {
      const obvs$ = svc.getActivityTitle('', null, null);
      scheduler.expectObservable(obvs$).toBe('(x|)', { x: 'Entered automation' });
    });
  });

  describe('getActivitySubtitle', () => {
    it('should return the activity subtitle string', () => {
      const obvs$ = svc.getActivitySubtitle('ABC', null, null);
      scheduler
        .expectObservable(obvs$)
        .toBe('(x|)', { x: 'An Inbox message was sent to (or received from) an account' });
    });
  });

  describe('getActionSubtitle', () => {
    it('should return the when string', () => {
      const obvs$ = svc.getActionSubtitle('ABC', activity, null);
      scheduler.expectObservable(obvs$).toBe('(x|)', {
        x: 'When an <strong>inbox message is sent or received</strong> in a conversation with an account',
      });
    });
  });
});
