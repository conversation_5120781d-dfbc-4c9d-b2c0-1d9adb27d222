import { inject, Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ActivityInterface, WorkflowStepInterface } from '@vendasta/automata';
import { catchError, Observable, switchMap } from 'rxjs';
import { ActivityTableEntry, AutomationDisplayService, NodeData } from '../../../common/automation-display.service';
import { CRMCompanyFormSubmitted, CRMContactFormSubmitted } from '../../../../component-loader/constants';
import { ObjectType } from '@galaxy/crm/static';
import { CRMFormsSelectorService } from '../../../../../data-services/crm-forms-selector.service';

interface DisplayConfig {
  objectType: ObjectType;
  action: string;
  actionWithData: string;
  activity: string;
  description: string;
}

export const FormTaskConfigMap = new Map<string, DisplayConfig>([
  [
    CRMContactFormSubmitted,
    {
      objectType: 'Contact',
      action: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.ACTION',
      actionWithData: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.ACTION_WITH_DATA',
      activity: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.ACTIVITY',
      description: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.DESCRIPTION',
    },
  ],
  [
    CRMCompanyFormSubmitted,
    {
      objectType: 'Company',
      action: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_FORM_SUBMITTED.ACTION',
      actionWithData: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_FORM_SUBMITTED.ACTION_WITH_DATA',
      activity: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_FORM_SUBMITTED.ACTIVITY',
      description: 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_COMPANY_FORM_SUBMITTED.DESCRIPTION',
    },
  ],
]);

@Injectable({
  providedIn: 'root',
})
export class CrmObjectFormSelectorDisplayService extends AutomationDisplayService {
  private readonly formsSelectorService = inject(CRMFormsSelectorService);

  constructor(protected readonly translateService: TranslateService) {
    super();
  }

  private getDisplayConfig(step: WorkflowStepInterface): DisplayConfig | undefined {
    return FormTaskConfigMap.get(
      step?.waitForTriggerTaskDefinitionId ?? step?.taskDefinitionId ?? CRMContactFormSubmitted,
    );
  }

  /**
   * This is the text shown in the automation editor.
   */
  getAutomationNodeData(
    _namespace: string,
    step: WorkflowStepInterface,
    _isWaitUntilNode: boolean,
  ): Observable<NodeData> {
    const data = JSON.parse(step?.data ?? '{}');
    const formConfigId = data.user_provided_form_config_id ?? '';
    const displayConfig = this.getDisplayConfig(step);

    // Check if form config ID is missing and set validation error
    if (!formConfigId) {
      return this.nodeDisplayData(
        this.translateService.stream(
          displayConfig?.action ?? 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.ACTION',
        ),
        this.validationErrorsDetected(
          step,
          this.translateService.instant('AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.CONFIG_ERROR'),
          true,
        ),
      );
    }

    return this.formsSelectorService.get(formConfigId).pipe(
      switchMap((form) => {
        if (!form?.name) {
          return this.nodeDisplayData(
            this.translateService.stream(
              displayConfig?.action ?? 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.ACTION',
            ),
            this.validationErrorsDetected(
              step,
              this.translateService.instant('AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.CONFIG_ERROR'),
              true,
            ),
          );
        }
        return this.nodeDisplayData(
          this.translateService.stream(
            displayConfig?.actionWithData ?? 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.ACTION_WITH_DATA',
            {
              formName: form.name,
            },
          ),
          this.noValidationErrorsDetected(),
        );
      }),
      catchError(() => {
        return this.nodeDisplayData(
          this.translateService.stream(
            displayConfig?.action ?? 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.ACTION',
          ),
          this.validationErrorsDetected(
            step,
            this.translateService.instant('AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.CONFIG_ERROR'),
            true,
          ),
        );
      }),
    );
  }

  /**
   * This is the text that appears in the activity column in the activity table.
   * Each column has a title (big text) and body (small text).
   * Most, but not all, trigger activity entries have "Entered the automation" (or similar) as the title
   * and then a custom body.
   */
  getActivityColumn(_namespace: string, activity: ActivityInterface): ActivityTableEntry {
    const displayConfig = this.getDisplayConfig(activity?.workflowStep ?? {});
    return this.triggerActivityEntry(
      this.translateService.stream(
        displayConfig?.activity ?? 'AUTOMATIONS.EDITOR.TRIGGERS.CRM_CONTACT_FORM_SUBMITTED.ACTIVITY',
      ),
    );
  }
}
