import { Component, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators, UntypedFormArray } from '@angular/forms';
import { CommonActionPanelComponent } from '../../../common/common-action-panel-component.directive';
import { RuleInterface } from '@vendasta/automata';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { CommonModule } from '@angular/common';
import { MatCheckbox } from '@angular/material/checkbox';
import { VaFormsModule } from '@vendasta/forms';

@Component({
  selector: 'automata-add-contact-to-kixie-powerlist-panel',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FormsModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatCheckbox,
    VaFormsModule,
  ],
  templateUrl: './add-contact-to-kixie-powerlist-panel.component.html',
  styleUrl: './add-contact-to-kixie-powerlist-panel.component.scss',
})
export class AddContactToKixiePowerlistPanelComponent extends CommonActionPanelComponent implements OnInit {
  form: UntypedFormGroup;
  contactIdControl: UntypedFormControl;
  connectionIdControl: UntypedFormControl;
  powerlistIdControl: UntypedFormControl;
  isEditMode = true;

  constructor() {
    super();
  }

  override ngOnInit(): void {
    super.ngOnInit();

    const jsonData = this.step ? JSON.parse(this.step.data) : {};

    this.contactIdControl = new UntypedFormControl(jsonData.contactId || '', Validators.required);
    this.connectionIdControl = new UntypedFormControl(jsonData.connectionId || '', Validators.required);
    this.powerlistIdControl = new UntypedFormControl(jsonData.powerlistId || '', Validators.required);

    this.form = new UntypedFormGroup({
      contactId: this.contactIdControl,
      connectionId: this.connectionIdControl,
      powerlistId: this.powerlistIdControl,
    });

    this.savingEnabled = true;
  }

  override getData(): any {
    const jsonData = this.step ? JSON.parse(this.step.data) : {};

    jsonData.contactId = this.contactIdControl.value;
    jsonData.connectionId = this.connectionIdControl.value;
    jsonData.powerlistId = this.powerlistIdControl.value;

    this.isEditMode = false;
    return jsonData;
  }

  onEdit(): void {
    this.isEditMode = true;
  }

  override getControlsToValidateOnSave(): (UntypedFormControl | UntypedFormGroup | UntypedFormArray)[] {
    return [this.form];
  }

  override isFormValid(): boolean {
    this.form.markAllAsTouched();
    return this.form.valid;
  }

  override getRules(): RuleInterface[] {
    return [];
  }
}
