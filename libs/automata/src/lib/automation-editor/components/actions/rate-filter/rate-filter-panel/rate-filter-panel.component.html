<div class="content">
  <div
    class="description subtitle"
    [innerHtml]="'AUTOMATIONS.EDITOR.TASKS.RATE_FILTER.DESCRIPTION' | translate | iTrustThisHtml"
  ></div>
  <div class="description title">
    {{ 'AUTOMATIONS.EDITOR.TASKS.RATE_FILTER.CONDITION' | translate }}
  </div>

  <form [formGroup]="form">
    <div class="form-container">
      <div class="form-row">
        <div class="label">
          <glxy-label>
            {{ 'AUTOMATIONS.COMMON.IF' | translate }}
          </glxy-label>
        </div>

        <div class="field-if">
          <glxy-form-field [bottomSpacing]="'none'">
            <mat-select
              formControlName="rateLimitingType"
              [placeholder]="selectedEntityType | entity: false : true | async"
            >
              <mat-option [value]="specificEntity">
                {{
                  'AUTOMATIONS.EDITOR.TASKS.RATE_FILTER.SPECIFIC_ENTITY'
                    | translate: { entity: selectedEntityType | entity: false : true | async | lowercase }
                }}
              </mat-option>
              <mat-option [value]="anyEntity">
                {{
                  'AUTOMATIONS.EDITOR.TASKS.RATE_FILTER.AUTOMATION'
                    | translate: { entity: selectedEntityType | entity: false : true | async | lowercase }
                }}
              </mat-option>
            </mat-select>
            <glxy-error
              *ngIf="form.controls.rateLimitingType.hasError('invalid') && form.controls.rateLimitingType.dirty"
            >
              {{ 'AUTOMATIONS.EDITOR.TASKS.RATE_FILTER.ENTITY_TYPE_INVALID' | translate }}
            </glxy-error>
          </glxy-form-field>
        </div>
      </div>

      <div class="form-row">
        <div class="label">
          {{ 'AUTOMATIONS.EDITOR.TASKS.RATE_FILTER.REACHED_STEP_MORE' | translate }}
        </div>

        <div class="field-freq">
          <glxy-form-field [bottomSpacing]="'none'">
            <input
              matInput
              formControlName="frequency"
              type="number"
              [placeholder]="'AUTOMATIONS.EDITOR.TASKS.RATE_FILTER.FREQUENCY' | translate"
            />
            <glxy-error *ngIf="form.controls.frequency.hasError('pattern')">
              {{ 'AUTOMATIONS.EDITOR.TASKS.RATE_FILTER.OUT_OF_BOUNDS' | translate }}
            </glxy-error>
          </glxy-form-field>
        </div>
        <div class="label">
          {{ 'AUTOMATIONS.EDITOR.TASKS.RATE_FILTER.TIMES' | translate }}
        </div>
      </div>

      <div class="form-row">
        <div class="label">
          {{ 'AUTOMATIONS.EDITOR.TASKS.RATE_FILTER.IN_THE_PAST' | translate }}
        </div>

        <div class="field-length">
          <glxy-form-field [bottomSpacing]="'none'">
            <input
              matInput
              formControlName="length"
              type="number"
              [placeholder]="'AUTOMATIONS.EDITOR.TASKS.RATE_FILTER.LENGTH' | translate"
            />
            <glxy-error *ngIf="form.controls.length.hasError('pattern')">
              {{ 'AUTOMATIONS.EDITOR.TASKS.RATE_FILTER.OUT_OF_BOUNDS' | translate }}
            </glxy-error>
          </glxy-form-field>
        </div>
        <div class="field-period">
          <glxy-form-field [bottomSpacing]="'none'">
            <mat-select formControlName="period">
              <mat-option [value]="periodHours">
                {{ 'AUTOMATIONS.EDITOR.TASKS.RATE_FILTER.HOURS' | translate }}
              </mat-option>
              <mat-option [value]="periodDays">
                {{ 'AUTOMATIONS.EDITOR.TASKS.RATE_FILTER.DAYS' | translate }}
              </mat-option>
            </mat-select>
            <glxy-error *ngIf="form.controls.period.hasError('invalid') && form.controls.period.dirty">
              {{ 'AUTOMATIONS.EDITOR.TASKS.RATE_FILTER.PERIOD_INVALID' | translate }}
            </glxy-error>
          </glxy-form-field>
        </div>
      </div>
    </div>
  </form>
</div>
