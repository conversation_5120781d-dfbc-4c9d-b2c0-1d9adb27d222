import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  AbstractControl,
  UntypedFormArray,
  UntypedFormControl,
  UntypedFormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { BoolEquals, EntityType, Not, RuleInterface } from '@vendasta/automata';
import { BehaviorSubject, Subject } from 'rxjs';
import { CommonActionPanelComponent } from '../../../common/common-action-panel-component.directive';
import { positiveIntegerWithMaxValidatorBuilder } from '../../../common/utils/number-validator';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { EntityPipe } from '../../../../../shared/entity.pipe';
import { ReactiveFormsModule } from '@angular/forms';

export const rateLimitingTypeSpecificEntity = 'specificEntity';
export const rateLimitingTypeAnyEntity = 'anyEntity';

export const periodHours = 'periodHours';
export const periodDays = 'periodDays';

@Component({
  selector: 'automata-rate-filter-panel',
  templateUrl: './rate-filter-panel.component.html',
  styleUrls: ['./rate-filter-panel.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    GalaxyAlertModule,
    MatButtonModule,
    MatCardModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatIconModule,
    MatSelectModule,
    GalaxyPipesModule,
    EntityPipe,
  ],
})
export class RateFilterPanelComponent extends CommonActionPanelComponent implements OnInit, OnDestroy {
  specificEntity = rateLimitingTypeSpecificEntity;
  anyEntity = rateLimitingTypeAnyEntity;
  periodHours = periodHours;
  periodDays = periodDays;
  selectedEntityType: EntityType;
  EntityType = EntityType;
  form: UntypedFormGroup;
  isSubmitted = new BehaviorSubject<boolean>(false);
  private destroyed$$ = new Subject<void>();

  ngOnInit(): void {
    super.ngOnInit();
    this.selectedEntityType = super.getAutomation().entityType;
    const jsonData = this.step ? JSON.parse(this.step.data) : {};
    this.form = new UntypedFormGroup({
      rateLimitingType: new UntypedFormControl(jsonData.rate_limiting_type, [this.rateLimitingTypeValidator]),
      frequency: new UntypedFormControl(jsonData.frequency || 1, [positiveIntegerWithMaxValidatorBuilder(100)]),
      length: new UntypedFormControl(jsonData.length || 1, [positiveIntegerWithMaxValidatorBuilder(100)]),
      period: new UntypedFormControl(jsonData.period, [this.periodValidator]),
    });
    this.savingEnabled = true;
  }

  rateLimitingTypeValidator(control: AbstractControl): ValidationErrors | null {
    if (control.value !== rateLimitingTypeAnyEntity && control.value !== rateLimitingTypeSpecificEntity) {
      return { invalid: true };
    }
    return null;
  }

  periodValidator(control: AbstractControl): ValidationErrors | null {
    if (control.value !== periodDays && control.value !== periodHours) {
      return { invalid: true };
    }
    return null;
  }

  ngOnDestroy(): void {
    this.destroyed$$.next();
    this.destroyed$$.complete();
  }

  positiveIntegerValidator(max: number) {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      if (value === null || value === undefined || value < 1 || value > max || !Number.isInteger(value)) {
        return { invalidNumber: true };
      }
      return null;
    };
  }

  validateForm() {
    this.form.controls.rateLimitingType.setValidators([Validators.required, this.rateLimitingTypeValidator]);
    this.form.controls.frequency.setValidators([Validators.required, this.positiveIntegerValidator(100)]);
    this.form.controls.length.setValidators([Validators.required, this.positiveIntegerValidator(100)]);
    this.form.controls.period.setValidators([Validators.required, this.periodValidator]);
    this.form.updateValueAndValidity();
  }

  getControlsToValidateOnSave(): (UntypedFormControl | UntypedFormGroup | UntypedFormArray)[] {
    return [this.form];
  }

  getData(): any {
    const jsonData = this.step ? JSON.parse(this.step.data) : {};
    if (!this.form) {
      return jsonData;
    }
    jsonData.rate_limiting_type = this.form.controls.rateLimitingType.value;
    jsonData.frequency = this.form.controls.frequency.value;
    jsonData.length = this.form.controls.length.value;
    jsonData.period = this.form.controls.period.value;
    return jsonData;
  }

  getRules(): RuleInterface[] {
    const isRateLimited = new Not(new BoolEquals('{.rate_limited}', false));
    return [isRateLimited.toRuleInterface()];
  }

  isFormValid(): boolean {
    this.form.markAsTouched();
    this.isSubmitted.next(true);
    this.validateForm();
    return this.form.valid;
  }
}
