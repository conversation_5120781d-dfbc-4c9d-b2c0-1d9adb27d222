import { Component, Inject, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormControl,
  FormsModule,
  ReactiveFormsModule,
  UntypedFormArray,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { Context, RuleInterface } from '@vendasta/automata';
import { CommonActionPanelComponent } from '../../../common/common-action-panel-component.directive';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { AutomationVariableMenuComponent, insertVariable } from '../../../../../common/components';
import { MatSuffix } from '@angular/material/form-field';
import { ALL_VARIABLE_MENU_DATA_TYPES } from '../../../../../common/constants';
import { combineLatest, Observable } from 'rxjs';
import { catchError, map, switchMap, take } from 'rxjs/operators';
import { App, AppKey } from '@vendasta/marketplace-apps';
import {
  AutomataContextService,
  AUTOMATION_MARKET_ID_INJECTION_TOKEN$,
  AUTOMATION_PARTNER_ID_INJECTION_TOKEN$,
} from '@galaxy/automata/shared';
import { AppPartnerService } from '@galaxy/marketplace-apps';
import { Environment, EnvironmentService } from '@galaxy/core';
import { MatInput } from '@angular/material/input';
import { CdkTextareaAutosize } from '@angular/cdk/text-field';
import {
  SendEmailViaInboxTaskDefinitionId,
  SendEventViaInboxTaskDefinitionId,
  SendSMSViaInboxTaskDefinitionId,
} from '../../../../component-loader/constants';

const MessageBodyLabel: { [key: string]: string } = {
  [SendSMSViaInboxTaskDefinitionId]: 'AUTOMATIONS.EDITOR.TASKS.SEND_SMS_INBOX.MESSAGE_BODY',
  [SendEventViaInboxTaskDefinitionId]: 'AUTOMATIONS.EDITOR.TASKS.SEND_EVENT_INBOX.MESSAGE_BODY',
  [SendEmailViaInboxTaskDefinitionId]: 'AUTOMATIONS.EDITOR.TASKS.SEND_EMAIL_INBOX.MESSAGE_BODY',
};

const Description: { [key: string]: string } = {
  [SendSMSViaInboxTaskDefinitionId]: '',
  [SendEventViaInboxTaskDefinitionId]: 'AUTOMATIONS.EDITOR.TASKS.SEND_EVENT_INBOX.DESCRIPTION',
  [SendEmailViaInboxTaskDefinitionId]: '',
};

@Component({
  selector: 'automata-send-inbox-on-channel-panel',
  templateUrl: './send-inbox-on-channel-panel.component.html',
  styleUrls: ['./send-inbox-on-channel-panel.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    GalaxyAlertModule,
    MatButtonModule,
    MatCardModule,
    FormsModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    AutomationVariableMenuComponent,
    MatSuffix,
    MatInput,
    CdkTextareaAutosize,
  ],
})
export class SendInboxOnChannelPanelComponent extends CommonActionPanelComponent implements OnInit {
  messageBodyFormControl = new FormControl<string>('', Validators.required);
  allDataTypes = ALL_VARIABLE_MENU_DATA_TYPES;
  messageBodyLabel: string;
  messageBodyHint: string;

  inboxProIDs: Map<Environment, string> = new Map<Environment, string>([
    [Environment.PROD, 'MP-DKT6XHPM6NCCDNK2TPDPVD3PG3V7ZHWP'],
    [Environment.DEMO, 'MP-SN554CTLCKV2XLZ7XZ3N2WV8S38RZMQW'],
  ]);
  context = Context;
  isSMSAction = false;

  productName$ = combineLatest([this.partnerId$, this.marketId$]).pipe(
    switchMap(([pid, mid]) => {
      const env = this.environmentService.getEnvironment();
      const appKey = { appId: this.inboxProIDs.get(env) } as AppKey;
      return this.appPartnerService.getMulti([appKey], pid, mid, {}, true);
    }),
    map((apps: App[]) => {
      if (!apps?.[0]) {
        throw new Error('not found');
      }
      const app = apps[0];
      return app.sharedMarketingInformation.name;
    }),
    catchError(() => {
      return this.translateService.instant('AUTOMATIONS.EDITOR.TASKS.SEND_SMS_INBOX.FALLBACK_PRODUCT_NAME');
    }),
    take(1),
  );
  public automationContext = this.automationContextService.currentContext;

  constructor(
    private readonly automationContextService: AutomataContextService,
    @Inject(AUTOMATION_PARTNER_ID_INJECTION_TOKEN$) private readonly partnerId$: Observable<string>,
    @Inject(AUTOMATION_MARKET_ID_INJECTION_TOKEN$) private readonly marketId$: Observable<string>,
    private appPartnerService: AppPartnerService,
    private environmentService: EnvironmentService,
  ) {
    super();
  }

  ngOnInit(): void {
    super.ngOnInit();
    this.messageBodyLabel = MessageBodyLabel?.[this.taskDefinitionId];
    this.messageBodyHint = Description?.[this.taskDefinitionId];

    this.isSMSAction = this.taskDefinitionId === SendSMSViaInboxTaskDefinitionId;

    const jsonData = this.step ? JSON.parse(this.step.data) : { fields: [] };
    this.messageBodyFormControl.setValue(jsonData.message_body);

    this.savingEnabled = true;
  }

  getRules(): RuleInterface[] {
    return [];
  }

  getData(): any {
    const jsonData = this.step ? JSON.parse(this.step?.data ?? '') : {};
    jsonData.message_body = this.messageBodyFormControl.value;
    return jsonData;
  }

  getControlsToValidateOnSave(): (UntypedFormControl | UntypedFormGroup | UntypedFormArray)[] {
    return [this.messageBodyFormControl];
  }

  isFormValid(): boolean | string {
    return this.messageBodyFormControl.valid;
  }

  insertVariable(elementID: string, control: AbstractControl, variable: string): void {
    insertVariable(elementID, control, variable);
  }
}
