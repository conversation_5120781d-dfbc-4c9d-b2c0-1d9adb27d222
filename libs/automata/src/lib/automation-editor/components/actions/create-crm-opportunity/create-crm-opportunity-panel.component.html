<div class="content">
  <ng-container *ngIf="components$ | async as components">
    <div class="inputs" *ngFor="let formControl of formArray.controls; let i = index">
      <automata-data-entry
        [dataEntryOptions]="components"
        [formControl]="formControl"
        (remove)="removeDataEntryComponent(i)"
      ></automata-data-entry>
    </div>
  </ng-container>

  @if (pipelineSelectorInitialValues$ | async; as initialValues) {
    <automata-crm-pipeline-selector
      [required]="true"
      [initialValue]="initialValues"
      [showStageSelection]="true"
      [defaultStageSelection]="FIRST_STAGE"
    ></automata-crm-pipeline-selector>
  }

  <button mat-button color="primary" (click)="addDataEntryComponent()">
    {{ 'AUTOMATIONS.EDITOR.TASKS.CREATE_CRM_OPPORTUNITY.ADD_ADDITIONAL_FIELDS' | translate }}
  </button>
</div>
