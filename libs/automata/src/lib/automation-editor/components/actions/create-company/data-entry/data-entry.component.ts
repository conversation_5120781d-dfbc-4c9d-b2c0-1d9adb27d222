import { CommonModule, NgComponentOutlet } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  DestroyRef,
  EventEmitter,
  forwardRef,
  Injector,
  Input,
  OnDestroy,
  Output,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormsModule,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  ValidationErrors,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { Subscription } from 'rxjs';
import { DataEntry, supportedFilterFieldTypes } from '../../../filters';

export interface DataEntryReturn {
  fieldId: string;
  value: any;
  comparisonValue?: any;
}

export interface DataEntryComparison {
  label: string;
  value: any;
}

@Component({
  selector: 'automata-data-entry',
  templateUrl: './data-entry.component.html',
  styleUrls: ['./data-entry.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    GalaxyAlertModule,
    MatButtonModule,
    MatCardModule,
    FormsModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatIconModule,
    MatSelectModule,
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DataEntryComponent),
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => DataEntryComponent),
      multi: true,
    },
  ],
})
export class DataEntryComponent implements ControlValueAccessor, OnDestroy {
  @Input({ required: true }) dataEntryOptions: DataEntry[] = [];
  @Input() comparisons: DataEntryComparison[] = [];
  @Input() isRequired = false;
  @Output() remove = new EventEmitter();
  @ViewChild(NgComponentOutlet, { static: false }) ngComponentOutlet: NgComponentOutlet;
  supportedFieldTypes = supportedFilterFieldTypes;
  initialValue: any;
  initialComparisonValue: any;

  selectedField = '';
  onChangeFn: any;
  disabled = false;
  subscription: Subscription;

  constructor(
    private injector: Injector,
    private readonly destroyRef: DestroyRef,
    private readonly cdr: ChangeDetectorRef,
  ) {}

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  registerOnChange(fn: any): void {
    this.onChangeFn = fn;
  }

  registerOnTouched(_fn: any): void {
    // no-op
  }

  writeValue(input: DataEntryReturn): void {
    if (input) {
      this.initialValue = input.value;
      this.selectedField = input.fieldId;
      this.initialComparisonValue = input.comparisonValue;
      setTimeout(() => this.registerValueChanged(), 10);
    }
  }

  validate(_control: AbstractControl): ValidationErrors | null {
    if (this.ngComponentOutlet) {
      const componentInstance = this.ngComponentOutlet['_componentRef'].instance;
      if (!componentInstance.isFormValid()) {
        return { invalid: true };
      }
    }
    return null;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  registerValueChanged() {
    if (this.ngComponentOutlet) {
      const componentInstance = this.ngComponentOutlet['_componentRef'].instance;
      this.subscription = componentInstance.selectedValue$.subscribe((value: any) =>
        this.onChangeFn({
          fieldId: this.selectedField,
          value: value,
        }),
      );
      this.cdr.detectChanges();
    }
  }

  getDataEntry(fieldId: string): DataEntry {
    return this.dataEntryOptions.find((dataEntry: DataEntry) => dataEntry.fieldId === fieldId);
  }

  removeClicked(): void {
    this.remove.emit();
  }
}
