<div class="content">
  <ng-container *ngIf="components$ | async as components">
    <div class="inputs" *ngFor="let formControl of formArray.controls; let i = index">
      <automata-data-entry
        [dataEntryOptions]="components"
        [formControl]="formControl"
        [isRequired]="i === 0"
        (remove)="removeDataEntryComponent(i)"
      ></automata-data-entry>
    </div>
  </ng-container>

  <button mat-button color="primary" (click)="addDataEntryComponent()">
    {{ 'AUTOMATIONS.EDITOR.TASKS.CREATE_COMPANY.ADD_ADDITIONAL_FIELDS' | translate }}
  </button>
</div>
