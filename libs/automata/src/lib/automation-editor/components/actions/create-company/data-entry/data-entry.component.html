<div class="fieldContainer">
  <button *ngIf="!isRequired" mat-icon-button (click)="removeClicked()">
    <mat-icon>close</mat-icon>
  </button>
  <ng-container *ngIf="selectedField as field">
    <ng-container *ngIf="getDataEntry(field) as dataEntry">
      <ng-container *ngIf="comparisons?.length > 0">
        <div class="field-comparison-list">
          <div class="comparison">
            <glxy-form-field class="type-field">
              <glxy-label>{{ dataEntry.label | translate }}</glxy-label>
              <mat-select [(ngModel)]="initialComparisonValue">
                <mat-option *ngFor="let comparison of comparisons" [value]="comparison.value">
                  {{ comparison.label | translate }}
                </mat-option>
              </mat-select>
            </glxy-form-field>
          </div>
          <div class="field">
            <ng-template
              [ngComponentOutlet]="dataEntry.dataEntryComponent"
              [ngComponentOutletInputs]="{
                initialValue: this.initialValue,
                showVariableItemMenu: true,
                label: ' ',
                config: { fieldId: dataEntry.fieldId, crmObjectType: dataEntry.crmObjectType },
              }"
            ></ng-template>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="comparisons?.length < 1">
        <div class="field-list">
          <ng-template
            [ngComponentOutlet]="dataEntry.dataEntryComponent"
            [ngComponentOutletInputs]="{
              initialValue: this.initialValue,
              showVariableItemMenu: true,
              label: dataEntry.label,
              config: { fieldId: dataEntry.fieldId, crmObjectType: dataEntry.crmObjectType },
            }"
          ></ng-template>
        </div>
      </ng-container>
    </ng-container>
  </ng-container>
</div>
