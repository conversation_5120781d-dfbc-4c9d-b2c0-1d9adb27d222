<div class="title" *ngIf="title">{{ title | translate }}</div>
<div *ngFor="let control of formArray.controls; let i = index" class="return-key-values-container">
  <glxy-form-field class="key-name-field">
    <glxy-label>{{ keyLabel | translate }}</glxy-label>
    <input matInput [formControl]="formArray.controls[i].controls.key" />
  </glxy-form-field>
  <glxy-form-field class="value-field">
    <glxy-label>{{ valueLabel | translate }}</glxy-label>
    <input matInput [id]="'value-' + id + '-' + i" [formControl]="formArray.controls[i].controls.value" />
    <automata-automation-variable-menu
      [supportedDataTypes]="allDataTypes"
      (variableSelected)="insertVariable('value-' + id + '-' + i, formArray.controls[i].controls.value, $event)"
      matSuffix
      useGalaxyCompat="true"
      class="dynamic-content-icon"
    ></automata-automation-variable-menu>
  </glxy-form-field>
  <button mat-icon-button color="secondary" (click)="removeField(i)">
    <mat-icon>close</mat-icon>
  </button>
</div>
<button mat-button color="primary" (click)="addField()" class="additional-fields">
  {{ 'AUTOMATIONS.EDITOR.TASKS.TRIGGER_WEBHOOK.PANEL.ADD_FIELD' | translate }}
</button>
