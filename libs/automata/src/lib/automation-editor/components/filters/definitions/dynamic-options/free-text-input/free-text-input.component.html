<ng-container>
  <glxy-form-field>
    <glxy-label>
      {{ label | translate }}
    </glxy-label>
    <input [formControl]="textInputControl" matInput [id]="id" />
    <automata-automation-variable-menu
      *ngIf="showVariableItemMenu"
      matSuffix
      useGalaxyCompat="true"
      [dontIncludeDefaultCategories]="true"
      [supportedDataTypes]="supportedDataTypes"
      (variableSelected)="insertTheVariable(textInputControl, $event)"
    ></automata-automation-variable-menu>
    <glxy-error *ngIf="textInputControl.invalid && textInputControl.dirty">
      {{ 'AUTOMATIONS.EDITOR.FILTERS.ORDER_DATA.ERRORS.FREE_TEXT_ERROR' | translate }}
    </glxy-error>
  </glxy-form-field>
</ng-container>
