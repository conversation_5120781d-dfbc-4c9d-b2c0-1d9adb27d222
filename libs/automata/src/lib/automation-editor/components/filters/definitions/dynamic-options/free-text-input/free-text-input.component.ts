import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import {
  AbstractControl,
  FormControl,
  FormsModule,
  ReactiveFormsModule,
  UntypedFormArray,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { DynamicEntryComponent, DynamicEntryComponentCrmConfig, DynamicOptionsComponent } from '../dynamic-options';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { v4 as uuidv4 } from 'uuid';
import { ALL_VARIABLE_MENU_DATA_TYPES } from '../../../../../../common/constants';
import { AutomationVariableMenuComponent, insertVariable } from '../../../../../../common/components';

@Component({
  selector: 'automata-free-text-input',
  templateUrl: './free-text-input.component.html',
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule,
    TranslateModule,
    GalaxyAlertModule,
    MatButtonModule,
    MatCardModule,
    FormsModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatIconModule,
    MatSelectModule,
    AutomationVariableMenuComponent,
  ],
})
export class FreeTextInputComponent implements OnInit, DynamicOptionsComponent, DynamicEntryComponent {
  textInputControl: FormControl<string> = new FormControl<string>('', [Validators.maxLength(1000)]);
  selectedValue$ = this.textInputControl.valueChanges;
  @Input() config: DynamicEntryComponentCrmConfig;
  @Input() showVariableItemMenu: boolean;
  @Input() initialValue: string;
  @Input() label = 'AUTOMATIONS.COMMON.TEXT';
  public supportedDataTypes = ALL_VARIABLE_MENU_DATA_TYPES;
  id = uuidv4();

  ngOnInit(): void {
    if (this.initialValue !== null && this.initialValue !== undefined) {
      this.textInputControl.setValue(this.initialValue);
    }
  }

  getSelectedValue(): string {
    return this.textInputControl.value;
  }

  getControlsToValidateOnSave(): (UntypedFormControl | UntypedFormGroup | UntypedFormArray)[] {
    return [this.textInputControl];
  }

  isFormValid(): boolean {
    return this.textInputControl.valid;
  }

  insertTheVariable(control: AbstractControl, variable: string): void {
    insertVariable(this.id, control, variable);
  }
}
