import { Inject, Injectable } from '@angular/core';
import { AbstractControl } from '@angular/forms';
import { AUTOMATION_NAMESPACE_INJECTION_TOKEN$ } from '@galaxy/automata/shared';
import { AutomataService, AutomationInterface, DataType } from '@vendasta/automata';
import { WorkflowStepOutputParameterContainer } from '@vendasta/automata/lib/_internal/objects';
import { SchemaService } from '@vendasta/auxiliary-data-components';
import { CRMFieldSchemaApiService } from '@vendasta/crm';
import { ListFieldSchemaResponse } from '@vendasta/crm/lib/_internal/objects';
import { Observable, of } from 'rxjs';
import { catchError, map, shareReplay, switchMap, take } from 'rxjs/operators';
import { ALL_VARIABLE_MENU_DATA_TYPES } from '../../constants';
import { VariableMenuItem } from '../variable-menu.component';

const SALESPERSON_CUSTOM_DATA = 'salesPersonCustomFields';
const BUSINESS_CUSTOM_DATA = 'businessCustomFields';
const COMPANY_FIELDS = 'crmCompanyFields';
const CONTACT_FIELDS = 'crmContactFields';

@Injectable({ providedIn: 'root' })
export class AutomationVariableMenuService {
  private variablesFromPrecedingSteps$: Observable<WorkflowStepOutputParameterContainer[]>;
  public automation: AutomationInterface;

  constructor(
    private readonly automataService: AutomataService,
    private readonly schemaService: SchemaService,
    private readonly crmFieldSchemaApiService: CRMFieldSchemaApiService,
    @Inject(AUTOMATION_NAMESPACE_INJECTION_TOKEN$) private readonly namespace$: Observable<string>,
  ) {}

  private getAssignedSalespersonCustomFields$(idReplacementTag: string): Observable<VariableMenuItem[]> {
    return this.namespace$.pipe(
      switchMap((namespace) => this.schemaService.listFieldSchemas('user', namespace, false, '', 100)),
      map((response) => response.fieldSchemas),
      map((fieldSchemas) =>
        fieldSchemas.map(
          (schema): VariableMenuItem => ({
            textKey: schema.fieldName,
            replacementTag: SALESPERSON_CUSTOM_DATA + "['" + idReplacementTag + "', '" + schema.fieldId + "']",
          }),
        ),
      ),
      catchError((err) => {
        console.error(err);
        return of([]);
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
  }

  private getBusinessCustomFields$(idReplacementTag: string): Observable<VariableMenuItem[]> {
    return this.namespace$.pipe(
      switchMap((namespace) => this.schemaService.listFieldSchemas('business', namespace, false, '', 100)),
      map((response) => response.fieldSchemas),
      map((fieldSchemas) =>
        fieldSchemas.map(
          (schema): VariableMenuItem => ({
            textKey: schema.fieldName,
            replacementTag: BUSINESS_CUSTOM_DATA + "['" + idReplacementTag + "', '" + schema.fieldId + "']",
          }),
        ),
      ),
      catchError((err) => {
        console.error(err);
        return of([]);
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
  }

  private getCRMCompanyFields$(idReplacementTag: string): Observable<VariableMenuItem[]> {
    return this.namespace$.pipe(
      switchMap((namespace) =>
        this.crmFieldSchemaApiService.listFieldSchema({
          namespace: namespace,
          crmObjectType: 'Company',
          pagingOptions: { pageSize: 200 },
        }),
      ),
      map((response: ListFieldSchemaResponse) => response.fieldSchemas || []),
      map((fieldSchemas) => {
        return fieldSchemas.map(
          (schema): VariableMenuItem => ({
            textKey: schema.fieldName,
            replacementTag: COMPANY_FIELDS + "['" + idReplacementTag + "', '" + schema.fieldId + "']",
          }),
        );
      }),
      map((items) => items.sort((a, b) => a.textKey.localeCompare(b.textKey))),
      catchError((err) => {
        console.error(err);
        return of([]);
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
  }

  private getCRMContactFields$(idReplacementTag: string): Observable<VariableMenuItem[]> {
    return this.namespace$.pipe(
      switchMap((namespace) =>
        this.crmFieldSchemaApiService.listFieldSchema({
          namespace: namespace,
          crmObjectType: 'Contact',
          pagingOptions: { pageSize: 200 },
        }),
      ),
      map((response: ListFieldSchemaResponse) => response.fieldSchemas || []),
      map((fieldSchemas) => {
        return fieldSchemas.map(
          (schema): VariableMenuItem => ({
            textKey: schema.fieldName,
            replacementTag: CONTACT_FIELDS + "['" + idReplacementTag + "', '" + schema.fieldId + "']",
          }),
        );
      }),
      map((items) => items.sort((a, b) => a.textKey.localeCompare(b.textKey))),
      catchError((err) => {
        console.error(err);
        return of([]);
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
  }

  // Converts outputParams to VariableMenuItems and tries to setup subitems to include structured data so it's not just
  // the raw values of the stream ({.WorkflowStep.stepid.account_group_id}), but derivative values like
  // {{ businessName['{.WorkflowStep.stepid.account_group_id}'] }}
  // because tinyMCE can only launch the variable menu selector as an iframe modal the values here need to be json
  // serialized and stored in a cookie in that case. If forJSONSerialization is true this won't include those derivative
  // values and will return bare values instead of observables for subItems$ because observables can't be json serialized
  private convertWorkflowStepOutputParameterContainerIntoVariableMenuItems(
    paramContainers: WorkflowStepOutputParameterContainer[],
    includedDataTypes: DataType[],
    dontIncludeSubItems: boolean,
    includeRepeatedParams: boolean,
  ): VariableMenuItem[] {
    if (!paramContainers) {
      return [];
    }
    if ((includedDataTypes || []).length === 0) {
      //Default to all data types
      includedDataTypes = ALL_VARIABLE_MENU_DATA_TYPES;
    }
    const outputParamsSubItems: VariableMenuItem[] = [];
    let workflowStepKeyPrefix = '';
    for (const r of paramContainers) {
      const supportedOutputParameters = r.outputParameters?.filter((outputParam) => {
        const hasSupportedDataType = includedDataTypes.includes(outputParam.settings.dataType);
        // Only include parameters in the list if the supported data type is included and if it's not repeated or repeated params are supported
        return hasSupportedDataType && (includeRepeatedParams || !outputParam.settings.repeated);
      });
      // only include steps with valid output parameters
      const outputParamOptions = [];
      if (supportedOutputParameters?.length > 0) {
        workflowStepKeyPrefix = '{.WorkflowStep.' + r.workflowStep.id + '.';
        for (const outputParam of supportedOutputParameters) {
          const parameterReplacementTag = workflowStepKeyPrefix + outputParam.settings.id + '}';
          let subItems: Observable<VariableMenuItem[]> = of([]);
          if (!dontIncludeSubItems) {
            subItems = this.getSubItemsForDataType(outputParam.settings.dataType, parameterReplacementTag);
          }
          outputParamOptions.push({
            replacementTag: parameterReplacementTag,
            textKey: outputParam.settings.name,
            subItems$: subItems,
            settings: {
              shouldNotSurroundWithCurlyBraces: true,
            },
          });
        }
        outputParamsSubItems.push({
          replacementTag: '',
          textKey: r.workflowStep.name,
          subItems$: of(outputParamOptions),
        });
      }
    }
    if (outputParamsSubItems.length === 0) {
      return [];
    }
    const automationDetailsSubItems = [
      {
        replacementTag: '{.WorkflowStep.current_activity_details.automation_name}',
        textKey: 'HTML_EDITOR.DYNAMIC_CONTENT.AUTOMATION_NAME',
        settings: {
          shouldNotSurroundWithCurlyBraces: true,
        },
      },
      {
        replacementTag: '{.WorkflowStep.current_activity_details.started_processing_stamp}',
        textKey: 'HTML_EDITOR.DYNAMIC_CONTENT.WHEN_RUN',
        settings: {
          shouldNotSurroundWithCurlyBraces: true,
        },
      },
    ];
    return [
      {
        textKey: 'HTML_EDITOR.DYNAMIC_CONTENT.AUTOMATION_DETAILS',
        replacementTag: '',
        subItems$: of(automationDetailsSubItems),
      },
      {
        textKey: 'HTML_EDITOR.DYNAMIC_CONTENT.FROM_PREVIOUS_STEP',
        replacementTag: '',
        subItems$: of(outputParamsSubItems),
      },
    ];
  }

  public init(automation: AutomationInterface, precedingStepIds: string[]): void {
    this.automation = automation;
    if (precedingStepIds.length > 0) {
      this.variablesFromPrecedingSteps$ = this.automataService
        .getWorkflowStepPredecessorOutputParams(automation.id, precedingStepIds, true)
        .pipe(shareReplay(1));
    } else {
      this.variablesFromPrecedingSteps$ = of([]).pipe(shareReplay(1));
    }
  }

  public variablesFromPrecedingStepsForDataTypes$(
    dataTypes: DataType[],
    dontIncludeSubItems: boolean,
    includeRepeatedParams: boolean,
  ): Observable<VariableMenuItem[]> {
    // Get variables from previous steps and field schemas of what we could possibly need.
    return this.variablesFromPrecedingSteps$.pipe(
      take(1),
      map((containers) => {
        return this.convertWorkflowStepOutputParameterContainerIntoVariableMenuItems(
          containers,
          dataTypes,
          dontIncludeSubItems,
          includeRepeatedParams,
        );
      }),
    );
  }

  private getSubItemsForDataType(dataType: DataType, replacementTagPrefix: string): Observable<VariableMenuItem[]> {
    const supportedReplacementTags = DataTypeToSupportedReplacementTags.get(dataType) || [];
    const staticMenuItems = supportedReplacementTags.map((s) =>
      replacementTagToVariableMenuItem(s, replacementTagPrefix),
    );
    const dynamicMenuItems: VariableMenuItem[] = [];

    // Ideally this could be pushed into the map with the static values for replacment but I couldn't figure out an interface to do that
    // in a nice way, so I'm resulting to this until it becomes a problem and needs to be refactored.
    if (dataType === DataType.DATA_TYPE_ACCOUNT_GROUP_ID) {
      dynamicMenuItems.push({
        replacementTag: '',
        subItems$: this.getAssignedSalespersonCustomFields$(replacementTagPrefix),
        textKey: 'HTML_EDITOR.DYNAMIC_CONTENT.SALESPERSON_CUSTOM_DATA',
      });
      dynamicMenuItems.push({
        replacementTag: '',
        subItems$: this.getBusinessCustomFields$(replacementTagPrefix),
        textKey: 'HTML_EDITOR.DYNAMIC_CONTENT.BUSINESS_CUSTOM_DATA',
      });
    }
    if (dataType === DataType.DATA_TYPE_COMPANY_ID) {
      return this.getCRMCompanyFields$(replacementTagPrefix);
    }
    if (dataType === DataType.DATA_TYPE_CONTACT_ID) {
      return this.getCRMContactFields$(replacementTagPrefix);
    }

    return of(staticMenuItems.concat(dynamicMenuItems));
  }
}

export function insertVariable(elementID: string, control: AbstractControl, variable: string): void {
  const input = document.getElementById(elementID) as HTMLInputElement;
  const startPos = input.selectionStart;
  input.focus();
  input.value =
    input.value.substr(0, input.selectionStart) +
    variable +
    input.value.substr(input.selectionStart, input.value.length);
  input.selectionStart = startPos;
  input.focus();
  control.setValue(input.value);
}

export interface ReplacementTag {
  tag: string;
  textKey: string;
}

function replacementTagToVariableMenuItem(s: ReplacementTag, replacementTagPrefix: string): VariableMenuItem {
  return {
    replacementTag: s.tag + "['" + replacementTagPrefix + "']",
    textKey: s.textKey,
  };
}

const DataTypeToSupportedReplacementTags: Map<DataType, ReplacementTag[]> = new Map<DataType, ReplacementTag[]>([
  [
    DataType.DATA_TYPE_ACCOUNT_GROUP_ID,
    [
      { tag: 'businessName', textKey: 'TEMPLATES.BUSINESS_NAME' },
      { tag: 'businessWebsite', textKey: 'TEMPLATES.BUSINESS_WEBSITE' },
      { tag: 'customerIdentifier', textKey: 'TEMPLATES.CUSTOMER_IDENTIFIER' },
      { tag: 'accountIdentifier', textKey: 'TEMPLATES.ACCOUNT_IDENTIFIER' },
      { tag: 'businessPhoneNumber', textKey: 'TEMPLATES.BUSINESS_PHONE_NUMBER' },
      { tag: 'businessCountry', textKey: 'TEMPLATES.BUSINESS_COUNTRY' },
      { tag: 'businessState', textKey: 'TEMPLATES.BUSINESS_STATE' },
      { tag: 'businessStreetAddress', textKey: 'TEMPLATES.BUSINESS_STREET_ADDRESS' },
      { tag: 'businessStreetAddress2', textKey: 'TEMPLATES.BUSINESS_STREET_ADDRESS_2' },
      { tag: 'businessCity', textKey: 'TEMPLATES.BUSINESS_CITY' },
      { tag: 'businessPostalCode', textKey: 'TEMPLATES.BUSINESS_POSTAL_CODE' },
      { tag: 'businessOrigin', textKey: 'TEMPLATES.BUSINESS_ORIGIN' },
      { tag: 'businessCategoryIds', textKey: 'TEMPLATES.BUSINESS_CATEGORY_IDS' },
      { tag: 'businessEmail', textKey: 'TEMPLATES.BUSINESS_EMAIL' },
      { tag: 'businessRaw', textKey: 'TEMPLATES.BUSINESS_RAW' },
      { tag: 'companyIdentifier', textKey: 'TEMPLATES.COMPANY_IDENTIFIER' },
      { tag: 'salesPersonFirstName', textKey: 'TEMPLATES.ASSIGNED_SALESPERSON_FIRST_NAME' },
      { tag: 'salesPersonLastName', textKey: 'TEMPLATES.ASSIGNED_SALESPERSON_LAST_NAME' },
      { tag: 'salesPersonPhoneNumber', textKey: 'TEMPLATES.ASSIGNED_SALESPERSON_PHONE_NUMBER' },
      { tag: 'salesPersonEmail', textKey: 'TEMPLATES.ASSIGNED_SALESPERSON_EMAIL' },
    ],
  ],
  [
    DataType.DATA_TYPE_ORDER_ID,
    [
      { tag: 'orderId', textKey: 'TEMPLATES.ORDER_ID' },
      { tag: 'orderLineItems', textKey: 'TEMPLATES.ORDER_LINE_ITEMS' },
      { tag: 'status', textKey: 'TEMPLATES.ORDER_STATUS' },
      { tag: 'salesPersonId', textKey: 'TEMPLATES.ORDER_SALESPERSON_ID' },
      { tag: 'expiryDate', textKey: 'TEMPLATES.ORDER_EXPIRY' },
      { tag: 'contractStartDate', textKey: 'TEMPLATES.ORDER_CONTRACT_START' },
      { tag: 'orderRaw', textKey: 'TEMPLATES.ORDER_RAW' },
    ],
  ],
  [
    DataType.DATA_TYPE_SMB_USER_ID,
    [
      { tag: 'userFirstName', textKey: 'TEMPLATES.USER_FIRST_NAME' },
      { tag: 'userLastName', textKey: 'TEMPLATES.USER_LAST_NAME' },
      { tag: 'userEmail', textKey: 'TEMPLATES.USER_EMAIL' },
      { tag: 'userRaw', textKey: 'TEMPLATES.USER_RAW' },
    ],
  ],
  // Add other data types here if they have relevant substitution params from templates µs, ideally any data type that represents an entity would be here
  [DataType.DATA_TYPE_PRODUCT, [{ tag: 'productName', textKey: 'TEMPLATES.PRODUCT_NAME' }]],
]);
