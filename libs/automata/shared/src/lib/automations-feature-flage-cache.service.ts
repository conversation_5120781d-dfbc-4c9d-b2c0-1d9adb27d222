import { Inject, Injectable } from '@angular/core';
import { FeatureFlagService, FeatureFlagStatusInterface } from '@galaxy/partner';
import { Observable, throwError } from 'rxjs';
import { catchError, map, shareReplay, switchMap } from 'rxjs/operators';
import { AUTOMATION_PARTNER_ID_INJECTION_TOKEN$ } from './injection-tokens';

export const INTERNAL_FEATURE_FLAG = 'internal_automations';
export const DRAFT_FEATURE_FLAG = 'automation_wip_features';
export const ALLOW_EDIT_TEMPLATES = 'automation_templates_editing';
export const ALLOW_EDIT_BUILT_IN_AUTOMATIONS = 'default_automations_editing';
export const AUTOMATION_AI_ASSISTANT_FEATURE_FLAG = 'automation_ai_assistant_action';
export const CRM_CUSTOM_OBJECT_FEATURE_FLAG = 'pcc_crm_custom_objects';
export const BCC_CRM_CUSTOM_OBJECT_FEATURE_FLAG = 'bcc_crm_custom_objects';

// Platform integrations feature flags

export const SHOW_BROADLYPARTNERAPI_INTEGRATION = 'show_broadlypartnerapi_integration';
export const SHOW_CLIO_INTEGRATION = 'show_clio_integration';
export const SHOW_SERVICETITAN_INTEGRATION = 'show_servicetitan_integration';
export const SHOW_TEKMETRIC_INTEGRATION = 'show_tekmetric_integration';
export const SHOW_SHOPMONKEY_INTEGRATION = 'show_shopmonkey_integration';
export const SHOW_CCC_INTEGRATION = 'show_ccc_integration';
export const SHOW_JOBNIMBUS_INTEGRATION = 'show_jobnimbus_integration';
export const SHOW_MINDBODY_INTEGRATION = 'show_mindbody_integration';
export const SHOW_SHOPWARE_INTEGRATION = 'show_shopware_integration';
export const SHOW_FTP_INTEGRATION = 'show_ftp_integration';
export const SHOW_QBD_INTEGRATION = 'show_QBD_integration';
export const SHOW_NAPATRACS_INTEGRATION = 'show_napatracs_integration';
export const SHOW_NAPATRACSENTERPRISE_INTEGRATION = 'show_napatracsenterprise_integration';
export const SHOW_ROWRITER_INTEGRATION = 'show_rowriter_integration';
export const SHOW_DENTRIX_INTEGRATION = 'show_dentrix_integration';
export const SHOW_MITCHELLMANAGER_INTEGRATION = 'show_mitchellmanager_integration';

@Injectable({ providedIn: 'root' })
export class AutomationsFeatureCache {
  readonly featureFlags$: Observable<FeatureFlagStatusInterface> = this.partnerId$.pipe(
    switchMap((partnerId) =>
      this.flagSvc.batchGetStatus(partnerId, '', [
        INTERNAL_FEATURE_FLAG,
        DRAFT_FEATURE_FLAG,
        ALLOW_EDIT_TEMPLATES,
        ALLOW_EDIT_BUILT_IN_AUTOMATIONS,
        SHOW_BROADLYPARTNERAPI_INTEGRATION,
        SHOW_CLIO_INTEGRATION,
        SHOW_SERVICETITAN_INTEGRATION,
        SHOW_TEKMETRIC_INTEGRATION,
        SHOW_SHOPMONKEY_INTEGRATION,
        SHOW_CCC_INTEGRATION,
        SHOW_JOBNIMBUS_INTEGRATION,
        SHOW_MINDBODY_INTEGRATION,
        SHOW_SHOPWARE_INTEGRATION,
        SHOW_FTP_INTEGRATION,
        SHOW_QBD_INTEGRATION,
        SHOW_NAPATRACS_INTEGRATION,
        SHOW_NAPATRACSENTERPRISE_INTEGRATION,
        SHOW_ROWRITER_INTEGRATION,
        SHOW_DENTRIX_INTEGRATION,
        SHOW_MITCHELLMANAGER_INTEGRATION,
        AUTOMATION_AI_ASSISTANT_FEATURE_FLAG,
        CRM_CUSTOM_OBJECT_FEATURE_FLAG,
      ]),
    ),
    shareReplay({ bufferSize: 1, refCount: true }),
    catchError((e) => {
      console.error(e);
      return throwError(e);
    }),
  );

  readonly includeInternalFeatures$: Observable<boolean> = this.featureFlags$.pipe(
    map((flags) => flags[INTERNAL_FEATURE_FLAG]),
  );
  readonly includeDraftFeatures$: Observable<boolean> = this.featureFlags$.pipe(
    map((flags) => flags[DRAFT_FEATURE_FLAG]),
  );

  constructor(
    @Inject(AUTOMATION_PARTNER_ID_INJECTION_TOKEN$) private readonly partnerId$: Observable<string>, // This actually needs to be the partner id, not the namespace
    private readonly flagSvc: FeatureFlagService,
  ) {}
}
