import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  inject,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import {
  PaymentMethod,
  PaymentMethodService,
  PaymentMethodType,
  RetailCustomerConfigurationService,
  RetailCustomerConfiguration,
  BillingI18nModule,
} from '@galaxy/billing';
import { firstValueFrom, Observable, of, take } from 'rxjs';
import { SelectPaymentMethodDialogData, SelectedPaymentMethod } from './payment-method';
import { OrdersSelectPaymentMethodDialogComponent } from './orders-select-payment-method-dialog/orders-select-payment-method-dialog.component';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AsyncPipe, NgClass } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { PaymentMethodDisplayComponent } from '@vendasta/smb-invoicing';
import { CustomerRecipientInterface, SalesOrdersService } from '@vendasta/sales-orders';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { OrdersPaymentMethodService } from './orders-payment-method.service';
import { distinctUntilChanged, shareReplay, switchMap, withLatestFrom } from 'rxjs/operators';
import {
  SubmitForChargeDialogComponent,
  SubmitForChargeDialogData,
} from '../../submit-for-charge-dialog/submit-for-charge-dialog.component';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { OrderLineItemValidationService } from '../../order-line-item-validation/order-line-item-validation.service';
import { OrdersEditOrderFormValidationService } from '../../edit-order/edit-order-form-validation.service';
import { OrderStoreService } from '../../../core/orders.service';
import { ProductAnalyticsModule, ProductAnalyticsService } from '@vendasta/product-analytics';
import { UNIFIED_PAGE_POSTHOG_CATEGORY } from '../../../shared/constants';
import { GalaxyPopoverModule, PopoverPositions } from '@vendasta/galaxy/popover';
import { ConnectedPosition } from '@angular/cdk/overlay';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { hasChargeableItemsDueNow } from '../../../shared/utils';

const CHARGE_POSTHOG_EVENT = 'order-payment-method-charged';

@Component({
  imports: [
    NgClass,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatFormFieldModule,
    GalaxyBadgeModule,
    PaymentMethodDisplayComponent,
    TranslateModule,
    MatCheckboxModule,
    AsyncPipe,
    GalaxyPopoverModule,
    BillingI18nModule,
    ProductAnalyticsModule,
    GalaxyLoadingSpinnerModule,
  ],
  providers: [TranslateService],
  selector: 'orders-payment-method-selector',
  templateUrl: './orders-payment-method.component.html',
  styleUrls: ['./orders-payment-method.component.scss'],
})
export class OrderPaymentMethodSelectorComponent implements OnChanges, OnInit {
  @Input() stripeConnectId: string;
  @Input() merchantId: string;
  @Input() customerId: string;
  @Input() orderId: string;
  @Input() userId: string;
  @Input() stripeKey: string;
  @Input() editButton: string;
  @Input() makeDefaultButton?: string;
  @Input() addCardButton: string;
  @Input() isEditable: boolean;
  @Input() hintText: string;
  @Input() emptyCardHintText: string;
  @Input() submitDialogText: string;
  @Input() paymentMethods: PaymentMethod[];
  @Input() defaultPaymentMethod: PaymentMethod;
  @Input() canRemoveSelectedMethod: boolean;
  @Input() orderCustomerRecipient: CustomerRecipientInterface;
  @Input() canChargeOrder: boolean;

  @Output() selectedPaymentMethod: EventEmitter<SelectedPaymentMethod> = new EventEmitter<SelectedPaymentMethod>();
  @Output() orderCharged: EventEmitter<boolean> = new EventEmitter<boolean>();

  confirmationModal = inject(OpenConfirmationModalService);

  chargeOrder = false;
  isLoadingChargeDialog = false;

  sortedMethods: Map<PaymentMethodType, PaymentMethod[]>;
  protected retailCustomerConfiguration$: Observable<RetailCustomerConfiguration>;
  protected hasAlreadyActiveProducts$: Observable<boolean>;
  order$ = this.orderStoreService.order$;

  showPopover = false;
  position?: ConnectedPosition;
  constructor(
    public dialog: MatDialog,
    private paymentMethodService: PaymentMethodService,
    private cd: ChangeDetectorRef,
    private snackbarService: SnackbarService,
    private translateService: TranslateService,
    private ordersPaymentMethodService: OrdersPaymentMethodService,
    private readonly customerConfigService: RetailCustomerConfigurationService,
    private salesOrdersApiService: SalesOrdersService,
    private readonly lineItemValidationService: OrderLineItemValidationService,
    private readonly editOrderFormValidationService: OrdersEditOrderFormValidationService,
    private readonly orderStoreService: OrderStoreService,
    private readonly injector: Injector,
    private readonly productAnalyticsService: ProductAnalyticsService,
  ) {}

  ngOnInit(): void {
    this.ordersPaymentMethodService.setPaymentMethod(this.defaultPaymentMethod?.details?.id);
    if (!this.makeDefaultButton) {
      this.makeDefaultButton = this.translateService.instant('PAYMENT_METHOD_SELECTOR.USE_AS_DEFAULT');
    }

    this.retailCustomerConfiguration$ = this.customerConfigService
      .get(this.merchantId, this.customerId)
      .pipe(distinctUntilChanged(), shareReplay({ bufferSize: 1, refCount: true }));

    this.hasAlreadyActiveProducts$ = this.lineItemValidationService.hasAlreadyActiveProducts$;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.paymentMethods?.previousValue !== changes.paymentMethods?.currentValue) {
      this.sortedMethods = this._filterOutNonCardPaymentMethods(this.paymentMethods);
    }
  }

  showPopovers(position: ConnectedPosition): void {
    this.position = position;
    this.showPopover = true;
  }

  hidePopovers(): void {
    this.showPopover = false;
  }

  public openPaymentMethodDialog(): void {
    this.retailCustomerConfiguration$
      .pipe(
        take(1),
        switchMap((retailConfiguration) =>
          this.dialog
            .open(OrdersSelectPaymentMethodDialogComponent, {
              width: '600px',
              data: {
                accountId: this.stripeConnectId,
                merchantId: this.merchantId,
                customerId: this.customerId,
                userId: this.userId,
                stripeKey: this.stripeKey,
                paymentMethods: this.sortedMethods,
                submitDialogText: this.submitDialogText,
                defaultPaymentMethod: this.defaultPaymentMethod?.details?.id,
                orderCustomerRecipient: this.orderCustomerRecipient,
                defaultCustomerRecipientId: retailConfiguration?.contactId,
              } as SelectPaymentMethodDialogData,
              autoFocus: false,
              injector: Injector.create({
                providers: [{ provide: OrderStoreService, useValue: this.orderStoreService }],
                parent: this.injector,
              }),
            })
            .afterClosed(),
        ),
      )
      .subscribe((m) => {
        this.selectedPaymentMethod.emit(m);
        this.ordersPaymentMethodService.setPaymentMethod(m?.paymentMethodId);
      });
  }

  public openChargeConfirmationDialog(): void {
    this.editOrderFormValidationService
      .validateFormsForCharging()
      .pipe(
        switchMap((isFormValid) => {
          if (!isFormValid) {
            return of(null);
          }

          return this.lineItemValidationService.areBillingTermsValidOrOpenModal$();
        }),
        switchMap((areBillingTermsValid) => {
          if (!areBillingTermsValid) {
            return of(null);
          }
          return this.lineItemValidationService.areBillingTermsValidOrOpenModal$();
        }),
        take(1),
        switchMap((areBillingTermsValid) => {
          if (!areBillingTermsValid) {
            return of(null);
          }

          this.isLoadingChargeDialog = true;
          return this.orderStoreService.order$.pipe(
            take(1),
            switchMap((order) => {
              const hasChargeableItems = hasChargeableItemsDueNow(order);
              if (!hasChargeableItems) {
                return of(order);
              }
              return this.orderStoreService.getOrderWithCalculatedTaxes();
            }),
          );
        }),
        switchMap((orderWithCalculatedTaxes) => {
          this.isLoadingChargeDialog = false;
          if (!orderWithCalculatedTaxes) {
            return of(null);
          }
          return this.dialog
            .open(SubmitForChargeDialogComponent, {
              width: '600px',
              data: {
                order: orderWithCalculatedTaxes,
              } as SubmitForChargeDialogData,
              autoFocus: false,
            })
            .afterClosed()
            .pipe(take(1));
        }),
        take(1),
        withLatestFrom(this.ordersPaymentMethodService.paymentMethod$),
        switchMap(([res, paymentMethodToken]) => {
          if (res && paymentMethodToken) {
            return this.salesOrdersApiService.updatePaymentMethodToken(
              this.orderId,
              this.customerId,
              paymentMethodToken,
            );
          }
          return of(false);
        }),
        switchMap((order) => {
          if (order) {
            return this.salesOrdersApiService.chargeOrder(this.orderId, this.customerId);
          }
          return of(false);
        }),
      )
      .subscribe({
        next: (submitted) => {
          this.isLoadingChargeDialog = false;
          if (submitted) {
            // track the charge with PostHog
            this.productAnalyticsService.trackEvent(CHARGE_POSTHOG_EVENT, UNIFIED_PAGE_POSTHOG_CATEGORY, 'charge');

            this.snackbarService.openSuccessSnack('LIB_ORDERS.SALES_ORDERS.SUCCESS.ORDER_UPDATED');
            this.orderCharged.emit(true);
          }
        },
        error: (err) => {
          this.isLoadingChargeDialog = false;
          if (err?.message === 'Order has no lineItem') {
            this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ORDERS.ERROR_AT_LEAST_ONE_LINE_ITEM');
          } else {
            this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ERRORS.GENERIC_ERROR');
          }
        },
      });
  }

  public addPaymentMethodDialog(): void {
    this.dialog
      .open(OrdersSelectPaymentMethodDialogComponent, {
        width: '600px',
        data: {
          accountId: this.stripeConnectId,
          merchantId: this.merchantId,
          customerId: this.customerId,
          userId: this.userId,
          stripeKey: this.stripeKey,
          submitDialogText: this.submitDialogText,
          hideExistingMethods: true,
        } as SelectPaymentMethodDialogData,
        autoFocus: false,
      })
      .afterClosed()
      .subscribe((m) => {
        this.selectedPaymentMethod.emit(m);
        this.ordersPaymentMethodService.setPaymentMethod(m?.paymentMethodId);
      });
  }

  public async makePaymentMethodDefault(paymentMethod: PaymentMethod): Promise<void> {
    this.selectedPaymentMethod.emit({
      paymentMethodId: paymentMethod.details?.id,
      newMethod: false,
      userId: undefined,
    });
    this.ordersPaymentMethodService.setPaymentMethod(paymentMethod.details?.id);
  }

  public async deletePaymentMethod(paymentMethod: PaymentMethod): Promise<void> {
    try {
      await firstValueFrom(this.paymentMethodService.deletePaymentMethod(this.merchantId, paymentMethod.details?.id));
    } catch {
      this.snackbarService.openErrorSnack('PAYMENT_METHOD_SELECTOR.ERROR_REMOVING_PAYMENT_METHOD');
      return;
    }

    const type = paymentMethod.type;
    const methods = this.sortedMethods.get(type) || [];

    if (!methods) {
      return;
    }

    const index = methods.findIndex((m) => m.details?.id === paymentMethod.details?.id);
    if (index > -1) {
      methods.splice(index, 1);
      methods.length === 0 ? this.sortedMethods.delete(type) : this.sortedMethods.set(type, methods);
    }

    this.sortedMethods = new Map(this.sortedMethods);
    this.cd.detectChanges();
    return;
  }

  private _typeSortPriority(type: PaymentMethodType): number {
    switch (type) {
      case PaymentMethodType.CARD:
        return 1;
      case PaymentMethodType.ACH_DEBIT:
        return 2;
      case PaymentMethodType.ACSS_DEBIT:
        return 3;
      default:
        return 4;
    }
  }

  private _filterOutNonCardPaymentMethods(methods: PaymentMethod[]): Map<PaymentMethodType, PaymentMethod[]> {
    const cardPaymentMethods = methods.filter((m) => m.type === PaymentMethodType.CARD);
    const grouped = new Map<PaymentMethodType, PaymentMethod[]>();
    if (cardPaymentMethods.length > 0) {
      grouped.set(PaymentMethodType.CARD, cardPaymentMethods);
    }
    return grouped;
  }

  protected readonly PopoverPositions = PopoverPositions;
}
