import { Component, Inject, Injector, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { RetailPaymentData, RetailPaymentService } from '../../../../core/retail-payment.service';
import { Observable } from 'rxjs';
import { AsyncPipe } from '@angular/common';
import { CommonField, CustomerRecipientInterface, CustomField, Field, Order } from '@vendasta/sales-orders';
import { OrdersSelectPaymentMethodDialogComponent } from '../../../retail-summary/select-payment-method/orders-select-payment-method-dialog/orders-select-payment-method-dialog.component';
import { SelectPaymentMethodDialogData } from '../../../retail-summary/select-payment-method/payment-method';
import { OrderStoreService } from '../../../../core/orders.service';
import { PaymentMethodDisplayComponent } from '@vendasta/smb-invoicing';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { OrderAction } from '../../../../core/permissions/permissions';
import { OrderPermissionsService } from '../../../../core/permissions/permissions.service';
import { TranslateModule } from '@ngx-translate/core';
import { ProductAnalyticsModule } from '@vendasta/product-analytics';
import { ProcessOrderDialogService } from './process-order-dialog/process-order-dialog.service';

export enum CollectPaymentDialogResult {
  Submitted = 'Submitted',
  Processed = 'Processed',
  Charged = 'Charged',
}

export interface CollectPaymentDialiagData {
  customFields: CustomField[];
  commonFields: CommonField[];
  extraFields: Field[];
}

@Component({
  selector: 'orders-collect-payment-dialog',
  templateUrl: './collect-payment-dialog.component.html',
  imports: [
    MatDialogModule,
    MatIconModule,
    MatButtonModule,
    AsyncPipe,
    PaymentMethodDisplayComponent,
    MatProgressSpinner,
    GalaxyLoadingSpinnerModule,
    TranslateModule,
    ProductAnalyticsModule,
  ],
  styleUrls: ['./collect-payment-dialog.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class CollectPaymentDialogComponent {
  retailPaymentData$: Observable<RetailPaymentData>;
  order$: Observable<Order>;
  loadingOrder$: Observable<boolean>;
  canCollectPaymentFromCustomer$: Observable<boolean>;
  isLoadingChargeDialog = false;

  constructor(
    public dialog: MatDialog,
    public dialogRef: MatDialogRef<CollectPaymentDialogComponent>,
    private readonly retailPaymentService: RetailPaymentService,
    private readonly orderStoreService: OrderStoreService,
    private readonly snackbarService: SnackbarService,
    private readonly injector: Injector,
    private readonly permissionsService: OrderPermissionsService,
    private readonly processOrderService: ProcessOrderDialogService,
    @Inject(MAT_DIALOG_DATA) public data: CollectPaymentDialiagData,
  ) {
    this.retailPaymentData$ = this.retailPaymentService.retailPaymentData$;
    this.order$ = this.orderStoreService.order$;
    this.loadingOrder$ = this.orderStoreService.loadingOrder$;
    this.canCollectPaymentFromCustomer$ = this.permissionsService.CanDoAction(OrderAction.CollectPaymentFromCustomer);
  }

  addPaymentMethod(retailPaymentData: RetailPaymentData, order: Order): void {
    this.dialog
      .open(OrdersSelectPaymentMethodDialogComponent, {
        width: '600px',
        data: {
          accountId: retailPaymentData.stripeConnectId,
          merchantId: order.partnerId,
          customerId: order.businessId,
          stripeKey: retailPaymentData.stripeKey,
          paymentMethods: retailPaymentData.paymentMethodMap,
          submitDialogText: 'Save',
          defaultPaymentMethod: retailPaymentData.defaultPaymentMethod?.details?.id,
          orderCustomerRecipient: order.customerRecipient as CustomerRecipientInterface,
          defaultCustomerRecipientId: retailPaymentData?.defaultCustomerRecipientId,
        } as SelectPaymentMethodDialogData,
        autoFocus: false,
        injector: Injector.create({
          providers: [{ provide: OrderStoreService, useValue: this.orderStoreService }],
          parent: this.injector,
        }),
      })
      .afterClosed()
      .subscribe((response) => {
        if (response) {
          return this.retailPaymentService.selectPaymentMethod(response);
        }
      });
  }

  onCharge(retailPaymentData: RetailPaymentData, order: Order): void {
    this.isLoadingChargeDialog = true;
    this.retailPaymentService.openChargeConfirmationDialog(retailPaymentData, order).subscribe({
      next: (submitted) => {
        this.isLoadingChargeDialog = false;
        if (submitted) {
          this.snackbarService.openSuccessSnack('LIB_ORDERS.SALES_ORDERS.SUCCESS.ORDER_UPDATED');
          this.dialogRef.close(CollectPaymentDialogResult.Charged);
        }
      },
      error: (err) => {
        this.isLoadingChargeDialog = false;
        if (err?.message === 'Order has no lineItem') {
          this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ORDERS.ERROR_AT_LEAST_ONE_LINE_ITEM');
        } else {
          this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ERRORS.GENERIC_ERROR');
        }
      },
    });
  }

  sendForCustomerApproval() {
    this.dialogRef.close(CollectPaymentDialogResult.Submitted);
  }

  processOrder(order: Order): void {
    this.dialogRef.addPanelClass('hidden-dialog');

    order.customFields = this.data.customFields;
    order.commonFields = this.data.commonFields;
    order.extraFields = this.data.extraFields;

    this.processOrderService.openProcessOrderDialog(order).subscribe({
      next: (submitted) => {
        if (submitted) {
          this.snackbarService.openSuccessSnack('LIB_ORDERS.SALES_ORDERS.SUCCESS.ORDER_UPDATED');
          this.dialogRef.close(CollectPaymentDialogResult.Processed);
        } else {
          this.dialogRef.removePanelClass('hidden-dialog');
        }
      },
    });
  }
}
