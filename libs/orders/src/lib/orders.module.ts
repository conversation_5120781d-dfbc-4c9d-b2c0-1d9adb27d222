import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { ActiveItemsModule, ItemPricingTableModule } from '@galaxy/inventory-ui';
import { LexiconModule } from '@galaxy/lexicon';
import { PartnerService } from '@galaxy/partner';
import { TranslateModule } from '@ngx-translate/core';
import { BillingUiModule } from '@vendasta/billing-ui';
import { ProductActivationPrereqFormModule } from '@vendasta/businesses';
import { VaFormsModule } from '@vendasta/forms';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyButtonGroupModule } from '@vendasta/galaxy/button-group';
import { GalaxyDatepickerModule } from '@vendasta/galaxy/datepicker';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyInputModule } from '@vendasta/galaxy/input';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { GalaxySnackbarModule, SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyStickyFooterModule } from '@vendasta/galaxy/sticky-footer';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { GalaxyUploaderModule } from '@vendasta/galaxy/uploader';
import { ProductAnalyticsModule } from '@vendasta/product-analytics';
import { ProjectTrackerFileUploadServiceInterfaceToken, ProjectTrackerModule } from '@vendasta/project-tracker';
import {
  SalesOrderConfirmationModule,
  SalesOrderContentsModule,
  SalesOrderDetailsCardModule,
  SalesOrderFormModule,
  SalesOrderStoreService,
} from '@vendasta/sales-ui';
import { VaOrderFormModule, VaOrderSummaryModule, VaPricingModule } from '@vendasta/store';
import { AttachmentServiceData, AttachmentServiceDataInterfaceToken, TaskAttachmentService } from '@vendasta/task';
import { VaBadgeModule, VaIconModule } from '@vendasta/uikit';
import { WorkOrderDetailsComponent, WorkOrderFormComponent, WorkOrderModule } from '@vendasta/work-order';
import { Observable } from 'rxjs';
import { distinctUntilChanged, filter, map, shareReplay } from 'rxjs/operators';
import en from './assets/en_devel.json';
import { AgreementsComponent } from './components/agreements/agreements.component';
import { AttachmentsDialogComponent } from './components/attachments/attachments-dialog.component';
import { AdminAttachmentsComponent } from './components/attachments/admin-attachments/admin-attachments.component';
import { CustomerAttachmentsComponent } from './components/attachments/customer-attachments/customer-attachments.component';
import { CancelOrderDialogComponent } from './components/cancel-order-dialog/cancel-order-dialog.component';
import { CreateOrderDetailsComponent } from './components/create-order-details/create-order-details.component';
import { CreateOrderModule } from './components/create-order/create-order.module';
import { CreateOrderComponent } from './components/create-order/create/create-order.component';
import { CurrencySelectorComponent } from './components/currency-selector/currency-selector.component';
import { CurrencySymbolPipe } from './components/currency-symbol/currency-symbol.component';
import { EditOrderComponent } from './components/edit-order/edit-order.component';
import { OrderFulfillmentLearnMoreDialogComponent } from './components/fulfillment/learn-more-dialog/order-fulfillment-learn-more-dialog.component';
import { OrderFulfillmentComponent } from './components/fulfillment/order-fulfillment.component';
import { HistoryComponent } from './components/history/history.component';
import { LineItemsAdapterService } from './components/line-items/line-items-adapter.service';
import { LineItemsSelectorService } from './components/line-items/line-items-selector.service';
import { LineItemsToInventoryItemsPipe } from './components/line-items/line-items-to-inventory-items.pipe';
import { LineItemsComponent } from './components/line-items/line-items.component';
import { LineItemsService } from './components/line-items/line-items.service';
import { NotesComponent } from './components/notes/notes.component';
import { OrderActionsComponent } from './components/order-actions/order-actions.component';
import { OrderContractDurationComponent } from './components/order-details/contract-details/contract-duration.component';
import { OrderDetailsComponent } from './components/order-details/order-details.component';
import { CustomPriceFormComponent } from './components/order-form/custom-price-form/custom-price-form.component';
import { OrderFormComponent } from './components/order-form/order-form.component';
import { OrderSubmittedComponent } from './components/order-submitted/order-submitted.component';
import { PaymentElementFormComponent } from './components/payment-element-form/payment-element-form.component';
import { OrderPricingComponent } from './components/pricing/pricing.component';
import { ProductRequirementsDialogComponent } from './components/product-requirements-dialog/product-requirements-dialog.component';
import { TagsComponent } from './components/tags/tags.component';
import { OrderTermsComponent } from './components/terms/order-terms.component';
import { VariablePricesComponent } from './components/variable-prices/variable-prices.component';
import { ViewOrEditOrderComponent } from './components/view-or-edit-order/view-or-edit-order.component';
import { ViewOrderComponent } from './components/view-order/view-order.component';
import { OrderComponent } from './order.component';
import { OrderPageComponent } from './pages/order-page/order-page.component';
import { StripeService } from './stripe.service';
import { OrderActivityComponent } from './components/order-activity/order-activity.component';
import { FulfillmentRowComponent } from './components/fulfillment-status-card/fulfillment-row/fulfillment-row.component';
import { FulfillmentStatusCardComponent } from './components/fulfillment-status-card/fulfillment-status-card.component';
import { RetailSummaryComponent } from './components/retail-summary/retail-summary.component';
import { CondensedOrderDetailsComponent } from './components/condensed-order-details/condensed-order-details.component';
import { CompanyNameComponent } from './components/order-details/company-details/company-name.component';
import { CompanyAddressComponent } from './components/order-details/company-details/company-address.component';
import { BusinessHeaderComponent } from './components/business-header/business-header.component';
import { OrderContractStartComponent } from './components/order-details/contract-details/contract-start.component';
import { OrderLineItemValidationBannerComponent } from './components/order-line-item-validation/order-line-item-validation-banner.component';
import { InvalidBillingTermsDialogComponent } from './components/invalid-billing-terms-dialog/invalid-billing-terms-dialog.component';
import { PreviewPublicOrderDialogComponent } from './components/preview-public-order-dialog/preview-public-order-dialog.component';
import { SMBInvoicingModule } from '@vendasta/smb-invoicing';
import { MatRadioModule } from '@angular/material/radio';
import { OrderPaymentMethodSelectorComponent } from './components/retail-summary/select-payment-method/orders-payment-method.component';
import { OrdersPaymentMethodService } from './components/retail-summary/select-payment-method/orders-payment-method.service';
import { AssociateUserToOrderComponent } from './components/associate-user-to-order/associate-user-to-order.component';
import { FuturePaymentsDisplayComponent } from './components/future-payments-display/future-payments-display.component';
import { WholesaleSummaryComponent } from './components/wholesale-summary/wholesale-summary.component';
import { DynamicOrderFormComponent } from './components/order-form/godaddy-order-form/dynamic-order-form-page.component';
import { TermsOfServiceDialogComponent } from './components/order-form/godaddy-order-form/terms-of-service-dialog.component';
import { GalaxyPageNotFound404Module } from '@vendasta/galaxy/page-not-found-404';

export const MODULE_IMPORTS = [
  CommonModule,
  FormsModule,
  GalaxyPageModule,
  MatButtonModule,
  MatCardModule,
  MatCheckboxModule,
  MatChipsModule,
  MatIconModule,
  MatTooltipModule,
  TranslateModule,
  LexiconModule.forChild({
    componentName: 'common/orders',
    baseTranslation: en,
  }),
  GalaxyAlertModule,
  GalaxySnackbarModule,
  VaOrderFormModule,
  VaOrderSummaryModule,
  ReactiveFormsModule,
  SalesOrderDetailsCardModule,
  MatProgressSpinnerModule,
  MatExpansionModule,
  MatDialogModule,
  GalaxyButtonGroupModule,
  GalaxyEmptyStateModule,
  GalaxyDatepickerModule,
  GalaxyInputModule,
  MatDatepickerModule,
  MatGridListModule,
  MatMenuModule,
  MatFormFieldModule,
  MatListModule,
  MatSelectModule,
  MatDividerModule,
  MatInputModule,
  MatTabsModule,
  VaIconModule,
  GalaxyUploaderModule,
  VaBadgeModule,
  VaFormsModule,
  SalesOrderContentsModule,
  ProductActivationPrereqFormModule,
  SalesOrderFormModule,
  SalesOrderConfirmationModule,
  MatProgressSpinnerModule,
  MatAutocompleteModule,
  RouterModule,
  GalaxyPipesModule,
  ProjectTrackerModule,
  VaPricingModule,
  WorkOrderModule,
  ProductAnalyticsModule,
  BillingUiModule,
  GalaxyFormFieldModule,
  WorkOrderFormComponent,
  GalaxyAvatarModule,
  ItemPricingTableModule,
  GalaxyLoadingSpinnerModule,
  CreateOrderModule,
  ActiveItemsModule,
  WorkOrderDetailsComponent,
  GalaxyBadgeModule,
  GalaxyTooltipModule,
  GalaxyStickyFooterModule,
  MatRadioModule,
  SMBInvoicingModule,
  GalaxyPageNotFound404Module,
];

export const MODULE_DECLARATIONS = [
  OrderComponent,
  OrderPricingComponent,
  OrderTermsComponent,
  OrderFormComponent,
  DynamicOrderFormComponent,
  TermsOfServiceDialogComponent,
  OrderFulfillmentComponent,
  OrderFulfillmentLearnMoreDialogComponent,
  CreateOrderComponent,
  OrderSubmittedComponent,
  CreateOrderDetailsComponent,
  HistoryComponent,
  LineItemsComponent,
  NotesComponent,
  AdminAttachmentsComponent,
  AttachmentsDialogComponent,
  CustomerAttachmentsComponent,
  CustomPriceFormComponent,
  TagsComponent,
  CurrencySelectorComponent,
  OrderActionsComponent,
  ViewOrderComponent,
  EditOrderComponent,
  AgreementsComponent,
  PaymentElementFormComponent,
  CurrencySymbolPipe,
  ProductRequirementsDialogComponent,
  ViewOrEditOrderComponent,
  VariablePricesComponent,
  LineItemsToInventoryItemsPipe,
  OrderDetailsComponent,
  OrderPageComponent,
  CancelOrderDialogComponent,
  PreviewPublicOrderDialogComponent,
];

@NgModule({
  declarations: MODULE_DECLARATIONS,
  imports: [
    MODULE_IMPORTS,
    FulfillmentRowComponent,
    FulfillmentStatusCardComponent,
    RetailSummaryComponent,
    WholesaleSummaryComponent,
    CondensedOrderDetailsComponent,
    CompanyNameComponent,
    CompanyAddressComponent,
    BusinessHeaderComponent,
    OrderContractStartComponent,
    OrderContractDurationComponent,
    OrderLineItemValidationBannerComponent,
    InvalidBillingTermsDialogComponent,
    OrderPaymentMethodSelectorComponent,
    AssociateUserToOrderComponent,
    FuturePaymentsDisplayComponent,
    OrderActivityComponent,
  ],
  providers: [
    LineItemsService,
    LineItemsAdapterService,
    LineItemsSelectorService,
    SnackbarService,
    SalesOrderStoreService,
    {
      provide: AttachmentServiceDataInterfaceToken,
      useFactory: (partner: PartnerService): Observable<AttachmentServiceData> => {
        return partner.getPartnerId().pipe(
          filter<string>(Boolean),
          map((partnerId) => {
            const data: AttachmentServiceData = {
              partnerId: partnerId,
            };
            return data;
          }),
          distinctUntilChanged(),
          shareReplay({ refCount: true, bufferSize: 1 }),
        );
      },
      deps: [PartnerService],
    },
    StripeService,
    TaskAttachmentService,
    {
      provide: ProjectTrackerFileUploadServiceInterfaceToken,
      useExisting: TaskAttachmentService,
    },
    OrdersPaymentMethodService,
  ],
  exports: [
    OrderComponent,
    OrderTermsComponent,
    OrderFormComponent,
    DynamicOrderFormComponent,
    OrderFulfillmentComponent,
    CreateOrderComponent,
    ViewOrderComponent,
    EditOrderComponent,
    CreateOrderDetailsComponent,
    OrderSubmittedComponent,
    CustomPriceFormComponent,
    LineItemsComponent,
    NotesComponent,
    AdminAttachmentsComponent,
    CustomerAttachmentsComponent,
    TagsComponent,
    OrderActionsComponent,
    PaymentElementFormComponent,
    ProductRequirementsDialogComponent,
    ViewOrEditOrderComponent,
    VariablePricesComponent,
    OrderDetailsComponent,
    OrderPageComponent,
    CancelOrderDialogComponent,
    OrderActivityComponent,
    OrderLineItemValidationBannerComponent,
    InvalidBillingTermsDialogComponent,
    PreviewPublicOrderDialogComponent,
    OrderPaymentMethodSelectorComponent,
    AssociateUserToOrderComponent,
    FuturePaymentsDisplayComponent,
  ],
})
export class OrdersModule {}
