import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Environment, EnvironmentService } from '@galaxy/core';
import { TemplateType, TemplatesService } from '@vendasta/templates';
import { Observable, of } from 'rxjs';
import { EmailContentData } from '../types/contentblocks';
import { SENDER_ID_TOKEN } from '../../../email-activity/src/dependencies';
import { switchMap } from 'rxjs/operators';

const TEMPLATE_RENDERING_SERVER_DEMO = 'https://email-builder-template-renderer-demo.apigateway.co/renderer/v1';
const TEMPLATE_RENDERING_SERVER_PROD = 'https://email-builder-template-renderer-prod.apigateway.co/renderer/v1';

const NPS_REDIRECT_DEMO = 'https://reputation-api-demo.apigateway.co/leave-review';
const NPS_REDIRECT_PROD = 'https://reputation-api-prod.apigateway.co/leave-review';

// For local template rendering work:
// const TEMPLATE_RENDERING_SERVER_DEMO = 'https://localhost/';
// const TEMPLATE_RENDERING_SERVER_PROD = 'https://localhost/';

export interface EmailPreviewBusinessDetails {
  accountGroupId: string;
  name: string;
  hasSnapshot: boolean;
  hasSalesperson: boolean;
}

export interface EmailPreviewHydrationData {
  partnerID: string;
  namespace?: string;
  marketID: string;
  locale?: string;
  business: EmailPreviewBusinessDetails;
  userID: string;
  businessID?: string;
  contactID?: string;
  disablePreviewHydration?: boolean; // Controls whether the preview has its logo replaced _before_ rendering
  useFakeData?: boolean;
}

@Injectable({
  providedIn: 'any',
})
export class PreviewRenderingService {
  private previewHydrationParams: EmailPreviewHydrationData;
  private readonly templateRenderingServiceURL;
  private readonly npsRedirectURL;
  private locale: string = undefined;

  constructor(
    private readonly httpClient: HttpClient,
    private readonly templates: TemplatesService,
    private readonly envService: EnvironmentService,
    @Inject(SENDER_ID_TOKEN) readonly senderId$: Observable<string>,
  ) {
    this.templateRenderingServiceURL =
      envService.getEnvironment() === Environment.PROD
        ? TEMPLATE_RENDERING_SERVER_PROD
        : TEMPLATE_RENDERING_SERVER_DEMO;
    this.npsRedirectURL = envService.getEnvironment() === Environment.PROD ? NPS_REDIRECT_PROD : NPS_REDIRECT_DEMO;
  }

  setPreviewHydrationParams(params: EmailPreviewHydrationData): void {
    this.previewHydrationParams = params;
  }

  setLocale(locale: string): void {
    this.locale = locale;
  }

  renderContentHTML(emailContent: EmailContentData): Observable<string> {
    const headers = new HttpHeaders({
      'Secret-Key': 'b0725e29-58ef-468f-976a-96efb3b1a494',
      Accept: 'text/html',
    });

    return this.httpClient.post(this.templateRenderingServiceURL, emailContent, {
      headers,
      responseType: 'text',
    });
  }

  hydrateEmailContent(emailContent: EmailContentData, contentWithTags: string): Observable<string> {
    const p = this.previewHydrationParams || { disablePreviewHydration: true };
    if (p.disablePreviewHydration) {
      return this.replaceDefaultLogoParam(contentWithTags);
    }
    return this.hydrateTemplate(contentWithTags);
  }

  hydrateTemplate(templateHtml: string): Observable<string> {
    return this.senderId$.pipe(
      switchMap((senderId) => {
        const parameters = {
          account_group_id: this.previewHydrationParams?.business?.accountGroupId,
          namespace: senderId,
          market_id: this.previewHydrationParams?.marketID,
          partner_id: this.previewHydrationParams?.partnerID,
          user_id: this.previewHydrationParams?.userID,
          contact_id: this.previewHydrationParams?.contactID ?? 'Contact',
          net_promoter_score_url: `https://${window.location.host}/leave-review?accountgroupid=${this.previewHydrationParams?.business?.accountGroupId}`,
          net_promoter_score_redirect_url: `${this.npsRedirectURL}?AccountGroupID=${this.previewHydrationParams?.business?.accountGroupId}`,
        };
        return this.templates.render(
          templateHtml,
          parameters,
          TemplateType.TEMPLATE_TYPE_DJANGO,
          {
            accountGroupId: 'account_group_id',
            partnerId: 'partner_id',
            namespace: 'namespace',
            marketId: 'market_id',
            userId: 'user_id',
            contactId: 'contact_id',
          },
          this.locale || undefined,
          undefined,
          undefined,
          this.previewHydrationParams?.useFakeData ?? true,
        );
      }),
    );
  }

  hydrateSubject(templateSubject: string): Observable<string> {
    const parameters = {
      account_group_id: this.previewHydrationParams?.business?.accountGroupId,
      market_id: this.previewHydrationParams?.marketID,
      partner_id: this.previewHydrationParams?.partnerID,
      user_id: this.previewHydrationParams?.userID,
      contact_id: this.previewHydrationParams?.contactID ?? 'Contact',
    };
    return this.templates.render(
      templateSubject,
      parameters,
      TemplateType.TEMPLATE_TYPE_GOLANG_TEXT,
      {
        accountGroupId: 'account_group_id',
        partnerId: 'partner_id',
        marketId: 'market_id',
        userId: 'user_id',
        contactId: 'contact_id',
      },
      this.locale || undefined,
      undefined,
      undefined,
      this.previewHydrationParams?.useFakeData ?? true,
    );
  }

  replaceDefaultLogoParam(content: string): Observable<string> {
    return of(content.replace('{{ partnerLogo }}', 'https://via.placeholder.com/220x72?text=%20'));
  }
}
