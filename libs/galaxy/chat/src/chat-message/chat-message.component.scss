@use 'design-tokens' as *;

$bubble-min-height: 36px;
$bubble-border-radius: calc($bubble-min-height/2);
$bubble-border-radius-sharp: 4px;
$status-height: 24px;

// css var --animation-duration is used throughout this stylesheet. this allows
// observatory examples to slow down the animation for fine-tuning the ux. this
// value is used as the default.
$animation-duration: 0s;

:host {
  display: flex;
  flex-direction: column;
  max-width: 100%;
}

:host-context(.chat-wrapper.type-sent) :host {
  align-items: flex-end;
}

:host-context(.chat-wrapper.type-received) :host {
  align-items: flex-start;
  .metadata {
    flex-direction: row-reverse;
  }
}

.bubble-container {
  max-width: 100%;
  display: flex;
  align-items: center;
  gap: $spacing-1;
}

.bubble {
  max-width: 100%;
  font-size: $font-preset-4-size;
  line-height: 1.4;
  // ^ yields a 36px bubble height for one-line messages, so the bubble matches
  // the avatar size.
  word-break: break-word;
  color: $primary-text-color;
  border-radius: $bubble-border-radius;
  min-height: $bubble-min-height;
  z-index: 2;
  transition: border-radius calc(var(--animation-duration, $animation-duration) * 1.25) ease-in-out;
}

//
// Content is Message

.content-message {
  background-color: $chat-bubble-background-color;
  padding-top: var(--custom-chat-padding-top, $spacing-2);
  padding-bottom: var(--custom-chat-padding-bottom, $spacing-2);
  padding-left: var(--custom-chat-padding-left, $spacing-3);
  padding-right: var(--custom-chat-padding-right, $spacing-3);
}
.content-message ::ng-deep a {
  color: $link-color;
  text-decoration: underline;
  text-underline-offset: 3px;
}

:host-context(.type-sent.from-you) .content-message {
  background-color: var(--custom-chat-bubble-color, $glxy-blue-800);
  color: var(--custom-chat-bubble-text-color, $white);

  ::ng-deep a {
    color: $white;
  }
}

// handle markdown formatting
.bubble .message-is-markdown ::ng-deep {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: $spacing-3 0 $spacing-2;
  }
  ul,
  ol {
    margin: $spacing-2 0;
    padding-left: $spacing-4;
  }
  p {
    margin: $spacing-2 0;
  }
  img {
    max-width: 100%;
    height: auto;
  }
  table {
    width: 100%;
    max-width: 100%;
    margin: $spacing-2 0;
    border: 1px solid $border-color;
    border-spacing: 0;
    border-collapse: collapse;

    td,
    th {
      padding: $spacing-2;
      vertical-align: top;
      border: 1px solid $border-color;
    }
  }

  // make sure there is no padding on the first and last element
  *:first-child {
    margin-top: 0;
  }
  *:last-child {
    margin-bottom: 0;
  }

  code {
    white-space: break-spaces;
  }
}

//
// Content is Attachment

.content-attachment {
  border: 1px solid $border-color;
  max-width: 280px;
  width: 100%;
  overflow: hidden;
}

//
// Sent message styling

:host-context(.chat-wrapper.type-sent) {
  &:not(:first-of-type):not(:last-of-type) .bubble {
    border-top-right-radius: $bubble-border-radius-sharp;
    border-bottom-right-radius: $bubble-border-radius-sharp;
  }
  &:first-of-type .bubble {
    border-bottom-right-radius: $bubble-border-radius-sharp;
  }
  &:last-of-type .bubble {
    border-top-right-radius: $bubble-border-radius-sharp;
  }
  &:only-of-type .bubble {
    border-radius: $bubble-border-radius;
  }
}

//
// Received message styling

:host-context(.chat-wrapper.type-received) {
  &:not(:first-of-type):not(:last-of-type) .bubble {
    border-top-left-radius: $bubble-border-radius-sharp;
    border-bottom-left-radius: $bubble-border-radius-sharp;
  }

  &:first-of-type .bubble {
    border-bottom-left-radius: $bubble-border-radius-sharp;
  }
  &:last-of-type .bubble {
    border-top-left-radius: $bubble-border-radius-sharp;
  }
  &:only-of-type .bubble {
    border-radius: $bubble-border-radius;
  }
}

.metadata {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: $spacing-2;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: $font-preset-5-size;
  color: $tertiary-text-color;
  font-weight: 400;
  z-index: 1;
  padding: 0 $spacing-3;
  margin-bottom: $spacing-1;
  // ^ when the metadata is 0 height, its bottom margin serves as the gap
  // between bubbles in the same message group
  height: $status-height;
  max-height: 0;
  opacity: 0;
  transition:
    max-height var(--animation-duration, $animation-duration) ease-in-out,
    opacity var(--animation-duration, $animation-duration) ease-in-out;
  pointer-events: none;

  // reveal metadata when tapped, if not empty
  &.show:not(:empty) {
    max-height: $status-height;
    opacity: 1;
    pointer-events: auto;
  }
}

.message-status-time {
  display: flex;
  flex-direction: row;
  align-items: center;

  // show a bullet between status and time if both are non-empty
  .message-status:not(:empty) + .message-time:not(:empty) {
    &::before {
      content: '·';
      margin: 0 $spacing-1;
    }
  }
}

// failed message
:host(.failed) {
  // set color for failed status and icon
  .error-icon {
    color: $error-text-color;
    flex-shrink: 0;
    opacity: 0;
    // animate icon when it mounts so it fades in at same speed as status
    animation: fade-in var(--animation-duration, $animation-duration) ease-in forwards;
  }
  .metadata {
    color: $error-text-color;

    // always hide timestamp for failed messages
    .message-time {
      display: none;
    }
  }
}

// last message in any group
:host(:last-child) {
  // always show metadata when message is last bubble in group
  .metadata {
    max-height: $status-height;
  }
}

// other messages in group besides last one
:host(:not(:last-child):not(.failed)) {
  // always show the timestamp on non-failed messages when they tap to show
  // status. using hover/focus to show the timestamp right at the start of the
  // tap. this is a bit finicky, but need this specificity so that the timestamp
  // doesn't show up when the user sends a new message and the previous status
  // fades out. (without this specificity, the timestamp would show up during
  // the fade out, which looks weird and causes the status to jump to the left.)
  &:focus .metadata,
  &:hover .metadata,
  .metadata.show {
    .message-time:not(:empty) {
      display: unset;
    }
  }
}

// last bubble in last group of sent messages
:host-context(glxy-chat-message-group:first-child) .type-sent :host(:last-child):not(.sending),
:host-context(glxy-chat-message-group:first-child)
  .type-received.show-received-metadata
  :host(:last-child):not(.sending) {
  // make metadata visible on most recent message, unless status is sending.
  // once the sending is complete, the status will appear.
  .metadata {
    opacity: 1;
  }
}

// last bubble in not-last group
:host-context(glxy-chat-message-group:not(:first-child)) :host(:last-child) {
  // make metadata invisible and fade it in when tapped
  .metadata {
    opacity: 0;
    &.show {
      opacity: 1;
    }
  }
}

// when a bubble is added to the last group, animate its height and opacity so
// it slides up from the bottom
:host-context(glxy-chat-message-group:first-child) {
  .type-sent.from-you {
    .glxy-chat-message + :host(.sending) {
      animation: grow-in calc(var(--animation-duration, $animation-duration) * 2) ease-in forwards;
    }
  }
  .type-sent:not(.from-you),
  .type-received {
    .glxy-chat-message + :host {
      animation: grow-in calc(var(--animation-duration, $animation-duration) * 2) ease-in forwards;
    }
  }
}

@keyframes grow-in {
  0% {
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    z-index: 1;
  }
  50% {
    opacity: 1;
  }
  99% {
    max-height: 200px;
    opacity: 1;
    overflow: hidden;
    z-index: 1;
  }
  100% {
    max-height: unset;
    opacity: 1;
    overflow: inherit;
    z-index: unset;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

// ==============================

// isTyping Animation

.dot {
  animation: typingAnimation 2s infinite ease-in-out;
  background-color: $glxy-grey-500;
  border-radius: 50%;
  height: 6px;
  width: 6px;
  margin-right: $spacing-1;
  vertical-align: middle;
  display: inline-block;

  :host-context(.type-sent.from-you) & {
    background-color: $white;
  }

  &:last-child {
    margin-right: 0;
  }
  &:nth-child(1) {
    animation-delay: 200ms;
  }
  &:nth-child(2) {
    animation-delay: 300ms;
  }
  &:nth-child(3) {
    animation-delay: 400ms;
  }
}

@keyframes typingAnimation {
  0% {
    transform: translateY(0px);
  }
  15% {
    transform: translateY(-7px);
  }
  30% {
    transform: translateY(0px);
  }
}
