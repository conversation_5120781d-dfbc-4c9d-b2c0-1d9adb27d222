import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { first } from 'rxjs/operators';
import {
  FileInfo,
  FileUploadError,
  FileUploadStatus,
  GalaxyBaseUploaderServiceInterface,
  UploadContext,
  UploadResponse,
} from './uploader.interface';

@Injectable()
export class GalaxyUploaderService implements GalaxyBaseUploaderServiceInterface {
  /** List of current files in the upload queue */
  protected files: FileInfo[] = [];

  /** Max number of files allowed for upload */
  private maxFiles?: number;

  /** Max size of the files allowed for upload. Bytes. */
  private maxFileSize?: number;

  /** Max number of files to upload in parallel. 0 means no limit */
  private numParallel?: number;

  /** Whether or not to auto upload files when added to the queue */
  private autoUpload?: boolean;

  /** URL to upload files to */
  public uploadUrl = '';

  /** Emits the current list of file info objects in the queue */
  public files$$: BehaviorSubject<FileInfo[]> = new BehaviorSubject(this.files);

  /**
   * @deprecated Use fileUploaded$ (one $)
   */
  public fileUploaded$$: Subject<FileInfo> = new Subject();
  /**
   * Emits when a file has been uploaded successfully
   */
  public readonly fileUploaded$: Observable<FileInfo> = this.fileUploaded$$.asObservable();

  /** Emits when a file has failed to upload */
  public fileErrored$$ = new BehaviorSubject<FileUploadError | null>(null);

  constructor(protected http: HttpClient) {}

  /** Trigger an upload the provided file */
  private uploadFile(fileInfo: FileInfo): void {
    fileInfo.status = FileUploadStatus.InProgress;
    fileInfo.uploadSub = this.buildRequest(fileInfo)
      .pipe(first())
      .subscribe({
        next: (response) => this.onSuccess(fileInfo, response),
        error: () => this.onError(fileInfo),
      });

    this.refreshFiles();
  }

  /** Check queue to see if we can upload the next queued file */
  private checkQueue(): void {
    const numInProgress = this.files.filter(
      (fileInfo: FileInfo) => fileInfo.status === FileUploadStatus.InProgress,
    ).length;

    const queued = this.files.filter((fileInfo: FileInfo) => fileInfo.status === FileUploadStatus.Queued);

    // If nothing is queued or too many uploads are running at once, wait for later
    if (!queued.length || (this.numParallel && numInProgress >= this.numParallel)) {
      return;
    }

    const file = queued.pop();
    if (file) {
      this.uploadFile(file);
      this.checkQueue();
    }
  }

  /**
   * Get the index of a file, in the queue
   *
   * @param fileInfo - The file info to lookup the index of
   */
  private getFileIndex(fileInfo: FileInfo): number {
    const { file } = fileInfo;

    // If not source file, just check names
    if (!file) {
      return this.files.findIndex((fileEntry: FileInfo) => fileEntry.name === fileInfo.name);
    }

    // Otherwise do a deep check
    return this.files.findIndex((fileEntry: FileInfo) => {
      const { file: compareTo } = fileEntry;
      if (!compareTo) {
        return file.name === fileEntry.name;
      }

      return file.name === compareTo.name && file.size === compareTo.size && file.type === compareTo.type;
    });
  }

  /**
   * Mark a file as successfully uploaded
   *
   * @param fileInfo - File info object that has been uploaded
   * @param resp - API response data
   */
  private onSuccess(fileInfo: FileInfo, resp: UploadResponse): void {
    fileInfo.resp = resp;
    fileInfo.url = this.prependProtocol(resp?.data?.url);

    fileInfo.status = FileUploadStatus.Success;
    this.refreshFiles();

    this.fileUploaded$$.next(fileInfo);

    this.checkQueue();
  }

  /**
   * Mark a file as failed to upload
   *
   * @param fileInfo - File info object that has failed
   * @param error - Reason for the failure
   */
  private onError(fileInfo: FileInfo): void {
    fileInfo.status = FileUploadStatus.Fail;
    this.refreshFiles();

    this.fileErrored$$.next({
      fileInfo,
      error: new Error('GALAXY.UPLOADER.ERROR.GENERIC'),
    });

    this.checkQueue();
  }

  /**
   * Remove a file from the queue, making sure to stop any that are currently being uploaded
   *
   * @param index - index of the file to stop uploading and remove
   */
  private removeFileAtIndex(index: number): void {
    const files = this.files.splice(index, 1);
    if (files.length) {
      const fileInfo = files[0];

      // We only care to unsub and kickoff the next in the queue, in this case
      if (fileInfo.status === FileUploadStatus.InProgress) {
        fileInfo.uploadSub?.unsubscribe();
        this.checkQueue();
      }
    }

    this.refreshFiles();
  }

  /**
   * If the url is missing a protocol, add it.
   *
   * @param url - URL to add protocol to
   */
  private prependProtocol(url: string): string {
    if (!url.startsWith('http')) {
      let prependString = 'https:';
      if (!url.startsWith('//')) {
        prependString += '//';
      }
      return prependString + url;
    }
    return url;
  }

  /**
   * Make a file info object and add it to the queue, and make sure it adheres to constraints.
   * Note that file type is checked by the html input via "accept" list
   *
   * @param file - File to add to the queue
   * @param context - Optional data to inform subscribers "where" this file was uploaded (e.g. from which component)
   */
  public addFile(file: File, context?: UploadContext): void {
    const fileInfo = {
      file,
      name: file.name,
      context: context,
    } as FileInfo;

    // Cannot add exact duplicates
    if (this.getFileIndex(fileInfo) > -1) {
      return this.fileErrored$$.next({
        fileInfo,
        error: new Error('GALAXY.UPLOADER.ERROR.DUPLICATE_FILE_ATTEMPT'),
      });
    }

    if (!!this.maxFileSize && file.size > this.maxFileSize) {
      return this.fileErrored$$.next({
        fileInfo,
        error: new Error('GALAXY.UPLOADER.ERROR.MAX_FILE_SIZE_EXCEEDED'),
      });
    }

    // If a file has been added, and not uploaded/in progress, kill it and use the new one
    if (this.maxFiles === 1 && this.files.length) {
      const { status } = this.files[0];

      if (status === FileUploadStatus.Fail || status === FileUploadStatus.Queued) {
        this.clear();
      }
    }

    if (this.maxFiles && this.maxFiles > 0 && this.files.length >= this.maxFiles) {
      return this.fileErrored$$.next({
        fileInfo,
        error: new Error('GALAXY.UPLOADER.ERROR.MAX_FILES_EXCEEDED'),
      });
    }

    fileInfo.status = FileUploadStatus.Queued;
    this.files.push(fileInfo);
    this.refreshFiles();

    if (this.autoUpload) {
      this.checkQueue();
    }
  }

  /**
   * Remove a file from the queue
   *
   * @param fileInfo - File to remove from the queue
   */
  public removeFile(fileInfo: FileInfo): void {
    const index = this.getFileIndex(fileInfo);
    this.removeFileAtIndex(index);
  }

  /** Clear out the queue */
  public clear(): void {
    this.files.forEach((file) => this.removeFile(file));
  }

  /**
   * Public interface for kicking off the entire queue to be uploaded
   */
  public uploadQueuedFiles(): void {
    this.checkQueue();
  }

  public setFiles(files: FileInfo[]): void {
    this.files = files;
    this.refreshFiles();
  }

  public setMaxFiles(limit: number): void {
    this.maxFiles = limit;
  }

  public setMaxFileSize(limit: number): void {
    this.maxFileSize = limit;
  }

  public setUploadUrl(url: string): void {
    this.uploadUrl = url;
  }

  public setParallelUploads(limit: number): void {
    this.numParallel = limit;
  }

  public setAutoUpload(allowAuto: boolean): void {
    this.autoUpload = allowAuto;
  }

  /**
   * Used to build an api request object for uploading files.
   * Note: This is to be overridden if adding own requeset functionality, testing, etc.
   *
   * @param fileInfo - File to upload
   */
  public buildRequest(fileInfo: FileInfo): Observable<UploadResponse> {
    const body = new FormData();
    if (fileInfo.file) body.append('file', fileInfo.file);
    return this.http.post(this.uploadUrl, body, { withCredentials: true }) as Observable<UploadResponse>;
  }

  /**
   * Refresh the file list for consumers
   */
  protected refreshFiles(): void {
    this.files$$.next(this.files);
  }
}
