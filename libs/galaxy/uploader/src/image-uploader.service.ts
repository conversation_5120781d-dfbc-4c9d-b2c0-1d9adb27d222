import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { EMPTY, Observable } from 'rxjs';
import { FileInfo, UploadContext, UploadResponse } from './uploader.interface';
import { GalaxyUploaderService } from './uploader.service';

export interface ImageDimensions {
  width?: number;
  height?: number;
}

@Injectable()
export class GalaxyImageUploaderService extends GalaxyUploaderService {
  private maxDimensions?: ImageDimensions;
  private minDimensions?: ImageDimensions;

  /**
   * Used to build an api request object for uploading files.
   * Note: This is to be overridden if adding own requeset functionality, testing, etc.
   *
   * @param fileInfo - File to upload
   * @param params - Query parameters to include on the upload URL
   */
  buildRequest(fileInfo: FileInfo, params?: HttpParams): Observable<UploadResponse> {
    // Try to upload the cropped image instance, otherwise use the file itself
    const imageToUpload = fileInfo?.data?.croppedImage || fileInfo.file;
    if (!imageToUpload) {
      this.fileErrored$$.next({
        fileInfo,
        error: new Error('GALAXY.UPLOADER.ERROR.NO_VALID_FILE_IMAGE'),
      });
      return EMPTY;
    }

    const body = new FormData();
    body.append('file', imageToUpload);
    const options: Record<string, unknown> = { withCredentials: true };
    if (params) {
      options['params'] = params;
    }
    return this.http.post(this.uploadUrl, body, options) as Observable<UploadResponse>;
  }

  /**
   * Set the cropped image property on the provided file info object
   * @param info - Info object to set the new image on
   * @param croppedImage - The edited image to add to the file info
   */
  public setCroppedImage(info: FileInfo, croppedImage: Blob): void {
    if (!info.data) {
      info.data = {};
    }

    info.data.croppedImage = croppedImage;

    this.refreshFiles();
  }

  /**
   * Set the max dimensions of files allowed to be added to the queue
   * @param dimensions - Max width and/or height allowed to be uploaded
   */
  public setMaxDimensions(dimensions: ImageDimensions): void {
    this.maxDimensions = dimensions;
  }

  /**
   * Set the min dimensions of files allowed to be added to the queue
   * @param dimensions - Min width and/or height allowed to be uploaded
   */
  public setMinDimensions(dimensions: ImageDimensions): void {
    this.minDimensions = dimensions;
  }

  /**
   * Add a file to the queue
   * @param file - The file to add
   * @param context - Optional data to inform subscribers "where" this file was uploaded (e.g. from which component)
   */
  public addFile(file: File, context?: UploadContext): void {
    if (!this.maxDimensions && !this.minDimensions) {
      return super.addFile(file, context);
    }

    const reader = new FileReader();
    reader.onload = () => {
      // Need to create an image from the file so that we can test dimensions
      const image = new Image();
      image.onload = () => {
        const { width, height } = this.maxDimensions || {};
        if ((width && image.width > width) || (height && image.height > height)) {
          return this.fileErrored$$.next({
            fileInfo: {
              name: file.name,
              file,
              context,
            } as FileInfo,
            error: new Error('GALAXY.UPLOADER.ERROR.MAX_DIMENSIONS_EXCEEDED'),
          });
        }
        const minWidth = this.minDimensions?.width;
        const minHeight = this.minDimensions?.height;
        if ((minWidth && image.width < minWidth) || (minHeight && image.height < minHeight)) {
          return this.fileErrored$$.next({
            fileInfo: {
              name: file.name,
              file,
              context,
            } as FileInfo,
            error: new Error('GALAXY.UPLOADER.ERROR.MIN_DIMENSIONS_NOT_MET'),
          });
        }
        super.addFile(file, context);
      };

      image.src = reader.result?.toString() || '';
    };

    reader.readAsDataURL(file);
  }
}
