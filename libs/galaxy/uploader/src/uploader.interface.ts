import { InjectionToken } from '@angular/core';
import { BehaviorSubject, Observable, Subject, Subscription } from 'rxjs';

/** Status of a file in the queue */
export enum FileUploadStatus {
  Queued,
  InProgress,
  Success,
  Fail,
}

/** Information for processing a file upload */
export interface FileInfo {
  context?: UploadContext; // TODO: Make required (breaking change)
  name: string;
  status?: FileUploadStatus;
  url?: string;
  file?: File;
  uploadSub?: Subscription;
  data?: any;
  resp?: any;
}

/** Error object containing the source error and file it occurred for */
export interface FileUploadError {
  fileInfo: FileInfo;
  error: Error;
}

/** Response for upload success. Taken from Google JSON Guide https://google.github.io/styleguide/jsoncstyleguide.xml */
export interface UploadResponse {
  data: {
    url: string;
  };
}

export interface UploadContext {
  uuid: string;
}

/** Interface for services to extend */
export interface GalaxyBaseUploaderServiceInterface {
  uploadUrl: string;
  files$$: BehaviorSubject<FileInfo[]>;
  /**
   * @deprecated Use fileUploaded$ (one $)
   */
  fileUploaded$$: Subject<FileInfo>;
  fileUploaded$: Observable<FileInfo>;
  fileErrored$$: BehaviorSubject<FileUploadError | null>;

  addFile(file: File, context: UploadContext): void;
  removeFile(fileInfo: FileInfo): void;
  clear(): void;
  uploadQueuedFiles(): void;
  setFiles(files: FileInfo[]): void;
  setMaxFiles(limit: number): void;
  setMaxFileSize(limit: number): void;
  setUploadUrl(url: string): void;
  setParallelUploads(limit: number): void;
  setAutoUpload(allowAuto: boolean): void;
  buildRequest(fileInfo: FileInfo): Observable<UploadResponse>;
}

export const GALAXY_UPLOADER_SERVICE_TOKEN = new InjectionToken<GalaxyBaseUploaderServiceInterface>(
  '[GalaxyUploaderService]: token for galaxy uploader',
);
