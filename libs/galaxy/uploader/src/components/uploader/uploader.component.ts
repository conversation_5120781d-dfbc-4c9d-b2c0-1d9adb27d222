import { HttpClient } from '@angular/common/http';
import {
  booleanAttribute,
  Component,
  DestroyRef,
  ElementRef,
  EventEmitter,
  HostBinding,
  Inject,
  Input,
  OnChanges,
  OnInit,
  Optional,
  Output,
  SimpleChange,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { filter, tap } from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';
import { FileInfo, FileUploadError, GALAXY_UPLOADER_SERVICE_TOKEN, UploadContext } from '../../uploader.interface';
import { GalaxyUploaderService } from '../../uploader.service';
import mime from 'mime';

let browserSupportsDrag: boolean | undefined;

function changesValid(change: SimpleChange): boolean {
  if (!change) {
    return false;
  }

  return change.firstChange || (change.currentValue && change.currentValue !== change.previousValue);
}

@Component({
  selector: 'glxy-uploader',
  templateUrl: './uploader.component.html',
  styleUrls: ['./uploader.component.scss'],
  standalone: false,
})
export class GalaxyUploaderComponent implements OnInit, OnChanges {
  @HostBinding('class') class = 'glxy-uploader';

  /**
   * Max number of files allowed to upload
   * 0 means no limit
   */
  @Input() maxFiles?: number;

  /**
   * Max files size, in bytes, of individual files that can be uploaded
   * 0 means no limit
   */
  @Input() maxFileSize?: number;

  /**
   * Comma separated file types to accept
   * See MDN on File Type Specifiers https://mzl.la/3gytrGi
   */
  @Input() accept?: string;

  /** URL to upload files to */
  @Input() uploadUrl!: string;

  /**
   * Number of parallel uploads to allow
   * 0 means no limit
   */
  @Input() numParallel?: number = 10;

  /** Whether or not to upload files when they are added to the list */
  @Input({ transform: booleanAttribute }) autoUpload = true;

  /** Choose the the layout of the component */
  @Input() layout: 'default' | 'horizontal' | 'button-only' = 'default';

  /** Base set of files that the service should manage */
  @Input() files: FileInfo[] = [];

  /** If provided, will be used as the hint instead of the file constraints text */
  @Input() hintText?: string;

  /** If provided, will override the button text. First item is singular, second is plural */
  @Input() buttonText: [string, string] = ['GALAXY.UPLOADER.CHOOSE_FILE', 'GALAXY.UPLOADER.CHOOSE_FILE_PL'];

  /** If provided, will override the desription text. First item is singular, second is plural */
  @Input() descriptionText: [string, string] = ['GALAXY.UPLOADER.DRAG_AND_DROP', 'GALAXY.UPLOADER.DRAG_AND_DROP_PL'];

  /** If provided, color the outline red to imply uploader has an error */
  @HostBinding('class.has-error')
  @Input({ transform: booleanAttribute })
  hasError = false;

  /**
   * Emits events related to the status of uploads and file selection
   */
  @Output() fileUploadErrored: EventEmitter<FileUploadError> = new EventEmitter();

  /**
   * Emits info for files uploaded successfully
   */
  @Output() fileUploaded: EventEmitter<FileInfo> = new EventEmitter();

  /**
   * Emits the list of files and their status when changes occur
   */
  @Output() filesChanged: EventEmitter<FileInfo[]> = new EventEmitter();

  @ViewChild('fileInput') fileInputEl?: ElementRef;
  @ViewChild('dropTarget') dropTargetEl?: ElementRef;

  supportsDrag: boolean = this.testDragSupport();

  private uuid: string = uuidv4();

  constructor(
    @Inject(GALAXY_UPLOADER_SERVICE_TOKEN) @Optional() public uploadService: GalaxyUploaderService,
    private http: HttpClient,
    private snackbarService: SnackbarService,
    private readonly destroyRef: DestroyRef,
  ) {
    if (!this.uploadService) {
      this.uploadService = new GalaxyUploaderService(this.http);
    }
  }

  ngOnInit(): void {
    this.uploadService.fileErrored$$
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter((error) => !!error),
      )
      .subscribe({
        next: (error: FileUploadError | null) => {
          if (error) this.fileUploadErrored.emit(error);
        },
      });

    this.uploadService.fileUploaded$
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter((fileInfo) => !!fileInfo),
        tap((fileInfo) =>
          !fileInfo.context?.uuid
            ? console.warn('No context on FileInfo. This may cause unrelated file upload components to update.')
            : undefined,
        ),
        filter((fileInfo) => fileInfo.context?.uuid === this.uuid || !fileInfo.context?.uuid),
      )
      .subscribe({
        next: (fileInfo: FileInfo) => this.fileUploaded.emit(fileInfo),
      });

    this.uploadService.files$$
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter((filesChanged) => !!filesChanged),
      )
      .subscribe({
        next: (files: FileInfo[]) => this.filesChanged.emit(files),
      });

    this.uploadService.setFiles(this.files);
    if (this.maxFiles) this.uploadService.setMaxFiles(this.maxFiles);
    this.uploadService.setUploadUrl(this.uploadUrl);
    this.uploadService.setAutoUpload(this.autoUpload);
    if (this.maxFileSize) this.uploadService.setMaxFileSize(this.maxFileSize);
    if (this.numParallel) this.uploadService.setParallelUploads(this.numParallel);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changesValid(changes.files)) {
      this.uploadService.setFiles(this.files);
    }

    if (changesValid(changes.maxFiles)) {
      this.uploadService.setMaxFiles(changes.maxFiles.currentValue);
    }

    if (changesValid(changes.uploadUrl)) {
      this.uploadService.setUploadUrl(this.uploadUrl);
    }

    if (changesValid(changes.autoUpload)) {
      this.uploadService.setAutoUpload(this.autoUpload);
    }

    if (changesValid(changes.maxFileSize)) {
      this.uploadService.setMaxFileSize(changes.maxFileSize.currentValue);
    }

    if (changesValid(changes.numParallel)) {
      this.uploadService.setParallelUploads(changes.numParallel.currentValue);
    }
  }

  /**
   * Prevent bubbling of the DOM event to any other elements in the hierarchy.
   *
   * @param event - DOM event related to the input event
   */
  preventBubbling(event: Event): void {
    event.stopPropagation();
    event.preventDefault();
  }

  /**
   * Initiate the system file browser by proxying to
   * the hidden input element.
   */
  browseForFiles(): void {
    this.fileInputEl?.nativeElement.click();
  }

  /**
   * Event fired from the input event, when files have
   * been selected via the system browser.
   *
   * @param event - DOM event related to files being selected
   */
  filesSelected(event: any): void {
    const { files } = event.target;
    this.addFiles(files, { uuid: this.uuid });
    this.resetFileInput();
  }

  /**
   * Triggered when files have been dropped onto the drop target.
   *
   * @param event - DOM event related to fles being dropped on a drop target
   */
  filesDropped(event: any): void {
    const dt = event.dataTransfer;
    const files = dt.files;
    this.addFiles(files, { uuid: this.uuid });
  }

  /**
   * Sets the state of the drop target to indicate files can be dropped here.
   *
   * @param event - DOM event related to the drag action
   */
  dragOver(event: DragEvent): void {
    this.preventBubbling(event);
    this.dropTargetEl?.nativeElement.classList.add('dragging-over');
  }

  /**
   * Sets the state back from drag over to default state.
   *
   * @param event
   */
  dragDone(event: DragEvent): void {
    this.preventBubbling(event);
    this.dropTargetEl?.nativeElement.classList.remove('dragging-over');
  }

  /**
   * Tell the uploader service to remove a file from the upload queue.
   *
   * @param fileInfo - Info for the file to remove from the queue
   */
  deleteFile(fileInfo: FileInfo): void {
    this.uploadService.removeFile(fileInfo);
  }

  /**
   * Trigger the uploader service to upload all queued files.
   */
  uploadQueuedFiles(): void {
    this.uploadService.uploadQueuedFiles();
  }

  /**
   * Given a list of files, add them to the upload queue.
   *
   * @param files - List of File objects to add to the queue
   * @param context - Optional data to inform subscribers "where" this file was uploaded (e.g. from which component)
   */
  private addFiles(files: FileList, context?: UploadContext): void {
    const supportedFileType = this.isSupportedFileType(files);
    const supportedMaxSize = this.isSupportedMaxFileSize(files);

    if (!supportedFileType) {
      this.snackbarService.openErrorSnack('GALAXY.UPLOADER.ERROR.FILE_TYPE_NOT_SUPPORTED');
      return;
    }
    if (!supportedMaxSize) {
      this.snackbarService.openErrorSnack('GALAXY.UPLOADER.ERROR.MAX_FILE_SIZE_EXCEEDED');
      return;
    }

    Object.values(files).forEach((file: File) => this.uploadService.addFile(file, context));
  }

  /**
   * Tests to make sure the browser can support drag and
   * drop for file transfer.
   */
  private testDragSupport(): boolean {
    if (browserSupportsDrag === undefined) {
      browserSupportsDrag = 'draggable' in document.createElement('span');
    }

    return !!browserSupportsDrag;
  }

  /**
   * Resets file input after a file is uploaded.
   * Prevents scenario where user uploads a file, deletes it and then tries to upload it again.
   * The input HTML shouldn't block the file in this case.
   * @private
   */
  private resetFileInput(): void {
    if (this.fileInputEl) this.fileInputEl.nativeElement.value = null;
  }

  /**
   * Checks to see if the file type is supported by the uploader
   * @param files - List of File objects to validate file type
   * @private
   */

  private isSupportedFileType(files: FileList): boolean {
    const acceptedTypes = this.accept?.split(',').map((type) => {
      const trimmedType = type.trim();
      const mimeType = mime.getType(trimmedType);
      return mimeType || trimmedType;
    });

    if (!acceptedTypes) {
      return true;
    }

    // Check if .ico is an accepted type and include windows supported mimeType
    const containsIco = this.accept?.includes('.ico');
    if (containsIco) {
      acceptedTypes.push('image/x-icon');
    }

    return Array.from(files).every((file) => {
      return acceptedTypes.some((acceptedType) => {
        if (acceptedType.startsWith('*')) {
          // Accept any file type
          return true;
        } else if (acceptedType.endsWith('/*')) {
          // Check for wildcard pattern like image/*
          const prefix = acceptedType.slice(0, -1); // Remove the wildcard part
          return file.type.startsWith(prefix);
        } else {
          return file.type === acceptedType; // Exact match
        }
      });
    });
  }

  /**
   * Checks to see if the file size is supported by the uploader
   * @param files - List of File objects to validate file type
   * @private
   */
  private isSupportedMaxFileSize(files: FileList): boolean {
    const acceptedMaxFileSize = this.maxFileSize;

    if (acceptedMaxFileSize) {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        if (file.size > acceptedMaxFileSize) {
          return false;
        }
      }
    }
    return true;
  }
}
