import { booleanAttribute, Component, HostBinding, Inject, Input, Optional, ViewEncapsulation } from '@angular/core';
import { PageService } from '../page.service';
import { GALAXY_PAGE_OPTIONS, GalaxyPageOptions } from '../tokens';

@Component({
  selector: 'glxy-page-toolbar',
  templateUrl: './page-toolbar.component.html',
  styleUrls: ['./page-toolbar.component.scss'],
  encapsulation: ViewEncapsulation.None,
  standalone: false,
})
export class PageToolbarComponent {
  @HostBinding('class') class = 'glxy-page-toolbar';
  @Input({ transform: booleanAttribute }) showExtendedToolbar = false;

  @HostBinding('class.extended-toolbar-has-padding')
  @Input({ transform: booleanAttribute })
  extendedToolbarPadding = true;

  @Input() showNavToggle: GalaxyPageOptions['showToggle'] = this.pageService.showNavToggle ?? false;

  constructor(
    private readonly pageService: PageService,
    @Optional() @Inject(GALAXY_PAGE_OPTIONS) private readonly pageOptions: GalaxyPageOptions | undefined,
  ) {}

  toggleNav(): void {
    this.pageOptions?.toggleNav();
  }
}
