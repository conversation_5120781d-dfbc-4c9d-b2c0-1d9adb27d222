import { animate, state, style, transition, trigger } from '@angular/animations';
import { Component, inject, Input, OnDestroy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { GalaxyAiIconService } from '@vendasta/galaxy/ai-icon';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { ReviewDeletedStatus } from '@vendasta/reputation';
import { PhoneNumberFormatPipe } from '@vendasta/uikit';
import formatDistance from 'date-fns/formatDistance';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { finalize, first, map, share, shareReplay, take, takeUntil, tap } from 'rxjs/operators';
import { starCount } from '../common/utils';
import {
  ReviewCardResponseTemplatesService,
  ReviewResponseTemplateParameters,
} from '../response-templates/response-templates.service';
import { ConnectAccountConfig } from './connect-account-dialog.component';
import { removeGoogleTranslation } from './remove-google-translation';
import { ReviewsService } from './review-card.service';
import { ReviewHistoryCardComponent } from './review-history-card/review-history-card.component';
import {
  CardText,
  CommentEditorVisibleMap,
  ConciergeReviewResponseData,
  DigitalAgentViewData,
  HighlightOptions,
  ResponseTemplate,
  Review,
  ReviewComment,
  ReviewConfig,
  ReviewPublishSettings,
  ReviewResponseSetting,
  ReviewStatus,
  SOURCE_IDS,
  VTMReviewFlags,
} from './review-types';

const APARTMENT_RATINGS = 'APAR';

@Component({
  selector: 'reviews-common-card',
  templateUrl: './common-review-card.component.html',
  styleUrls: ['./common-review-card.component.scss'],
  animations: [
    trigger('showHelperText', [
      state(
        'true',
        style({
          height: '*',
          margin: '*',
          opacity: 1,
        }),
      ),
      state(
        'false',
        style({
          height: '0px',
          margin: '0px 76px 0px 16px',
          opacity: 0,
        }),
      ),
      transition('true <=> false', [animate('0.25s')]),
    ]),
  ],
  standalone: false,
})
export class ReviewCardComponent implements OnInit, OnDestroy {
  @Input() reviewConfig: ReviewConfig;
  @Input() review: Review;
  @Input() cardText: CardText;
  @Input() connectAccountConfig: ConnectAccountConfig;
  @Input() reviewResponseSettings: ReviewResponseSetting;
  @Input() reviewPublishSettings: ReviewPublishSettings;
  @Input() taskManagerReviewResponseData$: Observable<ConciergeReviewResponseData>;
  @Input() responseTemplates: ResponseTemplate[];
  @Input() taxonomyIds: string[];
  @Input() highlightOptions: HighlightOptions;
  // templateApplicationId specifies the application which the response templates belong to. It is "reputation" by default.
  @Input() templateApplicationId = 'reputation';
  @Input() digitalAgentViewData: DigitalAgentViewData;
  @Input() showFulfillmentRespondedBadge = false;
  // trackExternalLinkClicks enables tracking on external links for reviews. This should be enabled for Reputation Client only
  @Input() trackExternalLinkClicks = false;
  // Feature flag value for non_english_review_response_templates
  @Input() nonEnglishReviewResponseTemplates = false;
  // Feature flag value for hide_google_translated_review_content
  @Input() hideGoogleTranslatedReviewContent = false;
  // Feature flag value for suggestResponseOnAllSources
  @Input() suggestResponseOnAllSources = false;
  // Whether the "suggest response" button should be visible. It is enabled by default.
  @Input() openAIEnabled = true;
  @Input() reviewHistoryEnabled = true;

  GalaxyAiIconService = inject(GalaxyAiIconService);

  showRespondBox = false;
  showRespondBoxWithSuggestedText = false;
  showTaskManagerRespondBox = false;
  showTaskManagerComment = true;
  provideTaskManagerFeedback = false;
  showComments = false;
  showRespondButton = true;
  isCommentEditable = false;
  reviewDataLink: string;
  isDebug$: Observable<boolean>;
  shouldDisplayReviewWidgetMenu = false;
  isPublishedThirdPartyReview = false;
  isPublishedMyListingReview = false;
  disableThirdPartyReviewSourceIDs = [
    SOURCE_IDS.YELP,
    SOURCE_IDS.HOTELS_DOT_COM,
    SOURCE_IDS.BOOKING,
    SOURCE_IDS.TRIPADVISOR,
    SOURCE_IDS.MY_LISTING,
  ];
  ReviewStatus = ReviewStatus;
  taskManagerResponse$: Observable<ConciergeReviewResponseData>;
  isSharingReviewEnabled = false;
  isPublishingReviewEnabled = false;
  isPositiveRecommendation = false;
  isNegativeRecommendation = false;
  isRecommendation = false;
  enableResponse = false;
  responseBoxText$: Observable<string>;

  language: string;

  commentEditorVisibleMap$$: BehaviorSubject<CommentEditorVisibleMap> = new BehaviorSubject({});
  commentEditorVisibleMap$: Observable<CommentEditorVisibleMap>;

  review$$: BehaviorSubject<Review> = new BehaviorSubject(null);
  review$: Observable<Review> = this.review$$.asObservable();
  businessReviewsURL: string;
  businessSettingsURL: string;

  sourceIds = SOURCE_IDS;
  reviewDeletedStatus = ReviewDeletedStatus;

  lastTemplateUsed: { referencedTemplate: ResponseTemplate; renderedTemplate: string } = null;

  isLastFailedReviewComment = false;
  lastFailedReviewCommentReason = false;
  showlastFailedReviewCommentResponseBox = true;
  isHelperTextVisible = false;
  canPublishDirect$: Observable<boolean>;
  canPublishDirectLoading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  digitalAgentView: boolean;
  VTMReviewFlags = VTMReviewFlags;
  digitalAgentTaskFlags: string[] = [];
  initialResponseValue: string;
  digitalAgentButtonDisabled$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  isPublishLoading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  isRequestingApprovalLoading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  hasOwnerResponded: boolean;
  hasDigitalAgentResponded: boolean;
  reviewResponseTemplatePage = 0;
  editedFromNow: string;
  private unsubscribe: Subject<void> = new Subject();
  private suggestingResponse$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  suggestingResponse$: Observable<boolean> = this.suggestingResponse$$.asObservable();
  reviewStars: number[];

  constructor(
    private reviewsService: ReviewsService,
    private translateService: TranslateService,
    private templatesService: ReviewCardResponseTemplatesService,
    private snowplow: ProductAnalyticsService,
    private snackbarService: SnackbarService,
    private router: Router,
    private dialog: MatDialog,
    private route: ActivatedRoute,
    private formatPhoneNumber: PhoneNumberFormatPipe,
  ) {}

  ngOnInit(): void {
    if (this.reviewConfig.partnerId === APARTMENT_RATINGS) {
      this.responseTemplates = [];
      this.responseBoxText$ = this.translateService.stream(
        'COMMON_REVIEW_CARD.REVIEWS.MANAGE.REVIEW_CARD.RESPONSE_BOX.NO_TEMPLATE_RESPONSE_TOOLTIP',
      );
    } else {
      this.responseBoxText$ = this.translateService.stream(
        'COMMON_REVIEW_CARD.REVIEWS.MANAGE.REVIEW_CARD.RESPONSE_BOX.RESPOND_TOOLTIP',
      );
    }
    this.highlightReviewContent();
    this.commentEditorVisibleMap$ = this.commentEditorVisibleMap$$.asObservable();
    this.isDebug$ = this.route.queryParams.pipe(
      map((params) => params.debug === 'true'),
      first(),
      shareReplay(1),
    );
    this.reviewDataLink = this.getReviewDataLink(this.review.listingID);
    this.showRespondButton = this.reviewConfig.enableResponse;
    this.enableResponse = this.reviewConfig.enableResponse;

    this.shouldDisplayReviewWidgetMenu = this.shouldShowReviewWidget();
    this.isPublishedMyListingReview = this.review.isPublished && this.review.sourceID === SOURCE_IDS.MY_LISTING;
    this.isPublishedThirdPartyReview = this.shouldDisplayReviewWidgetMenu && this.isPublishedReview();
    if (this.taskManagerReviewResponseData$) {
      this.taskManagerResponse$ = this.taskManagerReviewResponseData$;
    }

    this.isSharingReviewEnabled = this.reviewConfig && this.reviewConfig.socialMarketingEnabled;
    this.isPublishingReviewEnabled =
      this.review.sourceID === SOURCE_IDS.MY_LISTING || this.shouldDisplayReviewWidgetMenu;
    this.isPositiveRecommendation =
      this.review.sourceID === SOURCE_IDS.FACEBOOK && this.review.recommendation === 'positive';
    this.isNegativeRecommendation =
      this.review.sourceID === SOURCE_IDS.FACEBOOK && this.review.recommendation === 'negative';
    this.isRecommendation = this.isPositiveRecommendation || this.isNegativeRecommendation;

    this.language = this.translateService.defaultLang;
    this.translateService.onLangChange
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((event) => (this.language = event.lang));
    const hasNoReviewComments = this.review.comments && this.review.comments.length < 1;
    this.showRespondBox = hasNoReviewComments && this.canShowResponseBox();
    this.showRespondBoxWithSuggestedText =
      this.openAIEnabled &&
      this.suggestResponseOnAllSources &&
      this.enableResponse &&
      (this.reviewResponseSettings || !this.isResponseSettingRequired()) &&
      (hasNoReviewComments || this.review.sourceID === SOURCE_IDS.FACEBOOK) &&
      !this.canShowResponseBox();
    if (
      !this.showRespondBox &&
      this.showRespondBoxWithSuggestedText &&
      this.reviewConfig.partnerId !== APARTMENT_RATINGS
    ) {
      this.responseBoxText$ = this.translateService.stream(
        'COMMON_REVIEW_CARD.REVIEWS.MANAGE.REVIEW_CARD.RESPONSE_BOX.RESPOND_TOOLTIP_REVIEW_SITES',
      );
    }
    this.review = this.transformReview(this.review);
    this.review$$.next(this.review);

    this.review$.pipe(takeUntil(this.unsubscribe)).subscribe((r) => {
      this.review = r;
      this.digitalAgentButtonDisabled$$.next(
        r.status === ReviewStatus.DIGITAL_AGENT_RESPONDED || r.status === ReviewStatus.OWNER_RESPONDED,
      );
    });

    if (
      this.review.lastFailedReviewCommentReason &&
      this.review.lastFailedReviewCommentReason.includes('Got a 404 response from Google My Business')
    ) {
      this.lastFailedReviewCommentReason = true;
    }
    this.isLastFailedReviewComment =
      this.review.lastFailedReviewCommentText && this.review.status === ReviewStatus.ACTION_REQUIRED;

    this.businessReviewsURL = this.reviewConfig?.company ? this.buildBusinessProductURL('reviews') : null;
    this.businessSettingsURL = this.buildBusinessProductURL('settings');
    this.canPublishDirectLoading$$.next(true);
    this.canPublishDirect$ = this.reviewsService
      .canPublishReviewCommentToSourceAPI(this.review.reviewID, this.reviewConfig.accountgroupId)
      .pipe(
        map((canPublishData) => canPublishData.canPublish),
        finalize(() => this.canPublishDirectLoading$$.next(false)),
      );
    if (this.digitalAgentViewData) {
      this.digitalAgentView = true;
      this.digitalAgentTaskFlags = this.digitalAgentViewData?.task?.tags
        ? [...this.digitalAgentViewData.task.tags]
        : [];
      this.taskManagerReviewResponseData$.pipe(take(1)).subscribe((res) => {
        if (res?.tags?.includes(VTMReviewFlags.RESPONSE_AWAITING_APPROVAL)) {
          this.digitalAgentTaskFlags?.push(VTMReviewFlags.RESPONSE_AWAITING_APPROVAL);
        }
      });
      this.reviewsService.task = this.digitalAgentViewData.task;
    }

    if (this.digitalAgentView) {
      // Hard coding to false until endpoint is setup in VTM
      this.isCommentEditable = false;
    } else if ([SOURCE_IDS.GOOGLE_MAPS, SOURCE_IDS.FACEBOOK].includes(this.review.sourceID)) {
      this.isCommentEditable = this.reviewConfig.isCommentEditable && this.canShowResponseBox();
    } else {
      this.isCommentEditable = this.reviewConfig.isCommentEditable;
    }

    this.hasOwnerResponded = this.review.comments.some((comment) => comment.postedByOwner === true);
    this.hasDigitalAgentResponded = this.review.comments.some((comment) => comment.postedByDigitalAgent === true);

    if (this.review.edited) {
      this.editedFromNow = this.fromNow(this.review.edited);
    }

    this.reviewStars = starCount(this.review.stars);
  }

  respondButtonPressed(): void {
    this.provideTaskManagerFeedback = false;

    if (this.canShowResponseBox()) {
      this.showRespondBox = true;
      const cevm = this.commentEditorVisibleMap$$.getValue();
      this.commentEditorVisibleMap$$.next(this.closeCommentEditor(cevm));
    } else {
      if (this.review.sourceID === SOURCE_IDS.GOOGLE_MAPS) {
        if (this.reviewResponseSettings && this.reviewResponseSettings.gmbAuthUrl) {
          window.location.href = this.reviewResponseSettings.gmbAuthUrl;
        } else {
          window.open(this.businessSettingsURL, '_blank');
        }
      } else if (this.review.sourceID === SOURCE_IDS.FACEBOOK) {
        if (this.reviewResponseSettings && this.reviewResponseSettings.facebookAuthUrl) {
          window.location.href = this.reviewResponseSettings.facebookAuthUrl;
        } else {
          window.open(this.businessSettingsURL, '_blank');
        }
      } else {
        window.open(this.review.respondUrl, '_blank');
      }
    }
  }

  connectButtonPressed(): void {
    if (this.review.sourceID === SOURCE_IDS.GOOGLE_MAPS) {
      if (this.reviewResponseSettings && this.reviewResponseSettings.gmbAuthUrl) {
        window.location.href = this.reviewResponseSettings.gmbAuthUrl;
      } else {
        window.open(this.businessSettingsURL, '_blank');
      }
    } else if (this.review.sourceID === SOURCE_IDS.FACEBOOK) {
      if (this.reviewResponseSettings && this.reviewResponseSettings.facebookAuthUrl) {
        window.location.href = this.reviewResponseSettings.facebookAuthUrl;
      } else {
        window.open(this.businessSettingsURL, '_blank');
      }
    } else {
      window.open(this.review.respondUrl, '_blank');
    }
  }

  respondOnSourceButtonPressed(): void {
    window.open(this.review.respondUrl, '_blank');
  }

  cancelButtonPressed(): void {
    this.showRespondBox = false;
    this.showRespondBoxWithSuggestedText = false;
  }

  canShowResponseBox(): boolean {
    if (!this.reviewResponseSettings || this.provideTaskManagerFeedback) {
      return false;
    }

    switch (this.review.sourceID) {
      case SOURCE_IDS.MY_LISTING:
        return true;
      case SOURCE_IDS.GOOGLE_MAPS: {
        const gmbAccount = this.reviewResponseSettings.gmbAccount;
        return (gmbAccount !== null && gmbAccount !== undefined) || !!this.reviewResponseSettings.gmbAccountConnected;
      }
      case SOURCE_IDS.FACEBOOK: {
        const fbAccounts = this.reviewResponseSettings.facebookAccounts;
        const hasFacebookConnected =
          (fbAccounts && fbAccounts.length > 0) || !!this.reviewResponseSettings.facebookAccountConnected;
        return this.reviewResponseSettings.facebookResponseEnabled && hasFacebookConnected;
      }
      case SOURCE_IDS.APARTMENTS:
        return this.reviewResponseSettings.apartmentsResponseEnabled;
      case SOURCE_IDS.APARTMENT_RATINGS:
        return this.reviewResponseSettings.apartmentRatingsApiResponseEnabled;
      default:
        return false;
    }
  }

  isResponseSettingRequired(): boolean {
    switch (this.review.sourceID) {
      case SOURCE_IDS.MY_LISTING:
      case SOURCE_IDS.GOOGLE_MAPS:
      case SOURCE_IDS.FACEBOOK:
      case SOURCE_IDS.APARTMENTS:
      case SOURCE_IDS.APARTMENT_RATINGS:
        return true;
      default:
        return false;
    }
  }

  provideFeedback(): void {
    this.provideTaskManagerFeedback = true;
    this.showTaskManagerRespondBox = true;
    this.showRespondBox = false;
    this.showRespondBoxWithSuggestedText = false;
  }

  approveResponse(): void {
    this.showRespondBox = false;
    this.showRespondBoxWithSuggestedText = false;
    this.reviewsService.approveConciergeResponse(
      this.review.reviewID,
      this.reviewConfig.accountgroupId,
      this.reviewConfig.partnerId,
      () => this.refreshFeedbackData(),
    );
  }

  saveComment(commentText: string): void {
    this.isPublishLoading$$.next(true);
    this.reviewsService
      .createReviewComment(this.review$, commentText, this.reviewConfig.accountgroupId, this.reviewConfig.company.name)
      .pipe(
        takeUntil(this.unsubscribe),
        finalize(() => this.isPublishLoading$$.next(false)),
      )
      .subscribe((r) => {
        if (r !== undefined) {
          this.review$$.next(r);
          this.showRespondBox = false;
          this.showlastFailedReviewCommentResponseBox = false;
          this.isLastFailedReviewComment = false;
          this.showRespondBoxWithSuggestedText = false;
        }
      });
  }

  saveFeedback(feedbackText: string): void {
    this.reviewsService.createConciergeFeedback(
      this.review.reviewID,
      feedbackText,
      this.reviewConfig.accountgroupId,
      this.reviewConfig.partnerId,
      () => this.refreshFeedbackData(),
    );
  }

  refreshFeedbackData(): void {
    this.taskManagerResponse$ = this.reviewsService
      .getConciergeReviewResponse(this.review.reviewID, this.reviewConfig.accountgroupId, this.reviewConfig.partnerId)
      .pipe(share());
    this.showTaskManagerRespondBox = false;
    this.showTaskManagerComment = true;
  }

  cancelFeedback(): void {
    this.provideTaskManagerFeedback = false;
    this.showTaskManagerRespondBox = false;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  editComment(comment: ReviewComment, updatedCommentText: string, reviewSource: SOURCE_IDS): void {
    this.reviewsService
      .editReviewComment(comment, updatedCommentText, this.reviewConfig.accountgroupId)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        this.hideCommentEditor(comment.commentId);
      });
  }

  hideCommentEditor(commentId: string): void {
    this.showRespondButton = true;
    const cevm = this.commentEditorVisibleMap$$.getValue();
    cevm[commentId] = !cevm[commentId];
    this.commentEditorVisibleMap$$.next(this.closeCommentEditor(cevm, commentId));
    this.showRespondBox = false;
    this.showRespondBoxWithSuggestedText = false;
  }

  removeGoogleTranslation(content: string): string {
    if (this.hideGoogleTranslatedReviewContent) {
      return removeGoogleTranslation(content);
    }
    return content;
  }

  showCommentEditor(commentId: string): void {
    this.showRespondButton = false;
    const cevm = this.commentEditorVisibleMap$$.getValue();
    cevm[commentId] = !cevm[commentId];
    this.commentEditorVisibleMap$$.next(this.closeCommentEditor(cevm, commentId));
    this.showRespondBox = false;
    this.showRespondBoxWithSuggestedText = false;
    this.showlastFailedReviewCommentResponseBox = false;
  }

  closeCommentEditor(
    commentEditorVisibilityMap: CommentEditorVisibleMap,
    excludedCommentId?: string,
  ): CommentEditorVisibleMap {
    for (const key in commentEditorVisibilityMap) {
      if (key !== excludedCommentId) {
        commentEditorVisibilityMap[key] = false;
      }
    }
    return commentEditorVisibilityMap;
  }

  showLastFailedReviewCommentEditor(): void {
    this.showRespondButton = false;
    const cevm = this.commentEditorVisibleMap$$.getValue();
    this.commentEditorVisibleMap$$.next(this.closeCommentEditor(cevm));
    this.showlastFailedReviewCommentResponseBox = true;
    this.showRespondBox = false;
    this.showRespondBoxWithSuggestedText = false;
  }

  hideLastFailedReviewCommentEditor(): void {
    this.showRespondButton = false;
    this.showlastFailedReviewCommentResponseBox = false;
    this.showRespondBox = false;
    this.showRespondBoxWithSuggestedText = false;
  }

  updateStatus(status: ReviewStatus): void {
    this.reviewsService
      .updateReviewStatus(this.review$, status, this.reviewConfig.accountgroupId, this.reviewConfig.partnerId)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((r) => {
        if (r) {
          this.review$$.next(r);
        }
      });
    if (status !== ReviewStatus.ACTION_REQUIRED) {
      this.isLastFailedReviewComment = false;
    }
  }

  publish(): void {
    this.reviewsService
      .publishReview(this.review$, this.reviewConfig.accountgroupId, this.reviewConfig.partnerId)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((r) => {
        if (r) {
          this.review$$.next(r);
          this.isPublishedMyListingReview = true;
        }
      });
  }

  unpublish(): void {
    this.reviewsService
      .unpublishReview(this.review$, this.reviewConfig.accountgroupId, this.reviewConfig.partnerId)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((r) => {
        if (r) {
          this.review$$.next(r);
          this.isPublishedMyListingReview = false;
        }
      });
  }

  publishToReviewWidget(): void {
    this.reviewsService
      .publishToReviewWidget(this.review$, this.reviewConfig.accountgroupId, this.reviewConfig.partnerId)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((r) => {
        if (r) {
          this.review$$.next(r);
          this.isPublishedThirdPartyReview = true;
        }
      });
  }

  unpublishFromReviewWidget(): void {
    this.reviewsService
      .unpublishFromReviewWidget(this.review$, this.reviewConfig.accountgroupId, this.reviewConfig.partnerId)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((r) => {
        if (r) {
          this.review$$.next(r);
          this.isPublishedThirdPartyReview = false;
        }
      });
  }

  updateDeletedStatus(deletedStatus: ReviewDeletedStatus): void {
    this.reviewsService
      .updateReviewUserDeletedInfo(this.review$, this.reviewConfig.userId, deletedStatus)
      .subscribe((r) => {
        if (r) {
          this.review$$.next(r);
        }
      });
  }

  handleInputChange(value: string): void {
    if (!value || value.length <= 0) {
      this.lastTemplateUsed = null;
    }
    if (this.digitalAgentView) {
      const reviewStatus: ReviewStatus = this.review$$.getValue()?.status;
      this.digitalAgentButtonDisabled$$.next(
        Boolean(!value) ||
          reviewStatus === ReviewStatus.DIGITAL_AGENT_RESPONDED ||
          reviewStatus === ReviewStatus.OWNER_RESPONDED,
      );
    }
  }

  suggestReviewResponse(responseBox: HTMLTextAreaElement, review: Review): void {
    this.suggestingResponse$$.next(true);
    this.reviewsService
      .suggestReviewResponse(review.reviewID, this.reviewConfig.accountgroupId, this.hideGoogleTranslatedReviewContent)
      .pipe(
        takeUntil(this.unsubscribe),
        tap((response) => {
          if (response) {
            responseBox.value = response;
            this.updateReviewDraft(response);
          }
        }),
        finalize(() => this.suggestingResponse$$.next(false)),
      )
      .subscribe({
        next: (response) => {
          this.lastTemplateUsed = {
            referencedTemplate: null,
            renderedTemplate: response,
          };
        },
        error: () => {
          this.snackbarService.openErrorSnack(
            'COMMON_REVIEW_CARD.REVIEWS.MANAGE.REVIEW_CARD.RESPONSE_BOX.ERROR_LOADING_TEMPLATE',
          );
        },
      });
  }

  renderResponseTemplate(
    responseBox: HTMLTextAreaElement,
    responseTemplate: ResponseTemplate,
    config: ReviewConfig,
    review: Review,
  ): void {
    const parameters: ReviewResponseTemplateParameters = {
      reviewerFirstName: review.reviewerName ? review.reviewerName.split(' ')[0] : null,
      reviewerFullName: review.reviewerName,
      starRating: `${review.rating} star`,
      businessEmail: config.accountGroupEmail,
      username: config.hasUser ? config.userFirstName + ' ' + config.userLastName : null,
    };
    if (config.company) {
      parameters.businessName = config.company.name;
      parameters.businessAddress = config.company.address;
      parameters.businessPhoneNumber =
        config.company?.workNumbers?.length > 0
          ? this.formatPhoneNumber.transform(config.company.workNumbers[0], config.country)
          : null;
      parameters.businessWebsite = config.company.website;

      this.templatesService
        .renderResponseTemplate(responseTemplate.externalTemplateId, parameters, this.templateApplicationId)
        .pipe(
          takeUntil(this.unsubscribe),
          tap((renderedTemplate) => {
            const decoded: string = decodeEntities(renderedTemplate);
            responseBox.value = decoded;
            if (this.digitalAgentView) {
              this.digitalAgentButtonDisabled$$.next(
                this.review$$.getValue().status === ReviewStatus.DIGITAL_AGENT_RESPONDED,
              );
            }
            this.updateReviewDraft(decoded);
          }),
        )
        .subscribe({
          next: (renderedTemplate) => {
            this.lastTemplateUsed = {
              referencedTemplate: responseTemplate,
              renderedTemplate: renderedTemplate,
            };
          },
          error: () => {
            this.snackbarService.openErrorSnack(
              'COMMON_REVIEW_CARD.REVIEWS.MANAGE.REVIEW_CARD.RESPONSE_BOX.ERROR_LOADING_TEMPLATE',
            );
          },
        });
    }
  }

  fromNow(date: Date): string {
    return formatDistance(date, new Date(), { addSuffix: true });
  }

  toggleShowHelperText(): void {
    this.isHelperTextVisible = !this.isHelperTextVisible;
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  handleRequestApproval(commentText: string): void {
    this.isRequestingApprovalLoading$$.next(true);
    this.reviewsService
      .handleRequestApproval(
        this.review.reviewID,
        this.reviewConfig.partnerId,
        this.digitalAgentViewData.task,
        this.reviewConfig.accountgroupId,
        this.taskManagerResponse$,
        commentText,
      )
      .pipe(
        take(1),
        finalize(() => this.isRequestingApprovalLoading$$.next(false)),
      )
      .subscribe();
  }

  updateReviewDraft(response: string): void {
    if (this.digitalAgentView) {
      this.reviewsService
        .updateReviewDraft(
          this.review.reviewID,
          this.reviewConfig.partnerId,
          this.digitalAgentViewData.task,
          this.reviewConfig.accountgroupId,
          this.taskManagerResponse$,
          response,
        )
        .pipe(take(1))
        .subscribe({
          next: () =>
            this.snackbarService.openSuccessSnack(
              'COMMON_REVIEW_CARD.REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.REVIEW_RESPONSE_UPDATED',
            ),
          error: () =>
            this.snackbarService.openErrorSnack(
              'COMMON_REVIEW_CARD.REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.ERROR_SAVING_REVIEW_RESPONSE',
            ),
        });
    }
  }

  handleBlur(response: string): void {
    if (this.initialResponseValue !== response) {
      this.updateReviewDraft(response);
    }
  }

  handleFocus(value: string): void {
    this.initialResponseValue = value;
  }

  showConnectBanner(): boolean {
    return (
      (this.review.sourceID === SOURCE_IDS.GOOGLE_MAPS && !this.reviewResponseSettings.gmbAccountConnected) ||
      (this.review.sourceID === SOURCE_IDS.FACEBOOK && !this.reviewResponseSettings.facebookAccountConnected)
    );
  }

  responseBoxTextCopied(success: boolean): void {
    if (success) {
      this.snackbarService.openSuccessSnack('COMMON_REVIEW_CARD.REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.RESPONSE_COPIED');
    } else {
      this.snackbarService.openErrorSnack(
        'COMMON_REVIEW_CARD.REVIEWS.MANAGE.REVIEW_CARD.MESSAGES.FAIL_TO_COPY_RESPONSE',
      );
    }
  }

  private transformReview(review: Review): Review {
    let shareUrl = review.shareUrl;

    if (review.sourceID === this.sourceIds.MY_LISTING && review.shareUrl) {
      shareUrl = review.shareUrl.replace(`domain=${review.domain}`, 'domain=My Listing');
    }

    return {
      ...review,
      shareUrl,
    };
  }

  private highlightReviewContent(): void {
    if (
      this.highlightOptions &&
      this.review.content &&
      this.highlightOptions.beginOffsets &&
      this.highlightOptions.beginOffsets.length > 0
    ) {
      if (
        Math.max(...this.highlightOptions.beginOffsets) + this.highlightOptions.phrase.length <=
        this.review.content.length
      ) {
        this.highlightOptions.beginOffsets = this.highlightOptions.beginOffsets.sort((a, b) => a - b);

        let improperOffset = false;
        const highlightedContent = this.highlightOptions.beginOffsets.reduce(
          (content, curIndex) => {
            const beforeHighlight = this.review.content.substring(content.lastOffset, curIndex);
            const toHighlight = this.review.content.slice(curIndex, curIndex + this.highlightOptions.phrase.length);
            const afterHighlight =
              curIndex === this.highlightOptions.beginOffsets[this.highlightOptions.beginOffsets.length - 1]
                ? this.review.content.substring(curIndex + this.highlightOptions.phrase.length)
                : '';

            if (toHighlight.toLowerCase() !== this.highlightOptions.phrase.toLowerCase()) {
              improperOffset = true;
            }
            return {
              content: content.content + beforeHighlight + '<mark><i>' + toHighlight + '</i></mark>' + afterHighlight,
              lastOffset: curIndex + this.highlightOptions.phrase.length,
            };
          },
          {
            content: '',
            lastOffset: 0,
          },
        );

        if (!improperOffset) {
          this.review.content = highlightedContent.content;
          return;
        }
      }
      this.review.content = this.review.content.replace(new RegExp(this.highlightOptions.phrase, 'gi'), (s: string) => {
        return `<mark><i>${s}</i></mark>`;
      });
    }
  }

  private getReviewDataLink(listingsId: string): string {
    const environment = this.reviewConfig.environment;
    return `http://repcore-${environment}.appspot.com/superadmin/listing-info?lid=${listingsId}`;
  }

  private shouldShowReviewWidget(): boolean {
    if (this.reviewConfig.dontShowWidgetPublishAction) {
      return false;
    }

    const allowedWithAddon =
      this.reviewConfig.isWidgetAllReviewsAddonEnabled &&
      this.disableThirdPartyReviewSourceIDs.findIndex((index) => index === this.review.sourceID) < 0;

    const allowedWithoutAddon =
      this.review.sourceID === SOURCE_IDS.GOOGLE_MAPS || this.review.sourceID === SOURCE_IDS.FACEBOOK;

    return allowedWithAddon || allowedWithoutAddon;
  }

  private isPublishedReview(): boolean {
    const isPublishedThirdPartyReview = this.review.publishThirdPartyReview;
    if (
      this.reviewPublishSettings &&
      (isPublishedThirdPartyReview === undefined || isPublishedThirdPartyReview === null)
    ) {
      return (
        (Number(this.review.rating) >= this.reviewPublishSettings.thirdPartyMinimumPublishRating &&
          this.reviewPublishSettings.thirdPartyMinimumPublishRating > 0) ||
        (this.reviewPublishSettings.thirdPartyMinimumPublishRating === 1 && this.review.rating[0] === 'N')
      );
    }

    return isPublishedThirdPartyReview;
  }

  private buildBusinessProductURL(page: string): string {
    if (!this.reviewConfig.productEntryURL) {
      return null;
    }

    const businessID = this.reviewConfig.accountgroupId;
    const productEntryURL = this.reviewConfig.productEntryURL;
    return `${productEntryURL}/account/${businessID}/app/${page}`;
  }

  openReviewHistoryCardModal() {
    this.dialog.open(ReviewHistoryCardComponent, {
      data: this.review,
      width: '600px',
    });
  }
}

export const decodeEntities = (() => {
  // this prevents any overhead from creating the object each time
  const element = document.createElement('div');

  function decodeHTMLEntities(str: any): any {
    if (str && typeof str === 'string') {
      // strip script/html tags
      str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gim, '');
      str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gim, '');
      element.innerHTML = str;
      str = element.textContent;
      element.textContent = '';
    }

    return str;
  }

  return decodeHTMLEntities;
})();
