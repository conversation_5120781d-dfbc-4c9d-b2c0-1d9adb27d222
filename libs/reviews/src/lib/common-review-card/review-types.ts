import { ReviewDeletedStatus } from '@vendasta/reputation';
import { TaskInterface } from '@vendasta/task';

export interface Review {
  sourceID: number;
  sourceName?: string;
  listingID: string;
  reviewID: string;
  title?: string;
  rating?: string;
  stars: number;
  url?: string;
  content?: string;
  published: Date;
  created: Date;
  status: ReviewStatus;
  shareUrl: string;
  respondUrl: string;
  isPublished: boolean;
  reviewerName?: string;
  reviewerEmail?: string;
  reviewerUrl?: string;
  domain?: string;
  comments?: ReviewComment[];
  publishThirdPartyReview?: boolean;
  recommendation?: RecommendationType;
  businessName?: string;
  lastFailedReviewCommentText?: string;
  lastFailedReviewCommentReason?: string;
  lastFailedReviewCommentCreated?: Date;
  deletedStatus?: ReviewDeletedStatus;
  edited?: Date;
}

export interface ReviewConfig {
  accountgroupId: string;
  accountGroupEmail: string;
  country: string;
  environment: string;
  isCommentEditable: boolean;
  isWidgetAllReviewsAddonEnabled: boolean;
  dontShowWidgetPublishAction?: boolean;
  partnerId: string;
  socialMarketingEnabled: boolean;
  enableResponse: boolean;
  hasUser: boolean;
  userFirstName: string;
  userLastName: string;
  company: Company;
  userId: string;
  partnerName?: string;
  productEntryURL?: string;
}

export interface Company {
  name?: string;
  address?: string;
  city?: string;
  workNumbers?: string[];
  website?: string;
}

export enum ReviewStatus {
  DIGITAL_AGENT_RESPONDED = 'digital-agent-responded',
  OWNER_RESPONDED = 'owner-responded',
  ACTION_REQUIRED = 'action-required',
  NO_ACTION_REQUIRED = 'no-action-required',
  RESPONSE_AWAITING_APPROVAL = 'response-awaiting-approval',
}

export enum RecommendationType {
  POSITIVE = 'positive',
  NEGATIVE = 'negative',
}

export interface ReviewComment {
  commentText: string;
  created: Date;
  published?: Date;
  commentId: string;
  commentAuthor: string;
  postedByOwner?: boolean;
  reviewID: string;
  postedByDigitalAgent?: boolean;
}

export interface ReviewFeedData {
  reviews: Review[];
  wordCloud: any;
  ratingStats: any;
}

export interface UpdateReviewRequest {
  accountGroupId: string;
  partnerId: string;
  reviewID: string;
  status?: ReviewStatus;
  isPublished?: boolean;
  isPublishedThirdPartyReview?: boolean;
}

export interface UpdateReviewCommentRequest {
  accountGroupId: string;
  reviewCommentId: string;
  updatedCommentText: string;
}

export interface UpdateReviewCommentResponse {
  agid: string;
  commentId: string;
  commentText: string;
  commenterEmail: string;
  commenterName: string;
  createdDateTime: string;
  modifiedDateTime: string;
  postedByDigitalAgent: boolean;
  postedByOwner: boolean;
  publishedDateTime: string;
  rid: string;
}

export interface CreateReviewCommentRequest {
  accountGroupId: string;
  reviewID: string;
  commentText: string;
  companyName?: string;
}

export interface CreateReviewCommentResponse {
  agid: string;
  commentText: string;
  createdDateTime: string;
  commentId: string;
  commenterName: string;
  postedByOwner: boolean;
}

export interface CanPublishReviewCommentResponse {
  data: CanPublishReviewCommentData;
}

export interface CanPublishReviewCommentData {
  canPublish: boolean;
  message?: string;
  link?: string;
}

export interface ConciergeReviewResponseRequest {
  accountGroupId: string;
  reviewId: string;
  partnerId: string;
}

export interface ConciergeReviewResponseData {
  agent_name: string;
  response: string;
  feedback: string;
  partner_name: string;
  favicon_url: string;
  tags?: string[];
}

export interface ConciergeReviewResponseResponse {
  data: ConciergeReviewResponseData;
}

export interface ApproveConciergeResponseRequest {
  partnerId: string;
  reviewId: string;
  accountGroupId: string;
}

export interface ProvideConciergeFeedbackRequest {
  partnerId: string;
  reviewId: string;
  accountGroupId: string;
  feedback: string;
}

export interface SocialService {
  type: SocialServiceType;
  displayName: string;
  name: string;
  ssid: string;
  profileUrl?: string;
  profileImageUrl?: string;
  clientTags: string[];
  pages: SocialService[];
  deleteUrl: string;
  authUrl: string;
  tokenIsBroken?: boolean;
  locationPathName?: string;
  googleUserID?: string;
  spid?: string;
  displayPersonalFacebookAccount?: boolean;
}

export enum SocialServiceType {
  TWITTER = 'TW_USER',
  TWITTER_EMPLOYEE = 'TW_EMPLOYEE',
  TWITTER_EMPLOYEE_ALIAS = 'employees',
  INSTAGRAM = 'IG_USER',
  GMB = 'GMB',
  FACEBOOK = 'FB_PAGE',
  LINKEDIN = 'LI_USER',
  INSTAGRAM_BUSINESS_ACCOUNT = 'IG_BUSINESS_ACCOUNT',
}

export interface UpdateCallbackData {
  isPublished?: boolean;
  publishThirdPartyReview?: boolean;
  status?: ReviewStatus;
  comments?: ReviewComment[];
}

export interface CardText {
  recommendationTitle: string;
  notRecommendationTitle: string;
}

export interface ReviewResponseSetting {
  gmbAccountConnected?: boolean;
  facebookAccountConnected?: boolean;
  gmbAccount?: SocialService;
  facebookAccounts?: SocialService[];
  gmbAuthUrl?: string;
  facebookAuthUrl?: string;
  facebookResponseEnabled: boolean;
  apartmentsResponseEnabled: boolean;
  apartmentRatingsApiResponseEnabled: boolean;
}

export interface ResponseTemplate {
  title: string;
  categories: string[];
  ratings: number[];
  content: string;
  responseTemplateId: string;
  externalTemplateId: string;
  createdBy: string;
  created: Date;
  owner: string;
}

export enum SOURCE_IDS {
  GOOGLE_MAPS = 10010,
  FACEBOOK = 10050,
  BING_MAPS = 10020,
  INSTAGRAM = 11610,
  LINKEDIN = 10070,
  APARTMENT_RATINGS = 10090,
  APARTMENTS = 10790,
  YELP = 10000,
  HOTELS_DOT_COM = 12930,
  BOOKING = 12940,
  TRIPADVISOR = 10200,
  MY_LISTING = 12000,
}

export const DEFAULT_SOURCE_NAMES = {
  [SOURCE_IDS.MY_LISTING]: 'COMMON_REVIEW_CARD.REVIEWS.MANAGE.REVIEW_CARD.MY_LISTING_SOURCE_NAME',
  [SOURCE_IDS.GOOGLE_MAPS]: 'COMMON_REVIEW_CARD.REVIEWS.MANAGE.REVIEW_CARD.SOURCE_NAME.GOOGLE',
  [SOURCE_IDS.FACEBOOK]: 'COMMON_REVIEW_CARD.REVIEWS.MANAGE.REVIEW_CARD.SOURCE_NAME.FACEBOOK',
};

export interface ReviewPublishSettings {
  minimumPublishRating: number;
  thirdPartyMinimumPublishRating: number;
}

export interface CommentEditorVisibleMap {
  [commentId: string]: boolean;
}

export interface HighlightOptions {
  phrase: string;
  beginOffsets: number[];
}

export enum VTMReviewFlags {
  RESPONSE_APPROVED = 'Response Approved',
  FEEDBACK_PROVIDED = 'Feedback Provided',
  RESPONSE_AWAITING_APPROVAL = 'Awaiting Approval',
}

export interface DigitalAgentViewData {
  task: TaskInterface;
}
