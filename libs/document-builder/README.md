# document-builder

This library exposes an editor written in React using Slate.

## Using the editor
The editor is built with React, so it takes some configuration changes in your application to get it running.
Don't worry - you can still lazy-load the bundle to prevent unnecessary module bloat.

### tsconfig changes
Add `"jsx": "react"` in your tsconfig.json file:
```json
    {
      "extends": "../../tsconfig.base.json",
      "files": [],
      "include": [],
      "compilerOptions": {
        "jsx": "react"
      }
   }
```
otherwise you will get a compilation error.

To find out more about how the document builder galaxy adapter works you can read [here](./src/lib/galaxy-adapter/README.md)

### Using the editor in your application
Import the `DocumentBuilderModule`. It's important you lazy-load this module only where you need it because
it may pull in a large number of dependencies.
```
    imports: [DocumentBuilderModule]
```

Use the `document-builder` component in your template:
```
    <document-builder></document-builder>
```

## Troubleshooting
If you get an error undefined for `REACT_APP_SC_ATTR` then use the `loadDocumentBuilderPolyfills` file from 
`@galaxy/document-builder/polfyills`. It patches the global namespace for `process` - it does not load additional dependencies
nor does it mutate anything that may already be set. All it does is ensure the global variables are setup correctly.

```typescript
// Put this in your app's polyfills.ts file
import { loadDocumentBuilderPolyfills } from '@galaxy/document-builder/polyfills';
loadDocumentBuilderPolyfills('sales-center-client');
```


## Proposal Builder as a Microfrontend
Proposal Builder is available as a microfrontend using federated modules.
This allows for a hosting application, or shell, to remotely download and inject Proposal Builder content without the need to compile all of the proposal builder code in the shell itself - as you would typically do with importing modules.

### Injecting the Proposal Builder microfrontend into a shell
1. Follow the guide at [UPDATE WITH CONFLUENCE LINK](https://vendasta.jira.com/something) to set up your app to be capable of retrieving and hosting microfrontends
2. Modify your app's federated modules manifest files for local, demo and prod environments and include the following:
```JSON
{
  "proposalBuilderClientRemote": {
    "type": "module",
    "remoteEntry": "https://cdn.apigateway.co/proposal-builder-client/remoteEntry.js"
  }
}
```

3. Modify your app's routing module that will handle route definitions:
```typescript
const routes: Routes = [
  ...
  {
    path: 'proposals',
    loadChildren: () =>
      loadRemoteModule({
        type: 'manifest',
        remoteName: 'proposalBuilderClientRemote',
        exposedModule: './Entry',
      }).then((m) => m.DocumentBuilderUIModule)
  }
];
```

4. Modify your webpack.config file and include the following shared modules:
```javascript
shared: share({
        ...

        '@angular/core': { eager: true, singleton: true, strictVersion: true, requiredVersion: 'auto' },
        '@angular/common': { eager: true, singleton: true, strictVersion: true, requiredVersion: 'auto' },
        '@angular/common/http': { eager: true, singleton: true, strictVersion: true, requiredVersion: 'auto' },
        '@angular/router': { eager: true, singleton: true, strictVersion: true, requiredVersion: 'auto' },
        '@galaxy/document-builder/core': {
          singleton: true,
          eager: true,
          import: 'libs/document-builder/core/src/index',
          requiredVersion: '1.0.0',
        },

        ...
      }),
```


## Running unit tests

Run `nx test document-builder` to execute the unit tests.
