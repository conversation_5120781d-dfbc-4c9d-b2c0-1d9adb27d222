import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule, DatePipe } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatChip, MatChipListbox, MatChipOption } from '@angular/material/chips';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatDrawer, MatDrawerContainer, MatDrawerContent } from '@angular/material/sidenav';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { AddressAPIService } from '@vendasta/address';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyChatModule } from '@vendasta/galaxy/chat';
import { GalaxyChatComposerModule } from '@vendasta/galaxy/chat-composer';
import { GalaxyCheckboxModule } from '@vendasta/galaxy/checkbox';
import { ContactInfoCardComponent } from '@vendasta/galaxy/contact-info-card';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyInfiniteScrollTriggerModule } from '@vendasta/galaxy/infinite-scroll-trigger';
import { GalaxyInputModule } from '@vendasta/galaxy/input';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyNavItemComponent } from '@vendasta/galaxy/nav';
import { GalaxyNavLayoutModule } from '@vendasta/galaxy/nav-layout';
import { GalaxyPageNavModule } from '@vendasta/galaxy/page-nav';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { GalaxyPopoverModule } from '@vendasta/galaxy/popover';
import { GalaxyUploaderModule } from '@vendasta/galaxy/uploader';
import { MarkdownModule } from 'ngx-markdown';
import { InboxTermsOfService } from '../../core/src/lib/inbox-terms-of-service.service';
import { ListingService } from '../../core/src/lib/listing.service';
import { ConversationPipesModule } from '../../pipes';
import { InboxConversationPreviewComponent } from './components/inbox-conversation-preview/inbox-conversation-preview.component';
import { InboxConversationTitleComponent } from './components/inbox-conversation-title/inbox-conversation-title.component';
import { InboxPanePreviewComponent } from './components/inbox-pane-preview/inbox-pane-preview.component';
import { InboxPaneComponent } from './components/inbox-pane/inbox-pane.component';
import { InboxSelectAccountComponent } from './components/inbox-select-account/inbox-select-account.component';
import { InboxSelectContactComponent } from './components/inbox-select-contact/inbox-select-contact.component';
import { InboxViewsEmptyComponent } from './components/inbox-views-empty/inbox-views-empty.component';
import { InboxViewsComponent } from './components/inbox-views/inbox-views.component';
import { InboxWelcomeComponent } from './components/inbox-welcome/inbox-welcome.component';
import { InboxZeroComponent } from './components/inbox-zero/inbox-zero.component';
import { ConversationUIRouting } from './conversation-ui.routing';
import { SmsAccessGuard } from './guards/sms-access.guard';
import { InboxTicketCardComponent } from './pages/inbox-chat/components/inbox-info-pane/inbox-ticket-card/inbox-ticket-card.component';
import { InboxTicketListComponent } from './pages/inbox-chat/components/inbox-info-pane/inbox-ticket/inbox-ticket-list.component';
import { InboxTicketComponent } from './pages/inbox-chat/components/inbox-info-pane/inbox-ticket/inbox-ticket.component';
import { InboxInsertPaymentLinkFormComponent } from './pages/inbox-chat/components/inbox-insert-payment-link/inbox-insert-payment-link-form.component';
import { InboxInsertPaymentLinkComponent } from './pages/inbox-chat/components/inbox-insert-payment-link/inbox-insert-payment-link.component';
import { InboxInsertReviewLinkComponent } from './pages/inbox-chat/components/inbox-insert-review-link/inbox-insert-review-link.component';
import { InboxInsertTemplateComponent } from './pages/inbox-chat/components/inbox-insert-template/inbox-insert-template.component';
import { InboxMessageTextComponent } from './pages/inbox-chat/components/inbox-message-text/inbox-message-text.component';
import { InboxMessagesTopBarComponent } from './pages/inbox-chat/components/inbox-messages-top-bar/inbox-messages-top-bar.component';
import { BookingAvailabilityComponent } from './pages/inbox-chat/components/inbox-messages/booking-availability/booking-availability.component';
import { CitationsComponent } from './pages/inbox-chat/components/inbox-messages/citations/citations.component';
import { InboxEventComponent } from './pages/inbox-chat/components/inbox-messages/inbox-event/inbox-event.component';
import { InboxMessagesComponent } from './pages/inbox-chat/components/inbox-messages/inbox-messages.component';
import { InboxUploadFileComponent } from './pages/inbox-chat/components/inbox-upload-file/inbox-upload-file.component';
import { InboxUploadService } from './pages/inbox-chat/components/inbox-upload-file/inbox-upload.service';
import { InboxChatComponent } from './pages/inbox-chat/inbox-chat.component';
import { CreateContactFormComponent } from './pages/inbox-create-contact/create-contact-form.component';
import { InboxNewMessageComponent } from './pages/inbox-new-message/inbox-new-message.component';
import { InboxSendMessageComponent } from './pages/inbox-send-message/inbox-send-message.component';
import { InboxSendMessageResolver } from './pages/inbox-send-message/inbox-send-message.resolver';
import { TranslationModule } from './translation-module';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { MatDatepicker, MatDatepickerInput, MatDatepickerToggle } from '@angular/material/datepicker';
import { InboxEvaluationButtonsComponent } from './pages/inbox-chat/components/inbox-messages/inbox-evaluation-buttons/inbox-evaluation-buttons.component';
import { MessageUIComponentsComponent } from './pages/inbox-chat/components/inbox-messages/ui-components/message-ui-components.component';
import { InboxErrorComponent } from './components/inbox-error/inbox-error.component';
import { InboxChatEmptyStateComponent } from './pages/inbox-chat/components/inbox-chat-empty-state/inbox-chat-empty-state.component';

export const MODULE_DECLARATIONS_EXPORTS = [
  InboxPaneComponent,
  InboxNewMessageComponent,
  InboxMessagesComponent,
  InboxWelcomeComponent,
  InboxMessageTextComponent,
  InboxConversationPreviewComponent,
  InboxSendMessageComponent,
  InboxChatComponent,
  InboxInsertReviewLinkComponent,
  InboxInsertPaymentLinkComponent,
  InboxInsertPaymentLinkFormComponent,
  InboxInsertTemplateComponent,
  InboxUploadFileComponent,
  InboxPanePreviewComponent,
  CreateContactFormComponent,
  InboxViewsComponent,
  InboxTicketComponent,
];

// This list is used for compatibility reasons, as dependent components are
// converted to standalone components, these should be removed from this list.
const STANDALONE_COMPONENTS = [
  InboxSelectContactComponent,
  InboxConversationTitleComponent,
  InboxMessagesTopBarComponent,
  InboxTicketCardComponent,
  InboxTicketListComponent,
  InboxZeroComponent,
  CitationsComponent,
  InboxEventComponent,
  BookingAvailabilityComponent,
  MessageUIComponentsComponent,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    FormsModule,
    MatInputModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatFormFieldModule,
    MatMenuModule,
    MatButtonModule,
    MatCardModule,
    MatSelectModule,
    GalaxyInputModule,
    GalaxyCheckboxModule,
    GalaxyBadgeModule,
    GalaxyUploaderModule,
    OverlayModule, // Requires by GalaxyPopoverModule
    GalaxyPopoverModule,
    ConversationUIRouting,
    GalaxyPipesModule,
    GalaxyChatModule,
    GalaxyInfiniteScrollTriggerModule,
    ConversationPipesModule,
    GalaxyChatComposerModule,
    MatTooltipModule,
    GalaxyLoadingSpinnerModule,
    MatDialogModule,
    GalaxyFormFieldModule,
    MatDividerModule,
    GalaxyEmptyStateModule,
    InboxViewsEmptyComponent,
    GalaxyAvatarModule,
    GalaxyAlertModule,
    MatProgressBarModule,
    MatAutocompleteModule,
    GalaxyPageNavModule,
    GalaxyNavLayoutModule,
    GalaxyNavItemComponent,
    GalaxyButtonLoadingIndicatorModule,
    MatTabsModule,
    MatListModule,
    MarkdownModule.forRoot(),
    MatDrawerContainer,
    MatDrawerContent,
    ContactInfoCardComponent,
    MatDrawer,
    ...STANDALONE_COMPONENTS,
    InboxSelectAccountComponent,
    MatChipListbox,
    MatChipOption,
    MatDatepickerInput,
    MatDatepickerToggle,
    MatDatepicker,
    InboxEvaluationButtonsComponent,
    MatChip,
    InboxErrorComponent,
    InboxChatEmptyStateComponent,
  ],
  declarations: MODULE_DECLARATIONS_EXPORTS,
  providers: [
    ListingService,
    SmsAccessGuard,
    DatePipe,
    InboxTermsOfService,
    InboxSendMessageResolver,
    AddressAPIService,
    InboxUploadService,
  ],
  exports: MODULE_DECLARATIONS_EXPORTS,
})
export class ConversationUIModule {}
