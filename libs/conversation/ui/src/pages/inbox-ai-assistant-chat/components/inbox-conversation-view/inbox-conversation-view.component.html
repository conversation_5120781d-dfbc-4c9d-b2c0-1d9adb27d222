@let selectedConversation = currentConversationDetail()?.conversation;

<div class="inbox-chat">
  @if (loading()) {
    <glxy-loading-spinner class="show-loading"></glxy-loading-spinner>
  } @else if (!!selectedConversation && !!messages()) {
    @if (hasValidAssistant() && messages().length === 0) {
      <inbox-chat-empty-state
        class="no-messages-view"
        [title]="aiTitle()"
        [description]="aiDescription()"
        [iconName]="assistantIconName()"
        [avatarUrl]="assistantAvatarUrl()"
        [suggestionChips]="aiSuggestionChips()"
        (suggestionClicked)="sendSuggestionMessage($event)"
      ></inbox-chat-empty-state>
    } @else {
      <inbox-messages
        class="inbox-messages"
        [messages]="messages()"
        [conversation]="selectedConversation"
      ></inbox-messages>
    }

    <inbox-message-text
      #inboxTextComponent
      (sendMessage)="sendMessage($event)"
      [conversationDetail]="currentConversationDetail() ?? null"
      [availableChannels]="availableChannels"
      [isImpersonating]="!!(isImpersonating$ | async)"
    ></inbox-message-text>

    <div class="ai-footer">
      {{
        'INBOX.AI_CHAT.FOOTER.LABEL' | translate: { assistantName: currentAssistant()?.name || DEFAULT_OPENAI_BOT_NAME }
      }}
      <button
        [glxyPopover]="aiFooterPopover"
        (click)="aiFooterPopover.open()"
        cdkOverlayOrigin
        #popoverOrigin="cdkOverlayOrigin"
        mat-icon-button
        class="info-button"
      >
        <mat-icon>info_outline</mat-icon>
      </button>

      <glxy-popover
        #aiFooterPopover
        [origin]="popoverOrigin"
        [closeOnBackdropClick]="true"
        [closeOnEscapeKey]="true"
        [keepOnScreen]="true"
      >
        <div class="ai-responder-pop-over-content">
          {{ 'INBOX.AI_CHAT.FOOTER.DESCRIPTION' | translate }}
        </div>
      </glxy-popover>
    </div>
  } @else {
    <inbox-no-conversations-view class="no-conversations-view"></inbox-no-conversations-view>
  }
</div>
