@use 'design-tokens' as *;

:host {
  .system-message {
    margin: 25px 0 20px !important;
  }

  .evaluate {
    display: flex;
    align-items: center;
    gap: $spacing-1;

    .evaluate-button {
      width: 18px;
      height: 18px;
      line-height: 18px;
      padding: 0;
      font-size: $font-preset-4-size;
      color: color-mix(in srgb, currentcolor 70%, transparent);
    }
  }

  // Formatting Code from OpenAI
  .type-received {
    pre {
      padding: $spacing-2;
      border: 1px solid $glxy-grey-300;
      border-radius: 5px;
      margin: 0;
    }
  }

  .link-to-document {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: $spacing-1;
    text-decoration: none;
    // cheat the file icon to the left slightly so it aligns correctly with
    // other text content
    margin-left: -2px;

    .file-icon {
      font-size: 1.5em;
      line-height: 1;
      flex-shrink: 0;
    }

    .file-name {
      text-decoration: underline;
    }
  }

  .message-media-image {
    display: block;
  }

  .broken-media-empty-state {
    background-color: $glxy-grey-200;
    padding: $spacing-2;
  }

  .message-media {
    ::ng-deep.content-attachment {
      border: 0px !important;
      cursor: pointer;

      img {
        display: block;
      }

      video {
        display: block;
      }

      audio {
        display: block;
        padding-right: $spacing-4;
      }
    }
  }

  .view-original-message-button {
    font-weight: bold;
    text-decoration: none;
    margin-top: $spacing-2;
    display: flex;
  }

  .booking-availability {
    width: 100%;
    padding-bottom: 28px;
  }

  .first-inbox-item ::ng-deep {
    .thumbs {
      opacity: 1 !important;
      pointer-events: auto;
    }
  }
}

.event-label {
  font-weight: bold;
}

.chat-event-message {
  text-align: left;
  white-space: pre-wrap;
  overflow-wrap: break-word;
}

.event-label {
  font-weight: bold;
}

.chat-event-message {
  text-align: left;
  white-space: pre-wrap;
  overflow-wrap: break-word;
}
