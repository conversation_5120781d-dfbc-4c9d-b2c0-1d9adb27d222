import { Component, inject, signal, computed, effect, output, input, ViewChild } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import {
  Conversation,
  ConversationApiService,
  ConversationChannel,
  GlobalParticipantType,
  Message,
  MessageType,
  ParticipantType,
} from '@vendasta/conversation';
import { ChatContainerComponent, ChatMessageStatus } from '@vendasta/galaxy/chat';
import { defaultBackgroundColor } from '@vendasta/galaxy/avatar';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { BehaviorSubject, catchError, combineLatest, firstValueFrom, map, of } from 'rxjs';
import { DEFAULT_OPENAI_BOT_NAME } from '../../../../../../core/src/lib/inbox.constants';
import { ConversationMessage, InboxItem } from '../../../../../../core/src/lib/interface/conversation.interface';
import { MediaDialogComponent } from './media-dialog.component';
import { EmailPreviewDialogComponent, EmailPreviewDialogData } from './preview/email-preview.component';
import { isInboundEmailMessage, mapMessageStatus } from '../../../../../../core/src/lib/message-utils';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { TranslateService } from '@ngx-translate/core';
import { GalaxyAiIconService } from '@vendasta/galaxy/ai-icon';
import { Event } from '@vendasta/conversation/lib/_internal/objects/event';
import { getErrorMessage } from '../../../../../../core/src/lib/channels/message-errors.util';
import {
  DEFAULT_CHAT_RECEPTIONIST_AVATAR_SVG_ICON,
  VENDASTA_AI_AVATAR_SVG_ICON,
  AiAssistantService,
} from '@galaxy/ai-assistant';
import { InboxItemHydrationService } from './inbox-item-hydration.service';
import { InboxItemGroupingService } from './inbox-item-grouping.service';
import { SYSTEM_ASSISTANT_ID } from '@galaxy/ai-assistant';
import { ClickedComponentInfo } from './ui-components/message-ui-components.component';
import { USER_ID_TOKEN } from '../../../../../../core/src/lib/tokens';

@Component({
  selector: 'inbox-messages',
  templateUrl: './inbox-messages.component.html',
  styleUrls: ['./inbox-messages.component.scss'],
  providers: [InboxItemHydrationService, InboxItemGroupingService],
  standalone: false,
})
export class InboxMessagesComponent {
  readonly defaultBackgroundColor = defaultBackgroundColor;
  readonly mediaWithErrorsMessageIDs = signal<string[]>([]);
  readonly RECEIVED = 'received';
  readonly MessageType = MessageType;
  readonly OPENAI_BOT_NAME = DEFAULT_OPENAI_BOT_NAME;
  readonly AI_ASSISTANT_TYPE = ParticipantType.PARTICIPANT_TYPE_AI_ASSISTANT;
  readonly OPENAI_BOT_TYPE = ParticipantType.PARTICIPANT_TYPE_OPENAI_BOT;
  readonly DEFAULT_CHAT_RECEPTIONIST_AVATAR_SVG_ICON = DEFAULT_CHAT_RECEPTIONIST_AVATAR_SVG_ICON;
  readonly VENDASTA_AI_AVATAR_SVG_ICON = VENDASTA_AI_AVATAR_SVG_ICON;
  readonly SYSTEM_ASSISTANT_ID = SYSTEM_ASSISTANT_ID;
  readonly acceptedMimeType = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'video/mp4',
    'video/3gpp',
    'video/WebM',
    'video/ogg',
    'audio/ogg',
    'audio/acc',
    'audio/wav',
    'audio/mp3',
    'audio/mp4',
    'audio/mpeg',
    'audio/webm',
  ];
  private readonly messages$$ = new BehaviorSubject<ConversationMessage[]>([]);
  protected readonly messages$ = this.messages$$.asObservable();
  private readonly conversationDetail$$ = new BehaviorSubject<Conversation | null>(null);
  private latestMessageId: string | undefined;
  private readonly events$$ = new BehaviorSubject<Event[]>([]);
  private readonly _ = inject(GalaxyAiIconService); // used to load the ai avatar svg; do not remove

  protected readonly lastMessageErrorReason$ = this.messages$$.pipe(
    map((messages) => getErrorMessage(messages?.[0]?.channel, messages?.[0]?.sendStatus?.reason || '')),
  );

  protected readonly conversationId$ = this.conversationDetail$$.pipe(
    map((conversation) => conversation?.conversationId || ''),
  );

  readonly isAIConversation = this.conversationDetail$$.pipe(
    map((conversation) =>
      conversation?.subjectParticipants?.some(
        (sp) => sp.participantType === GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_OPENAI_BOT,
      ),
    ),
  );

  readonly messages = input<ConversationMessage[]>();
  readonly events = input<Event[]>();
  readonly conversation = input<Conversation>();

  @ViewChild('chatContainer') chatContainer: ChatContainerComponent | undefined;

  private readonly analytics = inject(ProductAnalyticsService);
  private readonly dialog = inject(MatDialog);
  private readonly conversationApiService = inject(ConversationApiService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly translateService = inject(TranslateService);
  private readonly inboxEventService = inject(InboxItemHydrationService);
  private readonly inboxItemService = inject(InboxItemGroupingService);

  readonly isConversationEvent = this.inboxItemService.isConversationEvent;
  readonly isConversationMessage = this.inboxItemService.isConversationMessage;
  readonly isConversationTemplate = this.inboxItemService.isConversationTemplate;
  readonly isBookingAvailability = this.inboxItemService.isBookingAvailability;
  readonly isSystemMessage = this.inboxItemService.isSystemMessage;
  readonly isUntypedEvent = this.inboxItemService.isUntypedEvent;
  private readonly currentUserId = toSignal(inject(USER_ID_TOKEN));
  private readonly aiAssistantService = inject(AiAssistantService);

  readonly systemAssistant = toSignal(
    this.aiAssistantService.getSystemAssistant().pipe(
      catchError((err) => {
        console.error('Error getting system AI assistant information', err);
        return of(null);
      }),
    ),
  );

  private readonly manualLoadingOpenAIReply = signal(false);

  private readonly $messages = toSignal(this.messages$, { initialValue: [] });

  private readonly automaticLoadingOpenAIReply = toSignal(
    combineLatest([this.conversationDetail$$, this.messages$]).pipe(
      map(([conversation, messages]) => {
        if (
          conversation?.channel !== ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT &&
          conversation?.channel !== ConversationChannel.CONVERSATION_CHANNEL_OPENAI
        ) {
          return false;
        }

        const hasSystemAssistant = conversation?.subjectParticipants?.some(
          (participant) => participant.internalParticipantId === SYSTEM_ASSISTANT_ID,
        );
        if (!hasSystemAssistant) {
          return false;
        }

        const latestMessage = messages?.[0];
        if (!latestMessage) {
          return false;
        }

        // NOTE: This is always assuming that Open AI will have a response for every message
        // However, we will always aim for a chat response from the backend if openai fails
        // We're making a tradeoff here until we have something in the backend that we can use to handle this
        return latestMessage?.sender?.participantType === ParticipantType.PARTICIPANT_TYPE_IAM_USER;
      }),
    ),
  );

  readonly loadingOpenAIReply = computed(() => {
    return this.manualLoadingOpenAIReply() || this.automaticLoadingOpenAIReply() || false;
  });

  constructor() {
    effect(async () => {
      const value = this.messages() ?? [];
      this.mediaWithErrorsMessageIDs.set([]);
      this.messages$$.next(value);

      const latestMessage = value?.[0];
      if (latestMessage?.id !== this.latestMessageId) {
        const isSentByMe = latestMessage?.sender?.internalParticipantId === this.currentUserId();
        if (this.chatContainer && !isSentByMe) {
          this.chatContainer.onNewMessageReceived();
        }
        this.latestMessageId = latestMessage?.id;
      }
    });
    // Reset manual loading when AI responds
    effect(() => {
      const messages = this.$messages();
      const latestMessage = messages?.[0];

      if (
        latestMessage?.sender?.participantType === ParticipantType.PARTICIPANT_TYPE_OPENAI_BOT ||
        latestMessage?.sender?.participantType === ParticipantType.PARTICIPANT_TYPE_AI_ASSISTANT
      ) {
        this.manualLoadingOpenAIReply.set(false);
      }
    });
    effect(() => {
      this.events$$.next(this.events() ?? []);
    });

    effect(() => {
      this.conversationDetail$$.next(this.conversation() ?? null);
    });
  }

  setLoadingOpenAIReplyTrue(): void {
    this.manualLoadingOpenAIReply.set(true);
  }

  getProfileSVGIcon(messageGroup: any): string {
    if (
      (messageGroup.sender?.participantType === this.OPENAI_BOT_TYPE ||
        messageGroup.sender?.participantType === this.AI_ASSISTANT_TYPE) &&
      !messageGroup.sender?.profileImageUrl
    ) {
      return messageGroup.sender?.internalParticipantId === SYSTEM_ASSISTANT_ID
        ? this.VENDASTA_AI_AVATAR_SVG_ICON
        : this.DEFAULT_CHAT_RECEPTIONIST_AVATAR_SVG_ICON;
    }
    return '';
  }

  readonly groupedItems$ = combineLatest([
    this.messages$,
    this.events$$.pipe(map((events) => events.map((event) => this.inboxEventService.hydrateEventWrapper(event)))),
  ]).pipe(
    map(([messages, wrappedEvents]) => [...messages, ...wrappedEvents]),
    map((allItems) => {
      return allItems.filter(Boolean);
    }),
    map((allItems) => allItems.map((item) => this.inboxEventService.hydrateInboxItemWrapper(item))),
    map((groupedItems) => this.inboxItemService.groupItems(groupedItems)),
    map((groupedItems) => groupedItems?.slice().reverse()),
  );

  openInDialog(url: string, contentType: string) {
    this.analytics.trackEvent('inbox', 'openMediaDialog', 'view');
    this.dialog.open(MediaDialogComponent, {
      data: { mediaUrl: url, mediaContentType: contentType },
      height: 'auto',
      maxWidth: '860px',
    });
  }

  trackById = (_: number, item: InboxItem): string | undefined => {
    if (!item || !item.item) return undefined;
    return item.id;
  };

  addMessageIdToMediaErrors(messageID: string) {
    this.mediaWithErrorsMessageIDs.update((errors) => [...errors, messageID]);
  }

  mapMessageStatus(message: ConversationMessage): ChatMessageStatus {
    return mapMessageStatus(message);
  }

  openEmailPreviewDialog(message: Message): void {
    this.dialog.open(EmailPreviewDialogComponent, {
      data: {
        htmlContent: message.originalContent,
        textContent: message.body,
      } as EmailPreviewDialogData,
      panelClass: 'mat-dialog-no-padding',
    });
  }

  originalMsgButtonEnabled(message: ConversationMessage): boolean {
    if (!message?.body?.length) {
      return false;
    }

    return isInboundEmailMessage(message);
  }

  async showOriginalMessage(message: ConversationMessage): Promise<void> {
    firstValueFrom(
      this.conversationApiService.getMessage({
        messageId: message.messageId,
        options: { includeOriginalContent: true },
      }),
    )
      .then((result: Message) => this.openEmailPreviewDialog(result))
      .catch(() =>
        this.snackbarService.openErrorSnack(
          this.translateService.instant('INBOX.ERROR.ORIGINAL_MSG_CONTENT_NOT_AVAILABLE'),
        ),
      );
  }

  componentClicked = output<ClickedComponentInfo>();

  onComponentClicked(info: ClickedComponentInfo) {
    this.componentClicked.emit(info);
  }
}
