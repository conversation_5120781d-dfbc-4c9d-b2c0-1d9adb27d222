@use 'design-tokens' as *;

$status-height: 24px;

:host {
  display: block;
}

.event-container {
  border: 1px solid $info-border-color;
  border-radius: $spacing-1;
  padding: $spacing-2;
  background-color: $info-background-color;
}

.event-footer {
  min-height: $status-height;
}

.event-title {
  font-weight: bold;
  font-size: 1em;
}

.event-subtitle {
  color: $secondary-text-color;
  font-size: 1em;
  margin-bottom: $spacing-2;
  padding-left: $spacing-2;
}

.inbound-event-container {
  border: 1px solid $weak-border-color;
  border-radius: $spacing-1;
  padding: $spacing-2;
  background-color: $primary-background-color;
  min-width: 75%;
}

.inbound-event-title {
  font-weight: bold;
  font-size: 1em;
  text-align: left;
  padding-left: $spacing-2;
  margin-top: $spacing-1;
  margin-bottom: $spacing-1;
}

.inbound-event-message {
  text-align: left;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  padding-left: $spacing-2;
  margin-bottom: $spacing-1;
  overflow: auto;
  word-break: break-word;
}

.event-link {
  font-weight: bold;
  text-decoration: none;
  margin-top: $spacing-2;
  display: flex;
}

.call-recording {
  display: block;
  margin-bottom: $spacing-3;
  margin-top: $spacing-2;
}

mat-expansion-panel {
  box-sizing: border-box;
}

.thumbs {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  pointer-events: none;
  color: $tertiary-text-color;
  margin-left: $spacing-2;

  &.show {
    opacity: 1;
    pointer-events: auto;
  }
}
