import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { InboxItemHydrationService } from './inbox-item-hydration.service';
import { Event, EventType, ConversationChannel } from '@vendasta/conversation';

describe('InboxItemHydrationService', () => {
  let service: InboxItemHydrationService;
  let routerMock: jest.Mocked<Router>;

  beforeEach(() => {
    const routerSpy = {
      navigate: jest.fn(),
      url: '/conversations/123',
    } as jest.Mocked<Router>;

    TestBed.configureTestingModule({
      providers: [InboxItemHydrationService, { provide: Router, useValue: routerSpy }],
    });

    service = TestBed.inject(InboxItemHydrationService);
    routerMock = TestBed.inject(Router) as jest.Mocked<Router>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('hydrateEventInfo', () => {
    describe('duplicate conversation events', () => {
      it('should generate originEventUrl when dead conversation ID is present', () => {
        const event: Event = {
          type: EventType.EVENT_TYPE_DUPLICATE_CONVERSATION,
          channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
          metadata: [{ key: 'dead_conversation_id', value: 'dead-456' }],
        } as Event;

        const result = service.hydrateEventInfo(event);

        expect(result.originEventUrl).toBe('/conversations/dead-456');
      });

      it('should not generate originEventUrl when dead conversation ID is missing', () => {
        const event: Event = {
          type: EventType.EVENT_TYPE_DUPLICATE_CONVERSATION,
          channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
          metadata: [],
        } as unknown as Event;

        const result = service.hydrateEventInfo(event);

        expect(result.originEventUrl).toBeUndefined();
      });

      it('should not generate originEventUrl when metadata is undefined', () => {
        const event: Event = {
          type: EventType.EVENT_TYPE_DUPLICATE_CONVERSATION,
          channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
          metadata: undefined,
        } as unknown as Event;

        const result = service.hydrateEventInfo(event);

        expect(result.originEventUrl).toBeUndefined();
      });

      it('should not generate originEventUrl when dead conversation ID is NOT present', () => {
        const event: Event = {
          type: EventType.EVENT_TYPE_DUPLICATE_CONVERSATION,
          channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
          metadata: [],
        } as unknown as Event;

        const result = service.hydrateEventInfo(event);

        expect(result.originEventUrl).toBeUndefined();
      });

      it('should handle BCC URL paths correctly', () => {
        (routerMock as any).url =
          'https://abc-dot-vbc-demo.appspot.com/account/location/AG-H6LPVMG7TK/inbox/channel/8378d53b-bede-4283-afbd-3fbb522c3430';

        const event: Event = {
          type: EventType.EVENT_TYPE_DUPLICATE_CONVERSATION,
          channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
          metadata: [{ key: 'dead_conversation_id', value: 'dead-999' }],
        } as Event;

        const result = service.hydrateEventInfo(event);

        expect(result.originEventUrl).toBe(
          'https://abc-dot-vbc-demo.appspot.com/account/location/AG-H6LPVMG7TK/inbox/channel/dead-999',
        );
      });

      it('should handle ML BCC URL paths correctly', () => {
        (routerMock as any).url =
          'https://abc-dot-vbc-demo.appspot.com/account/brands/G-RK85TT7Z/inbox/channel/84b3e931-8bae-4d0c-afce-f2de593890ae';
        const event: Event = {
          type: EventType.EVENT_TYPE_DUPLICATE_CONVERSATION,
          channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
          metadata: [{ key: 'dead_conversation_id', value: 'dead-999' }],
        } as Event;

        const result = service.hydrateEventInfo(event);

        expect(result.originEventUrl).toBe(
          'https://abc-dot-vbc-demo.appspot.com/account/brands/G-RK85TT7Z/inbox/channel/dead-999',
        );
      });

      it('should handle PC URL paths correctly', () => {
        (routerMock as any).url =
          'https://partner-central-demo.appspot.com/inbox/conversations/ee0cf030-ee74-4498-9260-7a2eddc57270';
        const event: Event = {
          type: EventType.EVENT_TYPE_DUPLICATE_CONVERSATION,
          channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
          metadata: [{ key: 'dead_conversation_id', value: 'dead-999' }],
        } as Event;

        const result = service.hydrateEventInfo(event);

        expect(result.originEventUrl).toBe('https://partner-central-demo.appspot.com/inbox/conversations/dead-999');
      });

      it('should set correct event info properties for duplicate conversation', () => {
        const event: Event = {
          type: EventType.EVENT_TYPE_DUPLICATE_CONVERSATION,
          channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
          metadata: [{ key: 'dead_conversation_id', value: 'dead-456' }],
        } as Event;

        const result = service.hydrateEventInfo(event);

        expect(result.EventType).toBe(EventType.EVENT_TYPE_DUPLICATE_CONVERSATION);
        expect(result.icon).toBe('textsms');
        expect(result.sentBy).toBe('INBOX.LABELS.CONTACT');
        expect(result.sentVia).toBe('INBOX.CHAT.VIA_SMS');
        expect(result.originEventUrl).toBe('/conversations/dead-456');
      });

      it('should use chatItemVia for sentVia field', () => {
        const event: Event = {
          type: EventType.EVENT_TYPE_DUPLICATE_CONVERSATION,
          channel: ConversationChannel.CONVERSATION_CHANNEL_EMAIL,
          metadata: [{ key: 'dead_conversation_id', value: 'dead-456' }],
        } as Event;

        const result = service.hydrateEventInfo(event);

        expect(result.sentVia).toBe('INBOX.CHAT.VIA_EMAIL');
      });
    });

    describe('other event types', () => {
      it('should not generate originEventUrl for non-duplicate conversation events', () => {
        const event: Event = {
          type: EventType.EVENT_TYPE_REVIEW_REQUEST,
          channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
          metadata: [{ key: 'dead_conversation_id', value: 'dead-456' }],
        } as Event;

        const result = service.hydrateEventInfo(event);

        expect(result.originEventUrl).toBeUndefined();
      });
    });
  });

  describe('hydrateEventWrapper', () => {
    it('should create event wrapper with hydrated event info', () => {
      const event: Event = {
        type: EventType.EVENT_TYPE_DUPLICATE_CONVERSATION,
        channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
        metadata: [{ key: 'dead_conversation_id', value: 'dead-456' }],
        created: new Date('2023-01-01'),
      } as Event;

      const result = service.hydrateEventWrapper(event);

      expect(result.event).toBe(event);
      expect(result.eventInfo.EventType).toBe(EventType.EVENT_TYPE_DUPLICATE_CONVERSATION);
      expect(result.eventInfo.originEventUrl).toBe('/conversations/dead-456');
      expect(result.isFirstItem).toBe(false);
    });
  });

  describe('chatItemVia', () => {
    it('should return correct via text for SMS channel', () => {
      const result = service.chatItemVia({ channel: ConversationChannel.CONVERSATION_CHANNEL_SMS } as any);
      expect(result).toBe('INBOX.CHAT.VIA_SMS');
    });

    it('should return correct via text for email channel', () => {
      const result = service.chatItemVia({ channel: ConversationChannel.CONVERSATION_CHANNEL_EMAIL } as any);
      expect(result).toBe('INBOX.CHAT.VIA_EMAIL');
    });

    it('should return correct via text for web chat channel', () => {
      const result = service.chatItemVia({ channel: ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT } as any);
      expect(result).toBe('INBOX.CHAT.VIA_WEBCHAT');
    });

    it('should return empty string for unknown channel', () => {
      const result = service.chatItemVia({ channel: 'UNKNOWN' } as any);
      expect(result).toBe('');
    });
  });
});
