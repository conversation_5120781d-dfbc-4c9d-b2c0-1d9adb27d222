import { byText, createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { TranslateService } from '@ngx-translate/core';
import { of } from 'rxjs';
import { CitationsComponent } from './citations.component';
import { BUSINESS_PROFILE_URL_TOKEN, MANAGE_KNOWLEDGE_URL_TOKEN } from '@galaxy/ai-knowledge';
import {
  ConversationService,
  ACCOUNT_GROUP_ID_TOKEN,
  CONVERSATION_HOST_APP_INTERFACE_TOKEN,
  CONVERSATION_ROUTES_TOKEN,
} from '../../../../../../../core/src';

describe('CitationsComponent', () => {
  let spectator: Spectator<CitationsComponent>;
  const createComponent = createComponentFactory({
    component: CitationsComponent,
    imports: [],
    providers: [
      {
        provide: CONVERSATION_HOST_APP_INTERFACE_TOKEN,
        useValue: {
          getAppOptions: jest.fn().mockReturnValue({ is_multilocation: false }),
        },
      },
      {
        provide: ConversationService,
        useValue: {
          currentConversationDetail$: of({}),
        },
      },
      {
        provide: TranslateService,
        useValue: {
          get: (key: string) => of(key),
          onTranslationChange: of(),
          onLangChange: of(),
          onDefaultLangChange: of(),
        },
      },
      {
        provide: MANAGE_KNOWLEDGE_URL_TOKEN,
        useValue: of(''),
      },
      {
        provide: BUSINESS_PROFILE_URL_TOKEN,
        useValue: of(''),
      },
      {
        provide: ACCOUNT_GROUP_ID_TOKEN,
        useValue: of(''),
      },
      {
        provide: CONVERSATION_ROUTES_TOKEN,
        useValue: of({ root: '/test' }),
      },
    ],
  });

  beforeEach(() => {
    spectator = createComponent();
  });

  describe('citations', () => {
    it('should show citations', async () => {
      const messageMetadata = [
        {
          key: 'citations',
          value: JSON.stringify([
            {
              type: 2,
              title: 'Title of website',
              link: 'http://test.com',
              content: 'Example content',
            },
          ]),
        },
      ];

      spectator.fixture.componentRef.setInput('messageMetadata', messageMetadata);

      await spectator.fixture.whenStable();
      spectator.fixture.detectChanges();
      expect(spectator.query(byText('INBOX.CITATIONS.SOURCE_MATERIAL'))).toExist();
    });

    it('should not show citations if none exist', async () => {
      spectator.fixture.componentRef.setInput('messageMetadata', []);

      await spectator.fixture.whenStable();
      spectator.fixture.detectChanges();
      expect(spectator.query(byText('INBOX.CITATIONS.SOURCE_MATERIAL'))).not.toExist();
    });

    it('should not show citations if messageMetadata is undefined', async () => {
      spectator.fixture.componentRef.setInput('messageMetadata', undefined);

      await spectator.fixture.whenStable();
      spectator.fixture.detectChanges();
      expect(spectator.query(byText('INBOX.CITATIONS.SOURCE_MATERIAL'))).not.toExist();
      expect(spectator.component.citations()).toEqual([]);
    });

    it('should show source material when only explanation exists', async () => {
      const messageMetadata = [
        {
          key: 'reasoning',
          value: JSON.stringify('Test explanation'),
        },
      ];

      spectator.fixture.componentRef.setInput('messageMetadata', messageMetadata);
      await spectator.fixture.whenStable();
      spectator.fixture.detectChanges();

      expect(spectator.query(byText('INBOX.CITATIONS.SOURCE_MATERIAL'))).toExist();
    });

    it('should show source material when both explanation and citations exist', async () => {
      const messageMetadata = [
        {
          key: 'reasoning',
          value: JSON.stringify('Test explanation'),
        },
        {
          key: 'citations',
          value: JSON.stringify([{ type: 2, title: 'Test citation', link: 'http://test.com' }]),
        },
      ];

      spectator.fixture.componentRef.setInput('messageMetadata', messageMetadata);
      await spectator.fixture.whenStable();
      spectator.fixture.detectChanges();

      expect(spectator.query(byText('INBOX.CITATIONS.SOURCE_MATERIAL'))).toExist();
    });
  });
});
