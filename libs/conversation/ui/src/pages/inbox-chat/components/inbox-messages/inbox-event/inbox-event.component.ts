import { Component, computed, HostBinding, inject, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { GalaxyChatModule } from '@vendasta/galaxy/chat';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { grey } from '@mui/material/colors';
import { ConversationChannel, EventType, ParticipantType } from '@vendasta/conversation';
import { EmailPreviewDialogComponent, EmailPreviewDialogData } from '../preview/email-preview.component';
import { MatDialog } from '@angular/material/dialog';
import { combineLatest, first, firstValueFrom, map, of } from 'rxjs';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ContentContentType, MessageService } from '@vendasta/email';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { switchMap } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';

import { CallTranscriptModalComponent } from './call-transcript-modal.component';
import { InboxEvaluationButtonsComponent } from '../inbox-evaluation-buttons/inbox-evaluation-buttons.component';
import { ACCOUNT_GROUP_ID_TOKEN, PARTNER_ID_TOKEN } from '../../../../../../../core/src/lib/tokens';
import { ConversationService } from '../../../../../../../core/src/lib/state/conversation.service';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { ConversationEvent, EventInfo } from '../../../../../../../core/src/lib/interface/conversation.interface';
import { ConversationPipesModule } from '../../../../../../../pipes';

const metadataMissedCallKey = 'event_missed_call';
const aiVoiceReceptionist = 'AI Voice Receptionist';

@Component({
  selector: 'inbox-event',
  imports: [
    CommonModule,
    GalaxyAvatarModule,
    TranslateModule,
    GalaxyChatModule,
    ConversationPipesModule,
    InboxEvaluationButtonsComponent,
  ],
  templateUrl: './inbox-event.component.html',
  styleUrl: './inbox-event.component.scss',
})
export class InboxEventComponent {
  @HostBinding('class') class = 'inbox-event';

  event = input.required<ConversationEvent>();

  private readonly dialog = inject(MatDialog);
  private readonly emailApiService = inject(MessageService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly translateService = inject(TranslateService);
  private readonly conversationService = inject(ConversationService);

  protected showEvaluationButtonsComponent = computed(() => {
    const event = this.event();
    const metadata = event?.event?.metadata || [];
    return (
      event.event.type === EventType.EVENT_TYPE_PHONE_CALL &&
      !metadata.find((meta) => meta.key === metadataMissedCallKey && meta.value === 'true')
    );
  });
  protected readonly EventType = EventType;
  protected readonly ConversationChannel = ConversationChannel;

  protected readonly grey = grey;
  showEvaluationButtons = false;

  readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);

  private readonly breakpointObserver = inject(BreakpointObserver);
  private readonly isExtraSmall = this.breakpointObserver.observe(Breakpoints.XSmall);

  constructor(private readonly http: HttpClient) {}

  async showOriginalEmail(event: EventInfo): Promise<void> {
    if (!event.emailId) return;

    firstValueFrom(
      this.emailApiService
        .getMessage(event.emailId)
        .pipe(map((result) => result.content.filter((content) => content.type === ContentContentType.TEXT_HTML)[0])),
    )
      .then((email) => this.openEmailPreviewDialog(email.value))
      .catch(() =>
        this.snackbarService.openErrorSnack(
          this.translateService.instant('INBOX.ERROR.ORIGINAL_MSG_CONTENT_NOT_AVAILABLE'),
        ),
      );
  }

  openEmailPreviewDialog(emailHtml?: string): void {
    if (!emailHtml) return;

    this.dialog.open(EmailPreviewDialogComponent, {
      data: {
        htmlContent: emailHtml,
      } as EmailPreviewDialogData,
      panelClass: 'mat-dialog-no-padding',
    });
  }

  async openTranscriptModal() {
    const customerName$ = this.conversationService.currentConversationDetail$.pipe(
      first(),
      map(
        (conversationDetail) =>
          conversationDetail?.participants.find(
            (participant) => participant.participantType === ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
          )?.name,
      ),
    );

    const [partnerId, accountGroupId, customerName] = await firstValueFrom(
      combineLatest([this.partnerId$, this.accountGroupId$, customerName$]),
    );
    const eventInfo = this.event().eventInfo;
    if (!eventInfo) return;

    let toName = '';
    let fromName = '';
    if (eventInfo.isAIVoiceCall) {
      if (eventInfo.IsOutBound) {
        fromName = aiVoiceReceptionist;
        if (customerName) toName = customerName;
      } else {
        toName = aiVoiceReceptionist;
        if (customerName) fromName = customerName;
      }
    }

    const dialogRef = this.dialog.open(CallTranscriptModalComponent, {
      width: '950px',
      maxWidth: '100vw',
      maxHeight: '100vh',
      data: {
        partnerId,
        accountGroupId,
        callRecordId: eventInfo.callRecordId,
        transcriptJSON: eventInfo.transcriptJSON,
        toName: toName || eventInfo.callToNumber,
        fromName: fromName || eventInfo.callFromNumber,
      },
    });

    const smallDialogSubscription = this.isExtraSmall.subscribe((size) => {
      if (size.matches) {
        dialogRef.updateSize('100vw', '100vh');
      } else {
        dialogRef.updateSize('950px', '80%');
      }
    });
    dialogRef.afterClosed().subscribe(() => {
      smallDialogSubscription.unsubscribe();
    });
  }

  callRecordingUrl = toSignal(
    toObservable(this.event).pipe(
      switchMap((event) => {
        if (!event.eventInfo?.callRecordingUrl) {
          return of(null);
        }
        return this.http.get(event.eventInfo?.callRecordingUrl, {
          responseType: 'text',
          withCredentials: true,
        });
      }),
    ),
  );

  onClick(event: MouseEvent) {
    const selectedText = window.getSelection()?.toString();

    if (selectedText) {
      return;
    }

    const target = event.target;
    if (target instanceof HTMLElement) {
      if (target.classList.contains('call-recording') || target.classList.contains('event-link')) {
        return;
      }
      this.showEvaluationButtons = !this.showEvaluationButtons;
    }
  }
}
