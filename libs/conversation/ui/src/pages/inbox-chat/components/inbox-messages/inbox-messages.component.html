<glxy-chat-container
  class="ph-no-capture"
  #chatContainer
  [chatId]="(conversationId$ | async) ?? ''"
  [animationDuration]="200"
  [downArrowText]="'INBOX.CHAT.MESSAGE_RECEIVED_TEXT' | translate"
>
  @if (lastMessageErrorReason$ | async) {
    <glxy-alert [type]="'error'" [size]="'small'">
      {{ lastMessageErrorReason$ | async | translate }}
    </glxy-alert>
  }
  @if (loadingOpenAIReply()) {
    <glxy-chat-message-group
      [type]="RECEIVED"
      [isTyping]="true"
      [messageFrom]="systemAssistant()?.name ?? ''"
      [messageMetadata]="'INBOX.CHAT.TYPING' | translate"
      [showProfilePic]="true"
      [profilePicUrl]="systemAssistant()?.avatarUrl"
      [profileSVGIcon]="!systemAssistant()?.avatarUrl ? VENDASTA_AI_AVATAR_SVG_ICON : ''"
    ></glxy-chat-message-group>
  }
  <ng-container *ngFor="let item of groupedItems$ | async; trackBy: trackById">
    <ng-container
      *ngIf="isSystemMessage(item); else checkUntypedEvent"
      [ngTemplateOutlet]="systemMessage"
      [ngTemplateOutletContext]="{ message: item }"
    >
    </ng-container>

    <ng-template #checkUntypedEvent>
      <ng-container
        *ngIf="isUntypedEvent(item); else messageGroupTemplate"
        [ngTemplateOutlet]="untypedEvent"
        [ngTemplateOutletContext]="{ event: item }"
      >
      </ng-container>
    </ng-template>
    <ng-template #messageGroupTemplate>
      <ng-container [ngTemplateOutlet]="messageGroup" [ngTemplateOutletContext]="{ messageGroup: item }">
      </ng-container>
    </ng-template>
  </ng-container>
</glxy-chat-container>

<ng-template #messageGroup let-messageGroup="messageGroup">
  <glxy-chat-message-group
    [type]="messageGroup.lastItem?.item | messageStatusType"
    [messageFrom]="(messageGroup.lastItem?.itemFrom | translate) || ('INBOX.CHAT.UNKNOWN_CONTACT' | translate)"
    [messageMetadata]="messageGroup.lastItem?.sentVia | translate"
    [sentFromYou]="messageGroup | isFromYou"
    [showProfilePic]="true"
    [profileIcon]="messageGroup | chatProfileIcon"
    [profilePicUrl]="messageGroup.sender?.profileImageUrl"
    [profileSVGIcon]="getProfileSVGIcon(messageGroup) || (messageGroup | chatProfileSvgIcon)"
    [profileBackgroundColor]="!!(messageGroup | chatProfileIcon) ? defaultBackgroundColor : ''"
    [profileSourceIconName]="messageGroup.channel | chatSourceIconName"
    [showReceivedMessageMetadata]="(isAIConversation | async) === true"
  >
    <ng-container *ngFor="let chatItem of messageGroup.messages; trackBy: trackById">
      <ng-container *ngIf="isConversationEvent(chatItem)">
        @if (chatItem.item?.isFirstItem) {
          <inbox-event class="first-inbox-item" [event]="chatItem.item"></inbox-event>
        } @else {
          <inbox-event [event]="chatItem.item"></inbox-event>
        }
      </ng-container>
      <glxy-chat-message
        *ngIf="
          (isConversationMessage(chatItem) || isConversationTemplate(chatItem) || isBookingAvailability(chatItem)) &&
          chatItem.item.body
        "
        [messageText]="
          chatItem.item?.body ?? ''
            | truncateMessage: chatItem.item
            | messageCodeBacktick
            | replaceNewLine
            | glxyMarkdown
            | linky: { email: false }
        "
        [messageStatus]="mapMessageStatus(chatItem.item)"
        [messageTime]="chatItem.item?.created | messageBubbleTime"
        [messageIsMarkdown]="true"
      >
        @if (originalMsgButtonEnabled(chatItem.item)) {
          <a class="view-original-message-button" (click)="showOriginalMessage(chatItem.item)">
            {{ 'INBOX.CHAT.VIEW_ORIGINAL_MESSAGE' | translate }}
          </a>
        }
        <inbox-citations-component
          *ngIf="
            (messageGroup.sender?.participantType === AI_ASSISTANT_TYPE ||
              messageGroup.sender?.participantType === OPENAI_BOT_TYPE) &&
            messageGroup.sender?.internalParticipantId !== SYSTEM_ASSISTANT_ID &&
            (isAIConversation | async) === false
          "
          [messageMetadata]="chatItem.item?.metadata"
        ></inbox-citations-component>
        <glxy-additional-metadata
          class="evaluate"
          *ngIf="
            messageGroup.sender?.participantType === AI_ASSISTANT_TYPE ||
            messageGroup.sender?.participantType === OPENAI_BOT_TYPE
          "
        >
          <inbox-evaluation-buttons [message]="chatItem.item"> </inbox-evaluation-buttons>
        </glxy-additional-metadata>
      </glxy-chat-message>

      @if (chatItem.item | showBookingAvailability: messages$ | async) {
        <inbox-booking-availability-component
          class="booking-availability"
          [inputMessage]="chatItem.item"
        ></inbox-booking-availability-component>
      }
      @if (isConversationMessage(chatItem)) {
        <inbox-message-ui-components
          [inputMessage]="chatItem.item"
          (toolCallStarted)="setLoadingOpenAIReplyTrue()"
          (componentClicked)="onComponentClicked($event)"
        ></inbox-message-ui-components>
      }
      <ng-container [ngTemplateOutlet]="mediaMessage" [ngTemplateOutletContext]="{ message: chatItem }"></ng-container>
    </ng-container>
  </glxy-chat-message-group>

  <glxy-chat-divider-text *ngIf="!!messageGroup?.createdAt">
    {{ messageGroup.createdAt | messageTime }}
  </glxy-chat-divider-text>
</ng-template>

<ng-template #untypedEvent let-event="event">
  <glxy-chat-event [messageMetadata]="event?.created | date: 'MMM d, y, h:mm a'">
    <div class="event-label">{{ event.item?.event?.labelKey | translate }}</div>
    @if (event.item?.event?.message) {
      <div class="chat-event-message">{{ event.item?.event?.message }}</div>
    }
  </glxy-chat-event>
</ng-template>
<ng-template #systemMessage let-message="message">
  <glxy-chat-divider-text class="system-message">
    <div [innerHTML]="message.item?.body | linky | messageCodeBacktick | truncateUrl"></div>
  </glxy-chat-divider-text>
</ng-template>

<ng-template #mediaMessage let-message="message">
  <ng-container *ngFor="let media of message?.item?.media" itemContent="attachment">
    <glxy-chat-message
      [class.message-media]="media.mediaContentType | inArray: acceptedMimeType"
      [itemContent]="(media.mediaContentType | inArray: acceptedMimeType) ? 'attachment' : 'message'"
      [messageStatus]="mapMessageStatus(message.item)"
      [messageTime]="message.created | date: 'MMM d, y, h:mm a'"
    >
      <ng-container *ngIf="mediaWithErrorsMessageIDs().indexOf(message.item?.id) === -1">
        <ng-container *ngIf="media.mediaContentType | inArray: acceptedMimeType; else otherFile">
          <div class="content-attachment">
            <img
              alt="media"
              class="message-media-image"
              *ngIf="media.mediaContentType.indexOf('image') !== -1"
              [src]="media | getMessageMediaUrl"
              width="100%"
              height="auto"
              (click)="openInDialog(media.mediaUrl, media.mediaContentType)"
              (error)="addMessageIdToMediaErrors(message.item?.id)"
            />
            <video
              *ngIf="media.mediaContentType.indexOf('video') !== -1"
              width="100%"
              height="auto"
              controls="controls"
              preload="metadata"
              (click)="openInDialog(media.mediaUrl, media.mediaContentType)"
            >
              <source
                [src]="media | getMessageMediaUrl"
                [type]="media.mediaContentType"
                (error)="addMessageIdToMediaErrors(message.item?.id)"
              />
            </video>
            <audio *ngIf="media.mediaContentType.indexOf('audio') !== -1" controls>
              <source
                [src]="media | getMessageMediaUrl"
                [type]="media.mediaContentType"
                (error)="addMessageIdToMediaErrors(message.item?.id)"
              />
            </audio>
          </div>
        </ng-container>
      </ng-container>
      <glxy-empty-state
        size="small"
        *ngIf="mediaWithErrorsMessageIDs().indexOf(message.item?.id) !== -1"
        class="broken-media-empty-state"
      >
        <glxy-empty-state-hero>
          <mat-icon>broken_image</mat-icon>
        </glxy-empty-state-hero>
        <p>{{ 'INBOX.ERROR.BROKEN_MEDIA_URL' | translate }}</p>
      </glxy-empty-state>
    </glxy-chat-message>

    <ng-template #otherFile>
      <a
        href="{{ mediaUrl }}"
        target="_blank"
        rel="noopener noreferrer"
        class="link-to-document"
        *ngIf="media | getMessageMediaUrl as mediaUrl"
        download="{{ media.mediaFileName }}"
      >
        <mat-icon [inline]="true" class="file-icon">insert_drive_file</mat-icon>
        <span class="file-name">{{ media.mediaFileName }}</span>
      </a>
    </ng-template>
  </ng-container>
</ng-template>
