import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Environment, EnvironmentService } from '@galaxy/core';
import { FileInfo, GalaxyUploaderService, UploadResponse } from '@vendasta/galaxy/uploader';
import { Observable, ReplaySubject } from 'rxjs';
import { map, switchMap, take } from 'rxjs/operators';
import { ChatSourceId } from '@vendasta/galaxy/chat-composer/src/chat-composer.component';

@Injectable()
export class InboxUploadService extends GalaxyUploaderService {
  conversationId$$: ReplaySubject<string> = new ReplaySubject<string>(1);
  conversationId$: Observable<string> = this.conversationId$$.asObservable();

  constructor(
    http: HttpClient,
    private readonly env: EnvironmentService,
  ) {
    super(http);
  }

  buildRequest(fileInfo: FileInfo): Observable<UploadResponse> {
    this.uploadUrl = 'https://conversation-demo.apigateway.co/upload-file';
    if (this.env.getEnvironment() === Environment.PROD) {
      this.uploadUrl = 'https://conversation-prod.apigateway.co/upload-file';
    }
    return this.conversationId$.pipe(
      map((conversationId) => new HttpParams().set('conversationID', conversationId)),
      switchMap((params) => {
        const body = new FormData();

        body.append('file', fileInfo.file);
        const options = { withCredentials: true };
        if (params) {
          options['params'] = params;
        }
        return this.http.post(this.uploadUrl, body, options) as Observable<UploadResponse>;
      }),
      take(1),
    );
  }
}

/**
 * Returns the max file size for a given channel in bytes
 * @param chatSourceId
 */
export function getMaxFileSize(chatSourceId?: ChatSourceId): number {
  if (!chatSourceId) {
    return 0;
  }
  switch (chatSourceId) {
    case 'sms':
      return 5 * 1024 * 1024; // 5MB
    case 'email':
      return 10 * 1024 * 1024; // 10MB
    default:
      return 250 * 1024 * 1024; // 250MB
  }
}

/**
 * Returns the accepted file types for a given channel
 * @param chatSourceId
 */
export function getAcceptFileTypes(chatSourceId?: ChatSourceId): string {
  if (!chatSourceId) {
    return '';
  }
  switch (chatSourceId) {
    case 'sms':
      return 'image/jpeg, image/jpg, image/png, image/gif';
    case 'whatsapp':
      return [
        'image/jpeg',
        'image/png',
        'video/3gp',
        'video/mp4',
        'text/plain',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/pdf',
      ].join(', ');
    default:
      return '*/*';
  }
}

/**
 * Returns the max file size for a given channel in MB
 * @param chatSourceId
 */
export function getMaxFileSizeInMB(chatSourceId?: ChatSourceId): number {
  if (!chatSourceId) {
    return 0;
  }
  return getMaxFileSize(chatSourceId) / 1024 / 1024;
}
