@if (!$loadingError()) {
  <mat-drawer-container class="mat-drawer-container" [hasBackdrop]="false">
    <mat-drawer-content>
      <div class="inbox-chat" *ngIf="currentConversationDetail$ | async" [ngClass]="{ 'drawer-open': isDrawerOpened }">
        <inbox-messages-top-bar
          [accountGroupId]="accountGroupId() ?? ''"
          [groupId]="groupId() ?? ''"
          [unseen]="(unseen$ | async) ?? unseenDefault"
          [isFollowing]="(isFollowing$ | async) ?? false"
          [isOpen]="isOpen()"
          [isConversationOverlayMode]="$isConversationOverlayOpened()"
          (markedUnread)="updateReadStatus($event)"
          (changeFollowStatus)="updateFollowStatus($event)"
          (changeOpenStatus)="updateOpenStatus($event)"
          (titleInBusinessAppClicked)="handleTitleClickInBusinessApp($event)"
          (changeAIResponseEnabled)="changeAIResponseStatus($event)"
          (openConversationOverlayClicked)="openConversationOverlay($event)"
          (closeConversationOverlayClicked)="closeConversationOverlay($event)"
        ></inbox-messages-top-bar>
        <ng-container *ngIf="(isOpenAIChannel$ | async) === false">
          <glxy-alert
            *ngFor="let bannerText of banners$ | async"
            class="availibility-banner"
            [border]="true"
            [borderRadius]="false"
            type="tip"
          >
            {{ bannerText }}
          </glxy-alert>
        </ng-container>
        <glxy-alert
          *ngIf="notificationWarningChat$ | async"
          type="info"
          [showAction]="true"
          actionTitle="{{ 'INBOX.WARNING.TURN_ON_NOTIFICATIONS' | translate }}"
          (actionClick)="openSettings()"
          [showClose]="true"
          (close)="onClose()"
          [border]="false"
          [borderRadius]="false"
          [stackedView]="viewModel ?? false"
        >
          <strong>{{ 'INBOX.WARNING.TURN_ON_NOTIFICATIONS' | translate }}</strong>
          {{ 'INBOX.WARNING.TURN_ON_NOTIFICATIONS_DETAILS' | translate }}
          <strong>{{ 'INBOX.WARNING.NOTIFICATION_SETTINGS' | translate }}</strong>
        </glxy-alert>
        <glxy-loading-spinner
          *ngIf="loadingMessages$ | async"
          [ngClass]="{ 'show-loading': (loadingMessages$ | async) }"
        ></glxy-loading-spinner>
        <inbox-messages
          *ngIf="currentConversationDetail$ | async as currentConversationDetail"
          [messages]="(messages$ | async | handleEmptyMessage) ?? []"
          [events]="(events$ | async) ?? []"
          [conversation]="currentConversationDetail.conversation"
        ></inbox-messages>
        <ng-container *ngIf="currentConversationDetail$ | async as currentConversationDetail">
          <inbox-message-text
            #inboxTextComponent
            (sendMessage)="sendMessage($event)"
            (fileChange)="fileChange($event)"
            [aiResponder]="$conversationAIResponder()"
            [clearMessageText]="(termsOfServiceResult$ | async)?.termsOfServiceAccepted ?? false"
            [conversationDetail]="currentConversationDetail"
            [availableChannels]="availableChannels$ | async"
            [isImpersonating]="(isImpersonating$ | async) ?? false"
            [prefilledMessage]="(inboxPrefilledMessage$ | async) ?? ''"
            [missingContactInformation]="(missingContactInformation$ | async) ?? true"
          ></inbox-message-text>
        </ng-container>
      </div>
    </mat-drawer-content>
    <mat-drawer
      *ngIf="hasBusinessAppSideBar()"
      class="details-drawer"
      mode="side"
      position="end"
      role="region"
      [opened]="isDrawerOpened"
    >
      <div class="drawer-header">
        <h2 class="drawer-title">{{ 'INBOX.INFO.CONVERSATION_DETAILS' | translate }}</h2>
        <button mat-icon-button (click)="closeDrawer()">
          <mat-icon>close</mat-icon>
        </button>
      </div>
      <div class="card">
        <glxy-contact-info-card *ngFor="let cardData of cardData$ | async" [cardData]="cardData">
        </glxy-contact-info-card>
      </div>
      <div class="media-section">
        <h3 class="media-title">{{ 'INBOX.INFO.SHARED_MEDIA' | translate }}</h3>
        <div class="media-items">
          <div class="media-item" *ngFor="let mediaMessage of mediaMessages$$ | async">
            @let media = mediaMessage.media[0];
            @if (media.mediaContentType.startsWith('image/')) {
              <img [src]="media.mediaUrl" alt="" (click)="openInDialog(media.mediaUrl, media.mediaContentType)" />
            }
            @if (media.mediaContentType.startsWith('video/')) {
              <video
                [src]="media.mediaUrl"
                controls
                (click)="openInDialog(media.mediaUrl, media.mediaContentType)"
              ></video>
            }
            @if (media.mediaContentType.startsWith('broken')) {
              <mat-icon>broken_image</mat-icon>
            }
          </div>
        </div>
      </div>
    </mat-drawer>
  </mat-drawer-container>
} @else {
  <inbox-error [error]="$loadingError()!" [customErrorMessage]="'INBOX.ERROR.LOAD_MESSAGES'"></inbox-error>
}
