@use 'design-tokens' as *;

.sub-options {
  display: flex;
  flex-direction: column;
  background-color: $secondary-background-color;
  padding: $spacing-3;
  border-radius: $default-border-radius;
}

.form-wrapper {
  display: flex;
  flex-direction: column;
}

.forwarding-enabled-input-group {
  display: flex;
  align-items: baseline;
}

.your-phone {
  @include text-preset-3--bold;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: $spacing-1;
  gap: $spacing-3;
}

.follow-up-text {
  @include text-preset-4--bold;
}

.config-cards {
  display: flex;
  flex-direction: column;
  gap: $spacing-4;
}

.top-spacing {
  margin-top: $spacing-4;
}

.info-icon {
  color: $secondary-text-color;
  font-size: $spacing-3;
  width: $spacing-3;
  height: $spacing-3;
  vertical-align: text-bottom;
  margin-left: $spacing-1;
}

.learn-more {
  color: $secondary-text-color;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  border-radius: $default-border-radius;
  margin-top: $spacing-2;

  &:hover {
    background-color: $secondary-background-color;
  }
}

.expand-icon {
  @include text-preset-4;
  height: $font-preset-4-size;
  width: $font-preset-4-size;
}

.quick-setup-flex {
  display: flex;
  align-items: flex-start;
}

.quick-setup-icon {
  margin-top: $negative-1;
  padding-right: $spacing-3;
}

.quick-setup-card {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.quick-setup-activate {
  color: $success-text-color;
  font-weight: 500;
}

.quick-setup-cancel {
  color: $error-text-color;
  font-weight: 500;
}

.quick-setup-code {
  background: $success-background-color;
  padding: 2px 6px;
  border-radius: $default-border-radius;
  font-family: monospace;
}

.quick-setup-code-cancel {
  background: $error-background-color;
}

.quick-setup-copy-icon {
  font-size: $font-preset-3-size;
  height: $font-preset-3-size;
  width: $font-preset-3-size;
  vertical-align: text-bottom;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.quick-setup-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;
}

.quick-setup-codes {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
}

.quick-setup-howto {
  white-space: pre-line;
  margin-bottom: $spacing-1;
}

.quick-setup-tip {
  margin-bottom: $spacing-1;
}

.code-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: $spacing-1;
}

.code-and-icon {
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: $spacing-1;

  &:hover {
    .quick-setup-copy-icon {
      opacity: 1;
    }
  }
}

.info-panel {
  border: none !important;
}

.panel-header {
  border-bottom: none !important;
}
