import { CommonModule } from '@angular/common';
import { Component, computed, EnvironmentInjector, inject, OnInit, runInInjectionContext, signal } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ConversationApiService, UpdateWidgetRequest } from '@vendasta/conversation';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { PopoverPositions } from '@vendasta/galaxy/popover';
import { PartnerSettingsApiService } from '@vendasta/listing-products';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { catchError, combineLatest, firstValueFrom, map, of, shareReplay, switchMap, tap, throwError } from 'rxjs';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  CONVERSATION_WEB_CHAT_ENABLED_TOKEN,
  InboxService,
  PARTNER_ID_TOKEN,
} from '../../../../../core/src';

import { getBackUrl } from '../constants';
import { WebChatInstructionsComponent } from '../web-chat-instructions/web-chat-instructions.component';
import { WebchatFormComponent } from '../webchat-form/webchat-form.component';
import { InboxProCtaComponent } from '../inbox-pro-cta/inbox-pro-cta.component';
import { Widget } from '../../webchat-configuration/webchat-form/widget';
import { toSignal } from '@angular/core/rxjs-interop';
import { isNativeApp } from '../../../../../core/src/lib/mobile';
import { AiAssistantService, ConnectionType } from '@galaxy/ai-assistant';

@Component({
  selector: 'inbox-edit-webchat',
  imports: [
    CommonModule,
    MatCardModule,
    MatSlideToggleModule,
    MatTooltipModule,
    GalaxyPageModule,
    TranslateModule,
    MatIconModule,
    ReactiveFormsModule,
    GalaxyAlertModule,
    GalaxyTooltipModule,
    WebChatInstructionsComponent,
    WebchatFormComponent,
    InboxProCtaComponent,
  ],
  templateUrl: './edit-webchat.component.html',
})
export class EditWebchatComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly conversationApi = inject(ConversationApiService);
  private readonly inboxService = inject(InboxService);
  private readonly webChatEnabled$ = inject(CONVERSATION_WEB_CHAT_ENABLED_TOKEN);
  private readonly injector = inject(EnvironmentInjector);
  // WARNING: this page is not reactive to location switching
  // It relies on switching location rules to navigate to the parent page (inbox settings)
  // If this changes then this component will need to be updated
  private readonly accountGroupId = toSignal(inject(ACCOUNT_GROUP_ID_TOKEN));
  private readonly localSEOClient = inject(PartnerSettingsApiService);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly snacks = inject(SnackbarService);
  private readonly aiAssistantService = inject(AiAssistantService);

  protected readonly isBusinessApp = this.inboxService.isBusinessApp;
  // WARNING: this page is not reactive to location switching
  // This will get the SEO config of the first account group
  private readonly localSEOConfig$ = this.isBusinessApp
    ? this.partnerId$.pipe(
        switchMap((partnerId) =>
          this.localSEOClient
            .getConfiguration({ partnerId: partnerId, businessId: this.accountGroupId() })
            .pipe(map((resp) => resp?.configuration)),
        ),
        catchError(() => of(null)), // page error handling is done in apiWebChat$
      )
    : of(null);
  // WARNING: this page is not reactive to location switching (this API call can't react to location changes)
  // This could be reactive to the widget ID, but right now it's bound to the URL
  private readonly apiWebChat$ = this.conversationApi
    .getWidget({
      widgetId: this.route.snapshot.params.widgetId,
    })
    .pipe(
      map((resp) => resp.widget),
      catchError((err) => {
        this.error.set(err.message);
        return of(null);
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
  private readonly webchatWithLocalSeoDetails$ = combineLatest([this.apiWebChat$, this.localSEOConfig$]).pipe(
    map(([widget, localSEOConfig]) => {
      if (!widget) return null;
      const w: Widget = { ...widget, isListingWebChat: false };
      if (!this.isBusinessApp || !localSEOConfig?.chatWidgetId) return w;
      if (w.widgetId === localSEOConfig?.chatWidgetId) {
        w.myListingUrl = localSEOConfig?.publicMyListingUrl;
        w.isListingWebChat = true;
      }
      return w;
    }),
    tap(() => this.fetching.set(false)),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  private readonly saving = signal(false);
  private readonly fetching = signal(true);

  // on page load get initial value of enable toggle (disable until data is loaded)
  protected readonly enabledCtrl = new FormControl({ value: true, disabled: true });
  TOOLTIP_BOTTOMRIGHT = [{ ...PopoverPositions.BottomRight }];

  protected readonly backUrl = getBackUrl(this.isBusinessApp, 3);
  protected readonly isLoading = computed(() => this.fetching() || this.saving());
  protected readonly error = signal<null | string>(null);
  protected readonly webchat = toSignal(this.webchatWithLocalSeoDetails$);
  protected readonly showInstallInstructions = toSignal(
    combineLatest([this.webChatEnabled$, this.webchatWithLocalSeoDetails$]).pipe(
      map(([webChatEnabled, webChat]) => webChatEnabled && !webChat?.isListingWebChat),
    ),
  );
  protected readonly showUpgradeCTA = toSignal(
    this.webChatEnabled$.pipe(map((webChatEnabled) => !webChatEnabled && !isNativeApp())),
  );
  protected readonly webChatForm = WebchatFormComponent.webChatFormGroupFactory();
  protected readonly knowledgeForm = WebchatFormComponent.knowledgeFormGroupFactory();

  ngOnInit(): void {
    this.initFormsAndControls();
  }

  private async initFormsAndControls(): Promise<void> {
    this.webChatForm.disable();
    this.knowledgeForm.disable();

    // on page load get initial value of webchat and inflated webchat with local seo details
    const enabled = firstValueFrom(
      this.apiWebChat$.pipe(
        map((widget) => {
          if (!widget) return true; // on error we will allow user to turn on/off webchat - we also assume it is enabled
          return widget.isEnabled;
        }),
      ),
    );
    const webchatWithMyListingsInfo = firstValueFrom(this.webchatWithLocalSeoDetails$);

    try {
      const isEnabled = await enabled;
      this.enabledCtrl.setValue(!!isEnabled);
    } catch (e) {
      console.debug('initFormsAndControls.enabled api get error', e);
    } finally {
      this.enabledCtrl.enable(); // on error we will allow user to turn on/off webchat
    }

    try {
      const webchat = await webchatWithMyListingsInfo;
      if (webchat) {
        WebchatFormComponent.webChatFormGroupInit(this.webChatForm, webchat);
        if (webchat.widgetId) this.initKnowledgeFormInInjectionContext(webchat.widgetId);
      }
      this.webChatForm.enable();
      if (webchat?.isListingWebChat) this.webChatForm.get('name')?.disable(); // name is not editable
      this.knowledgeForm.enable();
    } catch (e) {
      console.debug('initFormsAndControls.webchat api get error', e);
    }
  }

  private initKnowledgeFormInInjectionContext(widgetId: string): void {
    runInInjectionContext<void>(this.injector, () =>
      WebchatFormComponent.knowledgeFormGroupInit(this.knowledgeForm, widgetId),
    );
  }

  protected readonly associatedAssistant = toSignal(
    this.partnerId$.pipe(
      switchMap((partnerId) => {
        return this.aiAssistantService.listAllAssistantsAssociatedToConnection(
          partnerId,
          this.accountGroupId(),
          this.route.snapshot.params.widgetId,
          ConnectionType.WebchatWidget,
        );
      }),
      catchError(() => []),
      map((assistants) => {
        if (assistants?.length > 0) {
          // We only expect one assistant to be associated with a webchat connection
          return assistants[0];
        }
        return null;
      }),
    ),
  );

  protected back(): void {
    this.router.navigate([this.backUrl], { relativeTo: this.route });
  }

  protected savingWebChat(v: boolean): void {
    this.saving.set(v);
  }

  protected async handleEnable(): Promise<void> {
    this.enabledCtrl.disable();
    const prevVal = this.enabledCtrl.value;
    try {
      const widgetId = this.route.snapshot.params.widgetId;
      const req = new UpdateWidgetRequest({
        widgetId: widgetId,
        isEnabled: !prevVal,
        fieldMask: { paths: ['is_enabled'] },
      });
      await firstValueFrom(this.conversationApi.updateWidget(req).pipe(catchError((err) => throwError(() => err))));
      const success = !prevVal
        ? 'INBOX.WEBCHAT.SETTINGS.WEBCHAT_ENABLED_SET'
        : 'INBOX.WEBCHAT.SETTINGS.WEBCHAT_DISABLED_SET';
      this.snacks.openSuccessSnack(success);
    } catch (e) {
      const fail = !prevVal
        ? 'INBOX.WEBCHAT.SETTINGS.WEBCHAT_ENABLED_FAIL'
        : 'INBOX.WEBCHAT.SETTINGS.WEBCHAT_DISABLED_FAIL';
      this.enabledCtrl.setValue(prevVal); // revert change
      this.snacks.openErrorSnack(fail);
    } finally {
      this.enabledCtrl.enable();
    }
  }
}
