@if (accountGroupId()) {
  <mat-card>
    <mat-card-header>
      <mat-card-title>{{ 'INBOX.SETTINGS.INBOX_PRO_CTA.TITLE' | translate }}</mat-card-title>
    </mat-card-header>
    @if (appInfo$ | async; as appInfo) {
      <mat-card-content>
        <div class="header">{{ 'INBOX.SETTINGS.INBOX_PRO_CTA.HEADING' | translate }}</div>
        <div class="container">
          <div class="app-info">
            <div>
              <va-icon [diameter]="40" [iconName]="appInfo.name" [iconUrl]="appInfo.iconUrl"></va-icon>
            </div>
            <div>
              <div class="title">
                {{ appInfo.name }}
              </div>
              <div class="tagline">{{ appInfo.tagline }}</div>
            </div>
          </div>
          <div>
            <a
              mat-stroked-button
              color="primary"
              [routerLink]="['/account/location', accountGroupId(), 'store', 'app', marketplaceAppID]"
              class="product-button"
            >
              {{ 'INBOX.SETTINGS.INBOX_PRO_CTA.VIEW_PRODUCT' | translate }}
            </a>
          </div>
        </div>
      </mat-card-content>
    }
  </mat-card>
}
