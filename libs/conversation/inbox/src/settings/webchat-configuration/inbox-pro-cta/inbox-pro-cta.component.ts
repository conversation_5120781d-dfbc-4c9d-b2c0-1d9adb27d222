import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  MARKETPLACE_APP_ID_TOKEN,
  MARKET_ID_TOKEN,
  PARTNER_ID_TOKEN,
} from '../../../../../core/src';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { RouterModule } from '@angular/router';
import { combineLatest, switchMap, map } from 'rxjs';
import { PartnerApiService } from '@vendasta/marketplace-apps';
import { VaIconModule } from '@vendasta/uikit';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'inbox-pro-cta',
  imports: [CommonModule, RouterModule, TranslateModule, MatCardModule, MatButtonModule, VaIconModule],
  templateUrl: './inbox-pro-cta.component.html',
  styleUrls: ['./inbox-pro-cta.component.scss'],
})
export class InboxProCtaComponent {
  readonly partnerID$ = inject(PARTNER_ID_TOKEN);
  readonly marketID$ = inject(MARKET_ID_TOKEN);
  readonly marketplaceAppID = inject(MARKETPLACE_APP_ID_TOKEN);
  readonly accountGroupId = toSignal(inject(ACCOUNT_GROUP_ID_TOKEN));

  private readonly marketplaceService = inject(PartnerApiService);

  readonly appInfo$ = combineLatest([this.partnerID$, this.marketID$]).pipe(
    switchMap(([partnerId, marketId]) =>
      this.marketplaceService.getMultiApp({
        appKeys: [{ appId: this.marketplaceAppID }],
        partnerId,
        marketId,
      }),
    ),
    map((resp) => resp.apps[0].sharedMarketingInformation),
  );
}
