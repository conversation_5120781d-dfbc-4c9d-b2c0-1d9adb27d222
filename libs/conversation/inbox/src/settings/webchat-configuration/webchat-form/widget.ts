import {
  DataSourcesInterface as SDKDataSources,
  WidgetInterface as SDKWidget,
  WidgetPosition,
} from '@vendasta/conversation';

export interface DataSources extends SDKDataSources {
  userInput?: boolean;
}

export const DefaultWidgetPosition = WidgetPosition.WIDGET_POSITION_RIGHT;

export interface Widget extends SDKWidget {
  dataSources?: DataSources;
  myListingUrl?: string;
  isListingWebChat: boolean;
}

export const KnowledgeAppType = 'WebChatWidget';
