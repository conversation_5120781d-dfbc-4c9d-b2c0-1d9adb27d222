import { Route, UrlSegment, UrlMatchResult } from '@angular/router';
import { SettingsAccessGuard } from '../../ui/src/guards/settings-access.guard';
import { SmsAccessGuard as InboxSmsAccessGuard } from '../../ui/src/guards/sms-access.guard';
import { InboxHomeComponent } from '../../ui/src/pages/inbox-home/inbox-home.component';
import { InboxContainerComponent } from './inbox-container/inbox-container.component';
import { PlatformAssistantSettingsComponent } from './settings/platform-assistant-configuration/platform-assistant-settings/platform-assistant-settings.component';
import { SettingsComponent } from './settings/settings.component';
import { SmsRegistrationComponent } from './settings/sms-configuration/sms-registration/sms-registration.component';
import { EditWebchatComponent } from './settings/webchat-configuration/edit-webchat/edit-webchat.component';
import { NewWebchatComponent } from './settings/webchat-configuration/new-webchat/new-webchat.component';
import { ShowWebchatComponent } from './settings/webchat-configuration/show-webchat/show-webchat.component';
import { UnsavedChangesGuard } from './settings/webchat-configuration/webchat-form/webchat-form.component';
import { ChatComponent } from './wrappers/chat.component';
import { CreateContactComponent } from './wrappers/create-contact.component';
import { NewMessageComponent } from './wrappers/new-message.component';
import { SmsNumberSettingsPageComponent } from './settings/sms-configuration/sms-number-settings-page/sms-number-settings-page.component';
import { InboxContainerV2Component } from './inbox-container/inbox-container-v2.component';
import { InboxConversationOverlayComponent } from './inbox-container/inbox-conversation-overlay/inbox-conversation-overlay.component';
import { InboxAiAssistantChatComponent } from '../../ui/src/pages/inbox-ai-assistant-chat/inbox-ai-assistant-chat.component';
import { SYSTEM_ASSISTANT_ID } from '@galaxy/ai-assistant';

export const INBOX_ROUTES_CHILDREN: Route[] = [
  {
    path: '',
    component: InboxHomeComponent,
  },
  {
    path: 'views/:viewId/conversations',
    component: InboxHomeComponent,
  },
  {
    path: 'conversations/:id',
    component: ChatComponent,
  },
  {
    path: 'create-contact',
    component: CreateContactComponent,
    canActivate: [InboxSmsAccessGuard],
  },
  {
    path: 'new-message',
    component: NewMessageComponent,
  },
];
export const INBOX_TEMPLATE_MESSAGE_ROUTES: Route[] = [
  {
    path: 'message-templates',
    loadComponent: () => import('../../inbox/index').then((m) => m.ListMessageTemplatesComponent),
    data: {
      pageOptions: { ['displayFullScreen']: true, ['hideNavToolbar']: true },
    },
  },
  {
    path: 'message-templates/create',
    loadComponent: () =>
      import(
        '../../inbox/src/settings/message-templates/create-message-template/create-message-template.component'
      ).then((m) => m.CreateMessageTemplateComponent),
    data: {
      pageOptions: { ['displayFullScreen']: true, ['hideNavToolbar']: true },
    },
  },
  {
    path: 'message-templates/:templateId',
    loadComponent: () =>
      import('../../inbox/src/settings/message-templates/edit-message-template/edit-message-template.component').then(
        (m) => m.EditMessageTemplateComponent,
      ),
    data: {
      pageOptions: { ['displayFullScreen']: true, ['hideNavToolbar']: true },
    },
  },
];

export const INBOX_ROUTESV2: Route[] = [
  {
    path: '',
    component: InboxContainerV2Component,
    children: INBOX_ROUTES_CHILDREN,
  },
];

export const INBOX_ROUTES: Route[] = [
  {
    path: '',
    component: InboxContainerComponent,
    children: INBOX_ROUTES_CHILDREN,
  },
];

export const INBOX_AI_AURORA: Route[] = [
  {
    path: '',
    redirectTo: SYSTEM_ASSISTANT_ID,
    pathMatch: 'full',
  },
  {
    component: InboxAiAssistantChatComponent,
    data: {
      showToolbarOnMobile: true,
      isOverlay: true,
    },
    matcher: (segments: UrlSegment[]): UrlMatchResult | null => {
      // Match ":assistantId" (1 segment)
      if (segments.length === 1) {
        return {
          consumed: segments,
          posParams: {
            assistantId: segments[0],
          },
        };
      }
      // Match ":assistantId/chat/:conversationId" (3 segments)
      if (segments.length === 3 && segments[1].path === 'chat') {
        return {
          consumed: segments,
          posParams: {
            assistantId: segments[0],
            conversationId: segments[2],
          },
        };
      }
      return null;
    },
  },
];

export const INBOX_AI_ASSISTANT_CHAT_ROUTES: Route[] = [
  {
    component: InboxAiAssistantChatComponent,
    matcher: (segments: UrlSegment[]) => {
      // Match "/chat"
      if (segments.length === 1 && segments[0].path === 'chat') {
        return { consumed: segments };
      }
      // Match "/chat/:conversationId"
      if (segments.length === 2 && segments[0].path === 'chat') {
        return {
          consumed: segments,
          posParams: {
            conversationId: segments[1],
          },
        };
      }
      return null;
    },
  },
];

export const INBOX_CONVERSATION_OVERLAY: Route[] = [
  {
    path: '',
    component: InboxConversationOverlayComponent,
    children: [
      {
        path: '',
        component: ChatComponent,
      },
    ],
  },
];

export const INBOX_SETTINGS_ROUTES: Route[] = [
  {
    path: '',
    canActivate: [SettingsAccessGuard],
    children: [
      {
        path: '',
        component: SettingsComponent,
      },
      {
        path: 'sms-registration',
        component: SmsRegistrationComponent,
      },
      {
        path: 'phone-configuration',
        component: SmsNumberSettingsPageComponent,
      },
      {
        path: 'platform-chat',
        component: PlatformAssistantSettingsComponent,
      },
      ...INBOX_TEMPLATE_MESSAGE_ROUTES,
    ],
  },
];

export const INBOX_WIDGETS_ROUTES: Route[] = [
  {
    path: '',
    children: [
      {
        path: 'new',
        component: NewWebchatComponent,
        canDeactivate: [UnsavedChangesGuard],
      },
      {
        path: ':widgetId',
        component: ShowWebchatComponent,
      },
      {
        path: ':widgetId/edit',
        component: EditWebchatComponent,
        canDeactivate: [UnsavedChangesGuard],
      },
    ],
  },
];
