import { Environment } from '@galaxy/core';

export const IAMUserPrefix = 'U-'; // Unified User ID
export const AccountGroupPrefix = 'AG-';

export const CustomerPrefix = 'CUSTOMER-';
export const ContactPrefix = 'CONTACT-';

export const PCLearnMoreUrl = 'https://support.vendasta.com/hc/en-us/articles/*************/';

export const GOOGLE_SOURCE_ID = '10010';
export const FACEBOOK_SOURCE_ID = '10050';

export const LOCAL_STORAGE_ID = 'inbox-last-channel-id';

export const EXCLUDED_GEOGRAPHICAL_US_STATES = ['AA', 'AE', 'AP', 'AS', 'FM', 'GU', 'MH', 'MP', 'PR', 'PW', 'VI'];

export const DEFAULT_CV_ICON = 'https://vstatic-prod.apigateway.co/business-center-client/assets/cv-logo.png';
export const DEFAULT_CV_NAME = 'Customer Voice';
export const CustomerVoiceAppIDs = {
  [Environment.LOCAL]: 'MP-fba21121b71148c9bb33e11fcd92d520',
  [Environment.TEST]: 'MP-fba21121b71148c9bb33e11fcd92d520',
  [Environment.DEMO]: 'MP-fba21121b71148c9bb33e11fcd92d520',
  [Environment.PROD]: 'MP-c4974d390a044c28aec31e421aa662b2',
};

export const InboxProMarketplaceAppIDs = {
  [Environment.LOCAL]: 'MP-***************7XZ3N2WV8S38RZMQW',
  [Environment.TEST]: 'MP-***************7XZ3N2WV8S38RZMQW',
  [Environment.DEMO]: 'MP-***************7XZ3N2WV8S38RZMQW',
  [Environment.PROD]: 'MP-DKT6XHPM6NCCDNK2TPDPVD3PG3V7ZHWP',
};

export const REPLACE_NAME_WITH_PHONE_PLACEHOLDER = '-';

export const INVALID_CONTACT_NAMES = [undefined, null, REPLACE_NAME_WITH_PHONE_PLACEHOLDER, '- -', ''];

// Used as a placeholder / fallback name
export const DEFAULT_OPENAI_BOT_NAME = 'Vendasta AI';

export const CONTACTS_PAGE_SIZE = 5;
export const TEMPLATES_PAGE_SIZE = 20;
export const EMAIL_MESSAGE_BODY_MAX_LENGTH = 300;

// default view id for follow DefaultViewID
export const FOLLOWING_VIEW_ID = 'following';
export const ALL_VIEW_ID = 'all';

// feature flag ids
export const SMB_MESSAGE_PARTNER_FEATURE = 'inbox_smb_message_partner';
export const INBOX_OPEN_DEFAULT = 'inbox_open_default';
export const INBOX_SMS_A2P = 'inbox_sms_a2p';
export const INBOX_PREVIEW_CHANNEL_ICON = 'inbox_preview_channel';
export const PION_PARTNER_INBOX_SMS = 'pion_partner_inbox_sms';
export const INBOX_PLATFORM_CHAT = 'inbox_platform_chat';
export const INBOX_AI_RESPONSE = 'inbox_ai_responder';
export const ADDITIONAL_PROMPT_FEATURE = 'webchat_additional_prompt_instructions';
export const FULLSCREEN_INBOX_PC = 'fullscreen_inbox_view';

export const INBOX_ONE_ON_ONE = 'inbox_one_on_one';

export const INBOX_SIDE_BAR = 'business_app_conversation_sidebar';

export const VOICE_AI = 'voice_ai';
export const CUSTOM_AI_ASSISTANTS = 'custom_ai_assistants';

// TODO: Use CRM SDK provided constants when available (CRMAAS-355 + MEGA-221)
export const STANDARD_CRM_FIELD_EXTERNAL_IDS = {
  firstName: 'standard__first_name',
  lastName: 'standard__last_name',
  phoneNumber: 'standard__phone_number',
  email: 'standard__email',
  updated: 'system__contact_updated',
  created: 'system__contact_created',
  tags: 'standard__tags',
  source: 'standard__contact_source_name',
};

export type AppOptionsKeys = 'is_multilocation';

export const INBOX_CRM_SOURCE_NAME = 'Inbox';
