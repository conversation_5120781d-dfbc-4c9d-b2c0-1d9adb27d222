import { ConversationChannel, Participant, ParticipantType } from '@vendasta/conversation';
import { FieldValue } from '@vendasta/crm';
import { STANDARD_CRM_FIELD_EXTERNAL_IDS } from './inbox.constants';
import { ConversationAvailableChannels, ConversationDetail } from './interface/conversation.interface';
import { InboxContact } from './interface/inbox.interface';
import {
  buildCrmFieldsForCreateContact,
  getSubjectParticipantByType,
  isContactInformationMissing,
} from './participant-utils';

describe('test participant-utils', () => {
  describe('getSenderByType', () => {
    it('should get a valid participant as an IAM user', () => {
      const result = getSubjectParticipantByType(
        [
          new Participant({ participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER, isSubjectParticipant: true }),
          new Participant({ participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER, isSubjectParticipant: true }),
        ],
        ParticipantType.PARTICIPANT_TYPE_IAM_USER,
      );
      expect(result.participantType).toEqual(ParticipantType.PARTICIPANT_TYPE_IAM_USER);
    });
    it('should get a valid participant as a customer', () => {
      const result = getSubjectParticipantByType(
        [
          new Participant({ participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER, isSubjectParticipant: true }),
          new Participant({ participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER, isSubjectParticipant: true }),
        ],
        ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
      );
      expect(result.participantType).toEqual(ParticipantType.PARTICIPANT_TYPE_CUSTOMER);
    });
    it('should filter out participants that are not subject participants', () => {
      const result = getSubjectParticipantByType(
        [
          new Participant({ participantType: ParticipantType.PARTICIPANT_TYPE_IAM_USER, isSubjectParticipant: false }),
          new Participant({ participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER, isSubjectParticipant: false }),
        ],
        ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
      );
      expect(result).toBeNull();
    });
  });

  describe('buildCrmFieldsForCreateContact', () => {
    const contact = {
      firstName: 'John',
      lastName: 'Doe',
      email: '',
      phone: '+13062221111',
    } as InboxContact;

    it('should build the list of CRM field without including null or empty fields', () => {
      const result = buildCrmFieldsForCreateContact(contact);
      expect(result).toStrictEqual([
        {
          externalId: STANDARD_CRM_FIELD_EXTERNAL_IDS.firstName,
          stringValue: 'John',
        } as FieldValue,
        {
          externalId: STANDARD_CRM_FIELD_EXTERNAL_IDS.lastName,
          stringValue: 'Doe',
        } as FieldValue,
        {
          externalId: STANDARD_CRM_FIELD_EXTERNAL_IDS.phoneNumber,
          phoneFieldValues: { isoCountryCode: 'CA', nationalNumber: '3062221111', extension: '' },
        } as FieldValue,
        {
          externalId: STANDARD_CRM_FIELD_EXTERNAL_IDS.source,
          stringValue: 'Inbox',
        } as FieldValue,
      ]);
    });
    it('should build the list of CRM fields using all possible fields in the form', () => {
      contact.email = '<EMAIL>';
      const result = buildCrmFieldsForCreateContact(contact);
      expect(result).toStrictEqual([
        {
          externalId: STANDARD_CRM_FIELD_EXTERNAL_IDS.firstName,
          stringValue: 'John',
        } as FieldValue,
        {
          externalId: STANDARD_CRM_FIELD_EXTERNAL_IDS.lastName,
          stringValue: 'Doe',
        } as FieldValue,
        {
          externalId: STANDARD_CRM_FIELD_EXTERNAL_IDS.phoneNumber,
          phoneFieldValues: { isoCountryCode: 'CA', nationalNumber: '3062221111', extension: '' },
        } as FieldValue,
        {
          externalId: STANDARD_CRM_FIELD_EXTERNAL_IDS.email,
          stringValue: '<EMAIL>',
        } as FieldValue,
        {
          externalId: STANDARD_CRM_FIELD_EXTERNAL_IDS.source,
          stringValue: 'Inbox',
        } as FieldValue,
      ]);
    });
  });

  describe('missing contact information banner', () => {
    it('should be false if its not a SMS or Email conversation', () => {
      const conversationDetails = {
        conversation: {
          conversationId: 'CONVERSATION-123',
        },
      } as ConversationDetail;

      const availableChannels: ConversationAvailableChannels = {
        availableChannels: [ConversationChannel.CONVERSATION_CHANNEL_INTERNAL],
        preferredChannel: ConversationChannel.CONVERSATION_CHANNEL_INTERNAL,
      };

      expect(isContactInformationMissing(conversationDetails, availableChannels)).toBe(false);
    });
    it('should be false if there is a Customer with a phone number assigned, and no email', () => {
      const conversationDetails = {
        conversation: {
          conversationId: 'CONVERSATION-123',
        },
        participants: [
          new Participant({
            internalParticipantId: 'AG-123',
            participantType: ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP,
          }),
          new Participant({
            internalParticipantId: 'CONTACT-123',
            participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
            phoneNumber: '**********',
            isSubjectParticipant: true,
          }),
        ],
      } as ConversationDetail;

      const availableChannels: ConversationAvailableChannels = {
        availableChannels: [ConversationChannel.CONVERSATION_CHANNEL_SMS],
        preferredChannel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
      };

      expect(isContactInformationMissing(conversationDetails, availableChannels)).toEqual(false);
    });
    it('should be false if there is a Customer with email assigned, and no phone Number', () => {
      const conversationDetails = {
        conversation: {
          conversationId: 'CONVERSATION-123',
        },
        participants: [
          new Participant({
            internalParticipantId: 'AG-123',
            participantType: ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP,
          }),
          new Participant({
            internalParticipantId: 'CONTACT-123',
            participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
            phoneNumber: '**********',
            isSubjectParticipant: true,
          }),
        ],
      } as ConversationDetail;

      const availableChannels: ConversationAvailableChannels = {
        availableChannels: [ConversationChannel.CONVERSATION_CHANNEL_EMAIL],
        preferredChannel: ConversationChannel.CONVERSATION_CHANNEL_EMAIL,
      };

      expect(isContactInformationMissing(conversationDetails, availableChannels)).toEqual(false);
    });
    it('should be false if there are not available channels, but the phone number is not missing', () => {
      const conversationDetails = {
        conversation: {
          conversationId: 'CONVERSATION-123',
        },
        participants: [
          new Participant({
            internalParticipantId: 'AG-123',
            participantType: ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP,
          }),
          new Participant({
            internalParticipantId: 'CONTACT-123',
            participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
            phoneNumber: '**********',
            isSubjectParticipant: true,
          }),
        ],
      } as ConversationDetail;

      const availableChannels: ConversationAvailableChannels = {
        availableChannels: [],
        preferredChannel: ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED,
      };

      expect(isContactInformationMissing(conversationDetails, availableChannels)).toBe(false);
    });
    it('should be false if there are not available channels, but the email is not missing', () => {
      const conversationDetails = {
        conversation: {
          conversationId: 'CONVERSATION-123',
        },
        participants: [
          new Participant({
            internalParticipantId: 'AG-123',
            participantType: ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP,
          }),
          new Participant({
            internalParticipantId: 'CONTACT-123',
            participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
            email: '<EMAIL>',
            isSubjectParticipant: true,
          }),
        ],
      } as ConversationDetail;

      const availableChannels: ConversationAvailableChannels = {
        availableChannels: [],
        preferredChannel: ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED,
      };

      expect(isContactInformationMissing(conversationDetails, availableChannels)).toBe(false);
    });
    it('should be true if there is a Customer with missing phone number and email, and available messages is empty', () => {
      const conversationDetails$ = {
        conversation: {
          channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
        },
        participants: [
          new Participant({
            internalParticipantId: 'AG-123',
            participantType: ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP,
            isSubjectParticipant: true,
          }),
          new Participant({
            internalParticipantId: 'CONTACT-123',
            participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
            isSubjectParticipant: true,
          }),
        ],
      } as ConversationDetail;
      const availableChannels: ConversationAvailableChannels = {
        availableChannels: [],
        preferredChannel: ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED,
      };

      expect(isContactInformationMissing(conversationDetails$, availableChannels)).toBe(true);
    });
  });
});
