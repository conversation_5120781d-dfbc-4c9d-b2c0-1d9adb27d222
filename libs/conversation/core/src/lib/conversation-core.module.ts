import { inject, InjectionToken, ModuleWithProviders, NgModule } from '@angular/core';
import { getApp, initializeApp, provideFirebaseApp } from '@angular/fire/app';
import { getFirestore, provideFirestore } from '@angular/fire/firestore';
import { Environment, EnvironmentService } from '@galaxy/core';
import { ConversationChannel, GlobalParticipantType, InboxApiService, PlatformLocation } from '@vendasta/conversation';
import { combineLatest, isObservable, map, Observable, of, switchMap } from 'rxjs';
import { catchError, shareReplay } from 'rxjs/operators';
import { ConversationChannelService } from './channels/conversation-channel.abstract';
import { ConversationFacebookService } from './channels/conversation-facebook.service';
import { ConversationInstagramService } from './channels/conversation-instagram.service';
import { ConversationGoogleService } from './channels/conversation-google.service';
import { ConversationPlatformService } from './channels/conversation-platform.service';
import { ConversationSMSService } from './channels/conversation-sms.service';
import { ConversationWebchatService } from './channels/conversation-webchat.service';
import { ConversationService } from './state/conversation.service';
import { firebaseConfig } from './firebase.config';
import { FirestoreService } from './firestore.service';
import { InboxNavigationService } from './inbox-navigation.service';
import { InboxNotificationService } from './inbox-notification.service';
import { InboxProMarketplaceAppIDs } from './inbox.constants';
import { InboxService } from './inbox.service';
import { ConversationConfig } from './interface/config.interface';
import { RouteConfig } from './interface/routes.interface';
import { ListingService } from './listing.service';
import { ParticipantService } from './participant.service';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  COMPANY_NAME_TOKEN,
  CONVERSATION_CHANNEL_SERVICE_TOKEN,
  CONVERSATION_CONFIG_TOKEN,
  CONVERSATION_CONVERSATION_CHANNELS_ENABLED_TOKEN,
  CONVERSATION_COUNTRY_TOKEN,
  CONVERSATION_FACEBOOK_MESSENGER_AVAILABLE_TOKEN,
  CONVERSATION_GEOGRAPHICAL_STATE_TOKEN,
  CONVERSATION_GOOGLE_BUSINESS_MESSAGES_AVAILABLE_TOKEN,
  CONVERSATION_IMAGE_SERVICE_TOKEN,
  CONVERSATION_INSTAGRAM_MESSAGES_AVAILABLE_TOKEN,
  CONVERSATION_PLATFORM_LOCATION_TOKEN,
  CONVERSATION_ROUTES_TOKEN,
  CONVERSATION_SMS_ENABLED_TOKEN,
  CONVERSATION_WEB_CHAT_ENABLED_TOKEN,
  CONVERSATION_WHATSAPP_MESSAGES_AVAILABLE_TOKEN,
  FEATURE_FLAG_TOKEN,
  GROUP_ID_TOKEN,
  InboxImageService,
  MARKET_ID_TOKEN,
  MARKETPLACE_APP_ID_TOKEN,
  PARTNER_BRAND_NAME_TOKEN,
  PARTNER_ID_TOKEN,
  StubInboxImageService,
  USER_ID_TOKEN,
} from './tokens';
import { ViewService } from './view.service';
import { FeatureFlagService } from '@galaxy/partner';
import { ConversationWhatsappService } from './channels/conversation-whatsapp.service';
import { WhatsappTemplatesService } from './channels/whatsapp-templates.service';
import { ConversationEmailService } from './channels/conversation-email.service';
import { AiAssistantConversationService } from './state/ai-assistant-conversation.service';
import { ConversationListService } from './state/conversation-list.service';
import { InboxAlertService } from './channels/inbox-alert.service';
import { TemplateService } from './template.service';

/**
 * The following services are singletons and should be provided in the root module
 * These services are shared across all instances of the Inbox application.
 * They are not specific to any one instance and should be provided only once.
 */
const LIB_SINGLETON_PROVIDERS = [
  ViewService,
  ParticipantService,
  FirestoreService,
  InboxService,
  InboxNavigationService,
  ListingService,
  InboxNotificationService,
  ConversationGoogleService,
  ConversationFacebookService,
  ConversationInstagramService,
  ConversationSMSService,
  ConversationWebchatService,
  ConversationPlatformService,
  ConversationWhatsappService,
  ConversationEmailService,
  InboxAlertService,
  TemplateService,
];

/**
 * The INBOX_SERVICE_PROVIDERS are NOT singletons and should be provided ONLY in the Inbox instance that requires them
 * Inbox instance is any inbox application that is running in the host app. There could be multiple instances of inbox in the same host app.
 */
export const INBOX_SERVICE_PROVIDERS = [
  ConversationService,
  WhatsappTemplatesService,
  AiAssistantConversationService,
  ConversationListService,
];

@NgModule({
  providers: [
    provideFirebaseApp(() => initializeApp(firebaseConfig, 'inbox')),
    provideFirestore(() => getFirestore(getApp('inbox'))),
  ],
})
export class ConversationCoreModule {
  static forRoot(c: { config: InjectionToken<ConversationConfig> }): ModuleWithProviders<ConversationCoreModule> {
    const result = {
      ngModule: ConversationCoreModule,
      providers: [
        ...LIB_SINGLETON_PROVIDERS,
        { provide: USER_ID_TOKEN, deps: [CONVERSATION_CONFIG_TOKEN], useFactory: userIdFactory },
        { provide: ACCOUNT_GROUP_ID_TOKEN, deps: [CONVERSATION_CONFIG_TOKEN], useFactory: accountGroupIdFactory },
        {
          provide: CONVERSATION_COUNTRY_TOKEN,
          deps: [CONVERSATION_CONFIG_TOKEN],
          useFactory: countryFactory,
        },
        {
          provide: CONVERSATION_GEOGRAPHICAL_STATE_TOKEN,
          deps: [CONVERSATION_CONFIG_TOKEN],
          useFactory: geographicalState,
        },
        { provide: PARTNER_ID_TOKEN, deps: [CONVERSATION_CONFIG_TOKEN], useFactory: partnerIdFactory },
        { provide: PARTNER_BRAND_NAME_TOKEN, deps: [CONVERSATION_CONFIG_TOKEN], useFactory: partnerBrandNameFactory },
        { provide: MARKET_ID_TOKEN, deps: [CONVERSATION_CONFIG_TOKEN], useFactory: marketIdFactory },
        { provide: COMPANY_NAME_TOKEN, deps: [CONVERSATION_CONFIG_TOKEN], useFactory: companyNameFactory },
        {
          provide: CONVERSATION_PLATFORM_LOCATION_TOKEN,
          deps: [CONVERSATION_CONFIG_TOKEN],
          useFactory: platformLocationFactory,
        },
        {
          provide: CONVERSATION_CONVERSATION_CHANNELS_ENABLED_TOKEN,
          deps: [CONVERSATION_CONFIG_TOKEN],
          useFactory: conversationChannelsEnabledFactory,
        },
        {
          provide: FEATURE_FLAG_TOKEN,
          deps: [CONVERSATION_CONFIG_TOKEN],
          useFactory: featureFlagFactory,
        },
        {
          provide: CONVERSATION_IMAGE_SERVICE_TOKEN,
          deps: [CONVERSATION_CONFIG_TOKEN],
          useFactory: inboxImageServiceFactory,
        },
        { provide: CONVERSATION_ROUTES_TOKEN, deps: [CONVERSATION_CONFIG_TOKEN], useFactory: routesFactory },
        {
          provide: CONVERSATION_GOOGLE_BUSINESS_MESSAGES_AVAILABLE_TOKEN,
          deps: [CONVERSATION_CONFIG_TOKEN],
          useFactory: googleBusinessMessagesAvailableFactory,
        },
        {
          provide: CONVERSATION_FACEBOOK_MESSENGER_AVAILABLE_TOKEN,
          deps: [CONVERSATION_CONFIG_TOKEN],
          useFactory: facebookMessengerEnabledFactory,
        },
        {
          provide: CONVERSATION_INSTAGRAM_MESSAGES_AVAILABLE_TOKEN,
          deps: [CONVERSATION_CONFIG_TOKEN],
          useFactory: instagramMessagesEnabledFactory,
        },
        {
          provide: CONVERSATION_WHATSAPP_MESSAGES_AVAILABLE_TOKEN,
          deps: [CONVERSATION_CONFIG_TOKEN],
          useFactory: whatsAppMessagesEnabledFactory,
        },
        {
          provide: CONVERSATION_SMS_ENABLED_TOKEN,
          deps: [CONVERSATION_CONFIG_TOKEN],
          useFactory: smsEnabledFactory,
        },
        {
          provide: CONVERSATION_WEB_CHAT_ENABLED_TOKEN,
          deps: [CONVERSATION_CONFIG_TOKEN],
          useFactory: webchatEnabledFactory,
        },
        { provide: CONVERSATION_CONFIG_TOKEN, useExisting: c.config },
        { provide: CONVERSATION_CHANNEL_SERVICE_TOKEN, useFactory: abstractConversationChannelServiceFactory },
        {
          provide: GROUP_ID_TOKEN,
          deps: [CONVERSATION_CONFIG_TOKEN],
          useFactory: groupIdFactory,
        },
        {
          provide: MARKETPLACE_APP_ID_TOKEN,
          useFactory: marketplaceAppIdFactory,
        },
      ],
    };
    return result;
  }
}

function userIdFactory(config: ConversationConfig): Observable<string | undefined | null> {
  return isObservable(config.userId$) ? config.userId$ : of(config.userId$);
}

function accountGroupIdFactory(config: ConversationConfig): Observable<string | undefined | null> {
  return isObservable(config.accountGroupId$) ? config.accountGroupId$ : of(config.accountGroupId$);
}

function countryFactory(config: ConversationConfig): Observable<string | undefined | null> {
  return isObservable(config.country$) ? config.country$ : of(config.country$);
}

function geographicalState(config: ConversationConfig): Observable<string | undefined | null> {
  return isObservable(config.geographicalState$) ? config.geographicalState$ : of(config.geographicalState$);
}

function partnerIdFactory(config: ConversationConfig): Observable<string | undefined | null> {
  return isObservable(config.partnerId$) ? config.partnerId$ : of(config.partnerId$);
}

function partnerBrandNameFactory(config: ConversationConfig): Observable<string | undefined | null> {
  return isObservable(config.partnerBrandName$) ? config.partnerBrandName$ : of(config.partnerBrandName$);
}

function companyNameFactory(config: ConversationConfig): Observable<string | undefined | null> {
  return isObservable(config.companyName$) ? config.companyName$ : of(config.companyName$);
}

function platformLocationFactory(config: ConversationConfig): PlatformLocation {
  return config.platformLocation;
}

function marketplaceAppIdFactory(): string {
  const environmentService = inject(EnvironmentService);
  return InboxProMarketplaceAppIDs[environmentService.getEnvironment()];
}

function conversationChannelsEnabledFactory(
  config: ConversationConfig,
): Observable<ConversationChannel[] | undefined | null> {
  return isObservable(config.conversationChannelsEnabled$)
    ? config.conversationChannelsEnabled$
    : of(config.conversationChannelsEnabled$);
}

function routesFactory(config: ConversationConfig): Observable<RouteConfig | undefined | null> {
  return isObservable(config.routes$) ? config.routes$ : of(config.routes$);
}

function featureFlagFactory(config: ConversationConfig): Observable<boolean> {
  if (!config.featureFlag$) {
    return of(false);
  }
  return config.featureFlag$;
}

function inboxImageServiceFactory(config: ConversationConfig): InboxImageService {
  return config.inboxImageService || inject(StubInboxImageService);
}

function marketIdFactory(config: ConversationConfig): Observable<string | undefined | null> {
  return isObservable(config.marketId$) ? config.marketId$ : of(config.marketId$);
}

function smsEnabledFactory(config: ConversationConfig): Observable<boolean | undefined | null> {
  const inboxApiService = inject(InboxApiService);
  const isEnabled$ = isObservable(config.smsEnabled$) ? config.smsEnabled$ : of(config.smsEnabled$);
  return combineLatest([config.accountGroupId$, config.partnerId$, isEnabled$]).pipe(
    switchMap(([agid, partnerId, isEnabled]) => {
      if (!isEnabled || (!agid && !partnerId)) {
        return of({ configuration: { smsEnabled: false } });
      }

      let ownerId;
      let participantType;

      if (agid) {
        participantType = GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP;
        ownerId = agid;
      } else if (partnerId) {
        participantType = GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER;
        ownerId = partnerId;
      }

      return inboxApiService.getConfiguration({
        subjectParticipant: {
          internalParticipantId: ownerId,
          participantType: participantType,
        },
      });
    }),
    map((config): boolean => config.configuration.smsEnabled),
    shareReplay({ refCount: true, bufferSize: 1 }),
    catchError(() => of(false)),
  );
}

function webchatEnabledFactory(config: ConversationConfig): Observable<boolean> {
  const isEnabled$ = isObservable(config.webChatEnabled$) ? config.webChatEnabled$ : of(false);

  return isEnabled$.pipe(
    shareReplay({ refCount: true, bufferSize: 1 }),
    catchError(() => of(false)),
  );
}

function facebookMessengerEnabledFactory(config: ConversationConfig): Observable<boolean> {
  const inboxApiService = inject(InboxApiService);
  const enableable$ = isObservable(config.facebookMessengerSupported$)
    ? config.facebookMessengerSupported$
    : of(config.facebookMessengerSupported$);

  return combineLatest([config.accountGroupId$, enableable$]).pipe(
    switchMap(([agid, enableable]) => {
      if (!enableable || !agid) {
        return of({ configuration: { facebookMessengerEnabled: false } });
      }
      return inboxApiService.getConfiguration({
        subjectParticipant: {
          participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP,
          internalParticipantId: agid,
        },
      });
    }),
    map((config): boolean => config.configuration.facebookMessengerEnabled),
    shareReplay({ refCount: true, bufferSize: 1 }),
    catchError(() => of(false)),
  );
}

function instagramMessagesEnabledFactory(config: ConversationConfig): Observable<boolean> {
  const enableable$ = isObservable(config.instagramMessagesSupported$)
    ? config.instagramMessagesSupported$
    : of(config.instagramMessagesSupported$);

  return combineLatest([config.accountGroupId$, enableable$]).pipe(
    map(([agid, enableable]) => {
      return Boolean(enableable && agid);
    }),
    shareReplay({ refCount: true, bufferSize: 1 }),
    catchError(() => of(false)),
  );
}

function whatsAppMessagesEnabledFactory(config: ConversationConfig): Observable<boolean> {
  const ffService = inject(FeatureFlagService);
  const isSupported$ = isObservable(config.whatsAppMessagesSupported$)
    ? config.whatsAppMessagesSupported$
    : of(config.whatsAppMessagesSupported$);

  const isEnabled$ = isObservable(config.whatsAppEnabled$) ? config.whatsAppEnabled$ : of(false);

  const whatsAppIntegrationFeatureFlag = 'inbox_whatsapp_integration';
  return combineLatest([config.partnerId$, isSupported$, isEnabled$]).pipe(
    switchMap(([partnerId, isSupported, isEnabled]) => {
      if (!partnerId || !isSupported || !isEnabled) {
        return of({ [whatsAppIntegrationFeatureFlag]: false });
      }
      return ffService.batchGetStatus(partnerId, '', [whatsAppIntegrationFeatureFlag]);
    }),
    map((status) => status[whatsAppIntegrationFeatureFlag]),
    shareReplay({ refCount: true, bufferSize: 1 }),
    catchError(() => of(false)),
  );
}

function googleBusinessMessagesAvailableFactory(config: ConversationConfig): Observable<boolean | undefined | null> {
  const inboxService = inject(InboxApiService);
  const environmentService = inject(EnvironmentService);
  const enableable$ = isObservable(config.googleBusinessMessagesSupported$)
    ? config.googleBusinessMessagesSupported$
    : of(config.googleBusinessMessagesSupported$);

  return combineLatest([config.accountGroupId$, enableable$]).pipe(
    switchMap(([agid, enableable]) => {
      if (!enableable || environmentService.getEnvironment() !== Environment.PROD || !agid) {
        return of({ configuration: { googleBusinessMessagesEnabled: false } });
      }
      return inboxService.getConfiguration({
        subjectParticipant: {
          participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_ACCOUNT_GROUP,
          internalParticipantId: agid,
        },
      });
    }),
    map((config): boolean => config.configuration.googleBusinessMessagesEnabled),
    shareReplay({ refCount: true, bufferSize: 1 }),
    catchError(() => of(false)),
  );
}

function abstractConversationChannelServiceFactory(): Record<
  ConversationChannel,
  ConversationChannelService | undefined
> {
  const conversationGoogleService = inject(ConversationGoogleService);
  const conversationWebChatService = inject(ConversationWebchatService);
  const conversationFacebookService = inject(ConversationFacebookService);
  const conversationInstagramService = inject(ConversationInstagramService);
  const conversationPlatformService = inject(ConversationPlatformService);
  const conversationWhatsappService = inject(ConversationWhatsappService);
  let conversationSMSService: ConversationSMSService;
  let conversationEmailService: ConversationEmailService;

  const channelMap: Record<ConversationChannel, ConversationChannelService | undefined> = {
    [ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED]: undefined,
    [ConversationChannel.CONVERSATION_CHANNEL_GOOGLE_MESSAGES]: undefined, // Unused channel
    [ConversationChannel.CONVERSATION_CHANNEL_GOOGLE_BUSINESS_COMMUNICATIONS]: conversationGoogleService,
    [ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT]: conversationWebChatService,
    [ConversationChannel.CONVERSATION_CHANNEL_FACEBOOK]: conversationFacebookService,
    [ConversationChannel.CONVERSATION_CHANNEL_INSTAGRAM]: conversationInstagramService,
    [ConversationChannel.CONVERSATION_CHANNEL_INTERNAL]: conversationPlatformService,
    [ConversationChannel.CONVERSATION_CHANNEL_SMS]: undefined, // Conditionally added below
    [ConversationChannel.CONVERSATION_CHANNEL_EMAIL]: undefined, // Errors not handled TODO: Add email service
    [ConversationChannel.CONVERSATION_CHANNEL_OPENAI]: undefined, // Errors not handled
    [ConversationChannel.CONVERSATION_CHANNEL_WHATSAPP]: conversationWhatsappService,
    [ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT]: undefined, // Errors not handled
  };

  try {
    conversationSMSService = inject(ConversationSMSService);
    channelMap[ConversationChannel.CONVERSATION_CHANNEL_SMS] = conversationSMSService;
  } catch (e) {
    console.debug('ConversationSMSService could not be injected.', e);
  }

  try {
    conversationEmailService = inject(ConversationEmailService);
    channelMap[ConversationChannel.CONVERSATION_CHANNEL_EMAIL] = conversationEmailService;
  } catch (e) {
    console.debug('ConversationSMSService could not be injected.', e);
  }

  return channelMap;
}

function groupIdFactory(config: ConversationConfig): Observable<string | undefined | null> {
  return isObservable(config.groupId$) ? config.groupId$ : of(config.groupId$);
}
