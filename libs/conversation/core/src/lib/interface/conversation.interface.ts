import {
  ChannelAvailabilityInterface,
  ConversationChannel,
  EventType,
  GetMultiConversationDetailsV2ResponseDetailedConversation,
  MediaInterface,
  MessageType,
  Participant,
  ParticipantType,
  PlatformLocation,
  SendStatusInterface,
  UIComponentInterface,
} from '@vendasta/conversation';
import { BadgeColor } from '@vendasta/galaxy/badge/src/badge.component';
import { Timestamp } from 'firebase/firestore';
import { Event } from '@vendasta/conversation/lib/_internal/objects/event';

export { SubjectParticipant } from '@vendasta/conversation';

export interface FirestoreConversation {
  id?: string;
  conversationId?: string;
  externalConversationId?: string;
  previewContent?: string;
  channel?: ConversationChannel;
  /**
   * @deprecated This property exists for historical compatibility.  Use subjectParticipantsKey instead.
   */
  accountGroupId?: string;
  /**
   * @deprecated This property exists for historical compatibility.  Use subjectParticipantsKey instead.
   */
  partnerId?: string;
  /**
   * @deprecated This property exists for historical compatibility. Use subjectParticipantsKey instead.
   */
  vendorPartnerId?: string;
  latestMsgSentTime?: Timestamp;
  created?: Timestamp;
  deleted?: Timestamp;
  updated?: Timestamp;
  /**
   * @deprecated This property exists for historical compatibility
   */
  lastSeen?: Timestamp | null;
  lastSeenByParticipants?: Array<FirestoreLastSeenByParticipant>;
  conversationViewIds?: Array<string>;
}

export interface ConversationMessage {
  id?: string;
  conversationId?: string;
  messageId?: string;
  externalMessageId?: string;
  sender?: Participant;
  type: MessageType;
  channel: ConversationChannel;
  body?: string;
  media?: Media[];
  sendStatus?: SendStatusInterface;
  metadata?: KeyValuePair[];
  created?: Date;
  deleted?: Date;
  updated?: Date;
  UIComponents?: UIComponentInterface[];
}

export interface KeyValuePair {
  key: string;
  value: string;
}

export interface FirestoreEvent {
  conversationId: string;
  eventId: string;
  happenedAt: Date;
  labelKey: string;
  initiator?: string;
  /**
   * @deprecated This property exists for historical compatibility. Use {@link Event#message} type instead.
   */
  message?: string;
  created: Date;
  updated: Date;
  deleted: Date;
}

export interface ConversationUnseen {
  conversationId?: string;
  read: boolean;
}

export interface FirestoreLastSeenByParticipant {
  participantId?: string;
  lastSeenTime?: Timestamp;
}

export interface ConversationBadge {
  participantType: string;
  badgeColor: BadgeColor;
}

export type ConversationDetail = GetMultiConversationDetailsV2ResponseDetailedConversation;

export interface ConversationDetailCache {
  [conversationId: string]: ConversationDetail;
}

export interface SendMessage {
  participants?: Array<Participant>;
  location: PlatformLocation;
  channel: ConversationChannel;
}

export interface ConversationTitleInfo {
  title: string;
  subtitle?: string;
  secondarySubtitle?: string;
}

export interface ConversationRecipientInfo {
  id: string;
  type: ParticipantType;
}

export interface KabobDynamicButton {
  title: string;
  action: () => void;
  posthogLabel: string;
}

export interface TermsOfServiceResult {
  termsOfServiceTextMessage: string | null;
  termsOfServiceAccepted: boolean;
}

export interface Media {
  mediaUrl: string;
  mediaContentType: string;
  mediaFileName: string;
  mediaLocationPath: string;
  fileSize?: number;
}

export function convertMediaJSONIntoMedia(media: string): Media | null | 'not-json' {
  if (!media) {
    return null;
  }
  try {
    const data = JSON.parse(media);
    return {
      mediaUrl: data.MediaUrl,
      mediaContentType: data.MediaContentType,
      mediaFileName: data.MediaFileName,
      mediaLocationPath: data.MediaLocationPath,
      fileSize: data.FileSize,
    };
  } catch {
    return 'not-json';
  }
}

export interface MessageInfo {
  text?: string;
  template?: {
    id: string;
    bodyParameters: {
      field: number;
      text: string;
    }[];
  };
  channel: ConversationChannel;
  attachments: MediaInterface[];
}

export interface ConversationAvailableChannels {
  availableChannels: ConversationChannel[];
  channelAvailabilities: ChannelAvailabilityInterface[];
  preferredChannel: ConversationChannel;
}

export interface BookingAvailabilityOption {
  displayText: string;
  scheduleId: string;
}

export interface EventInfo {
  id: string;
  icon?: string;
  svgIcon?: string;
  title: string;
  subtitle?: string;
  content: string;
  sentBy: string;
  sentVia: string;
  EventType: EventType;
  IsOutBound: boolean;
  originEventUrl?: string;
  emailId?: string;
  channel?: string;
  callFromNumber?: string;
  callToNumber?: string;
  callDuration?: number;
  callRecordingUrl?: string;
  isAIVoiceCall?: boolean;
  transcriptJSON?: string;
  callRecordId?: string;
}

export interface ConversationEvent {
  created: Date;
  type: EventType;
  channel: ConversationChannel;
  event: Event;
  eventInfo: EventInfo;
  isFirstItem: boolean;
}

export interface InboxItem {
  id: string;
  sender?: Participant;
  created?: Date;
  type: EventType | MessageType;
  channel: ConversationChannel;
  item: ConversationEvent | ConversationMessage;
  sentVia: string;
  itemFrom: string;
}

export interface EscalateToSupportPayload {
  conversationSummary: string;
}
