import { PaymentLinkItem } from '@vendasta/billing';
import {
  ConversationChannel,
  Participant,
  SubjectParticipant,
  SubjectParticipantInterface,
} from '@vendasta/conversation';
import { Observable } from 'rxjs';
import {
  ConversationDetail,
  ConversationMessage,
  ConversationTitleInfo,
  KabobDynamicButton,
  SendMessage,
} from './conversation.interface';
import { AppOptionsKeys } from '../inbox.constants';
import { AssociatedUserInterface } from '@vendasta/business-center/lib/_internal/interfaces/api.interface';
import { CreateContactState, InboxNamespace } from './inbox.interface';
import { RouteConfig } from './routes.interface';

export interface HostAppInterface {
  /**
   * get the title information of the conversation.
   * The title information includes the name, phone number of the customer or partner name (BCC)
   * or the account group name(PCC), address information and internal ID.
   * The title shows up in the side panel
   * @param {ConversationDetail} conversationDetail - the conversationDetail
   * @return {ConversationTitleInfo} - conversation title information
   */
  getConversationTitleInfo(conversationDetail: ConversationDetail): ConversationTitleInfo;

  /**
   * get current recipient of the conversation. The recipient's data will be show in the title of the conversation
   * of the sidepanel and the top bar of the chat.
   * @param {ConversationDetail} conversationDetail - the conversationDetail
   * @return {string} - A title
   */
  getRecipient(conversationDetail: ConversationDetail): Participant | Participant[] | null;

  /**
   * All messages that belong to the Organization show up on the right side the other messages show up on the left side.
   * Organization means all messages that the sender is an IAM user and belongs to the same Partner ID (PCC) or the same Account Group ID(PCC).
   * @param {ConversationMessage} message - the message
   * @return {boolean} - true if the message belongs to the organization
   */
  isSenderFromOrganization(message: ConversationMessage): boolean;

  /**
   * check if the conversation belongs to a partner or expert
   * @param {ConversationDetail} conversationDetail - A conversationDetail
   * @return {boolean} - a recipient participant
   */
  isYourExpert(conversationDetail: ConversationDetail): boolean;

  /**
   * check if the recipent belongs to a participant with internal information deleted
   * @param {ConversationDetail} conversationDetail - A conversationDetail
   * @return {boolean} - internal information deleted
   */
  isRecipientInternalInfoDeleted(conversationDetail: ConversationDetail): boolean;

  /**
   * build the SendMessage parameters used for creating a conversation
   * @param currentIAMParticipant - current IAM Participant information
   * @param participants - message participants list including the sender. It could be AG, Partner or/and Vendor
   * @param channel - the channel of the conversation (Reference value)
   */
  buildSendMessageParams(
    currentIAMParticipant: Participant,
    participants: SubjectParticipant[],
    channel: ConversationChannel,
  ): Observable<SendMessage>;

  /**
   * Redirects the user to an existing or a new conversation
   * based on the provided current IAM Participant information and provided subject Participant list.
   * @param currentIAMParticipant
   * @param subjectParticipants
   * @param channel - the channel of the conversation (Reference value)
   */
  redirectToInternalConversation(
    currentIAMParticipant: Participant,
    subjectParticipants: SubjectParticipantInterface[],
    channel: ConversationChannel,
  ): void;

  /**
   * Create payment link based on location.
   * In Partner Center, Partner -> SMB
   * In Business App, SMB -> Customers
   * @param conversationDetail
   * @param paymentLinkItems
   * @param due
   */
  createPaymentLink(
    conversationDetail: ConversationDetail,
    paymentLinkItems: PaymentLinkItem[],
    due: Date,
  ): Observable<string>;

  /**
   * Generate shortened link based on location.
   * @param link
   * @param accountGroupId
   */
  shortenLink(link: string, accountGroupId: string): Observable<string>;

  /**
   * Calculate the tax rate automatically for display.
   * @param conversationDetail
   * @param amount
   */
  calculatePaymentLinkTax(conversationDetail: ConversationDetail, amount: number): Observable<number>;

  /**
   * Get the dynamic button list for kabob menu, depending on the platform Location
   * @param conversationDetail
   */
  getKabobAvailableActions(conversationDetail: ConversationDetail): Observable<KabobDynamicButton[]>;

  // TODO: INB-766 Add a SendMessage method as described in
  // https://github.com/vendasta/galaxy/pull/10395 to prevent
  // creating a sender participant with accountGroupId
  // for partner messages.

  /**
   * Get the app options for inbox
   */
  getAppOptions(): { [key in AppOptionsKeys]?: boolean };

  /**
   * Get the users for the partner or account group
   * @param accountGroupId
   */
  getUsers(accountGroupId: string): Observable<AssociatedUserInterface[]>;

  /**
   * Get the namespace that was used to initialize Inbox
   */
  getNamespace(): Observable<InboxNamespace>;

  /**
   * Navigate to the create contact page with the given state
   * @param state
   * @param routes
   */
  navigateToCreateContact(state: CreateContactState, routes: RouteConfig): void;

  /**
   * View recipient contact information in the appropriate context (Partner Center or Business Center)
   * @param conversationDetail - Conversation Detail
   */
  viewContact(conversationDetail: ConversationDetail): void;
}
