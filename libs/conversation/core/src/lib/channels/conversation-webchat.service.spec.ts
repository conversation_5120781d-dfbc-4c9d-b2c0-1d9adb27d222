import { RegistrationStage } from '@galaxy/sms';
import {
  Conversation,
  ConversationApiService,
  ConversationChannel,
  Participant,
  ParticipantType,
} from '@vendasta/conversation';
import { of, take } from 'rxjs';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  CONVERSATION_COUNTRY_TOKEN,
  CONVERSATION_SMS_ENABLED_TOKEN,
  CONVERSATION_WEB_CHAT_ENABLED_TOKEN,
} from '../tokens';
import { inboxServiceMock } from '../mocks';
import { ConversationWebchatService } from './conversation-webchat.service';
import { InboxService } from '../inbox.service';
import { ConversationDetail } from '../interface/conversation.interface';
import { TestBed } from '@angular/core/testing';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { InboxAlertService } from './inbox-alert.service';

describe('ConversationWebChatService', () => {
  let svc: ConversationWebchatService;

  describe('ConversationWebchatService', () => {
    beforeEach(() => {
      const inboxSvc = inboxServiceMock;
      inboxSvc.SMSNumber$ = of('**********');
      TestBed.configureTestingModule({
        imports: [TranslateTestingModule.withTranslations({})],
        providers: [
          ConversationWebchatService,
          {
            provide: ConversationApiService,
            useValue: {},
          },
          {
            provide: InboxAlertService,
            useValue: {
              getConversationCustomerErrors: jest.fn().mockReturnValue({
                participant: {
                  participantId: 'test-id',
                  participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
                  phoneNumber: '**********',
                } as Participant,
              }),
              getSMSMessageEnabledError: jest.fn().mockReturnValue({ title: 'SMS Not Enabled' }),
              getSMSPhoneNumberError: jest.fn().mockReturnValue({ title: 'SMS Phone Number Error' }),
              getSMSRegistrationError: jest.fn().mockReturnValue({ title: 'Registration Required' }),
              checkNotAvailableErrorForLeads: jest.fn().mockReturnValue({ title: 'Lead Error' }),
            },
          },
          {
            provide: ACCOUNT_GROUP_ID_TOKEN,
            useValue: of('AG-123'),
          },
          { provide: CONVERSATION_COUNTRY_TOKEN, useValue: of('us') },
          { provide: InboxService, useValue: inboxSvc },
          { provide: CONVERSATION_WEB_CHAT_ENABLED_TOKEN, useValue: of(true) },
          { provide: CONVERSATION_SMS_ENABLED_TOKEN, useValue: of(true) },
        ],
      });
    });

    it('should be created', () => {
      svc = TestBed.inject(ConversationWebchatService);
      expect(svc).toBeTruthy();
    });

    it('should return customer errors when present in webchat conversation', (done) => {
      const mockParticipant = {
        participantId: 'test-id',
        participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
      } as Participant;
      const mockConversation = {
        participants: [mockParticipant],
      } as ConversationDetail;

      TestBed.overrideProvider(InboxAlertService, {
        useValue: {
          getConversationCustomerErrors: jest.fn().mockReturnValue({
            participant: mockParticipant,
            alert: { title: 'Customer Error' },
          }),
        },
      });

      svc = TestBed.inject(ConversationWebchatService);
      svc
        .getChatComposerAlert('partnerId', 'accountGroupId', mockConversation)
        .pipe(take(1))
        .subscribe((alertOptions) => {
          expect(alertOptions).not.toBeNull();
          expect(alertOptions?.title).toBe('Customer Error');
          expect(TestBed.inject(InboxAlertService).getConversationCustomerErrors).toHaveBeenCalledWith(
            mockConversation,
          );
          done();
        });
    });
    it('should return null when no customer/anonymous participant present in webchat conversation', (done) => {
      TestBed.overrideProvider(InboxAlertService, {
        useValue: {
          getConversationCustomerErrors: jest.fn().mockReturnValue({
            participant: null,
          }),
        },
      });

      svc = TestBed.inject(ConversationWebchatService);
      svc
        .getChatComposerAlert('partnerId', 'accountGroupId', {} as ConversationDetail)
        .pipe(take(1))
        .subscribe((alertOptions) => {
          expect(alertOptions).toBeNull();
          expect(TestBed.inject(InboxAlertService).getConversationCustomerErrors).toHaveBeenCalled();
          done();
        });
    });

    it('should return lead error when country is not available for SMS', (done) => {
      TestBed.overrideProvider(CONVERSATION_COUNTRY_TOKEN, { useValue: of('mx') });
      TestBed.overrideProvider(InboxService, {
        useValue: {
          SMSNumber$: of('**********'),
          canAccessSMS$: of(false),
          hasAccessByCountry: () => false,
        },
      });

      const convoMock = {
        conversation: {
          conversationId: 'mock-id',
        } as Conversation,
      } as ConversationDetail;

      svc = TestBed.inject(ConversationWebchatService);
      svc
        .getChatComposerAlert('partnerId', 'accountGroupId', convoMock)
        .pipe(take(1))
        .subscribe((alertOptions) => {
          expect(alertOptions).not.toBeNull();
          expect(alertOptions?.title).toBe('Lead Error');
          expect(TestBed.inject(InboxAlertService).checkNotAvailableErrorForLeads).toHaveBeenCalledWith(
            convoMock,
            'mx',
          );
          done();
        });
    });

    it('should return lead error when webchat is not enabled', (done) => {
      TestBed.overrideProvider(CONVERSATION_WEB_CHAT_ENABLED_TOKEN, { useValue: of(false) });
      TestBed.overrideProvider(InboxService, {
        useValue: {
          SMSNumber$: of('**********'),
          canAccessSMS$: of(true),
          hasAccessByCountry: () => true,
        },
      });

      const convoMock = {
        conversation: {
          conversationId: 'mock-id',
        } as Conversation,
      } as ConversationDetail;

      svc = TestBed.inject(ConversationWebchatService);

      TestBed.runInInjectionContext(() => {
        const smsRegistrationService = {
          registrationStage$: of(RegistrationStage.RegistrationComplete),
        };
        svc.smsRegistrationService = smsRegistrationService;
      });

      svc
        .getChatComposerAlert('partnerId', 'accountGroupId', convoMock)
        .pipe(take(1))
        .subscribe((alertOptions) => {
          expect(alertOptions).not.toBeNull();
          expect(alertOptions?.title).toBe('Lead Error');
          expect(TestBed.inject(InboxAlertService).checkNotAvailableErrorForLeads).toHaveBeenCalledWith(
            convoMock,
            'us',
          );
          done();
        });
    });

    it('should return lead error when customer phone number is not present', (done) => {
      TestBed.overrideProvider(CONVERSATION_WEB_CHAT_ENABLED_TOKEN, { useValue: of(true) });
      TestBed.overrideProvider(InboxService, {
        useValue: {
          SMSNumber$: of('**********'),
          canAccessSMS$: of(true),
          hasAccessByCountry: () => true,
        },
      });

      const mockParticipant = {
        participantId: 'test-id',
        participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
        email: '<EMAIL>',
      } as Participant;
      TestBed.overrideProvider(InboxAlertService, {
        useValue: {
          getConversationCustomerErrors: jest.fn().mockReturnValue({
            participant: mockParticipant,
          }),
          checkNotAvailableErrorForLeads: jest.fn().mockReturnValue({ title: 'Lead Error' }),
        },
      });

      const convoMock = {
        conversation: {
          conversationId: 'mock-id',
        } as Conversation,
      } as ConversationDetail;

      svc = TestBed.inject(ConversationWebchatService);

      TestBed.runInInjectionContext(() => {
        const smsRegistrationService = {
          registrationStage$: of(RegistrationStage.RegistrationComplete),
        };
        svc.smsRegistrationService = smsRegistrationService;
      });

      svc
        .getChatComposerAlert('partnerId', 'accountGroupId', convoMock)
        .pipe(take(1))
        .subscribe((alertOptions) => {
          expect(alertOptions).not.toBeNull();
          expect(alertOptions?.title).toBe('Lead Error');
          expect(TestBed.inject(InboxAlertService).checkNotAvailableErrorForLeads).toHaveBeenCalledWith(
            convoMock,
            'us',
          );
          done();
        });
    });

    it('should return sms not enabled error when sms is not enabled', (done) => {
      TestBed.overrideProvider(InboxService, {
        useValue: {
          SMSNumber$: of('**********'),
          canAccessSMS$: of(false),
          hasAccessByCountry: () => true,
        },
      });

      svc = TestBed.inject(ConversationWebchatService);
      const convoMock = {
        conversation: {
          conversationId: 'mock-id',
        } as Conversation,
      } as ConversationDetail;
      svc
        .getChatComposerAlert('partnerId', '', convoMock)
        .pipe(take(1))
        .subscribe((alertOptions) => {
          expect(alertOptions).not.toBeNull();
          expect(alertOptions?.title).toBe('SMS Not Enabled');
          expect(TestBed.inject(InboxAlertService).getSMSMessageEnabledError).toHaveBeenCalled();
          done();
        });
    });

    it('should return sms phone number error when business does not have a phone number', (done) => {
      TestBed.overrideProvider(InboxService, {
        useValue: {
          SMSNumber$: of(null),
          canAccessSMS$: of(true),
          hasAccessByCountry: () => true,
        },
      });

      svc = TestBed.inject(ConversationWebchatService);

      TestBed.runInInjectionContext(() => {
        const smsRegistrationService = {
          registrationStage$: of(RegistrationStage.RegistrationComplete),
        };
        svc.smsRegistrationService = smsRegistrationService;
      });

      const convoMock = {
        conversation: {
          conversationId: 'mock-id',
        } as Conversation,
      } as ConversationDetail;
      svc
        .getChatComposerAlert('partnerId', '', convoMock)
        .pipe(take(1))
        .subscribe((alertOptions) => {
          expect(alertOptions).not.toBeNull();
          expect(alertOptions?.title).toBe('SMS Phone Number Error');
          expect(TestBed.inject(InboxAlertService).getSMSPhoneNumberError).toHaveBeenCalled();
          done();
        });
    });

    it('should return null when every condition is met and business is not located in the US (A2P registration is not required)', (done) => {
      TestBed.overrideProvider(CONVERSATION_COUNTRY_TOKEN, { useValue: of('ca') });

      TestBed.overrideProvider(InboxService, {
        useValue: {
          SMSNumber$: of('**********'),
          canAccessSMS$: of(true),
          hasAccessByCountry: () => true,
        },
      });

      svc = TestBed.inject(ConversationWebchatService);

      TestBed.runInInjectionContext(() => {
        const smsRegistrationService = {
          registrationStage$: of(RegistrationStage.RegistrationComplete),
        };
        svc.smsRegistrationService = smsRegistrationService;
      });

      const convoMock = {
        conversation: {
          conversationId: 'mock-id',
        } as Conversation,
      } as ConversationDetail;
      svc
        .getChatComposerAlert('partnerId', '', convoMock)
        .pipe(take(1))
        .subscribe((alertOptions) => {
          expect(alertOptions).toBeNull();
          done();
        });
    });

    it('should return null when all conditions are met', (done) => {
      TestBed.overrideProvider(InboxService, {
        useValue: {
          SMSNumber$: of('**********'),
          canAccessSMS$: of(true),
          hasAccessByCountry: () => true,
        },
      });
      const conversationDetails = {
        conversation: {
          channel: ConversationChannel.CONVERSATION_CHANNEL_WEB_CHAT,
        },
        participants: [
          {
            participantType: ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
            internalParticipantId: 'CONTACT-123',
            phoneNumber: '**********',
            email: '<EMAIL>',
          },
        ],
      } as ConversationDetail;

      svc = TestBed.inject(ConversationWebchatService);

      TestBed.runInInjectionContext(() => {
        const smsRegistrationService = {
          registrationStage$: of(RegistrationStage.RegistrationComplete),
        };
        svc.smsRegistrationService = smsRegistrationService;
      });

      svc.getChatComposerAlert('partnerId', 'accountGroupId', conversationDetails).subscribe((alertOptions) => {
        expect(alertOptions).toBeNull();
        done();
      });
    });
  });
});
