import { ChannelAvailabilityInterface, ConversationChannel } from '@vendasta/conversation';
import { AlertOptions } from './conversation-channel.abstract';

// Whatsapp errors: https://developers.facebook.com/docs/whatsapp/cloud-api/support/error-codes/
export const WHATSAPP_ERROR_TRANSLATION_MAP: Record<string, string> = {
  '131026': 'INBOX.ERROR.MESSAGE_ERROR.UNABLE_TO_DELIVER',
  '131042': 'INBOX.ERROR.MESSAGE_ERROR.PAYMENT_ISSUE',
  '131045': 'INBOX.ERROR.MESSAGE_ERROR.PHONE_NUMBER_REGISTRATION',
  '131047': 'INBOX.ERROR.MESSAGE_ERROR.MORE_THAN_24_HOURS',
};

export function getErrorMessage(channel: ConversationChannel, code: string): string {
  if (channel === ConversationChannel.CONVERSATION_CHANNEL_WHATSAPP) {
    return WHATSAPP_ERROR_TRANSLATION_MAP[code] || '';
  }
  return '';
}

export function buildAlertOptionsFromAvailability(
  channel: ConversationChannel,
  availability: ChannelAvailabilityInterface[],
): AlertOptions | null {
  const channelAvailability = availability.find((channelAvailability) => channelAvailability.channel === channel);

  if (channelAvailability?.statuses && channelAvailability.statuses.length > 0) {
    return {
      title: channelAvailability.statuses?.[0]?.i18nKey,
    } as AlertOptions;
  }

  return null;
}
