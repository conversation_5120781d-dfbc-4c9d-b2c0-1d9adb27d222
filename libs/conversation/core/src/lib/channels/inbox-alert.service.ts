import { Injectable, inject } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { CountryCode, NumberFormat, formatNumber } from 'libphonenumber-js';
import { AlertOptions } from './conversation-channel.abstract';
import { GlobalParticipantType, Participant, ParticipantType } from '@vendasta/conversation';
import { getSubjectParticipantByType } from '../participant-utils';
import { ConversationDetail } from '../interface/conversation.interface';
import { Router } from '@angular/router';
import { CONVERSATION_ROUTES_TOKEN } from '../tokens';
import { toSignal } from '@angular/core/rxjs-interop';
import { CONVERSATION_HOST_APP_INTERFACE_TOKEN } from '../tokens';

@Injectable({
  providedIn: 'root',
})
export class InboxAlertService {
  private readonly translator = inject(TranslateService);
  private readonly router = inject(Router);
  private readonly routes = toSignal(inject(CONVERSATION_ROUTES_TOKEN));
  private readonly hostAppInterface = inject(CONVERSATION_HOST_APP_INTERFACE_TOKEN);

  // This method will return the customer/anonymous participant and an alert (if applicable) in case the contact info isn't valid to send a message
  getConversationCustomerErrors(conversationDetail: ConversationDetail): {
    participant: Participant | null;
    alert?: AlertOptions;
  } {
    const anonymousParticipant = getSubjectParticipantByType(
      conversationDetail?.participants,
      ParticipantType.PARTICIPANT_TYPE_ANONYMOUS,
    );
    if (anonymousParticipant) return { participant: anonymousParticipant, alert: this.getLeadNotCapturedError() };

    const customerParticipant = getSubjectParticipantByType(
      conversationDetail?.participants,
      ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
    );
    if (!customerParticipant) return { participant: null };

    if (customerParticipant.isParticipantInternalInfoDeleted) {
      return { participant: customerParticipant, alert: this.getParticipantInfoDeletedError() };
    }
    if (!customerParticipant.phoneNumber && !customerParticipant.email) {
      return {
        participant: customerParticipant,
        alert: this.getMissingContactInfoError(conversationDetail),
      };
    }
    return { participant: customerParticipant };
  }

  checkNotAvailableErrorForLeads(conversationDetail: ConversationDetail, country: string): AlertOptions | null {
    const customerParticipant = getSubjectParticipantByType(
      conversationDetail?.participants,
      ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
    );
    if (!customerParticipant) return null;
    const phoneNumber = customerParticipant?.phoneNumber;
    const email = customerParticipant?.email;

    if (!phoneNumber) {
      return {
        title: this.translator.instant('INBOX.CHAT.RESPOND_TO_LEAD_AT_EMAIL', {
          email: email,
        }),
      };
    }
    const countryCode = country.toUpperCase() as CountryCode;
    let phone = '';
    try {
      phone = formatNumber(phoneNumber, countryCode, 'NATIONAL' as NumberFormat);
    } catch (error) {
      // fallback to US if country code is not valid
      phone = formatNumber(phoneNumber, 'US' as CountryCode, 'NATIONAL' as NumberFormat);
    }
    if (email) {
      return {
        title: this.translator.instant('INBOX.CHAT.RESPOND_TO_LEAD_AT_PHONE_NUMBER_OR_EMAIL', {
          phone: phone,
          email: email,
        }),
      };
    }
    return {
      title: this.translator.instant('INBOX.CHAT.RESPOND_TO_LEAD_AT_PHONE_NUMBER', {
        phone: phone,
      }),
    };
  }

  getMissingContactInfoError(conversationDetail: ConversationDetail): AlertOptions {
    const subjectParticipants = conversationDetail.conversation.subjectParticipantKey?.subjectParticipants;
    if (!subjectParticipants?.length) return {};

    const customerParticipant = subjectParticipants.find(
      (participant) => participant.participantType === GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_CUSTOMER,
    );
    if (!customerParticipant) return {};

    const viewContactAction = {
      title: this.translator.instant('INBOX.SENDER.VIEW_CONTACT'),
      callback: () => this.viewContact(conversationDetail),
    };
    return {
      title: this.translator.instant('INBOX.ERROR.NO_PHONE_NUMBER_OR_EMAIL'),
      action: !this.routes()?.useModal && !!customerParticipant?.internalParticipantId ? viewContactAction : undefined,
    };
  }

  getWhatsAppTemplateErrorMissingContactFirstName(conversationDetail: ConversationDetail): AlertOptions {
    const customerParticipant = getSubjectParticipantByType(
      conversationDetail.participants,
      ParticipantType.PARTICIPANT_TYPE_CUSTOMER,
    );
    if (!customerParticipant) return {};

    const viewContactAction = {
      title: this.translator.instant('INBOX.SENDER.VIEW_CONTACT'),
      callback: () => this.viewContact(conversationDetail),
    };

    return {
      title: this.translator.instant('INBOX.ERROR.WHATSAPP_TEMPLATE.CONTACT_MUST_HAVE_NAME'),
      action: !this.routes()?.useModal && !!customerParticipant?.internalParticipantId ? viewContactAction : undefined,
    };
  }

  getParticipantInfoDeletedError(): AlertOptions {
    return {
      title: this.translator.instant('INBOX.COMPOSER_ALERTS.PARTICIPANT_INTERNAL_INFO_DELETED.TITLE'),
      description: this.translator.instant('INBOX.COMPOSER_ALERTS.PARTICIPANT_INTERNAL_INFO_DELETED.DESCRIPTION'),
    };
  }

  getLeadNotCapturedError(): AlertOptions {
    return {
      title: 'INBOX.WEBCHAT.SETTINGS.WEBCHAT_DISABLED_MISSING_CONTACT_INFO',
    };
  }

  getSMSMessageEnabledError(): AlertOptions {
    return {
      title: 'INBOX.SETTINGS.SMS_MESSAGES.NOT_ENABLED.TITLE',
      description: 'INBOX.SETTINGS.SMS_MESSAGES.NOT_ENABLED.DESCRIPTION',
    };
  }

  getSMSPhoneNumberError(): AlertOptions {
    return {
      title: 'INBOX.SETTINGS.SMS_MESSAGES.NO_PHONE_NUMBER.TITLE',
      description: 'INBOX.SETTINGS.SMS_MESSAGES.NO_PHONE_NUMBER.DESCRIPTION',
    };
  }

  getEmailOnlyAlertError(): AlertOptions {
    return {
      title: 'INBOX.SETTINGS.EMAIL.UNSUPPORTED_FOR_PARTNER_CENTER.TITLE',
      description: 'INBOX.SETTINGS.EMAIL.UNSUPPORTED_FOR_PARTNER_CENTER.DESCRIPTION',
    };
  }

  getSMSRegistrationError(accountGroupId: string): AlertOptions {
    return {
      title: 'INBOX.SETTINGS.SMS_MESSAGES.COMPOSER_A2P_REGISTRATION_REQUIRED.TITLE',
      description: 'INBOX.SETTINGS.SMS_MESSAGES.COMPOSER_A2P_REGISTRATION_REQUIRED.DESCRIPTION',
      action: {
        title: 'INBOX.SETTINGS.SMS_MESSAGES.COMPOSER_A2P_REGISTRATION_REQUIRED.ACTION_TITLE',
        callback: () => {
          if (this.routes()?.useModal) {
            this.router.navigate(['', { outlets: { inbox: 'inbox/settings/sms-registration' } }]);
          } else {
            this.router.navigate([`/account/location/${accountGroupId}/settings/inbox/sms-registration`]);
          }
        },
      },
    };
  }

  private async viewContact(conversationDetail: ConversationDetail): Promise<void> {
    this.hostAppInterface.viewContact(conversationDetail);
  }
}
