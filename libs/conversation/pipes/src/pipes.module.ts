import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import {
  AcceptedFileType,
  ConversationBadgePipe,
  ConversationRoutePipe,
  ConversationUnseenPipe,
  ConversationYourExpertPipe,
  MaxFileSize,
  UnreadConversationsCountPipe,
} from './conversation.pipe';
import { FirebaseTimestampsToDatePipe } from './firebase-timestamp-to-date';
import { InArrayPipe } from './in-array.pipe';
import {
  ChatProfileIconPipe,
  ChatSourceIconName,
  EmptyMessagesPipe,
  GetMessageMediaUrlPipe,
  IsFromYouPipe,
  IsSystemMessagePipe,
  MessageCodeBacktickPipe,
  ChatItemFromPipe,
  MessageStatusType,
  ReplaceNewLinePipe,
  ShowBookingAvailabilityPipe,
  ShowMessageButtonPipe,
  TruncateMessagePipePipe,
  ChatProfileSvgIconPipe,
} from './message.pipes';
import { TrimDescriptionPipe } from './ticket-pane.pipe';
import { MessageBubbleTime, MessageTime, TimestampsPipe } from './timestamp.pipe';
import { ViewIconPipe, ViewNamePipe } from './views.pipe';

const PIPES = [
  TimestampsPipe,
  MessageStatusType,
  ConversationRoutePipe,
  ConversationBadgePipe,
  ConversationUnseenPipe,
  ConversationYourExpertPipe,
  IsSystemMessagePipe,
  ChatItemFromPipe,
  ChatSourceIconName,
  MessageCodeBacktickPipe,
  TrimDescriptionPipe,
  FirebaseTimestampsToDatePipe,
  InArrayPipe,
  MessageTime,
  MessageBubbleTime,
  ViewIconPipe,
  ViewNamePipe,
  UnreadConversationsCountPipe,
  MaxFileSize,
  AcceptedFileType,
  GetMessageMediaUrlPipe,
  IsFromYouPipe,
  ChatProfileIconPipe,
  TruncateMessagePipePipe,
  ReplaceNewLinePipe,
  ShowBookingAvailabilityPipe,
  ShowMessageButtonPipe,
  EmptyMessagesPipe,
  ChatProfileSvgIconPipe,
];

@NgModule({
  imports: [CommonModule],
  declarations: [PIPES],
  exports: [PIPES],
})
export class ConversationPipesModule {}
