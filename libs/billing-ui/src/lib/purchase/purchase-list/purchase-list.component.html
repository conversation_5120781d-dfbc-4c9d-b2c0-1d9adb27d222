<va-filter2-model [filterGroup]="filterGroup" [isLoading]="dataSource.loading$ | async">
  <va-filter-toolbar-actions>
    <div class="toolbar-actions">
      <button
        mat-stroked-button
        [matTooltip]="'Export all purchases in this view to csv'"
        [disabled]="(exportLoading | async) === true"
        (click)="exportList()"
      >
        <mat-icon *ngIf="(exportLoading | async) === false as loading; else exportLoadingIndicator">
          cloud_download
        </mat-icon>
        Export as CSV
        <ng-template #exportLoadingIndicator>
          <div *ngIf="exportLoading | async" class="loading-button">
            <mat-progress-spinner [diameter]="24" mode="indeterminate"></mat-progress-spinner>
          </div>
        </ng-template>
      </button>
      <va-mat-table-column-sorter
        [(columns)]="displayColumns"
        [columnNames]="displayColumnsNames"
        saveName="PURCHASES-COLUMNS"
        iconName="view_column"
        buttonLabel="Columns"
      ></va-mat-table-column-sorter>
    </div>
  </va-filter-toolbar-actions>
  <va-filter-content>
    <va-mat-table>
      <table mat-table [dataSource]="dataSource" aria-label="Purchases">
        <ng-container matColumnDef="timestamp">
          <th mat-header-cell *matHeaderCellDef>Date</th>
          <td mat-cell *matCellDef="let purchase">
            <div>
              <div>{{ purchase.timestamp | date: 'mediumDate' : 'N/A' }}</div>
              <span>{{ purchase.timestamp | date: 'mediumTime' : 'N/A' }}</span>
            </div>
          </td>
        </ng-container>
        <ng-container matColumnDef="purchaseId">
          <th mat-header-cell *matHeaderCellDef>ID</th>
          <td mat-cell *matCellDef="let purchase">
            <div class="purchase-id-container">
              <div>{{ purchase.purchaseId }}</div>
              <div class="badges">
                <glxy-badge [color]="'blue-solid'" [size]="'small'" *ngIf="purchase.renewal">Renewal</glxy-badge>
                <glxy-badge [color]="'grey-solid'" [size]="'small'" *ngIf="isVoided(purchase)">Voided</glxy-badge>
                <glxy-badge [color]="'green-solid'" [size]="'small'" *ngIf="hasDiscount(purchase)">Discount</glxy-badge>
              </div>
            </div>
          </td>
        </ng-container>
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef>Status</th>
          <td mat-cell *matCellDef="let purchase">
            {{ purchase.status | toString: purchaseStatusToDisplay }}
          </td>
        </ng-container>
        <ng-container matColumnDef="billingStrategy">
          <th mat-header-cell *matHeaderCellDef>Billing Strategy</th>
          <td mat-cell *matCellDef="let purchase">
            {{ purchase.billingStrategy | toString: billingStrategyToDisplay }}
          </td>
        </ng-container>
        <ng-container matColumnDef="dunningAttempt">
          <th mat-header-cell *matHeaderCellDef>Dunning Step</th>
          <td mat-cell *matCellDef="let purchase">
            {{ purchase.dunningAttempt }}
          </td>
        </ng-container>
        <ng-container matColumnDef="pauseDunningUntil">
          <th mat-header-cell *matHeaderCellDef>Dunning Paused Until</th>
          <td mat-cell *matCellDef="let purchase">
            <ng-container *ngIf="purchase | isDunningPaused">
              <div>
                <div>{{ purchase.pauseDunningUntil | date: 'mediumDate' : 'N/A' }}</div>
              </div>
            </ng-container>
          </td>
        </ng-container>
        <ng-container matColumnDef="numberOfItems">
          <th mat-header-cell *matHeaderCellDef class="product-data">Products</th>
          <td mat-cell *matCellDef="let purchase" class="product-data">
            <ng-container *ngIf="purchase.items.length > 1">{{ purchase.items.length }} products</ng-container>
            <ng-container *ngIf="purchase.items.length === 1 && getPurchaseItemRows(purchase) | async as purchaseItems">
              {{ purchaseItems[0].name }}
            </ng-container>
          </td>
        </ng-container>
        <ng-container matColumnDef="total">
          <th mat-header-cell *matHeaderCellDef class="number-data" billingUiPriceColumnStyle>Total</th>
          <td mat-cell *matCellDef="let purchase" class="number-data" billingUiPriceColumnStyle>
            <mat-icon *ngIf="promotionalCreditAmount(purchase)">card_giftcard</mat-icon>
            <span
              [matTooltip]="formatPaymentTotalTooltip(purchase)"
              [matTooltipDisabled]="purchase.creditAmount === 0"
              [matTooltipPosition]="'above'"
              [ngClass]="{ 'split-payment': purchase.creditAmount > 0 }"
            >
              {{ purchase.total | price: purchase.currency : null : true : false : true }}
            </span>
          </td>
        </ng-container>
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let purchase">
            <mat-menu #menu="matMenu">
              <button mat-menu-item (click)="openRetryDialog(purchase)" [disabled]="purchase.paid || purchase.voided">
                <mat-icon>attach_money</mat-icon>
                <span>Retry</span>
              </button>
              <button
                *ngIf="displayForAdmin"
                mat-menu-item
                (click)="openVoidDialog(purchase)"
                [disabled]="!(purchase | canVoidPurchase)"
              >
                <mat-icon>remove_circle</mat-icon>
                <span>Void</span>
              </button>
              <ng-container *ngIf="displayForAdmin">
                <button *ngIf="purchase.canPauseDunning" mat-menu-item (click)="openDunningDialog(purchase, 'pause')">
                  <mat-icon>pause_circle</mat-icon>
                  <span>Pause Dunning</span>
                </button>
                <button *ngIf="purchase.canResumeDunning" mat-menu-item (click)="openDunningDialog(purchase, 'resume')">
                  <mat-icon>play_circle</mat-icon>
                  <span>Resume Dunning</span>
                </button>
              </ng-container>
            </mat-menu>
            <button mat-icon-button [matMenuTriggerFor]="menu" (click)="$event.stopPropagation()">
              <mat-icon>more_vert</mat-icon>
            </button>
          </td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayColumns"></tr>
        <tr
          mat-row
          *matRowDef="let row; columns: displayColumns"
          [cdkDetailRow]="row"
          [cdkDetailRowTpl]="tpl"
          class="hover-row"
        ></tr>
      </table>
      <ng-container *ngIf="(dataSource.loading$ | async) === false && (dataSource.empty$ | async)">
        <div class="empty-state">
          <mat-icon>description</mat-icon>
          <p>No purchases.</p>
        </div>
      </ng-container>
      <mat-paginator
        #purchasePaginator
        [length]="length$ | async"
        [pageIndex]="0"
        [pageSize]="25"
        [pageSizeOptions]="[25, 50, 100, 250]"
      ></mat-paginator>

      <ng-template #tpl let-purchase>
        <ng-template #loadingPurchaseItems>
          <td colspan="100%">
            <mat-progress-bar mode="indeterminate"></mat-progress-bar>
          </td>
        </ng-template>
        <tr
          class="mat-row expanded-row"
          [@detailExpand]
          *ngIf="getPurchaseItemRows(purchase) | async as purchaseItems; else loadingPurchaseItems"
        >
          <td colspan="100%">
            <billing-ui-purchase-item-list
              class="transaction-list"
              [purchaseItems]="purchaseItems"
              [hasMore]="false"
              [loading]="false"
              [currency]="purchase.currency"
              [showLinkToAccount]="!displayForAdmin"
              [displayedColumns]="purchaseItemsDisplayColumns"
              (accountSelected)="navigateToAccount($event)"
            ></billing-ui-purchase-item-list>
            <div *ngIf="isInstant(purchase) && purchase.paid" class="payment-summary">
              <div class="payment-items">
                <div *ngIf="creditCardPaymentAmount(purchase) as ccPaymentAmount" class="row">
                  <div class="col col-md-8">Card payment</div>
                  <div class="col col-md-4">
                    {{ ccPaymentAmount / 100 | currency: purchase.currency : 'symbol' }}
                  </div>
                </div>
                <div *ngIf="creditAmount(purchase) as creditNoteAmount" class="row">
                  <div class="col col-md-8">Credit</div>
                  <div class="col col-md-4">
                    {{ creditNoteAmount / 100 | currency: purchase.currency : 'symbol' }}
                  </div>
                </div>
                <div *ngIf="promotionalCreditAmount(purchase) as vcashAmount" class="row">
                  <div class="col col-md-8 vcash">
                    <div><mat-icon>card_giftcard</mat-icon></div>
                    <div>&nbsp;vCash</div>
                  </div>
                  <div class="col col-md-4">
                    {{ vcashAmount / 100 | currency: purchase.currency : 'symbol' }}
                  </div>
                </div>
                <div class="row">
                  <div class="col col-md-8"></div>
                  <div class="col col-md-4">
                    <mat-divider></mat-divider>
                    <b>
                      {{ purchase.total | price: purchase.currency : null : true : false : true }}
                    </b>
                  </div>
                </div>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>
    </va-mat-table>
  </va-filter-content>
</va-filter2-model>
