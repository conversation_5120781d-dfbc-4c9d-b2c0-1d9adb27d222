<table mat-table [dataSource]="purchaseItems" *ngIf="purchaseItems?.length > 0">
  <ng-container matColumnDef="name">
    <th mat-header-cell *matHeaderCellDef matRipple (click)="headerClick()">Name</th>
    <td mat-cell *matCellDef="let row">{{ row.name }}</td>
  </ng-container>
  <ng-container matColumnDef="sku">
    <th mat-header-cell *matHeaderCellDef>Sku</th>
    <td mat-cell *matCellDef="let row">{{ row.sku }}</td>
  </ng-container>
  <ng-container matColumnDef="account">
    <th mat-header-cell *matHeaderCellDef>Account</th>
    <td mat-cell *matCellDef="let row">
      <ng-container *ngIf="shouldLinkToAccount(row); else noLinkAccountId">
        <a (click)="accountSelected.emit(row)" matTooltip="View Account Details">
          {{ row.companyName }}
        </a>
      </ng-container>
      <ng-template #noLinkAccountId>
        {{ row.companyName }}
      </ng-template>
    </td>
  </ng-container>
  <ng-container matColumnDef="day">
    <th mat-header-cell *matHeaderCellDef>Day</th>
    <td mat-cell *matCellDef="let row">
      <ng-container>
        <a (click)="dateSelected.emit(row)" matTooltip="View Purchase">
          {{ purchaseDayToDateString(row.purchaseDay) }}
        </a>
      </ng-container>
    </td>
  </ng-container>
  <ng-container matColumnDef="amount">
    <th mat-header-cell class="currency-header" *matHeaderCellDef>Amount</th>
    <td mat-cell class="currency-data" *matCellDef="let row">
      {{ row.amount | price: getCurrency(row) : null : true : false : true }}
    </td>
  </ng-container>
  <ng-container matColumnDef="discount">
    <th mat-header-cell class="currency-header" *matHeaderCellDef>Discount</th>
    <td mat-cell class="currency-data" *matCellDef="let row">
      {{ row.discountAmount | price: getCurrency(row) : null : true : false : true }}
    </td>
  </ng-container>
  <ng-container matColumnDef="setupFee">
    <th mat-header-cell class="currency-header" *matHeaderCellDef>Setup Fee</th>
    <td mat-cell class="currency-data" *matCellDef="let row">
      {{ row.setupFee | price: getCurrency(row) : null : true : false : true }}
    </td>
  </ng-container>
  <ng-container matColumnDef="managementFee">
    <th mat-header-cell class="currency-header" *matHeaderCellDef>Management Fee</th>
    <td mat-cell class="currency-data" *matCellDef="let row">
      {{ row.feeAmount | price: getCurrency(row) : null : true : false : true }}
    </td>
  </ng-container>
  <ng-container matColumnDef="tax">
    <th mat-header-cell class="currency-header" *matHeaderCellDef>Tax</th>
    <td mat-cell class="currency-data" *matCellDef="let row">
      <span [matTooltip]="formatAppliedTaxForDisplay(row, 'rate')" [matTooltipPosition]="'above'">
        {{ formatAppliedTaxForDisplay(row, 'amount') }}
      </span>
    </td>
  </ng-container>
  <ng-container matColumnDef="total">
    <th mat-header-cell class="currency-header" *matHeaderCellDef>Total</th>
    <td mat-cell class="currency-data" *matCellDef="let row">
      {{ row.total | price: getCurrency(row) : null : true : false : true }}
    </td>
  </ng-container>
  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
</table>
@if (loading) {
  <mat-progress-bar mode="indeterminate"></mat-progress-bar>
} @else if (hasMore === false && (!purchaseItems || purchaseItems.length < 1)) {
  <div class="no-purchases">
    <p>No purchases available.</p>
  </div>
}

<!-- Loader for infinite scroll -->
@if (hasMore) {
  <glxy-infinite-scroll-trigger
    [visiblilityMargin]="20"
    [minHeight]="0"
    (isVisible)="loadMore()"
  ></glxy-infinite-scroll-trigger>
}
