<va-mat-table>
  <table mat-table class="refund-table" [dataSource]="dataSource" aria-label="Refunds">
    <!-- Created Date Column -->
    <ng-container matColumnDef="created">
      <th mat-header-cell *matHeaderCellDef>Created Date</th>
      <td mat-cell *matCellDef="let row">
        <div>
          {{ row.created | date : 'mediumDate' }}
        </div>
        <div>
          {{ row.created | date : 'mediumTime' }}
        </div>
      </td>
    </ng-container>

    <!-- Reference ID Column -->
    <ng-container matColumnDef="referenceId">
      <th mat-header-cell *matHeaderCellDef>Reference ID</th>
      <td mat-cell *matCellDef="let row">
        <ng-container *ngIf="row.referenceType === invoiceType; else purchaseId">
          <a (click)="navigateToInvoice(row.referenceId)">
            {{ row.referenceId }}
          </a>
        </ng-container>
        <ng-template #purchaseId>
          <a
            *ngIf="row.referenceType === purchaseType; else baseReferenceId"
            (click)="navigateToPurchase(row.referenceId)"
          >
            {{ row.referenceId }}
          </a>
        </ng-template>
        <ng-template #baseReferenceId>
          {{ row.referenceId }}
        </ng-template>
      </td>
    </ng-container>

    <!-- Reason Column -->
    <ng-container matColumnDef="reason">
      <th mat-header-cell *matHeaderCellDef>Reason</th>
      <td mat-cell *matCellDef="let row">
        {{ row.reason }}
      </td>
    </ng-container>

    <!-- Type Column -->
    <ng-container matColumnDef="referenceType">
      <th mat-header-cell *matHeaderCellDef>Type</th>
      <td mat-cell *matCellDef="let row">
        {{ row.referenceType }}
      </td>
    </ng-container>

    <!-- Status Column -->
    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef>Status</th>
      <td mat-cell *matCellDef="let row">
        {{ row.status }}
      </td>
    </ng-container>

    <!-- Created Charge ID Column -->
    <ng-container *ngIf="displayForAdmin" matColumnDef="chargeId">
      <th mat-header-cell *matHeaderCellDef>Charge ID</th>
      <td mat-cell *matCellDef="let row">
        <a [href]="getChargeDetailsUrl(row.chargeId)" target="_blank">
          {{ row.chargeId }}
        </a>
      </td>
    </ng-container>

    <!-- Amount Column -->
    <ng-container matColumnDef="amount">
      <th mat-header-cell *matHeaderCellDef billingUiPriceColumnStyle>Amount</th>
      <td mat-cell *matCellDef="let row" billingUiPriceColumnStyle>
        {{ row.amount / 100 || 0 | currency : row.currency : 'symbol' : '1.2-2' }}
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>
  <mat-paginator
    #refundPaginator
    [length]="length$ | async"
    [pageIndex]="0"
    [pageSize]="25"
    [pageSizeOptions]="[25, 50, 100]"
  ></mat-paginator>
</va-mat-table>
