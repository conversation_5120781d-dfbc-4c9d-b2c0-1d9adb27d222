<va-filter2 name="invoice-list" [formGroup]="filterGroup" [isLoading]="dataSource.loading$ | async">
  <va-filter-toolbar-actions>
    <va-mat-table-column-sorter
      [(columns)]="displayColumns"
      [columnNames]="displayColumnsNames"
      saveName="SALES-INVOICE-TABLE-COLUMNS"
      iconName="view_column"
      buttonLabel="Columns"
    ></va-mat-table-column-sorter>
  </va-filter-toolbar-actions>
  <va-filter-sections>
    <va-filter-section>
      <va-filter-field>
        <mat-form-field>
          <input matInput placeholder="Invoice #" value="" formControlName="invoiceNumber" />
        </mat-form-field>
      </va-filter-field>
      <va-filter-field>
        <mat-form-field>
          <mat-select placeholder="Payment Status" formControlName="paymentStatus">
            <mat-option *ngFor="let status of paymentStatuses" [value]="status.value">
              {{ status.display }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </va-filter-field>
    </va-filter-section>
  </va-filter-sections>
  <va-filter-content>
    <va-mat-table>
      <table mat-table class="full-width-table" [dataSource]="dataSource" aria-label="Sales Invoices">
        <!-- Invoice Number Column -->
        <ng-container matColumnDef="invoiceNumber">
          <th mat-header-cell *matHeaderCellDef>Invoice #</th>
          <td mat-cell *matCellDef="let row">
            <ng-container *ngIf="displayForAdmin; else baseInvoiceId">
              <a [href]="getSalesInvoiceDetails(row.invoiceId)" target="_blank">
                {{ row.invoiceNumber }}
              </a>
            </ng-container>
            <ng-template #baseInvoiceId>
              {{ row.invoiceNumber }}
            </ng-template>
          </td>
        </ng-container>

        <!-- Account Column -->
        <ng-container matColumnDef="account">
          <th mat-header-cell *matHeaderCellDef>Account</th>
          <td mat-cell *matCellDef="let row">{{ row.merchantId }}</td>
        </ng-container>

        <!-- Id Column -->
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef>ID</th>
          <td mat-cell *matCellDef="let row">{{ row.id }}</td>
        </ng-container>

        <!-- Id Column -->
        <ng-container matColumnDef="externalId">
          <th mat-header-cell *matHeaderCellDef>External ID</th>
          <td mat-cell *matCellDef="let row">{{ row.externalId }}</td>
        </ng-container>

        <!-- Period Column -->
        <ng-container matColumnDef="period">
          <th mat-header-cell *matHeaderCellDef>Period</th>
          <td mat-cell *matCellDef="let row">
            {{ formatPeriod(row.startDate, row.endDate) }}
          </td>
        </ng-container>

        <!-- Invoice Type Column -->
        <ng-container matColumnDef="type">
          <th mat-header-cell *matHeaderCellDef>Type</th>
          <td mat-cell *matCellDef="let row">{{ formatType(row.type) }}</td>
        </ng-container>

        <!-- Payment Status Column -->
        <ng-container matColumnDef="paymentStatus">
          <th mat-header-cell *matHeaderCellDef>Payment Status</th>
          <td mat-cell *matCellDef="let row">{{ row.paymentStatus }}</td>
        </ng-container>

        <!-- Invoice Date Column -->
        <ng-container matColumnDef="invoiceDate">
          <th mat-header-cell *matHeaderCellDef>Invoice Date</th>
          <td mat-cell *matCellDef="let row">
            {{ row.invoiceDate | date : 'mediumDate' }}
          </td>
        </ng-container>

        <!-- Outstanding Column -->
        <ng-container matColumnDef="outstanding">
          <th mat-header-cell *matHeaderCellDef class="number-data">Outstanding</th>
          <td mat-cell *matCellDef="let row" class="number-data">
            {{ row.outstanding / 100 || 0 | currency : row.currency : 'symbol' : '1.2-2' }}
          </td>
        </ng-container>

        <!-- Total Column -->
        <ng-container matColumnDef="total">
          <th mat-header-cell *matHeaderCellDef class="number-data">Total</th>
          <td mat-cell *matCellDef="let row" class="number-data">
            {{ row.total / 100 || 0 | currency : row.currency : 'symbol' : '1.2-2' }}
          </td>
        </ng-container>

        <!-- Due Date Column -->
        <ng-container matColumnDef="dueDate">
          <th mat-header-cell *matHeaderCellDef>
            Due Date
            <mat-icon
              [inline]="true"
              color="primary"
              matTooltip="Outstanding amounts may be charged as early as 12:00 UTC on the invoice due date. The charge may appear to be on a different day in your local time zone."
            >
              info
            </mat-icon>
          </th>
          <td mat-cell *matCellDef="let row">
            {{ row.dueDate | date : 'mediumDate' }}
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let row">
            <div class="flex-row">
              <button mat-icon-button [matMenuTriggerFor]="menu" (click)="$event.stopPropagation()">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #menu="matMenu">
                <button mat-menu-item (click)="download(row)" [disabled]="isPDFLoading$(row.invoiceId) | async">
                  <mat-icon>file_download</mat-icon>
                  <span>Download PDF</span>
                </button>
                <div
                  matTooltip="Not opening? Try disabling ad-block in your browser."
                  [matTooltipDisabled]="viewPDFClicks.get(row.invoiceId) ? viewPDFClicks.get(row.invoiceId) < 2 : true"
                >
                  <button mat-menu-item (click)="view(row)" [disabled]="isPDFLoading$(row.invoiceId) | async">
                    <mat-icon>visibility</mat-icon>
                    <span>View PDF</span>
                  </button>
                </div>
                <div *ngIf="displayForAdmin">
                  <button mat-menu-item (click)="chargeSalesInvoice(row)">
                    <mat-icon>attach_money</mat-icon>
                    <span>Charge Invoice</span>
                  </button>
                  <button
                    mat-menu-item
                    (click)="sendSalesInvoiceEmail(row)"
                    [disabled]="isEmailLoading$(row.invoiceId) | async"
                  >
                    <mat-icon>mail_outline</mat-icon>
                    <span>Send Invoice Email</span>
                  </button>
                  <button
                    mat-menu-item
                    (click)="openSendSalesInvoiceReceiptEmailDialog(row)"
                    [disabled]="isEmailLoading$(row.invoiceId) | async"
                  >
                    <mat-icon>mail_outline</mat-icon>
                    <span>Send Invoice Receipt Email</span>
                  </button>
                </div>
              </mat-menu>
              <div class="working">
                <mat-spinner *ngIf="isRowWorking$(row.invoiceId) | async" [diameter]="30"></mat-spinner>
              </div>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayColumns"></tr>
      </table>
      <ng-container *ngIf="(dataSource.loading$ | async) === false && (dataSource.empty$ | async)">
        <div class="empty-state">
          <mat-icon>description</mat-icon>
          <p>No sales invoices.</p>
        </div>
      </ng-container>

      <mat-paginator
        #paginator
        [length]="length$ | async"
        [pageIndex]="0"
        [pageSize]="25"
        [pageSizeOptions]="[25, 50, 100, 250]"
      ></mat-paginator>
    </va-mat-table>
  </va-filter-content>
</va-filter2>
<a #downloadAnchor [hidden]="true"></a>
