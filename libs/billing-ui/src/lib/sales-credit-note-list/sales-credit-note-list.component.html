<va-mat-table>
  <ng-container action-bar-items>
    <va-mat-table-column-sorter
      [(columns)]="displayColumns"
      [columnNames]="displayColumnsNames"
      saveName="SALES-CREDIT-NOTE-TABLE-COLUMNS"
      iconName="view_column"
      buttonLabel="Columns"
    ></va-mat-table-column-sorter>
  </ng-container>
  <table
    mat-table
    class="full-width-table"
    [dataSource]="dataSource"
    aria-label="Sales Credit Notes"
    *ngIf="(dataSource.loading$$ | async) || (dataSource.empty$$ | async) === false; else emptyState"
  >
    <!-- Credit Note Number Column -->
    <ng-container matColumnDef="creditNoteNumber">
      <th mat-header-cell *matHeaderCellDef>Credit Note Number</th>
      <td mat-cell *matCellDef="let row">
        <ng-container *ngIf="displayForAdmin; else baseCreditNoteId">
          <a [href]="getSalesCreditNoteDetails(row.creditNoteId)" target="_blank">
            {{ row.creditNoteNumber }}
          </a>
        </ng-container>
        <ng-template #baseCreditNoteId>
          {{ row.creditNoteNumber }}
        </ng-template>
      </td>
    </ng-container>

    <!-- Account Column -->
    <ng-container matColumnDef="account">
      <th mat-header-cell *matHeaderCellDef>Account</th>
      <td mat-cell *matCellDef="let row">{{ row.merchantId }}</td>
    </ng-container>

    <!-- Credit Note Id Column -->
    <ng-container matColumnDef="creditNoteId">
      <th mat-header-cell *matHeaderCellDef>Credit Note ID</th>
      <td mat-cell *matCellDef="let row">{{ row.creditNoteId }}</td>
    </ng-container>

    <!-- External Id Column -->
    <ng-container matColumnDef="externalId">
      <th mat-header-cell *matHeaderCellDef>External ID</th>
      <td mat-cell *matCellDef="let row">{{ row.externalId }}</td>
    </ng-container>

    <!-- Credit Note Date Column -->
    <ng-container matColumnDef="creditNoteDate">
      <th mat-header-cell *matHeaderCellDef>Credit Note Date</th>
      <td mat-cell *matCellDef="let row">
        {{ row.creditNoteDate | date : 'mediumDate' }}
      </td>
    </ng-container>

    <!-- Due Date Column -->
    <ng-container matColumnDef="dueDate">
      <th mat-header-cell *matHeaderCellDef>Due Date</th>
      <td mat-cell *matCellDef="let row">
        {{ row.dueDate | date : 'mediumDate' }}
      </td>
    </ng-container>

    <!-- Payment Status Column -->
    <ng-container matColumnDef="paymentStatus">
      <th mat-header-cell *matHeaderCellDef>Payment Status</th>
      <td mat-cell *matCellDef="let row">{{ row.paymentStatus }}</td>
    </ng-container>

    <!-- Invoice Column -->
    <ng-container matColumnDef="invoiceNumber">
      <th mat-header-cell *matHeaderCellDef>Invoice Number</th>
      <td mat-cell *matCellDef="let row">{{ row.invoiceNumber }}</td>
    </ng-container>

    <!-- Reason Column -->
    <ng-container matColumnDef="creditNoteReason">
      <th mat-header-cell *matHeaderCellDef>Credit Note Reason</th>
      <td mat-cell *matCellDef="let row">{{ row.creditNoteReason }}</td>
    </ng-container>

    <!-- Total Column -->
    <ng-container matColumnDef="total">
      <th mat-header-cell *matHeaderCellDef class="number-data">Total</th>
      <td mat-cell *matCellDef="let row" class="number-data">
        {{ row.total / 100 || 0 | currency : row.currency : 'symbol' : '1.2-2' }}
      </td>
    </ng-container>

    <!-- Outstanding Column -->
    <ng-container matColumnDef="outstanding">
      <th mat-header-cell *matHeaderCellDef class="number-data">Outstanding</th>
      <td mat-cell *matCellDef="let row" class="number-data">
        {{ row.outstanding / 100 || 0 | currency : row.currency : 'symbol' : '1.2-2' }}
      </td>
    </ng-container>

    <!-- Actions Column -->
    <ng-container matColumnDef="actions">
      <th mat-header-cell *matHeaderCellDef>Actions</th>
      <td mat-cell *matCellDef="let row">
        <div class="flex-row">
          <button mat-icon-button [matMenuTriggerFor]="menu" (click)="$event.stopPropagation()">
            <mat-icon>more_vert</mat-icon>
          </button>
          <mat-menu #menu="matMenu">
            <button mat-menu-item (click)="download(row)" [disabled]="isRowWorking$(row.creditNoteId) | async">
              <mat-icon>file_download</mat-icon>
              <span>Download PDF</span>
            </button>
            <button mat-menu-item (click)="view(row)" [disabled]="isRowWorking$(row.creditNoteId) | async">
              <mat-icon>visibility</mat-icon>
              <span>View PDF</span>
            </button>
          </mat-menu>
          <div class="working">
            <mat-spinner *ngIf="isRowWorking$(row.creditNoteId) | async" [diameter]="30"></mat-spinner>
          </div>
        </div>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayColumns"></tr>
  </table>
  <mat-progress-bar *ngIf="dataSource.loading$$ | async" mode="indeterminate"></mat-progress-bar>

  <mat-paginator
    #paginator
    [length]="dataSource.length$ | async"
    [pageIndex]="0"
    [pageSize]="25"
    [pageSizeOptions]="[25, 50, 100, 250]"
  ></mat-paginator>
</va-mat-table>

<a #downloadAnchor [hidden]="true"></a>
<ng-template #emptyState>
  <div class="empty-state">
    <mat-icon>description</mat-icon>
    <p>No sales credit notes.</p>
  </div>
</ng-template>
