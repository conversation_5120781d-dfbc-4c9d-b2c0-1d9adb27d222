<h2 mat-dialog-title class="refund-title">
  {{ 'REFUND_DIALOG.TITLE' | translate }}
</h2>
<mat-dialog-content>
  <form [formGroup]="form">
    <p class="charge">
      {{ 'REFUND_DIALOG.ORIGINAL_CHARGE' | translate }}
      {{ data.total / 100 || 0 | currency: data.currency:'symbol':'1.2-2' }}
    </p>
    <glxy-form-field [prefixText]="currencySymbol">
      <glxy-label>{{ 'REFUND_DIALOG.AMOUNT' | translate }}</glxy-label>
      <input type="number" placeholder="{{ 'REFUND_DIALOG.AMOUNT' | translate }}" matInput formControlName="amount" />
      <glxy-error
        *ngIf="
          form.controls?.amount?.hasError('required') || form.controls?.amount?.hasError('isLessThanOrEqualToZero')
        "
      >
        {{ 'REFUND_DIALOG.REFUND_MUST_BE_GREATER_THAN_ZERO' | translate }}:&nbsp;{{
          0 | currency: data.currency:'symbol':'1.2-2'
        }}
      </glxy-error>
      <glxy-error *ngIf="form.controls?.amount?.hasError('max')">
        {{ 'REFUND_DIALOG.REFUND_CANNOT_EXCEED_AMOUNT' | translate }}:&nbsp;{{
          data.total / 100 | currency: data.currency:'symbol':'1.2-2'
        }}
      </glxy-error>
    </glxy-form-field>
    <glxy-form-field>
      <glxy-label>{{ 'REFUND_DIALOG.REASON_LABEL' | translate }}</glxy-label>
      <glxy-label-hint>
        {{ 'REFUND_DIALOG.REASON_LABEL_HINT' | translate }}
      </glxy-label-hint>
      <mat-select
        placeholder="{{ 'REFUND_DIALOG.SELECT_REASON' | translate }}"
        [formControl]="form.controls?.reason"
        required
      >
        <mat-option *ngFor="let l of refundLabels" [value]="l.value">
          {{ l.value }}
        </mat-option>
      </mat-select>
      <glxy-error *ngIf="form.controls?.reason?.hasError('required')">
        {{ 'REFUND_DIALOG.REASON_REQUIRED' | translate }}
      </glxy-error>
    </glxy-form-field>
    <glxy-form-field>
      <glxy-label>{{ 'REFUND_DIALOG.NOTES_LABEL' | translate }}</glxy-label>
      <glxy-label-hint>
        {{ 'REFUND_DIALOG.NOTES_LABEL_HINT' | translate }}
      </glxy-label-hint>
      <textarea
        matInput
        matTextareaAutosize
        placeholder="{{ 'REFUND_DIALOG.DETAILED_NOTES' | translate }}"
        [formControl]="form.controls?.note"
      ></textarea>
    </glxy-form-field>
  </form>
</mat-dialog-content>
<mat-dialog-actions class="dialog-actions" align="end">
  <button mat-button color="primary" mat-dialog-close>{{ 'REFUND_DIALOG.CANCEL' | translate }}</button>
  <button mat-raised-button color="primary" (click)="issueRefund()" [disabled]="form.invalid">
    {{ 'REFUND_DIALOG.CONFIRM' | translate }}
  </button>
</mat-dialog-actions>
