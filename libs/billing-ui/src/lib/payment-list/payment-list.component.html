<va-filter2-model [filterGroup]="filterGroup" [isLoading]="dataSource.loading$ | async">
  <va-filter-toolbar-actions>
    <div class="flex">
      <va-mat-table-column-sorter
        [(columns)]="displayColumns"
        [columnNames]="displayColumnsNames"
        saveName="PAYMENT-LIST-COLUMNS"
        iconName="view_column"
        buttonLabel="Columns"
      ></va-mat-table-column-sorter>
    </div>
  </va-filter-toolbar-actions>
  <va-filter-content>
    <va-mat-table>
      <table mat-table class="payment-table" [dataSource]="dataSource" aria-label="Payments">
        <!-- Created Date Column -->
        <ng-container matColumnDef="created">
          <th mat-header-cell *matHeaderCellDef>Created Date</th>
          <td mat-cell *matCellDef="let row">
            <div>
              {{ row.created | date : 'mediumDate' }}
            </div>
            <div>
              {{ row.created | date : 'mediumTime' }}
            </div>
          </td>
        </ng-container>

        <!-- Type Column -->
        <ng-container matColumnDef="type">
          <th mat-header-cell *matHeaderCellDef>Type</th>
          <td mat-cell *matCellDef="let row">
            {{ row.type }}
          </td>
        </ng-container>

        <!-- Allocation ID Column -->
        <ng-container matColumnDef="reference_id">
          <th mat-header-cell *matHeaderCellDef>Allocation ID</th>
          <td mat-cell *matCellDef="let row">
            <ng-container *ngIf="row.type === invoiceType && displayForAdmin; else purchaseId">
              <a (click)="navigateToInvoice(row.referenceId)">
                {{ row.referenceId }}
              </a>
            </ng-container>
            <ng-template #purchaseId>
              <a *ngIf="row.type === purchaseType; else baseReferenceId" (click)="navigateToPurchase(row.referenceId)">
                {{ row.referenceId }}
              </a>
            </ng-template>
            <ng-template #baseReferenceId>
              {{ row.referenceId }}
            </ng-template>
          </td>
        </ng-container>

        <!-- Description Column -->
        <ng-container matColumnDef="description">
          <th mat-header-cell *matHeaderCellDef>Description</th>
          <td mat-cell *matCellDef="let row">
            {{ row.description }}
          </td>
        </ng-container>

        <!-- Total Column -->
        <ng-container matColumnDef="total">
          <th mat-header-cell *matHeaderCellDef billingUiPriceColumnStyle>Total</th>
          <td mat-cell *matCellDef="let row" billingUiPriceColumnStyle>
            {{ row.total / 100 || 0 | currency : row.currency : 'symbol' : '1.2-2' }}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayColumns"></tr>

        <!-- Reference ID Column -->
        <ng-container *ngIf="displayForAdmin" matColumnDef="external_reference_id">
          <th mat-header-cell *matHeaderCellDef>Reference ID</th>
          <td mat-cell *matCellDef="let row">
            <ng-container *ngIf="displayStripeLink(row); else noLink">
              <a [href]="getChargeDetailsUrl(row.externalReferenceId)" target="_blank">
                {{ row.externalReferenceId }}
              </a>
            </ng-container>
            <ng-template #noLink>
              {{ row.externalReferenceId }}
            </ng-template>
          </td>
        </ng-container>

        <!-- Created Action Column -->
        <ng-container *ngIf="displayForAdmin" matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let row">
            <button mat-icon-button [matMenuTriggerFor]="menu" (click)="$event.stopPropagation()">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu #menu="matMenu">
              <button mat-menu-item (click)="openRefundDialog(row)" [disabled]="false">
                <mat-icon>money_off</mat-icon>
                <span>Issue Refund</span>
              </button>
            </mat-menu>
          </td>
        </ng-container>
      </table>

      <mat-paginator
        #paymentPaginator
        [length]="length$ | async"
        [pageIndex]="0"
        [pageSize]="25"
        [pageSizeOptions]="[25, 50, 100]"
      ></mat-paginator>
    </va-mat-table>
  </va-filter-content>
</va-filter2-model>
