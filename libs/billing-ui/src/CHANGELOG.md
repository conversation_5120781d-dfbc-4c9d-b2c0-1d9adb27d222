CHANGE LOG
==========
## 3.48.1
- Add `standalone: false` to all standalone components to prepare for Angular 19 update

## 3.48.0
- Add `showGreenPrice` to `SimplePriceDisplay` that changes the price to green if it is positive

## 3.47.0
- Set discount end date to be end of day ('T23:59:59Z') when creating/updating a discount
  - This acts to make the discount inclusive of the end date
- Remove unused FlexLayoutModule import

## 3.46.0
- Add `RetailDiscountModal` component for adding/editing retail discounts

## 3.45.0
- Export `CurrencyWithoutUSDPipe` Pipe; add it to list of providers in module

## 3.44.0
- Remove usages of fxLayout, fxFlex

## 3.43.0
- Export new standalone directive, `PriceColumnStyleDirective` which applies `text-align: right` style to pricing columns

## 3.42.0
- Show `setupFee` in the `VariablePriceInputComponent`
- Add `setupFee` label in the `VariablePriceInputComponent`
- Add 'getSetupFee' function to utils
- remove management fee from hint in `VariablePriceInputComponent`

## 3.41.0
- Add `frequencyFromDisplay` function
- Export `frequencyForDisplay`, `frequencyFromDisplay` from frequency utils

## 3.40.0
- Add `allowCustomStyling` input to `SimplePriceDisplayComponent`

## 3.39.0
- Export new pipes: CalculateTaxAmount and TaxDescription

## 3.38.0
- Update display of labels for `EditContactInfoDialogComponent` and `EditPaymentMethodDialogComponent`

## 3.37.2
- Fix styling issues for `EditPaymentMethodDialogComponent`

## 3.37.1
- Fix styling issues for `EditContactInfoDialogComponent`

## 3.37.0
- Calculate the management fee amount on key up events in `VariablePriceInputComponent`

## 3.36.0
- Add pause dunning, resume dunning actions to purchases table

## 3.35.0
- Add dunning paused filter to purchases table

## 3.34.0
- Add 'dunning paused until' column to admin view in purchases table

## 3.33.0
- Add frequency filters to subscription table

## 3.32.0
- Convert the subscription-list component to utilize the new list subscriptions endpoint 

## 3.31.0
- Add total amount to `VariablePriceInputComponent`

## 3.30.0
- Remove direct file download from purchase export

## 3.29.1
- Fixed issue with PricingRule interface mismatch

## 3.29.0
- Add notes field to issue refund dialog for refund clarification

## 3.28.0
- Export additional values that were being imported by PCC with relative imports

## 3.27.0
- Remove Financial Force specific Create Automated Credit Note button

### 3.26.1
- Remove value round from the variable price input component 

### 3.26.0
- Add warning to billing contact dialog if the country update will result in a subsidiary change

### 3.25.2
Retain decimal place and zeros in request spend change dialog when value changes

### 3.25.1
Format the current spend amount and set the currentSpendAmount and minSpendAmount to zero if input values are undefined

### 3.25.0
- Use NetSuite for external ERP links for Invoices and Credit Memos

### 3.24.0
- Remove invoice status column and filter
- Remove post invoice functionality
  - Both features above were removed as they only applied to FinancialForce which will no longer be using in favour of NetSuite

### 3.23.0
- Remove credit note status column from credit note list
  - Status only exists in FinancialForce which we no longer use for credit notes

### 3.22.3
- Replace `businessSelected` and `productSelected` outputs on `SubscriptionListComponent` with inputs allowing for an actual link in the HTML 

### 3.22.2
- Republish to remove @locl/core dependency

### 3.22.1
- The `EditContactInfoDialogComponent` will no longer make a request to get the country's states if the country is not set.

### 3.22.0
- Add `BillingStrategyPipe` to format billing strategy to a human readable string

### 3.21.1
- Update the use of confirm function in Confirmation service.

### 3.21.0
- Display setup fee as a separate column in the Purchase table
- Display taxes as amount values ($) with the rate (%) shown as a tooltip

### 3.20.2
- Fixed styling of progress bar on Edit Payment method dialog

### 3.20.1
- Use SnackbarService from the Galaxy Framework, instead of the AlertService from UIKit

### 3.20.0
- add Export button to `PurchaseItemList`. This button allows the user to export an unpaged list of all their purchases matching the current table filters into a csv format

### 3.19.0
- Display applied taxes in `PurchaseItemListComponent`

### 3.18.0
- Update referenceID table row in `PaymentListComponent` to hide Stripe link if the payment source is not from Stripe

### 3.17.0
- Display applied vcash in `PurchaseListComponent`, and no longer display Credit chip for credit notes

### 3.16.0
- Add event emissions for business and product name in `SubscriptionListComponent`

### 3.15.0
- Add `SubscriptionListComponent`

### 3.14.0
- Update the credit note table to display the date in local time without the timezone

### 3.13.0
- Update sales invoice table to show the month/year for the period rather than the date 

### 3.12.0
- Updated Billing SDK and remove UTC from date displays

### 3.11.0
- Fix Duplicate Requests to Send Invoice Receipt Email

### 3.10.3
- Use symbol-narrow within discount pipe

### 3.10.2
- Rework to use the formatDisplayPrice for cents to dollar conversion 

### 3.10.1
- Inside the Discount Display pipe, call angular currency pipe rather than the shared price pipe. We should never display "Contact Sales" or a frequency for a discount

## 3.10.0
- update due date charge time on info icon in `SalesInvoiceListComponent` 

### 3.9.1
- Inject `PaymentService` into billing ui module as a provider

### 3.9.0
- Update dependency on billing package to use new payment service
 
### 3.8.5
- Refund dialog is creating the wrong type of refunds

### 3.8.4
- Fix refund purchase emitter

### 3.8.3
- Add ability to filter sales invoice by id

### 3.8.2
- Event emitters when invoice reference selected on payment and refund page

### 3.8.1
- Refunds list component paginator

### 3.8.0
- Add refunds list component

### 3.7.1
- Added appliedValueMapper to Filter `includeVoided` for `PurchaseListComponent`

### 3.7.0
- Add filter `includeVoided` for `PurchaseListComponent`
- Add tag `Voided` to `PurchaseListComponent`

### 3.6.0
- Add format discount amount pipe

### 3.5.0
- Add button to admin view of `SalesCreditListComponent` for creating automated credit notes

### 3.4.0
- Add tooltip to "Due Date" column in `SalesInvoiceListComponent`
- Specify time zone on "Created" column in `PaymentListComponent`
 
### 3.3.0
- Show `View PDF` button to all users in `SalesCreditNoteListComponent`

### 3.2.0
- Add credit chip to table of PurchaseListComponent

### 3.1.0
- Change placeholder in payment method dialog

### 3.0.2
- Fix EditContactInfoDialogComponent by setting default form values if they do not exist

### 3.0.1
- Update @Vendasta dependencies

### 3.0.0
- Using Angular 9

### 2.0.1
- Update dependencies

### 2.0.0
- Using Angular 8

### 1.27.0
- Replace `@vendasta/core/billing` imports with `@vendasta/billing`

### 1.26.4
- Export the stripe service and script to be able to use in non frontend projects

### 1.26.3
- `PurchaseListComponent` rows change colour and cursor changes on hover

### 1.26.2
- Fixes lack of linewrap with city, state/province, and zip/postal code row in mobile enviroments in a`EditPaymentMethodDialogComponent`

### 1.26.1
- Fixes lack of linewrap with city, state/province, and zip/postal code row in mobile enviroments in a`EditContactInfoDialogComponent`

### 1.26.0
- Add empty state to `PurchaseListComponent` template

### 1.25.0
- Add `allowedStrategies` input to `PurchaseListComponent` to restrict table to specific billing strategies

### 1.23.0
- Append Purchase Item description to Product Name in `PurchaseItemList` component

### 1.22.1
- Fix PurchaseListComponent to listen for merchantId changes in order to update filters

### 1.22.0
- Open up "View PDF" button to all users in `SalesInviceList` component
- Export "viewFile()" shared function for use outside the billing-ui package

### 1.21.0
- Override line item name with description if description exists in `PurchaseItemList`
- Add "Void" button to purchase in `PurchaseItemList` for admin view

### 1.20.0
- Small refactor of the `SalesInvoiceListComponent` to fix a race condition

### 1.91.3
- Divide cents by 100 when converting to dollars in `RefundDialogComponent`

### 1.91.2
- Sanitize cents and dollars in `RefundDialogComponent` to avoid floating point errors

### 1.19.1
- Add guard for no additional emails on `EditContactInfoDialogComponent`
- Add empty validation to stripe card inputs on `EditPaymentMethodDialogComponent`

### 1.19.0
- Add `EditContactInfoDialogComponent`

### 1.18.14
- initialize datasource on Purchase list after view initialization

### 1.18.13
- Fix paginator on Purchase List table to allow selecting next page of results

### 1.18.12
- Add `PurchaseItemListComponent` as an export in `BillingUiModule`

### 1.18.11
- Export the financial force and stripe url services

### 1.18.10
- Add two methods to the BillingErpUrlService to get the details and list of bank reconciliations.

### 1.18.9
- Pass recaptcha key into payment method components.

### 1.18.8
- Pass cardholder name from token data to update payment method request.

### 1.18.7
- Add 'same as contact information' checkbox field to payment method form.

### 1.18.6
- Add `PaymentMethodInfoComponent` and `EditPaymentMethodDialogComponent`

### 1.18.5
- Export price pipe from billing ui module

### 1.18.3
- Update purchase table to include correct page size

### 1.18.2
- Removing the stub service from the billing UI module

### 1.18.1
- Hide View PDF buttons from partners on invoice and credit note lists until unknown issue is resolved

### 1.18.0
- Add new component to display the purchases for a merchant `PurchaseListComponent`

### 1.17.2 - 1.17.1
- Fix bug where we were trying to reference an element ref that didn't exist for the download workflow for sales credit notes and
sales invoices.
- Add the actual anchor tag.

### 1.17.0
- Add support for view and downloading pdfs.

### 1.16.0
- Add deeplink to FinancialForce from `SalesCreditNoteListComponent`

### 1.15.2
- Display SIN# instead of financial force identifier for rows on `SalesInvoiceListComponent`

### 1.15.1
- Add change action wording from 'Download' to 'Download PDF' on `SalesInvoiceListComponent`

### 1.14.0
- Add functionality to view an invoice PDF from the `SalesInvoiceListComponent` rather than having to download it

### 1.13.0
- Add Invoice "Type" column to `SalesInvoiceListComponent` table.
- In `SalesInvoiceListComponent`, if sales invoice is for an instant purchase, open warning dialog before allowing charge

### 1.12.0
- Add the refund dialog with integrations in the payment table. Add the actions column to the payments table and conditionally hide it.

### 1.11.0
- Update the `salesInvoiceListComponent` and `salesCreditNoteListComponent` to have an empty state
- Update `salesInvoiceListComponent` to have injectable datasource

### 1.10.5
- Update the `salesInvoiceListComponent` and `salesCreditNoteListComponent` to stop loading bar when error returned during list

### 1.10.4
- Update the `salesInvoiceListComponent` to have reload logic when posting a sales invoice (reload the single page from the list api)

### 1.10.3
- `PaymentStatus` and `CreditNoteStatus` columns in `SalesCreditNoteListComponent` added

### 1.10.2
- `Outstanding` column in `SalesCreditNoteListComponent` displays as number data

### 1.10.1
- `SalesCreditNoteListComponent` saves column config

### 1.10.0
- Update the `salesInvoiceListComponent` to include an action to send the sales invoice receipt email to the merchant.

### 1.9.0
- Create `SalesCreditNoteListComponent`

### 1.8.0
- Add `searchTerm` as an input to `SalesInvoiceListComponent`

### 1.7.1
- Update the `salesInvoiceListComponent` to only call post a single time when posting a sales invoice.

### 1.7.0
- Update the `salesInvoiceListComponent` to include and action to post the sales invoice to financial force.

### 1.6.1
- Fix `PaymentListComponent` by triggering the filters to update if the merchantId changes

### 1.6.0
- Create `PaymentListComponent`

### 1.6.0
- Update the `salesInvoiceListComponent` to include and action to post the sales invoice to financial force.

### 1.5.0
- Update `sendSalesInvoiceEmail` to display a confirmation modal in `sales-invoice-list` component

### 1.4.0
- Update `onSave` to show full error message in snackbar in `charge-invoice-dialog` component

### 1.3.0
- Update `onSave` to use a new snackbar config in `charge-invoice-dialog` component

### 1.2.3
- Update styles on charge dialog

### 1.2.2
- Display all `invoiceDate` and `dueDate` values in `SalesInvoiceList` component as UTC rather than local time

### 1.2.1
- Update styles on charge dialog

### 1.2.0
- Create `ChargeInvoiceDialogComponent`
- Add action to `SalesInvoiceListComponent` to display `ChargeInvoiceDialogComponent`

### 1.1.1
- Add .pdf extension to invoice download link

### 1.1.0
- Add `displayColumns` input to SalesInvoiceList component
- Don't start with `null` in SalesInvoiceList datasource filters. The consumer of this component should have complete control over every filter value. For that reason, the consumer should pass in a starting value for filters.

### 1.0.0
- Add Changelog, set version to version 1.0.0

### 0.0.1
- Publish initial version of billing-ui with SalesInvoiceList component
