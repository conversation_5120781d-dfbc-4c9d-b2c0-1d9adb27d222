import { AfterViewInit, Component, inject, Inject, Injector, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { distinctUntilChanged, firstValueFrom, fromEvent, map, Observable, Subscription } from 'rxjs';
import {
  createCrmObjectModalProviders,
  CrmCreateCrmObjectModalComponent,
  CrmCreateCrmObjectModalResult,
  CrmFieldService,
  StandardExternalIds,
} from '@galaxy/crm/static';
import { MatDialog } from '@angular/material/dialog';
import {
  GroupMeetingType,
  MeetingSchedulerStoreService,
} from '../../../data-providers/meeting-scheduler-store.service';
import { CRMApiService, CrmObject, CrmObjectSearch, ListCrmObjectsRequest } from '@vendasta/crm';
import { debounceTime, startWith, switchMap } from 'rxjs/operators';
import { PERSONAL_CALENDAR_ID_TOKEN } from '../../../data-providers/providers';
import { MatAutocomplete, MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import {
  MeetingLocationType,
  MeetingType,
  Preferences,
  TimeSpan,
  Contact,
  TeamEventMeetingType,
  DateRangeType,
  EventTypeDateRangeInterface,
} from '@vendasta/meetings';
import { Calendar } from '@vendasta/meetings/lib/shared/calendar';
import moment from 'moment';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { TranslateService } from '@ngx-translate/core';
import { TEAM_EVENT_TYPE, POSTHOG_KEYS, POSTHOG_CATEGORIES, POSTHOG_ACTIONS } from '../../../constants';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { CalendarConfig } from '../../../shared/components/calendar-date-picker/calendar-date-picker.component';

const PAGE_SIZE = 25;

export interface ContactData {
  contactID: string;
  accountGroupID: string;
  email: string;
  name: string;
  phoneNumber: string;
  firstName: string;
  lastName: string;
}

@Component({
  selector: 'meeting-scheduler-schedule-meeting-dialog',
  templateUrl: './schedule-meeting-dialog-component.html',
  styleUrls: ['./schedule-meeting-dialog-component.scss'],
  standalone: false,
})
export class ScheduleMeetingDialogComponent implements OnInit, AfterViewInit, OnDestroy {
  private readonly injector = inject(Injector);
  private readonly objectType = 'Contact';
  private lastGetUserListRequest: any;
  @ViewChild(MatAutocompleteTrigger) autocompleteTrigger!: MatAutocompleteTrigger;
  @ViewChild(MatAutocomplete) autocomplete!: MatAutocomplete;
  private dateTimeMap: Map<string, string[]> = new Map<string, string[]>();
  isBookingSubmitted = false;
  isBookingSubmitDisabled = true;

  personalMeetingTypes$: Observable<MeetingType[]>;
  groupMeetingTypes$: Observable<{ [calendarId: string]: GroupMeetingType[] }>;
  teamMeetingTypes$: Observable<MeetingType[]>;
  filteredPersonalMeetingTypes$: Observable<MeetingType[]>;
  filteredTeamMeetingTypes$: Observable<MeetingType[]>;

  meetingScheduleForm = new FormGroup({
    userName: new FormControl(''),
    userID: new FormControl(''),
    eventType: new FormControl<MeetingType | null>(null),
    location: new FormControl({ value: null, disabled: true, guideline: null }),
    teamMemberID: new FormControl(''),
    meetingDate: new FormControl<Date | null>(null),
    meetingTime: new FormControl(''),
    additionalEmails: new FormControl('', [Validators.email]),
  });

  scrollSubscription?: Subscription;
  subscription: Subscription[] = [];
  calender: Calendar;
  selectedUser: ContactData;
  cursor = '';
  userList: ContactData[] = [];
  isContactLoading = false;
  hasMoreContacts = false;
  inviteeEmails = new Set<string>();
  separatorKeysCodes: number[] = [ENTER, COMMA];
  locationGuideline = '';
  displayTeamMemberSelection = false;
  timezone = '';
  skipUserNameChange = false;
  hostPreferences$: Observable<Preferences>;
  availableTimeSlots: string[];
  calenderDateStart: Date;
  calenderDateEnd: Date;
  // Shared calendar component properties
  calendarConfig: CalendarConfig | null = null;
  selectedDate: Date | null = null;
  // Additional Email Support Functions
  currentDate: Date = new Date(); // starting from current month
  maxMonths = 12;

  // Custom date range properties
  hasCustomDateRange = false;
  customDateRangeStart: Date | null = null;
  customDateRangeEnd: Date | null = null;
  eventTypeEndDateInPast = false;
  eventTypeEndDate: Date | null = null;

  monthlyDateTimeMap = new Map<string, string[]>(); // cache by month key "YYYY-MM"
  validateDateFn: (d: Date | null) => boolean = () => false;

  // Add missing properties
  isCalenderDisabled = false;
  currentMonth = new Date();

  constructor(
    private readonly dialog: MatDialog,
    private readonly crmService: CRMApiService,
    private readonly crmFieldService: CrmFieldService,
    @Inject(PERSONAL_CALENDAR_ID_TOKEN) public readonly personalCalendarId$: Observable<string>,
    private readonly meetingSchedulerStoreService: MeetingSchedulerStoreService,
    private snackbarService: SnackbarService,
    private readonly translate: TranslateService,
    private readonly analyticsService: ProductAnalyticsService,
  ) {
    this.calculateCalenderDateEnd();
  }

  // Generic term for partner & SMB ID
  namespace: string;

  formatDateWithoutTimezone(date: Date | null): string {
    const options: Intl.DateTimeFormatOptions = {
      // timeZone: this.timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    };
    // Format date parts using Intl.DateTimeFormat
    const formatter = new Intl.DateTimeFormat('en-CA', options); // en-CA => YYYY-MM-DD
    return formatter.format(date);
  }

  disableBookMeeting(): boolean {
    if (!this.selectedUser?.name) {
      return true;
    }
    if (!this.getFormValue('meetingTime')) {
      return true;
    }
    return false;
  }

  // Add missing methods that the template expects
  onDateChange(date: Date): void {
    this.selectedDate = date;
    this.meetingScheduleForm.get('meetingDate')?.setValue(date);
    this.meetingScheduleForm.get('meetingTime')?.setValue('');
    this.isBookingSubmitDisabled = true;

    // Note: Calendar component handles updating available time slots via availableTimeSlotsChange event
  }

  onAvailableTimeSlotsChange(timeSlots: string[]): void {
    this.availableTimeSlots = timeSlots;
  }

  // Add missing methods
  private initializeCalendarForMeetingType(meetingType: MeetingType): void {
    if (!meetingType) return;

    // Reset local state but don't fetch - let calendar component handle fetching
    this.monthlyDateTimeMap = new Map<string, string[]>();
    this.dateTimeMap = new Map<string, string[]>();

    // For custom date ranges, start from the custom start date
    if (this.hasCustomDateRange && this.customDateRangeStart) {
      this.currentDate = new Date(this.customDateRangeStart);
      this.currentDate.setDate(1); // Set to first day of the month
    } else {
      this.currentDate = new Date();
    }

    this.isCalenderDisabled = true;

    // Initialize calendar configuration - this will trigger the calendar component to fetch
    this.calendarConfig = {
      calendarId: meetingType.calendarId,
      meetingTypeId: meetingType.id,
      timezone: this.timezone,
      userID: this.getFormValue('teamMemberID') || '',
      // Pass custom date range information to calendar component
      hasCustomDateRange: this.hasCustomDateRange,
      customDateRangeStart: this.customDateRangeStart,
      customDateRangeEnd: this.customDateRangeEnd,
      eventTypeEndDateInPast: this.eventTypeEndDateInPast,
    };

    // Set calendar date range
    this.calculateCalenderDateEnd();

    // Note: Calendar component will handle fetching when config changes
  }

  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0]; // YYYY-MM-DD
  }

  private getStartOfMonth(year: number, month: number): Date {
    return new Date(year, month, 1);
  }

  private getEndOfMonth(year: number, month: number): Date {
    return new Date(year, month + 1, 0, 23, 59, 59, 999);
  }

  validateDate = (d: Date | null): boolean => {
    // Calendar component handles its own validation
    // This is kept for compatibility but always returns true
    return d !== null;
  };

  ngOnInit() {
    // Fetch user preferred timezone
    this.validateDateFn = this.validateDate.bind(this);
    this.hostPreferences$ = this.meetingSchedulerStoreService.loadUserPreferences();
    this.subscription.push(
      this.hostPreferences$.subscribe((hostPreference) => {
        this.timezone = hostPreference?.timezone?.id;
      }),

      // Fetch user calender information
      this.meetingSchedulerStoreService.loadPersonalCalendar().subscribe((calendar) => {
        this.calender = calendar;
        if (calendar.applicationContext?.user_context === 'SMB') {
          this.namespace = calendar.applicationContext.business_id;
        } else {
          this.namespace = calendar.applicationContext.partner_id;
        }
      }),
    );

    // fetch event types of user
    this.personalMeetingTypes$ = this.meetingSchedulerStoreService.loadPersonalMeetingTypes({ returnCache: true }).pipe(
      map((meetingTypes) => {
        if (!meetingTypes) {
          return null;
        }
        return meetingTypes.sort((mt1, mt2) => {
          return mt1.name.localeCompare(mt2.name, undefined, { numeric: true });
        });
      }),
    );

    this.groupMeetingTypes$ = this.meetingSchedulerStoreService.loadGroupMeetingTypes({ returnCache: true });
    this.teamMeetingTypes$ = this.groupMeetingTypes$.pipe(
      map((groupMeetingTypes) => meetingTypeMapToArray(groupMeetingTypes)),
    );

    // Filter event type from personal list
    this.filteredPersonalMeetingTypes$ = this.meetingScheduleForm.get('eventType')!.valueChanges.pipe(
      startWith(''), // Initial empty value
      switchMap((value) => this._personal_meeting_type_filter(typeof value === 'string' ? value : value?.name || '')),
    );

    // Filter event type from team list
    this.filteredTeamMeetingTypes$ = this.meetingScheduleForm.get('eventType')!.valueChanges.pipe(
      startWith(''), // Initial empty value
      switchMap((value) => this._team_meeting_type_filter(typeof value === 'string' ? value : value?.name || '')),
    );

    // Fetch user from contact list
    this.subscription.push(
      this.meetingScheduleForm
        .get('userName')!
        .valueChanges.pipe(debounceTime(300), distinctUntilChanged())
        .subscribe(() => {
          if (this.skipUserNameChange) {
            this.skipUserNameChange = false;
            return;
          }
          this.selectedUser = {} as ContactData;
          this.fetchContacts(false);
        }),

      // Update states on event type selection
      this.meetingScheduleForm
        .get('eventType')!
        .valueChanges.pipe(distinctUntilChanged())
        .subscribe((meetingType: MeetingType | null) => {
          this.locationGuideline = '';
          this.meetingScheduleForm.get('location')!.setValue(null);
          this.meetingScheduleForm.get('location')!.disable();
          this.displayTeamMemberSelection = false;
          this.isBookingSubmitDisabled = true;
          this.meetingScheduleForm.get('teamMemberID')!.setValue(null);
          this.meetingScheduleForm.get('meetingDate')!.setValue(null);
          this.meetingScheduleForm.get('meetingTime')!.setValue(null);
          this.selectedDate = null;
          this.calendarConfig = null;
          this.isCalenderDisabled = true;
          this.eventTypeEndDateInPast = false; // Reset the flag
          this.eventTypeEndDate = null; // Reset the end date
          if (meetingType?.id) {
            // Check and set custom date range before processing the meeting type
            this.checkAndSetCustomDateRange(meetingType);

            if (
              meetingType.CalendarType.trim() === TEAM_EVENT_TYPE &&
              (this.getFormValue('eventType')?.meetingType === TeamEventMeetingType.MULTI_HOST ||
                this.getFormValue('eventType')?.meetingType === TeamEventMeetingType.CLIENT_CHOICE)
            ) {
              this.displayTeamMemberSelection = true;
              if (this.getFormValue('eventType')?.meetingType === TeamEventMeetingType.MULTI_HOST) {
                this.initializeCalendarForMeetingType(meetingType);
              }
            } else {
              this.initializeCalendarForMeetingType(meetingType);
            }
            if (meetingType.locationType === MeetingLocationType.IN_PERSON_USER_SITE) {
              this.meetingScheduleForm.get('location')!.setValue(meetingType.location);
              this.meetingScheduleForm.get('location')!.disable();
            } else if (meetingType.locationType === MeetingLocationType.IN_PERSON_CLIENT_SITE) {
              this.meetingScheduleForm.get('location')!.setValue(null);
              this.meetingScheduleForm.get('location')!.enable();
              this.locationGuideline = meetingType.locationGuidelines;
            }
          }
        }),

      // Update states on team member selection
      this.meetingScheduleForm.get('teamMemberID')!.valueChanges.subscribe(() => {
        // To handle null set during meeting type update not trigger fetchCalenderAvailability()
        if (this.getFormValue('teamMemberID')) {
          this.isBookingSubmitDisabled = true;
          this.meetingScheduleForm.get('meetingDate')!.setValue(null);
          this.meetingScheduleForm.get('meetingTime')!.setValue(null);
          this.selectedDate = null;
          const meetingType = this.getFormValue('eventType');
          if (meetingType) {
            this.initializeCalendarForMeetingType(meetingType);
          }
        }
      }),

      // Update states on meeting date selection
      this.meetingScheduleForm.get('meetingDate')!.valueChanges.subscribe((_meetingDate) => {
        this.meetingScheduleForm.get('meetingTime')!.setValue(null);
        this.isBookingSubmitDisabled = true;
        // Note: availableTimeSlots are updated by calendar component via availableTimeSlotsChange event
      }),

      // Update state on meeting time selection
      this.meetingScheduleForm.get('meetingTime')!.valueChanges.subscribe(() => {
        // Field should not be enabled when value is set to null
        if (this.meetingScheduleForm.get('meetingTime')) {
          this.isBookingSubmitDisabled = false;
        }
      }),
    );
  }

  private _personal_meeting_type_filter(value: string): Observable<MeetingType[]> {
    const filterValue = value?.toLowerCase() || '';

    return this.personalMeetingTypes$.pipe(
      map((meetingTypes: MeetingType[]) =>
        meetingTypes.filter((meetingType) => meetingType.name.toLowerCase().includes(filterValue)),
      ),
    );
  }

  private _team_meeting_type_filter(value: string): Observable<MeetingType[]> {
    const filterValue = value?.toLowerCase() || '';

    return this.teamMeetingTypes$.pipe(
      map((meetingTypes: GroupMeetingType[]) =>
        meetingTypes.filter((meetingType) => meetingType.name.toLowerCase().includes(filterValue)),
      ),
    );
  }

  ngOnDestroy() {
    this.subscription.forEach((sub) => sub.unsubscribe());
    this.subscription = [];
    this.scrollSubscription?.unsubscribe(); // Clean up subscriptions
  }

  // Event Type Support Function
  displayMeetingType(meetingType?: MeetingType): string {
    if (!meetingType) {
      return '';
    }
    return meetingType ? meetingType.name : '';
  }
  // Event Type Support Function

  // Additional Email Support Functions
  addInviteeEmail(value: string): void {
    if (this.meetingScheduleForm.get('additionalEmails').invalid) {
      return;
    }
    const trimmedValue = (this.getFormValue('additionalEmails') || '').trim();
    if (!trimmedValue) {
      return;
    }
    this.inviteeEmails.add(value);
    this.resetInviteeEmailInput();
  }

  private resetInviteeEmailInput(): void {
    this.meetingScheduleForm.get('additionalEmails').setValue('');
    this.meetingScheduleForm.get('additionalEmails').markAsPristine();
  }

  removeInviteeEmail(value: string): void {
    this.inviteeEmails.delete(value);
  }

  getMonthKey(date: Date): string {
    const monthKey = date.toISOString().slice(0, 7); // "YYYY-MM"
    const meetingType = this.getFormValue('eventType');
    const teamMemberID = this.getFormValue('teamMemberID') || '';

    // Include meeting type and team member in cache key to prevent conflicts
    return `${monthKey}-${meetingType?.id || 'no-type'}-${teamMemberID || 'no-member'}`;
  }

  calculateCalenderDateEnd() {
    // For custom date ranges, use the custom end date
    if (this.hasCustomDateRange && this.customDateRangeEnd) {
      this.calenderDateEnd = this.customDateRangeEnd;
      return;
    }

    // For non-custom date ranges, use the default 12-month window
    const now = new Date();
    const targetMonth = new Date(now);
    targetMonth.setMonth(targetMonth.getMonth() + this.maxMonths);

    // go to next month & day 0 = last day of previous month
    const endOfMonthLocal = new Date(targetMonth.getFullYear(), targetMonth.getMonth() + 1, 0, 23, 59, 59, 999);

    this.calenderDateEnd = endOfMonthLocal;
  }

  /**
   * Check if a meeting type has a custom date range and extract the dates
   */
  private checkAndSetCustomDateRange(meetingType: MeetingType): void {
    const dateRange = meetingType.dateRange as EventTypeDateRangeInterface;
    const now = new Date();

    // Reset the flags and end date
    this.eventTypeEndDateInPast = false;
    this.eventTypeEndDate = null;

    if (dateRange && dateRange.dateRangeType === DateRangeType.CUSTOM && dateRange.customDateRange) {
      this.hasCustomDateRange = true;

      // Store the end date
      this.eventTypeEndDate = dateRange.customDateRange.end ? new Date(dateRange.customDateRange.end) : null;

      // Check if the custom end date is in the past
      if (dateRange.customDateRange.end && new Date(dateRange.customDateRange.end) < now) {
        this.eventTypeEndDateInPast = true;
        // Don't proceed with setting up the date range if end date is in the past
        return;
      }

      // If the custom start date is in the past, use current date instead
      if (dateRange.customDateRange.start && new Date(dateRange.customDateRange.start) < now) {
        this.customDateRangeStart = now;
      } else {
        this.customDateRangeStart = dateRange.customDateRange.start;
      }

      this.customDateRangeEnd = dateRange.customDateRange.end;

      // For custom date ranges, jump to the start month
      if (this.customDateRangeStart) {
        this.currentDate = new Date(this.customDateRangeStart);
        this.currentDate.setDate(1); // Set to first day of the month
      }
    } else {
      // Reset to default behavior for non-custom date ranges
      this.hasCustomDateRange = false;
      this.customDateRangeStart = null;
      this.customDateRangeEnd = null;
      this.currentDate = new Date(); // Reset to current month
    }
  }

  private resetCalendarAndFetch(): void {
    this.monthlyDateTimeMap = new Map<string, string[]>();
    this.dateTimeMap = new Map<string, string[]>();

    // For custom date ranges, start from the custom start date
    if (this.hasCustomDateRange && this.customDateRangeStart) {
      this.currentDate = new Date(this.customDateRangeStart);
      this.currentDate.setDate(1); // Set to first day of the month
    } else {
      this.currentDate = new Date();
    }

    // Recalculate calendar date end based on date range type
    this.calculateCalenderDateEnd();

    this.fetchCalendarAvailabilityForMonth(this.currentDate);
  }

  getFormValue(controlName: string) {
    return this.meetingScheduleForm.get(controlName)?.value;
  }

  fetchCalendarAvailabilityForMonth(monthStart: Date): void {
    const monthKey = this.getMonthKey(monthStart);

    // Skip if data already cached, but only if the calendar is not currently disabled
    // This prevents issues where cached data might be stale
    if (this.monthlyDateTimeMap.has(monthKey) && !this.isCalenderDisabled) {
      this.finishLoading();
      return;
    }

    this.isCalenderDisabled = true;

    const meetingType = this.getFormValue('eventType');
    if (!meetingType?.id) {
      return this.finishLoading();
    }

    // Check if event type end date is in the past
    if (this.eventTypeEndDateInPast) {
      // Keep calendar disabled and don't make API call
      return;
    }

    const now = new Date();
    const startOfMonth = this.getStartOfMonth(monthStart.getFullYear(), monthStart.getMonth());
    const endOfMonth = this.getEndOfMonth(monthStart.getFullYear(), monthStart.getMonth());

    let start = now > startOfMonth ? now : startOfMonth;
    let end = endOfMonth > this.calenderDateEnd ? this.calenderDateEnd : endOfMonth;

    // For custom date ranges, respect the custom boundaries
    if (this.hasCustomDateRange) {
      if (this.customDateRangeStart && start < this.customDateRangeStart) {
        start = this.customDateRangeStart;
      }
      if (this.customDateRangeEnd && end > this.customDateRangeEnd) {
        end = this.customDateRangeEnd;
      }

      // Additional check: if custom start is current time (due to past date adjustment),
      // ensure we don't go before current time
      if (this.customDateRangeStart && this.customDateRangeStart.getTime() === now.getTime()) {
        start = now > start ? now : start;
      }
    }

    let userID = this.getFormValue('teamMemberID') || '';
    if (meetingType.meetingType === TeamEventMeetingType.CLIENT_CHOICE && !userID) {
      return this.finishLoading();
    }
    if (userID === 'any') userID = '';

    const timeRange: TimeSpan = { start, end };

    const response = this.meetingSchedulerStoreService.loadAvailableTimeSlots({
      calendarId: meetingType.calendarId,
      meetingTypeId: meetingType.id,
      timeZone: { id: this.timezone },
      userID,
      timeSpan: timeRange,
    });

    this.subscription.push(
      response.subscribe({
        next: (availableTimeSlots) => {
          const monthDateMap = new Map<string, string[]>();

          for (const slot of availableTimeSlots) {
            const date = this.formatDate(slot.start); // YYYY-MM-DD
            const time = new Date(slot.start).toLocaleTimeString('en-US', {
              timeZone: this.timezone,
              hour: '2-digit',
              minute: '2-digit',
              hour12: true,
            });

            this.pushToMapList(monthDateMap, date, time);
            this.pushToMapList(this.dateTimeMap, date, time);
          }

          // Cache flat times for this month
          const flatTimes = Array.from(monthDateMap.entries()).reduce<string[]>(
            (acc, [, times]) => acc.concat(times),
            [],
          );
          this.monthlyDateTimeMap.set(monthKey, flatTimes);

          const allDates = Array.from(this.dateTimeMap.keys()).sort();
          if (allDates.length) {
            this.calenderDateStart = new Date(allDates[0]);
          }

          this.finishLoading();
        },
        error: (_error) => {
          this.finishLoading();
        },
      }),
    );
  }

  private pushToMapList(map: Map<string, string[]>, key: string, value: string): void {
    let list = map.get(key);
    if (!list) {
      list = [];
      map.set(key, list);
    }
    list.push(value);
  }

  private finishLoading(): void {
    this.isCalenderDisabled = false;
    this.validateDateFn = this.validateDate.bind(this);
  }

  // User Name Support Functions
  ngAfterViewInit() {
    // User Name field enable scroller for infinite scrolling
    this.subscription.push(
      this.autocomplete.opened.subscribe(() => {
        setTimeout(() => {
          const panel = this.autocomplete.panel?.nativeElement;
          if (panel) {
            this.attachScrollListener(panel);
          }
        }, 0);
      }),
    );
  }

  attachScrollListener(panel: HTMLElement) {
    if (this.scrollSubscription) {
      this.scrollSubscription.unsubscribe(); // Unsubscribe previous listener if any
    }

    this.scrollSubscription = fromEvent(panel, 'scroll')
      .pipe(debounceTime(100))
      .subscribe(() => {
        if (
          !this.isContactLoading &&
          panel.scrollTop + panel.clientHeight >= panel.scrollHeight - 10 &&
          this.hasMoreContacts
        ) {
          this.fetchContacts(true);
        }
      });
  }

  private openCreateCrmObjectModal(): Observable<CrmCreateCrmObjectModalResult> {
    return this.dialog
      .open(CrmCreateCrmObjectModalComponent, {
        data: { objectType: this.objectType },
        injector: createCrmObjectModalProviders(this.objectType, this.injector),
      })
      .afterClosed();
  }

  async createContact() {
    const createModalResult = await firstValueFrom(this.openCreateCrmObjectModal());
    if (createModalResult?.crmObject?.crmObjectId) {
      const userObject = createModalResult.crmObject as CrmObject;
      const email = this.getEmailForContact(userObject);
      if (!email) {
        this.snackbarService.openErrorSnack(
          this.translate.instant('MEETING_SCHEDULER.MEETING_ACTION.DIALOG.CONTACT.CREATE_CONTACT.MISSING_EMAIL'),
        );
        return;
      }
      this.selectedUser = {
        firstName: this.getFirstNameForContact(userObject),
        lastName: this.getLastNameForContact(userObject),
        email: this.getEmailForContact(userObject),
        phoneNumber: this.getPhoneNumberForContact(userObject),
        name: this.getNameForContact(userObject),
      } as ContactData;
      this.skipUserNameChange = true;
      this.meetingScheduleForm.get('userName').setValue(this.selectedUser.name, { emitEvent: false });
    }
  }

  getEmailForContact(contact: CrmObject): string {
    return this.crmFieldService.getFieldValueFromCrmObject(contact, StandardExternalIds.Email)?.stringValue;
  }

  getPhoneNumberForContact(contact: CrmObject): string {
    return this.crmFieldService.getFieldValueFromCrmObject(contact, StandardExternalIds.PhoneNumber)?.stringValue;
  }

  getFirstNameForContact(contact: CrmObject): string {
    return this.crmFieldService.getFieldValueFromCrmObject(contact, StandardExternalIds.FirstName)?.stringValue;
  }

  getLastNameForContact(contact: CrmObject): string {
    return this.crmFieldService.getFieldValueFromCrmObject(contact, StandardExternalIds.LastName)?.stringValue;
  }

  getNameForContact(contact: CrmObject): string {
    const firstName = this.crmFieldService.getFieldValueFromCrmObject(
      contact,
      StandardExternalIds.FirstName,
    )?.stringValue;
    const lastName = this.crmFieldService.getFieldValueFromCrmObject(
      contact,
      StandardExternalIds.LastName,
    )?.stringValue;

    const fullName = [firstName, lastName].filter(Boolean).join(' ').trim();

    if (fullName) {
      return fullName;
    }
    return this.crmFieldService.getFieldValueFromCrmObject(contact, StandardExternalIds.Email)?.stringValue || '';
  }

  public fetchContacts(isLoadMore: boolean): void {
    let searchTerm = '';
    if (!isLoadMore) {
      this.cursor = '';
      searchTerm = this.getFormValue('userName');
    }

    if (this.lastGetUserListRequest) {
      this.lastGetUserListRequest.unsubscribe();
    }

    const request = {
      namespace: this.namespace,
      search: { searchTerm } as CrmObjectSearch,
      pagingOptions: { pageSize: PAGE_SIZE, cursor: this.cursor },
    } as ListCrmObjectsRequest;

    this.isContactLoading = true;
    this.lastGetUserListRequest = this.crmService.listContacts(request).subscribe({
      next: (response) => {
        const contacts =
          (response?.crmObjects?.map((contact: CrmObject) => ({
            name: this.getNameForContact(contact),
            email: this.getEmailForContact(contact),
            phoneNumber: this.getPhoneNumberForContact(contact),
            contactID: contact.crmObjectId,
            firstName: this.getFirstNameForContact(contact),
            lastName: this.getLastNameForContact(contact),
          })) as ContactData[]) ?? [];
        if (isLoadMore) {
          this.userList = [...this.userList, ...contacts];
        } else {
          this.userList = contacts;
        }
        this.hasMoreContacts = response?.pagingMetadata?.hasMore;
        this.cursor = response?.pagingMetadata?.nextCursor;
        this.isContactLoading = false;
      },
      error: () => {
        this.isContactLoading = false;
      },
    });
  }

  updateInvitee(user: ContactData) {
    this.selectedUser = user;
    this.skipUserNameChange = true;
  }
  // User Name Support Functions

  // Timezone Support Functions
  onTimezoneChanged(timezone: string): void {
    if (this.timezone === timezone) {
      return;
    }
    this.timezone = timezone;
    this.isBookingSubmitDisabled = true;
    this.meetingScheduleForm.get('meetingDate')!.setValue(null);
    this.meetingScheduleForm.get('meetingTime')!.setValue(null);
    this.selectedDate = null;
    const meetingType = this.getFormValue('eventType');
    if (meetingType) {
      this.initializeCalendarForMeetingType(meetingType);
    }
  }

  // Meeting Booking Support Functions
  bookMeeting() {
    this.isBookingSubmitted = true;
    const meetingType: MeetingType = this.getFormValue('eventType');

    const contacts: Contact[] = [];
    contacts.push(
      new Contact({
        firstName: this.selectedUser.firstName,
        lastName: this.selectedUser.lastName,
        isPrimary: true,
        email: this.selectedUser.email,
        phoneNumber: this.selectedUser.phoneNumber,
        timeZone: { id: this.timezone },
      }),
    );

    for (const emailID of this.inviteeEmails) {
      contacts.push(new Contact({ email: emailID, isPrimary: false, timeZone: { id: this.timezone } }));
    }

    const dateString = moment(this.getFormValue('meetingDate')).format('YYYY-MM-DD');
    const dateTimeString = `${dateString} ${this.getFormValue('meetingTime')}`;
    const localDateTime = moment.tz(dateTimeString, 'YYYY-MM-DD hh:mm A', this.timezone);
    const meetingStartTime = localDateTime.toDate();
    const location = this.getFormValue('location') || '';
    let userID = this.getFormValue('teamMemberID') || '';
    if (userID === 'any') {
      userID = '';
    }

    this.subscription.push(
      this.meetingSchedulerStoreService
        .hostBookMeeting({
          calendarId: meetingType.calendarId,
          meetingTypeId: meetingType.id,
          start: meetingStartTime,
          attendees: contacts,
          location: location,
          comment: '',
          userId: userID,
        })
        .subscribe({
          next: () => {
            this.isBookingSubmitted = false;
            this.analyticsService.trackEvent(
              POSTHOG_KEYS.MEETING_BOOKED_FROM_MY_MEETINGS,
              POSTHOG_CATEGORIES.USER,
              POSTHOG_ACTIONS.CLICK,
            );
            this.snackbarService.openSuccessSnack(
              this.translate.instant('MEETING_SCHEDULER.MEETING_ACTION.DIALOG.SUCCESS_MESSAGE'),
            );
            this.dialog.closeAll();
          },
          error: (err) => {
            this.isBookingSubmitted = false;
            console.error('Error booking meeting:', err);
            this.snackbarService.openErrorSnack(
              this.translate.instant('MEETING_SCHEDULER.MEETING_ACTION.DIALOG.ERROR_MESSAGE') +
                'Error Message - ' +
                err.error.message +
                '. Error Code - ' +
                err.error.code,
            );
          },
        }),
    );
    return;
  }
  // Meeting Booking Support Functions

  hostUserDisplayNames(): string {
    const hostUsers = this.getFormValue('eventType')?.hostUsers;
    return Array.isArray(hostUsers) ? hostUsers.map((host) => host.displayName).join(', ') : '';
  }

  protected readonly MeetingLocationType = MeetingLocationType;
  protected readonly TeamEventMeetingType = TeamEventMeetingType;
}

function meetingTypeMapToArray(meetingTypeMap: { [key: string]: MeetingType[] }): MeetingType[] {
  return Object.values(meetingTypeMap || {}).reduce((allTeamMeetingTypes, meetingTypes) => {
    return [...allTeamMeetingTypes, ...meetingTypes];
  }, []);
}
