{"COMMON.ACTION_LABELS.BACK": "Back", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.TITLE": "Event type details", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.NAME": "Name", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LINK_LABEL": "Link", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LOCATION_LABEL": "Location", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.DURATION_LABEL": "Duration (minutes)", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.CUSTOM_DURATION": "Custom", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.CUSTOM_DURATION_INPUT": "Custom duration (minutes)", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.DESCRIPTION": "Description (optional)", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.COLOR_LABEL": "Color", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LOCATION.VIDEO": "Video", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LOCATION.IN_PERSON_MEETING": "In Person", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LOCATION.IN_PERSON.SET_LOCATION_LABEL": "Who will set the meeting location?", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LOCATION.IN_PERSON.USER_SITE_MEETING_LABEL": "I'll set the location", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LOCATION.IN_PERSON.CLIENT_SITE_MEETING_LABEL": "In<PERSON><PERSON> sets the location", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.ROUND_ROBING_LABEL": "Round robin", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.CLIENT_SELECTION_LABEL": "Client selection", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.MULTI_HOST_LABEL": "Multi host", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.IN_PERSON_MEETING_PLACEHOLDER": "Enter address", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.CLIENT_SITE_MEETING_PLACEHOLDER": "Eg. 10km from Starbucks, Church St, NYC (Max 200 characters)", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.IN_PERSON_MEETING_INPUT_FIELD": "Address", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.CLIENT_SITE_MEETING_INPUT_FIELD": "Address instruction", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LOCATION.HYBRID_INPUT_FIELD": "Include video conferencing", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.LOCATION_PLACEHOLDER": "Select the location of meeting ", "MEETING_SCHEDULER.BOOKING_LINK.ABOUT_SECTION.TEAM_MEMBER_PLACEHOLDER": "Team member...", "MEETING_SCHEDULER.BOOKING_LINK.GROUP.TITLE": "Group details", "MEETING_SCHEDULER.BOOKING_LINK.SERVICE.TITLE": "Service menu details", "MEETING_SCHEDULER.BOOKING_LINK.GROUP.DESCRIPTION": "Use groups to create a collection of event types", "MEETING_SCHEDULER.BOOKING_LINK.SERVICE.DESCRIPTION": "Use a service menu to create a collection of groups and event types.", "MEETING_SCHEDULER.BOOKING_LINK.GROUP_EVENT_TYPE.TITLE": "Event types", "MEETING_SCHEDULER.BOOKING_LINK.GROUP_EVENT_TYPE.DESCRIPTION": "Select event types to be in the group", "MEETING_SCHEDULER.BOOKING_LINK.SERVICE_EVENT_TYPE.TITLE": "Groups & event types", "MEETING_SCHEDULER.BOOKING_LINK.SERVICE_EVENT_TYPE.DESCRIPTION": "Select groups or event types to be in the service menu", "MEETING_SCHEDULER.BOOKING_LINK.GROUP_EVENT_TYPE.SELECT": "Event types", "MEETING_SCHEDULER.BOOKING_LINK.SERVICE_EVENT_TYPE.SELECT": "Groups", "MEETING_SCHEDULER.BOOKING_LINK.GROUP_EVENT_TYPE.SELECT_PLACEHOLDER": "Select event types...", "MEETING_SCHEDULER.BOOKING_LINK.SERVICE_EVENT_TYPE.SELECT_PLACEHOLDER": "Select groups...", "MEETING_SCHEDULER.BOOKING_LINK.GROUP_EVENT_TYPES.LIST": "Event types", "MEETING_SCHEDULER.BOOKING_GROUP.CREATE": "New group", "MEETING_SCHEDULER.BOOKING_GROUP.UPDATE": "Edit group", "MEETING_SCHEDULER.BOOKING_GROUP.CREATE_BUTTON": "Create group", "MEETING_SCHEDULER.BOOKING_GROUP.UPDATE_BUTTON": "Update group", "MEETING_SCHEDULER.BOOKING_SERVICE.CREATE": "New service menu", "MEETING_SCHEDULER.BOOKING_SERVICE.UPDATE": "Edit service menu", "MEETING_SCHEDULER.BOOKING_SERVICE.CREATE_BUTTON": "Create service menu", "MEETING_SCHEDULER.BOOKING_SERVICE.UPDATE_BUTTON": "Update service menu", "MEETING_SCHEDULER.BOOKING_SERVICE_LINK.UPDATE_PROMPT_TITLE": "Update service menu link?", "MEETING_SCHEDULER.BOOKING_GROUP_LINK.UPDATE_PROMPT_TITLE": "Update group link?", "MEETING_SCHEDULER.BOOKING_GROUP_LINK.DO_NOT_UPDATE": "Don't update group link", "MEETING_SCHEDULER.BOOKING_GROUP_LINK.DO_UPDATE": "Update group link", "MEETING_SCHEDULER.BOOKING_SERVICE_LINK.DO_NOT_UPDATE": "Don't update service menu link", "MEETING_SCHEDULER.BOOKING_SERVICE_LINK.DO_UPDATE": "Update service menu link", "MEETING_SCHEDULER.BOOKING_LINK.TEAM_MEMBER.TITLE": "Team members", "MEETING_SCHEDULER.BOOKING_LINK.TEAM_MEMBER.SELECTION": "The available time slots on this event link wil reflect each team member's availability.", "MEETING_SCHEDULER.BOOKING_LINK.TEAM_MEMBER.ALL": "All", "MEETING_SCHEDULER.BOOKING_LINK.TEAM_MEMBER.INDIVIDUAL": "Individual", "MEETING_SCHEDULER.BOOKING_LINK.TEAM_MEMBER.INDICATOR": "Some team members haven't setup their calendar. They can still be selected for this service, but won't be scheduled untill they've finished setting up", "MEETING_SCHEDULER.BOOKING_LINK.RESERVE_WITH_GOOGLE": "Reserve With Google", "MEETING_SCHEDULER.BOOKING_LINK.RESERVE_WITH_GOOGLE_DESCRIPTION": "Allow customers to book this service directly from Google Search and Maps. A Google Business Profile account is required.", "MEETING_SCHEDULER.BOOKING_LINK.RESERVE_WITH_GOOGLE_DESCRIPTION.STANDARDS.HEADING": "Reserve with google must follow this standards. Failure to do so may result in your service being deleted by Google:", "MEETING_SCHEDULER.BOOKING_LINK.RESERVE_WITH_GOOGLE_DESCRIPTION.STANDARDS.GUIDELINES_NAME": "The name of your service must clearly reflect what the user will be booking.", "MEETING_SCHEDULER.BOOKING_LINK.RESERVE_WITH_GOOGLE_DESCRIPTION.STANDARDS.GUIDELINES_DESCRIPTION": "The description must accurately state what the service is providing.", "MEETING_SCHEDULER.BOOKING_LINK.RESERVE_WITH_GOOGLE_DESCRIPTION.STANDARDS.GUIDELINES_NAME_AND_DESCRIPTION": "Both the name and description of your service must use proper punctuation and grammer.", "MEETING_SCHEDULER.BOOKING_LINK.RESERVE_WITH_GOOGLE_DESCRIPTION.STANDARDS.GUIDELINES_SERVICES": "The following are not allowed in your service name or description: full capitalization, emojis, vulgar or offensive language, promotional content, URLS, email addresses, phone numbers, or payment methods.", "MEETING_SCHEDULER.BOOKING_LINK.RESERVE_WITH_GOOGLE_DESCRIPTION.STANDARDS.GUIDELINES_MEMBERSHIP": "Your service must be bookable without a membership, or belonging to an organization", "MEETING_SCHEDULER.BOOKING_LINK.BREADCRUMB": "New event type", "MEETING_SCHEDULER.BOOKING_LINK.BREADCRUMB_UPDATE": "Edit event type", "MEETING_SCHEDULER.BOOKING_LINK.CREATED_SUCCESS": "Event type created", "MEETING_SCHEDULER.BOOKING_LINK.CREATED_ERROR": "Something went wrong while creating the event link", "MEETING_SCHEDULER.BOOKING_LINK.EVENTS.DELETE_TITLE": "Delete event type?", "MEETING_SCHEDULER.BOOKING_LINK.GROUP.DELETE_TITLE": "Delete group?", "MEETING_SCHEDULER.BOOKING_LINK.SERVICE.DELETE_TITLE": "Delete service menu?", "MEETING_SCHEDULER.BOOKING_LINK.EVENTS.CANNOT_DELETE_TITLE": "Cannot delete this event type", "MEETING_SCHEDULER.BOOKING_LINK.GROUP.CANNOT_DELETE_TITLE": "Cannot delete this group", "MEETING_SCHEDULER.BOOKING_LINK.EVENTS.DELETE_CONTENT": "Deleting an event type will permanently remove the link.\n\n• The link will no longer work\n• Already booked meetings will not be cancelled and can still be modified", "MEETING_SCHEDULER.BOOKING_LINK.GROUP.DELETE_CONTENT": "Deletion is permanent.\n\nIf you have used the group link in emails or websites, they will not work.", "MEETING_SCHEDULER.BOOKING_LINK.SERVICE.DELETE_CONTENT": "Deletion is permanent.\n\nIf you have used the service menu link in emails or websites, they will not work.", "MEETING_SCHEDULER.BOOKING_LINK.EVENTS.CANNOT_DELETE_CONTENT": "Deletion is not allowed. This event type is associated with other group or service menus. Please remove those references first.", "MEETING_SCHEDULER.BOOKING_LINK.GROUP.CANNOT_DELETE_CONTENT": "Deletion is not allowed. This group associated with other service menus. Please remove those references first.", "MEETING_SCHEDULER.BOOKING_LINK.SERVICE.CANNOT_DELETE.HEADER": "Service menu: ({{count}} associations)", "MEETING_SCHEDULER.BOOKING_LINK.GROUP.CANNOT_DELETE.HEADER": "Group: ({{count}} associations)", "MEETING_SCHEDULER.BOOKING_LINK.UPDATED_SUCCESS": "Event type updated", "MEETING_SCHEDULER.BOOKING_LINK.UPDATED_ERROR": "Something went wrong while updating the event link", "MEETING_SCHEDULER.BOOKING_LINK.DELETED_SUCCESS": "Event link deleted", "MEETING_SCHEDULER.BOOKING_LINK.DELETED_ERROR": "Something went wrong while deleting the event link", "MEETING_SCHEDULER.BOOKING_LINK.MEETING_DURATION_ERROR": "Duration must be at least 15 minutes", "MEETING_SCHEDULER.BOOKING_LINK.CREATE": "Create event type", "MEETING_SCHEDULER.BOOKING_LINK.UPDATE": "Update event type", "MEETING_SCHEDULER.BOOKING_LINK.UPDATE_PROMPT_TITLE": "Update event link?", "MEETING_SCHEDULER.BOOKING_LINK.UPDATE_PROMPT_CONTENT": "Updating the link from {$INTERPOLATION} to {$INTERPOLATION_1} will stop guests from being able to book a meeting with you at {$INTERPOLATION_2}. Do you still want to update the link?", "MEETING_SCHEDULER.BOOKING_LINK.UPDATE_PROMPT_CONTENT.UPDATING_THE_LINK": "Updating the link from", "MEETING_SCHEDULER.BOOKING_LINK.UPDATE_PROMPT_CONTENT.TO": "to", "MEETING_SCHEDULER.BOOKING_LINK.UPDATE_PROMPT_CONTENT.WILL_STOP_GUESTS_FROM_BEING_ABLE_TO_BOOK": "will stop guests from being able to book a meeting with you at", "MEETING_SCHEDULER.BOOKING_LINK.UPDATE_PROMPT_CONTENT.DO_YOU_STILL_WANT_TO_UPDATE_THE_LINK": "Do you still want to update the link?", "MEETING_SCHEDULER.BOOKING_LINK.EVENTS.DO_NOT_DELETE": "Keep event type", "MEETING_SCHEDULER.BOOKING_LINK.EVENTS.DO_DELETE": "Delete event type", "MEETING_SCHEDULER.BOOKING_LINK.GROUP.DO_NOT_DELETE": "Keep group", "MEETING_SCHEDULER.BOOKING_LINK.GROUP.DO_DELETE": "Delete group", "MEETING_SCHEDULER.BOOKING_LINK.SERVICE.DO_NOT_DELETE": "Keep service menu", "MEETING_SCHEDULER.BOOKING_LINK.SERVICE.DO_DELETE": "Delete service menu", "MEETING_SCHEDULER.BOOKING_LINK.DO_NOT_UPDATE": "Don't update event link", "MEETING_SCHEDULER.BOOKING_LINK.DO_UPDATE": "Update event link", "MEETING_SCHEDULER.BOOKING_LINK.NEW": "Add", "MEETING_SCHEDULER.BOOKING_LINK.NEW_PERSONAL_MEETING": "New personal meeting", "MEETING_SCHEDULER.BOOKING_LINK.NEW_PERSONAL_MEETING.ONE_ON_ONE": "Allow your customers to book a time to meet with you one on one", "MEETING_SCHEDULER.BOOKING_LINK.BUSINESS.NEW": "New", "MEETING_SCHEDULER.BOOKING_LINK.BUSINESS.SERVICE": "service", "MEETING_SCHEDULER.BOOKING_LINK.BUSINESS.BUSINESS_OFFERS": "Allow your customers to book a service your business offers", "MEETING_SCHEDULER.BOOKING_LINK.SELECT": "Select", "MEETING_SCHEDULER.BOOKING_LINK.LINK_VALIDATION_REQUIRED": "A link must be provided", "MEETING_SCHEDULER.BOOKING_LINK.LINK_VALIDATION_PATTERN": "A link must contain only letters, numbers, -, and _", "MEETING_SCHEDULER.BOOKING_LINK.LINK_VALIDATION_UNIQUE": "You're already using this link", "MEETING_SCHEDULER.BOOKING_LINK.LINK_VALIDATION_UNKNOWN": "An unknown error occurred", "MEETING_SCHEDULER.BOOKING_LINK.NAME_VALIDATION_REQUIRED": "Name is required", "MEETING_SCHEDULER.BOOKING_LINK.GROUP_EVENT_TYPE_VALIDATION_REQUIRED": "You must select more than one group or event type.", "MEETING_SCHEDULER.BOOKING_LINK.EVENT_TYPE_VALIDATION_REQUIRED": "You must select more than one event type.", "MEETING_SCHEDULER.BOOKING_LINK.TEAM_MEMBERS_VALIDATION_REQUIRED": "You must select atleast {{count}} team member.", "MEETING_SCHEDULER.BOOKING_LINK.MAX_MULTI_HOST_TEAM_MEMBERS_VALIDATION_REQUIRED": "You can select a max of {{count}} team member for this event type.", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.TITLE": "Questions for invitee", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.DEFAULT_FIRST_NAME": "First name", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.DEFAULT_LAST_NAME": "Last name", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.DEFAULT_EMAIL": "Email", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.DEFAULT_PHONE_NUMBER": "Phone number", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.DEFAULT_COMMENTS": "Comments", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.TYPE_TEXT": "Text box", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.TYPE_EMAIL": "Email field", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.TYPE_PHONE_NUMBER": "Phone number field", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.TYPE_TEXT_AREA": "Text area", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.CUSTOM_QUESTIONS_HEADER_TITLE": "Questions", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.COLLAPSE": "Collapse", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.EXPAND": "Expand all", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.REQUIRED": "Required", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.TYPE": "Type", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.LABEL": "Label", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.DELETE": "Delete question", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.ADD": " Add question", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.ERROR_DUPLICATES": "You cannot have two nearly identical questions", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.DESCRIPTION_SUFFIX": "custom questions", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.ERROR_LABEL_MAX": "Max {{max}} characters", "MEETING_SCHEDULER.BOOKING_LINK.QUESTIONS_SECTION.ERROR_LABEL_REQUIRED": "Label is required", "MEETING_SCHEDULER.BOOKING_LINK.DEFAULT.QUESTIONS_SECTION": "Default questions", "MEETING_SCHEDULER.BOOKING_LINK.Custom.QUESTIONS_SECTION": "Custom questions", "MEETING_SCHEDULER.COMMON.DURATIONS.MINUTES": "minutes", "MEETING_SCHEDULER.BOOKING_LINK.EMBED_CODE_SECTION.SLIDE_OUT_TITLE": "Slide out", "MEETING_SCHEDULER.BOOKING_LINK.EMBED_CODE_SECTION.INLINE_TITLE": "Inline embed", "MEETING_SCHEDULER.BOOKING_LINK.EMBED_CODE_SECTION.SLIDE_OUT_EXPLANATION": "Add a floating button to your website that launches the booking form for {{eventType}} in a fixed slide-out", "MEETING_SCHEDULER.BOOKING_LINK.EMBED_CODE_SECTION.INLINE_EXPLANATION": "Add booking form for {{eventType}} directly to your website as a full-page embed. Width is responsive.", "MEETING_SCHEDULER.BOOKING_LINK.EMBED_CODE_SECTION.COPY_PASTE_EXPLANATION": "Copy and paste this embed code in your page's HTML, wherever you want your booking form for {{eventType}} to appear. Scroll bars will automatically appear when the embed exceeds the height of the container.", "MEETING_SCHEDULER.BOOKING_LINK.EMBED_CODE_SECTION.COPY_BUTTON_TEXT": "Copy code", "MEETING_SCHEDULER.BOOKING_LINK.EMBED_CODE_SECTION.COPY_TO_CLIP_BOARD": "Copied to clipboard", "MEETING_SCHEDULER.COMMON.ACTION_LABELS.CANCEL": "Cancel", "MEETING_SCHEDULER.FAILED_TO_LOAD_PERSONAL_CALENDAR": "Failed to load your personal calendar.", "MEETING_SCHEDULER.MEETING_ACTIONS.CANCEL_MEETING": "Cancel meeting", "MEETING_SCHEDULER.MEETING_ACTIONS.ALERTS.CANCELLED_MEETING_SUCCESS": "Your meeting was cancelled", "MEETING_SCHEDULER.MEETING_ACTIONS.ALERTS.CANCELLED_MEETING_ERROR": "Something went wrong when cancelling the meeting. Refresh the page and try again.", "MEETING_SCHEDULER.MEETING_ACTIONS.DIALOG.CANCELLATION.CANCELLATION_TITLE": "Cancel meeting", "MEETING_SCHEDULER.MEETING_ACTIONS.DIALOG.CANCELLATION.CURRENT_MEETING": "Current meeting", "MEETING_SCHEDULER.MEETING_ACTIONS.DIALOG.CANCELLATION.GUEST": "Guest", "MEETING_SCHEDULER.MEETING_ACTIONS.DIALOG.CANCELLATION.CANCELLATION_REASON": "Message to guest (optional)", "MEETING_SCHEDULER.MEETING_ACTIONS.DIALOG.CANCELLATION.CANCELLATION_REASON_PLACEHOLDER": "Cancellation Reason", "MEETING_SCHEDULER.MEETING_ACTIONS.DIALOG.CANCELLATION.DONT_CANCEL_BUTTON": "Don't cancel", "MEETING_SCHEDULER.MEETING_ACTIONS.DIALOG.CANCELLATION.CANCEL_BUTTON": "Cancel meeting", "MEETING_SCHEDULER.MEETING_ACTIONS.RESCHEDULE": "Reschedule", "MEETING_SCHEDULER.MEETING_ACTIONS.RESCHEDULE_MEETING": "Reschedule meeting", "MEETING_SCHEDULER.MEETING_ACTIONS.DIALOG.RESCHEDULE.CURRENT_MEETING": "Current meeting", "MEETING_SCHEDULER.MEETING_ACTIONS.DIALOG.RESCHEDULE.GUEST": "Guest", "MEETING_SCHEDULER.MEETING_ACTIONS.DIALOG.RESCHEDULE.DATE": "Date", "MEETING_SCHEDULER.MEETING_ACTIONS.DIALOG.RESCHEDULE.TIME_SLOT": "Time Slot", "MEETING_SCHEDULER.MEETING_ACTIONS.DIALOG.RESCHEDULE.MESSAGE_TO_GUEST": "Message to guests (optional)", "MEETING_SCHEDULER.MEETING_ACTIONS.DIALOG.RESCHEDULE.MESSAGE_TO_GUEST_PLACEHOLDER": "Message", "MEETING_SCHEDULER.MEETING_ACTIONS.DIALOG.RESCHEDULE.DO_NOT_RESCHEDULE": "Don't reschedule", "MEETING_SCHEDULER.MEETING_ACTIONS.DIALOG.RESCHEDULE.RESCHEDULE_AND_SEND_INVITEE": "Reschedule and send invite", "MEETING_SCHEDULER.MEETING_ACTIONS.ALERTS.RESCHEDULE_MEETING_SAVED": "Your meeting was rescheduled", "MEETING_SCHEDULER.MEETING_ACTIONS.ALERTS.RESCHEDULE_MEETING_ERROR": "This meeting couldn't be updated. Refresh the page and try again.", "MEETING_SCHEDULER.MEETING_ACTIONS.ALERTS.RESCHEDULE_MEETING_ERROR_BUSY": "You are not available during that time. Please select another time", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.CONNECTED": "Connected", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.DISCONNECT_SERVICE": "Disconnect service", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.DISCONNECT": "Disconnect", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.GOOGLE_SIGN_IN": "Sign in", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.GOOGLE_MEET_CONNECTED": "Google Meet account connected", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.GOOGLE_MEET_DISCONNECTED": "Disconnecting Google Meet - check back in a bit", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.ZOOM_CONNECTED": "Zoom account connected", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.ZOOM_DISCONNECTED": "Disconnecting Zoom - check back in a bit", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.NOT_CONNECTED": "Not connected", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.MICROSOFT_SIGN_IN": "Sign in", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.GOOGLE_SIGN_IN_TOOLTIP_TEXT": "Connect your Google account to sync with your Google Calendar.", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.MICROSOFT_SIGN_IN_TOOLTIP_TEXT": "Connect your Microsoft account to sync with Microsoft Exchange Online or Outlook.com calendars.", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.GOOGLE_CALENDAR": "Google Calendar", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.MICROSOFT_CALENDAR": "Microsoft 365 / Outlook.com", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.GOOGLE_DISCONNECT": "Disconnect Google Calendar?", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.MICROSOFT_DISCONNECT": "Disconnect Microsoft 365 / Outlook.com Calendar?", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.DISCONNECT_CONTENT": "A connected primary calendar is required to schedule new meetings\n\n•Existing meetings from the old calendar remain, but new meetings cannot be scheduled until a calendar is connected\n•Rescheduling or Canceling existing meetings may not work as expected\n•Do not disconnect the  calendar unless you plan to connect another one immediately\n•Please note that the associated meeting app will also be disconnected", "MEETING_SCHEDULER.MEETING_INTEGRATIONS.DISCONNECT_CANCEL": "Cancel", "MEETING_SCHEDULER.MEETING_LIST.BREADCRUMB": "My meetings", "MEETING_SCHEDULER.MEETING_LIST.MEETING_TYPE_FILTER_PLACEHOLDER": "Filter by type...", "MEETING_SCHEDULER.MEETING_LIST.CREATE_BOOKING_LINK": "Create event link", "MEETING_SCHEDULER.MEETING_LIST.GENERAL_BOOKING_LINK": "General event link", "MEETING_SCHEDULER.MEETING_LIST.COPY_LINK": "Copy event link", "MEETING_SCHEDULER.MEETING_LIST.PIN_TO_TOP": "Pin to top", "MEETING_SCHEDULER.MEETING_LIST.MORE_OPTIONS": "More options...", "MEETING_SCHEDULER.MEETING_LIST.MEETING_BOOKING_LINKS": "Event links", "MEETING_SCHEDULER.MEETING_LIST.NO_MEETING_TYPES_TITLE": "Customers want to meet with you", "MEETING_SCHEDULER.MEETING_LIST.NO_GROUP_MEETING_TYPES_TITLE": "Customers want to meet with your team", "MEETING_SCHEDULER.MEETING_LIST.NO_MEETING_TYPES_SUBTITLE": "Meet with clients at times that work best for everyone", "MEETING_SCHEDULER.MEETING_LIST.SETTINGS": "Settings", "MEETING_SCHEDULER.MEETING_LIST.USER_SETTINGS": "User settings", "MEETING_SCHEDULER.MEETING_LIST.VIEW_LINK": "View event link", "MEETING_SCHEDULER.MEETING_LIST.DELETE_LINK": "Delete", "MEETING_SCHEDULER.MEETING_LIST.UPCOMING": "Upcoming", "MEETING_SCHEDULER.MEETING_LIST.CALENDAR": "Calendar", "MEETING_SCHEDULER.MEETING_LIST.EVENTS": "Event types", "MEETING_SCHEDULER.MEETING_LIST.GROUP": "Groups", "MEETING_SCHEDULER.MEETING_LIST.SERVICE": "Service menu", "MEETING_SCHEDULER.MEETING_LIST.DEFAULT": "De<PERSON>ults", "MEETING_SCHEDULER.MEETING_LIST.CARDVIEW": "Card view", "MEETING_SCHEDULER.MEETING_LIST.TABLEVIEW": "Table view", "MEETING_SCHEDULER.MEETING_LIST.ALERT_MESSAGE": "We’re excited to announce the launch of our new Table view feature, designed to enhance your booking experience.", "MEETING_SCHEDULER.MEETING_LIST.ALERT_MESSAGE_LINK": "Try now", "MEETING_SCHEDULER.MEETING_LIST.PERSONAL_EVENT_TYPE": "Personal event type", "MEETING_SCHEDULER.MEETING_LIST.TEAM_EVENT_TYPE": "Team event type", "MEETING_SCHEDULER.MEETING_LIST.CREATE_EVENT_DESKTOP": "New event type", "MEETING_SCHEDULER.MEETING_LIST.CREATE_GROUP_DESKTOP": "New group", "MEETING_SCHEDULER.MEETING_LIST.CREATE_SERVICE_DESKTOP": "New service menu", "MEETING_SCHEDULER.MEETING_LIST.CREATE_EVENT_GROUP_SERVICE_MOBILE": "New", "MEETING_SCHEDULER.MEETING_LIST.PERSONAL_EVENT": "Personal", "MEETING_SCHEDULER.MEETING_LIST.TEAM_EVENT": "Team", "MEETING_SCHEDULER.MEETING_LIST.PERSONAL_EVENTS": "Personal events", "MEETING_SCHEDULER.MEETING_LIST.TEAM_EVENTS": "Team events", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.EVENT_NAME": "Event type name", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.LINK": "Link", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.TEAM_NAME": "Team name", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.DESCRIPTION": "Description", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.DURATION": "Duration", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.CATEGORY": "Event type", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.GROUP.NAME": "Group name", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.GROUP.MEMBER_COUNT": "Event type count", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.SERVICE.NAME": "Service menu name", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.SERVICE.MEMBER_COUNT": "Member count", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.EVENTS.EMPTY_STATE_TITLE": "No event types have been created yet.", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.GROUP.EMPTY_STATE_TITLE": "No groups have been created yet.", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.SERVICE.EMPTY_STATE_TITLE": "No service menus have been created yet.", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.EVENTS.EMPTY_STATE_SUBTITLE": "You currently have no event types created. Please create an event type to get started.", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.GROUP.EMPTY_STATE_SUBTITLE": "You currently have no groups created. Please create a group to get started.", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.SERVICE.EMPTY_STATE_SUBTITLE": "You currently have no service menu created. Please create a service menu to get started.", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.EVENTS.NAME": "Event type name", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.DEFAULT_PERSONAL_GROUP.NAME": "General Personal event Link", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.DEFAULT_PERSONAL_GROUP.DESCRIPTION": "This link will allow people to choose between all of your personal meetings", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.DEFAULT_TEAM_GROUP.NAME": "General Team Link - {{teamName}}", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.DEFAULT_TEAM_GROUP.DESCRIPTION": "This link will allow people to choose between all of your {{teamName}} team's meetings", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.PIN.UPDATE.API.ERROR": "Failed to update pin status. Please try again.", "MEETING_SCHEDULER.MEETING_LIST.TABLE.VIEW.PIN.UPDATE.RESTRICTED.ERROR": "Apologies, modifying the default groups is not permitted.", "MEETING_SCHEDULER.MEETING_LIST.UNKNOWN_USER": "Unknown user", "MEETING_SCHEDULER.MEETING_LIST.PAST": "Past", "MEETING_SCHEDULER.MEETING_LIST.ADDITIONAL_GUESTS": "Additional guests", "MEETING_SCHEDULER.MEETING_LIST.COMMENTS": "Comments", "MEETING_SCHEDULER.MEETING_LIST.SCHEDULED_ON": "Scheduled on", "MEETING_SCHEDULER.MEETING_LIST.MEETING_LINK": "Meeting link", "MEETING_SCHEDULER.MEETING_LIST.ATTACHMENT_LINK": "Attachments", "MEETING_SCHEDULER.MEETING_LIST.EMPTY_STATE.PAST_TITLE": "We couldn't find any past meetings", "MEETING_SCHEDULER.MEETING_LIST.EMPTY_STATE.UPCOMING_TITLE": "You don't have any meetings coming up", "MEETING_SCHEDULER.MEETING_LIST.EMPTY_STATE.DESCRIPTION": "Prospects can schedule meetings with you using your meeting link.", "MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.7_DAYS": "Last 7 days", "MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.30_DAYS": "Last 30 days", "MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.90_DAYS": "Last 90 days", "MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.6_MONTHS": "Last 6 months", "MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.12_MONTHS": "Last 12 months", "MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.CUSTOM": "Custom", "MEETING_SCHEDULER.MEETING_LIST.ALERTS.LOAD_MEETING_BUSINESS_LINK": "Could not load links between business and meetings. You may not see linked businesses on the meetings", "MEETING_SCHEDULER.MEETING_LIST.ALERTS.LOAD_MEETING_ADDITIONAL_ACTIONS": "Could not load additional actions. You may not see certain actions on your meetings", "MEETING_SCHEDULER.MEETING_LIST.ALERTS.LINK_OPENED": "Link opened in new tab", "MEETING_SCHEDULER.MEETING_LIST.ALERTS.LINK_COPIED": "Link copied to clipboard", "MEETING_SCHEDULER.MEETING_LIST.ALERTS.DELETE_BOOKING_LINK_SUCCESS": "Your event link was deleted", "MEETING_SCHEDULER.MEETING_LIST.ALERTS.DELETE_GROUP_SUCCESS": "Your group was deleted", "MEETING_SCHEDULER.MEETING_LIST.ALERTS.DELETE_SERVICE_MENU_SUCCESS": "Your service menu was deleted", "MEETING_SCHEDULER.MEETING_LIST.ALERTS.DELETE_BOOKING_LINK_ERROR": "Something went wrong when deleting your event link. Refresh the page and try again.", "MEETING_SCHEDULER.MEETING_LIST.ALERTS.DELETE_GROUP_ERROR": "Something went wrong when deleting your group. Refresh the page and try again.", "MEETING_SCHEDULER.MEETING_LIST.ALERTS.DELETE_SERVICE_MENU_ERROR": "Something went wrong when deleting your service menu. Refresh the page and try again.", "MEETING_SCHEDULER.MEETING_LIST.GROUP.UPDATED_SUCCESS": "Your group was updated", "MEETING_SCHEDULER.MEETING_LIST.GROUP.UPDATED_ERROR": "Something went wrong while updating the group", "MEETING_SCHEDULER.MEETING_LIST.GROUP.CREATED_SUCCESS": "Your group was created", "MEETING_SCHEDULER.MEETING_LIST.GROUP.CREATED_ERROR": "Something went wrong while creating group", "MEETING_SCHEDULER.MEETING_LIST.SERVICE.UPDATED_SUCCESS": "Your service menu was updated", "MEETING_SCHEDULER.MEETING_LIST.SERVICE.UPDATED_ERROR": "Something went wrong while updating the service menu", "MEETING_SCHEDULER.MEETING_LIST.SERVICE.CREATED_SUCCESS": "Your service menu was created", "MEETING_SCHEDULER.MEETING_LIST.SERVICE.CREATED_ERROR": "Something went wrong while creating service menu", "MEETING_SCHEDULER.MEETING_LIST.PERSONAL_SECTION_HEADER": "Your personal event links", "MEETING_SCHEDULER.MEETING_LIST.TEAMS_SECTION_HEADER": "Your team event links", "MEETING_SCHEDULER.MEETING_LIST.PERSONAL_GENERAL_LINK_DESCRIPTION": "This link will allow people to choose between all of your personal meetings", "MEETING_SCHEDULER.MEETING_LIST.GROUP_GENERAL_LINK_DESCRIPTION": "This link will allow people to choose between all of your team's meetings", "MEETING_SCHEDULER.MEETING_LIST.SCHEDULED_EVENTS_SECTION_HEADER": "Your scheduled events", "MEETING_SCHEDULER.MEETING_LIST.EDIT_MEETING_URL": "Edit meeting URL", "MEETING_SCHEDULER.MEETING_LIST.EDIT_URL": "Edit URL", "MEETING_SCHEDULER.MEETING_LIST.EMAIL_LABEL": "Email", "MEETING_SCHEDULER.MEETING_LIST.CONTACT_LABEL": "Phone number", "MEETING_SCHEDULER.MEETING_LIST.COMMENTS_LABEL": "Comments", "MEETING_SCHEDULER.MEETING_LIST.HOST_LABEL": "Host", "MEETING_SCHEDULER.MEETING_LIST.CUSTOM_QUESTION_LABEL": "Custom questions", "MEETING_SCHEDULER.MEETING_LIST.LOCATION_LABEL": "Location", "MEETING_SCHEDULER.MEETING_LIST.Filter_LABEL": "Filters", "MEETING_SCHEDULER.BOOKING_LINK.TEAM_EVENT.LABEL": "Change sales team", "MEETING_SCHEDULER.BOOKING_LINK.TEAM_EVENT.NO_TEAM_ASSIGNED.MESSAGE": "You are not a member of any team yet", "MEETING_SCHEDULER.MEETING_LIST.SALES_TEAM_DIALOG.TITLE": "Select team", "MEETING_SCHEDULER.MEETING_LIST.SALES_TEAM_DIALOG.PLACEHOLDER": "Search team name", "MEETING_SCHEDULER.MEETING_LIST.SALES_TEAM_DIALOG.CLOSE": "Cancel", "MEETING_SCHEDULER.MEETING_LIST.SALES_TEAM_DIALOG.CREATE_EVENT_TYPE": "Create team event type", "MEETING_SCHEDULER.MEETING_TYPE_DROPDOWN.TEAM_BOOKING_LINKS": "Team event links:", "MEETING_SCHEDULER.MEETING_TYPE_DROPDOWN.BOOKING_LINKS": "event links", "MEETING_SCHEDULER.MEETING_TYPE_DROPDOWN.COPY_LINK": "Copy link", "MEETING_SCHEDULER.MEETING_TYPE_DROPDOWN.COPY_GENERAL_LINK": "Copy general event link", "MEETING_SCHEDULER.MEETING_TYPE_DROPDOWN.COPY_BOOKING_LINK": "Copy event link", "MEETING_SCHEDULER.MEETING_TYPE.ALERTS.LINK_OPENED": "Link opened in new tab", "MEETING_SCHEDULER.MEETING_TYPE.ALERTS.LINK_COPIED": "Link copied to clipboard", "MEETING_SCHEDULER.JOIN_WITH_GOOGLE_MEET": "Join with Google Meet", "MEETING_SCHEDULER.JOIN_MEETING": "Join meeting", "MEETING_SCHEDULER.JOIN_WITH_ZOOM": "Join with <PERSON><PERSON>", "MEETING_SCHEDULER.JOIN_WITH_TEAMS": "Join with Microsoft Teams", "MEETING_SCHEDULER.CALENDER_VIEW.MEETING_LIST.JOIN": "Join", "MEETING_SCHEDULER.CALENDER_VIEW.MEETING_LIST.EDIT": "Edit", "MEETING_SCHEDULER.CALENDER_VIEW.MEETING_LIST.CANCEL": "Cancel", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.EDIT_START": "Start", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.EDIT_END": "End", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.COMMON_AVAILABILITY.HEADER": "Common Host Availability:", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.NO_CALENDAR_AVAILABILITY": "There are no common intervals available among the selected hosts.<br>Please select different hosts or adjust the general availability (My Meetings/Settings/Defaults).", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.EDIT_ADD_INTERVAL": "+ Add interval", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.EDIT_CANCEL": "Cancel", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.EDIT_SAVE": "Save", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.EDIT_NO_DISALLOWED_TIMES": "All times must be 15 minutes intervals", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.EDIT_NO_STARTS_AFTER_ENDS": "All intervals must end after they start", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.EDIT_NO_ZERO_DURATION_RANGES": "All intervals must span some time", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.EDIT_NO_INTERSECTIONS": "Intervals must not overlap", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.EDIT_NO_DUPLICATES": "Intervals must not be the same", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.EDIT_UNKNOWN_ERROR": "Unknown error", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.EDIT_TITLE": "Edit {$INTERPOLATION} availability", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.TITLE": "General availability", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.PROMPT": "Set the time you normally can accept meetings", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.UNAVAILABLE": "unavailable", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.MANDATORY": "Availability needs to be set for selected meeting type.", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.NO_AVAILABILITY": "No overlapping availability found. Please check team members selected or date/time slot chosen.", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.AVAILABILITY.MIN_DURATION": "Max overlap time available is {{duration}} min. Ensure duration + before buffer + after buffer is less than/equal to {{duration}} min ", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.NOTE_TIME_ZONE": "Timezone: ", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.BUTTONS.SAVE": "Save", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.BLANK_AVAILABILITY_SAVED": "Your availability was turned off", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.AVAILABILITY_SAVED": "Your availability was updated", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.AVAILABILITY_SAVED_ERROR": "Error updating availability", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.AVAILABILITY_TIME_REQUIRED_ERROR": "Time is required", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.AVAILABILITY_LOAD_ERROR": "Error loading availability. You may still change it.", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.TITLE": "Set your general availability", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.DESCRIPTION": "We‘ll use it to find openings in your schedule and automatically add new events.\n        Don‘t worry, you will be able to refine these times later.", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.LABELS.START_TIME": "Start", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.LABELS.END_TIME": "End", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.BUTTONS.COMPLETE_SETUP": "Complete setup", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ADD_TO_WEBSITE.TITLE": "Add to my website", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ADD_TO_WEBSITE.PROMPT": "Embed our booking page for effortless appointment scheduling", "MEETING_SCHEDULER.CUSTOMIZE_INVITATION_EMAIL.DESCRIPTION": "Description", "MEETING_SCHEDULER.CUSTOMIZE_INVITATION_EMAIL.SUBJECT": "Subject", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CUSTOMIZE_INVITATION_EMAIL.TITLE": "Customize invitation email", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CUSTOMIZE_INVITATION_EMAIL.DESCRIPTION": "Craft a unique message that reflects your brand's voice and resonates with your audience.", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CUSTOMIZE_INVITATION_EMAIL_SUBJECT.PLACEHOLDER": "Subject", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CUSTOMIZE_INVITATION_EMAIL_DESCRIPTION.PLACEHOLDER": "Description", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.ALERTS.AVAILABILITY_INCREMENT.SUCCESS": "Saved availability increment preference.", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.ALERTS.AVAILABILITY_INCREMENT.ERROR": "Failed to save availability increment preference.", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.ALERTS.BUFFER_DURATION_BEFORE_MEETING.SUCCESS": "Saved before meeting buffer.", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.ALERTS.BUFFER_DURATION_BEFORE_MEETING.ERROR": "Failed to save before meeting buffer.", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.ALERTS.BUFFER_DURATION_AFTER_MEETING.SUCCESS": "Saved after meeting buffer.", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.ALERTS.BUFFER_DURATION_AFTER_MEETING.ERROR": "Failed to save after meeting buffer.", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.ALERTS.NOTICE_TIME.SUCCESS": "Saved notice time.", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.ALERTS.NOTICE_TIME.ERROR": "Failed to save notice time.", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.AVAILABILITY_INCREMENT.TITLE": "Availability increment", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.AVAILABILITY_INCREMENT.DESCRIPTION": "Show my availability in increments of...", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.AVAILABILITY_INCREMENT.HINT": "Increment of the time slots displayed within your booking availability (in minutes)", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUFFER_DURATION.TITLE": "Meeting buffer after (minutes)", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUFFER_DURATION.DESCRIPTION": "Leave this much time free after meetings...", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUFFER_DURATION_BEFORE.TITLE": "Meeting buffer before (minutes)", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUFFER_DURATION_BEFORE.DESCRIPTION": "Leave this much time free before meetings...", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUFFER_DURATION.HINT": "Allow time for travel, taking notes, or other preparation (in minutes)", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CALENDAR_SLUG.TITLE": "Edit general meeting URL", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CALENDAR_SLUG.DESCRIPTION_PERSONAL": "You can customize the URL people use to book meetings with you. This should be something short and easy to remember.", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CALENDAR_SLUG.DESCRIPTION_GROUP": "You can customize the URL people use to book meetings with your team. This should be something short and easy to remember.", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CALENDAR_SLUG.UPDATE_PROMPT_CONTENT": " You are about to change this booking link from {$INTERPOLATION} to {$INTERPOLATION_1}. Guests won't be able to book with you at {$INTERPOLATION} afterword, and you'll need to send the updated link to anyone with the old link.", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CALENDAR_SLUG.DO_NOT_UPDATE": "Cancel update", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CALENDAR_SLUG.DO_UPDATE": "Update booking link", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CALENDAR_SLUG.UPDATED_SUCCESSFULLY": "The booking link has been updated", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CALENDAR_SLUG.UPDATE_ERROR": "The booking link couldn't be updated. Refresh the page to try again.", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CALENDAR_SLUG.ALREADY_TAKEN": "Another booking link is already using this URL. Please choose something else.", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CALENDAR_SLUG.VALIDATION.PATTERN": "Your URL can only consist of numbers and lowercase letters", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CALENDAR_SLUG.VALIDATION.LENGTH": "The end of your URL must be between 3 and 50 characters", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CALENDAR_SLUG.VALIDATION.GENERIC_ERROR": "The end of your URL must be between 3 and 50 characters", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.NOTICE_TIME.TITLE": "Advance notice (hours)", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.NOTICE_TIME.DESCRIPTION": "I need at least this much time to prevent last-minute bookings", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CALENDAR.TITLE": "Connect calendar", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CALENDAR.DESCRIPTION": "We'll use it to check for conflicts and create events", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.MEETING_INTEGRATION.TITLE": "Choose meeting app", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.MEETING_INTEGRATION.DESCRIPTION": "Which app would you like to make your default?", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CONNECTIONS.CONNECTED": "Connected", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CONNECTIONS.DISCONNECTED": "Disconnected", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CONNECTIONS.NOT_CONNECTED": "Not connected", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CONNECTIONS.COMING_SOON": "Coming soon", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CONNECTIONS.GOOGLE_MEET_CONNECTED": "Google Meet account connected", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CONNECTIONS.ZOOM_CONNECTED": "Zoom account connected", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CONNECTIONS.GOOGLE_MEET_DISCONNECTED": "Disconnecting Google Meet - check back in a bit", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CONNECTIONS.ZOOM_DISCONNECTED": "Disconnecting Zoom - check back in a bit", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CONNECTIONS.CONNECT_GOOGLE_MEET_TO_CONTINUE": "Connect Google Meet account to continue", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CONNECTIONS.CONNECT_MICROSOFT_TO_CONTINUE": "Connect Microsoft account to continue", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.CONNECTIONS.CONNECT_ZOOM_TO_CONTINUE": "Connect Zoom account to continue", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.SET_PREFERRED_CALENDAR_SUCCESS": "Saved calendar preference.", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.SET_PREFERRED_CALENDAR_ERROR": "Failed to set calendar preference. Please try again.", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.SET_PREFERRED_MEETING_APP_ERROR": "Failed to set meeting app preference. Please try again", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.SET_PREFERRED_MEETING_APP_SUCCESS": "Saved meeting app preference.", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.COMPLETE_SETUP": "Complete setup to get a link where customers can schedule meetings with you", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUTTONS.NEXT": "Next", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUTTONS.SETUP_LATER": "Setup later", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BUTTONS.SKIP": "<PERSON><PERSON>", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.NOTE_BROWSER_TIME_ZONE": "Browser timezone: ", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.NOTE_DISPLAYED_TIME_ZONE": "Displayed timezone: ", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.BREADCRUMB": "Settings", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.WIZARD": "View setup wizard", "MEETING_SCHEDULER.SCHEDULE_MEETING_SETTINGS.VIEW_SETTINGS": "View settings", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.BREADCRUMB": "Settings", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.STEPS.STEP_1": "Set URL", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.STEPS.STEP_2": "Connect calendar", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.STEPS.STEP_3": "Choose meeting app", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.STEPS.STEP_4": "Set availability", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.COMPLETE_SETUP_ERROR": "Error when completing setup", "MEETING_SCHEDULER.COMMON.DAYS.SUNDAY": "Sun", "MEETING_SCHEDULER.COMMON.DAYS.MONDAY": "Mon", "MEETING_SCHEDULER.COMMON.DAYS.TUESDAY": "<PERSON><PERSON>", "MEETING_SCHEDULER.COMMON.DAYS.WEDNESDAY": "Weds", "MEETING_SCHEDULER.COMMON.DAYS.THURSDAY": "<PERSON><PERSON><PERSON>", "MEETING_SCHEDULER.COMMON.DAYS.FRIDAY": "<PERSON><PERSON>", "MEETING_SCHEDULER.COMMON.DAYS.SATURDAY": "Sat", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.CONNECTED_SUCESSFULLY": "Connected successfully.", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.DISCONNECTED_SUCESSFULLY": "Disconnected successfully.", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.GENERIC_ERROR": "Something went wrong. Please try again later.", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.CALENDAR_CONNECTED": "Calendar connected", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.CALENDAR_DISCONNECTED": "Calendar disconnected", "MEETING_SCHEDULER.MEETINGS_SHARED.COPY_LINK": "Copy link", "MEETING_SCHEDULER.MEETINGS_SHARED.LINK_OPENED": "Link opened in new tab", "MEETING_SCHEDULER.MEETINGS_SHARED.ALERTS.LINK_COPIED": "Link copied", "MEETING_SCHEDULER.MEETINGS_SHARED.ALERTS.LINK_OPENED": "Link opened in new tab", "MEETING_SCHEDULER.CALENDAR.STATIC_CHANGE_WARNING_PERSONAL": "Updating this URL will prevent people from booking using the old URL. Events that are already scheduled will not be affected.", "MEETING_SCHEDULER.CALENDAR.STATIC_CHANGE_WARNING_GROUP": "Updating this URL will prevent people from booking with you using your team's old link. Events that are already scheduled will not be affected.", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.CONTACT": "Contact", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.CONTACT.PLACEHOLDER": "Contact", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.CONTACT.EMPTY_STATE": "No user found", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.CONTACT.MISSING_EMAIL": "Missing email id", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.CONTACT.CREATE_CONTACT.MISSING_EMAIL": "The created contact doesn't contain email id. Kindly add email to the contact to schedule meeting.", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.ADD_CONTACT": "+Add contact", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.GUEST_EMAIL": "Enter additional guest email", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.GUEST_EMAIL.PLACEHOLDER": "Additional guest email(s)", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.EVENT_TYPE": "Event type", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.EVENT_TYPE.PLACEHOLDER": "Search event type", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.EVENT_TYPE_END_DATE_PAST": "*No time slots available, the event type's date range ended on", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.LOCATION": "Location", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.LOCATION.PLACEHOLDER": "Enter meeting location", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.HOST": "Host", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.TEAM_MEMBER": "Team member", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.TEAM_MEMBER.ANY": "--- Any ---", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.TEAM_MEMBER.PLACEHOLDER": "Select team member", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.DATE_TIME": "Date & Time", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.DATE_TIME.DATE.PLACEHOLDER": "Select meeting date", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.DATE_TIME.TIME.PLACEHOLDER": "Select meeting time", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.CLOSE": "Cancel", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.BOOK_MEETING": "Book a meeting", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.SUCCESS_MESSAGE": "Meeting has been successfully booked.", "MEETING_SCHEDULER.MEETING_ACTION.DIALOG.ERROR_MESSAGE": "Failed to booking meeting.", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.DATE_RANGE.TITLE": "Limit future meetings", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.DATE_RANGE.PROMPT": "Limits how far in the future this event can be booked", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.DATE_RANGE.START_DATE_REQUIRED": "Start date is required", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.DATE_RANGE.END_DATE_REQUIRED": "End date is required", "MEETING_SCHEDULER.SCHEDULE_SETTINGS.ALERTS.DATE_RANGE.INVALID_RANGE": "End date must be after start date"}