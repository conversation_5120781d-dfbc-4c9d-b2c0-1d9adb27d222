<ng-template #loader>
  <div class="mat-elevation-z1 shimmer-container">
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>

    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
    <div class="stencil-shimmer shimmer"></div>
  </div>
</ng-template>
<div>
  <ng-container *ngIf="meetings$ | async as meetings; else loader">
    <mat-tab-group
      (selectedIndexChange)="changedMeetingListTab($event)"
      class="meeting-list-tabs"
      mat-stretch-tabs="false"
      mat-align-tabs="start"
      animationDuration="0ms"
    >
      <mat-tab label="{{ 'MEETING_SCHEDULER.MEETING_LIST.UPCOMING' | translate }}"></mat-tab>
      <mat-tab label="{{ 'MEETING_SCHEDULER.MEETING_LIST.PAST' | translate }}"></mat-tab>
    </mat-tab-group>
    <div class="page-container">
      <div class="meeting-list-filters">
        <meeting-scheduler-meeting-filter
          [meetingTypes]="allMeetingTypes$ | async"
          [selectedFilter]="selectedEventTypeFilter"
          (meetingTypeIdsSelected)="filterByMeetingTypeIds($event)"
        ></meeting-scheduler-meeting-filter>
        <div class="date-selector-style">
          <forms-time-range-selector
            class="meeting-list-time-filter"
            *ngIf="selectedTab === 'past'"
            [timeRangeOption]="timeRangeKeySelected"
            [timeRangeOptions]="timeRangeOptions"
            [max]="now"
            (dateRangeChange)="setDateRange($event)"
            (timeRangeChange)="setTimeRangeKey($event)"
          ></forms-time-range-selector>
        </div>
      </div>
      <div class="margin-top-component"></div>
      <ng-container *ngIf="(hideLoader$ | async) && meetings && meetings.length > 0; else nomeetings">
        <div class="pad-style">
          <mat-accordion>
            <ng-container *ngFor="let meeting of dataSource$ | async; let i = index">
              <div *ngIf="!isSameDate(meeting?.date.start | date: 'EEEE, MMM d, y', i)" class="date">
                {{ meeting?.date.start | date: ' MMM d, y' }}
              </div>

              <mat-expansion-panel class="expansion-panel">
                <mat-expansion-panel-header>
                  <mat-panel-title class="expansion-panel-title">
                    <div class="flex-class">
                      <div class="margin-right-10">
                        <mat-icon
                          class="expansion-panel-header-icon"
                          *ngIf="meeting?.location === '' && meeting?.joinMeeting !== ''"
                          >local_phone</mat-icon
                        >
                        <mat-icon
                          class="expansion-panel-header-icon"
                          *ngIf="meeting?.location !== '' && meeting?.joinMeeting === ''"
                        >
                          location_on
                        </mat-icon>
                        <mat-icon
                          class="expansion-panel-header-icon"
                          *ngIf="meeting?.location !== '' && meeting?.joinMeeting !== ''"
                        >
                          business
                        </mat-icon>
                      </div>
                      <div>
                        <div class="title-text">
                          {{ meeting.date.start | date: 'shortTime' }} - {{ meeting?.date.end | date: 'shortTime' }}
                        </div>
                        <span class="subtitle-text">
                          {{ meeting.title }} with
                          {{
                            meeting?.guest === 'undefined undefined' ? meeting?.contact.email : meeting?.guest || '-'
                          }}
                        </span>
                      </div>
                    </div>
                    <div (click)="stopPropagation($event)">
                      <div class="expansion-panel-header-button-group">
                        <button
                          mat-flat-button
                          color="primary"
                          class="expansion-panel-header-button-width margin-right-10"
                          *ngIf="meeting?.joinMeeting"
                          (click)="openMeetingLink(meeting?.joinMeeting)"
                        >
                          <mat-icon class="join-button-icon">video_call</mat-icon>
                          {{ 'MEETING_SCHEDULER.CALENDER_VIEW.MEETING_LIST.JOIN' | translate }}
                        </button>
                        <button
                          mat-stroked-button
                          class="expansion-panel-header-button-width"
                          [matMenuTriggerFor]="modifyMeeting"
                        >
                          <mat-icon class="edit-button-icon">edit</mat-icon>
                          {{ 'MEETING_SCHEDULER.CALENDER_VIEW.MEETING_LIST.EDIT' | translate }}
                        </button>
                        <mat-menu #modifyMeeting="matMenu" class="modify-meeting-menu">
                          <button mat-menu-item>
                            <meeting-scheduler-reschedule-meeting-button
                              [meeting]="meeting"
                              [isMatItem]="true"
                            ></meeting-scheduler-reschedule-meeting-button>
                          </button>
                          <button mat-menu-item>
                            <meeting-scheduler-cancel-meeting-button
                              [meeting]="meeting"
                              [isMatItem]="true"
                              [displayText]="'MEETING_SCHEDULER.CALENDER_VIEW.MEETING_LIST.CANCEL' | translate"
                            ></meeting-scheduler-cancel-meeting-button>
                          </button>
                        </mat-menu>
                      </div>
                    </div>
                  </mat-panel-title>
                </mat-expansion-panel-header>
                <div class="flex-class">
                  <div class="expansion-panel-body-item-width">
                    <div class="meeting-subtitle">
                      {{ 'MEETING_SCHEDULER.MEETING_LIST.EMAIL_LABEL' | translate }}
                    </div>
                    <div class="comment">{{ meeting?.contact?.email || '—' }}</div>
                  </div>
                  <div class="expansion-panel-body-item-width">
                    <div class="meeting-subtitle">
                      {{ 'MEETING_SCHEDULER.MEETING_LIST.CONTACT_LABEL' | translate }}
                    </div>
                    <div class="comment">{{ meeting?.contact?.phoneNumber || '—' }}</div>
                  </div>
                </div>
                <div class="flex-class">
                  <div class="expansion-panel-body-item-width">
                    <div class="meeting-subtitle">
                      {{ 'MEETING_SCHEDULER.MEETING_LIST.LOCATION_LABEL' | translate }}
                    </div>
                    <div class="comment">
                      <div>{{ meeting?.location || '—' }}</div>
                    </div>
                  </div>
                  <div class="expansion-panel-body-item-width">
                    <div class="meeting-subtitle">
                      {{ 'MEETING_SCHEDULER.MEETING_LIST.MEETING_LINK' | translate }}
                    </div>
                    <div class="comment">
                      <ng-container *ngIf="meeting?.joinMeeting; else noLink">
                        <a class="meeting-link" (click)="openMeetingLink(meeting?.joinMeeting)">
                          {{
                            meeting?.joinMeeting?.length > 40
                              ? meeting?.joinMeeting?.substring(0, 40) + '...'
                              : meeting?.joinMeeting
                          }}
                        </a>
                      </ng-container>
                      <ng-template #noLink> — </ng-template>
                    </div>
                  </div>
                </div>

                <div *ngIf="meeting?.hostNames.length > 0">
                  <div class="meeting-subtitle">
                    {{ 'MEETING_SCHEDULER.MEETING_LIST.HOST_LABEL' | translate }}
                  </div>
                  <div class="comment">{{ meeting?.hostNames.join(', ') || '—' }}.</div>
                </div>

                <div class="meeting-subtitle">
                  {{ 'MEETING_SCHEDULER.MEETING_LIST.COMMENTS_LABEL' | translate }}
                </div>
                <div class="comment">{{ meeting?.comments || '—' }}</div>

                <div *ngIf="meeting?.formAnswers.length > 0">
                  <div class="meeting-subtitle">
                    {{ 'MEETING_SCHEDULER.MEETING_LIST.CUSTOM_QUESTION_LABEL' | translate }}
                  </div>
                  <div *ngFor="let answer of meeting?.formAnswers" class="flex-item padding-left-10">
                    <div class="meeting-subtitle">{{ answer.label }}</div>
                    <div class="comment">{{ answer.answer.join(', ') }}</div>
                  </div>
                </div>
              </mat-expansion-panel>
            </ng-container>
          </mat-accordion>
        </div>
      </ng-container>
    </div>
  </ng-container>

  <ng-template #nomeetings>
    <div *ngIf="hideLoader$ | async; else loadingPage">
      <div class="pad-style">
        <mat-card appearance="outlined">
          <mat-card-content>
            <div class="">
              <glxy-empty-state [size]="'small'" class="empty-state-width">
                <div class="empty-state-container">
                  <div class="empty-state-hero-title">
                    <glxy-empty-state-hero>
                      <mat-icon>date_range</mat-icon>
                    </glxy-empty-state-hero>
                    <glxy-empty-state-title>
                      {{
                        (selectedTab === 'past'
                          ? 'MEETING_SCHEDULER.MEETING_LIST.EMPTY_STATE.PAST_TITLE'
                          : 'MEETING_SCHEDULER.MEETING_LIST.EMPTY_STATE.UPCOMING_TITLE'
                        ) | translate
                      }}
                      <mat-card-subtitle class="empty-state-subtitle">{{
                        'MEETING_SCHEDULER.MEETING_LIST.EMPTY_STATE.DESCRIPTION' | translate
                      }}</mat-card-subtitle>
                    </glxy-empty-state-title>
                  </div>
                </div>
              </glxy-empty-state>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </ng-template>
  <ng-template #loadingPage>
    <glxy-loading-spinner size="large" [fullWidth]="true" [fullHeight]="true"></glxy-loading-spinner>
  </ng-template>
</div>
