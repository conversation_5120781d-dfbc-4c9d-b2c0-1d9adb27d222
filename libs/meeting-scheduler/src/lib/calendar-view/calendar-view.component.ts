import { TranslateService } from '@ngx-translate/core';
import moment from 'moment-timezone';
import { BehaviorSubject, combineLatest, Observable, of, Subscription } from 'rxjs';
import { openSecureNewTab } from '../utils';
import {
  GOOGLE_MAP_URL_PREFIX,
  MEETING_SCHEDULER_CONTEXT_INJECTION_TOKEN$,
  MS_CONTEXT,
  POSTHOG_KEYS,
  POSTHOG_CATEGORIES,
  POSTHOG_ACTIONS,
} from '../constants';
import { ChangeDetectionStrategy, Component, Inject, OnDestroy, OnInit } from '@angular/core';
import {
  AttachmentInterface,
  Calendar,
  Meeting,
  MeetingFormAnswer,
  MeetingType,
  TeamEventMeetingType,
} from '@vendasta/meetings';
import {
  catchError,
  distinctUntilChanged,
  map,
  shareReplay,
  skip,
  startWith,
  switchMap,
  take,
  tap,
} from 'rxjs/operators';
import {
  GroupMeetingType,
  isGroupMeetingType,
  MeetingSchedulerStoreService,
} from '../data-providers/meeting-scheduler-store.service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

export interface CalenderViewDataSource {
  meetingId: string;
  title: string;
  joinMeeting: string;
  date: { start: Date; end: Date };
  guest: string;
  contact: { email: string; phoneNumber: string };
  additionalGuestEmails: string[];
  comments: string;
  location: string;
  created: Date;
  formAnswers: MeetingFormAnswer[];
  attachments: AttachmentInterface[];
  calendarId: string;
  meetingType: TeamEventMeetingType;
  hostNames: string[];
}

interface TimeSpan {
  start: Date;
  end: Date;
}

interface LoadMeetingFilters {
  meetingTypeIds: string[];
  timeSpan: TimeSpan;
}

@Component({
  selector: 'meeting-scheduler-calendar-view',
  templateUrl: './calendar-view.component.html',
  styleUrls: ['./calendar-view.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class CalenderViewComponent implements OnInit, OnDestroy {
  meetings$: Observable<Meeting[]>;
  dataSource$: Observable<CalenderViewDataSource[]>;
  allMeetingTypes$: Observable<MeetingType[]>;
  personalEventTypeMeetings$: Observable<Calendar>;
  personalEventTypes$: Observable<MeetingType[]>;
  teamEventTypeMeetings$: Observable<{ [calendarId: string]: GroupMeetingType[] }>;
  teamEventTypes$: Observable<MeetingType[]> = of(null);

  // to identify new Date meeting
  previousDate = '';

  hideLoader$ = new BehaviorSubject<boolean>(false);
  initialLoadComplete = false;

  eventTypeFilter$$: BehaviorSubject<string[]> = new BehaviorSubject([]);
  meetingTimeSpanFilter$$: BehaviorSubject<TimeSpan> = new BehaviorSubject({
    start: new Date(),
    end: this.getTenYearsLater(),
  });
  selectedTab: 'upcoming' | 'past' = 'upcoming';
  now = new Date();
  selectedEventTypeFilter: string;
  timeRangeKeySelected = '90days';
  timeRangeOptions = [
    {
      key: '7days',
      name: this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.7_DAYS'),
      dateRange: [moment(this.now).subtract(7, 'days').toDate(), this.now],
    },
    {
      key: '30days',
      name: this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.30_DAYS'),
      dateRange: [moment(this.now).subtract(30, 'days').toDate(), this.now],
    },
    {
      key: '90days',
      name: this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.90_DAYS'),
      dateRange: [moment(this.now).subtract(90, 'days').toDate(), this.now],
    },
    {
      key: '6months',
      name: this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.6_MONTHS'),
      dateRange: [moment(this.now).subtract(6, 'months').toDate(), this.now],
    },
    {
      key: '12months',
      name: this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.12_MONTHS'),
      dateRange: [moment(this.now).subtract(12, 'months').toDate(), this.now],
    },
    {
      key: 'custom',
      name: this.translate.instant('MEETING_SCHEDULER.MEETING_LIST.TIME_FILTER.CUSTOM'),
      dateRange: [moment(this.now).subtract(90, 'days').toDate(), this.now],
    },
  ];
  private subscription: Subscription;

  constructor(
    private readonly meetingSchedulerStoreService: MeetingSchedulerStoreService,
    private readonly translate: TranslateService,
    @Inject(MEETING_SCHEDULER_CONTEXT_INJECTION_TOKEN$) protected context: MS_CONTEXT,
    private readonly analyticsService: ProductAnalyticsService,
  ) {}

  ngOnInit(): void {
    this.meetingSchedulerStoreService
      .loadHasHostEverBeenConfigured()
      .pipe(take(1))
      .subscribe((hasHostEverBeenConfigured) => {
        if (hasHostEverBeenConfigured)
          this.analyticsService.trackEvent(
            POSTHOG_KEYS.VIEW_MY_MEETINGS,
            POSTHOG_CATEGORIES.USER,
            POSTHOG_ACTIONS.CLICK,
          );
      });

    this.personalEventTypes$ = this.meetingSchedulerStoreService.loadPersonalMeetingTypes({ returnCache: false }).pipe(
      map((meetingTypes) => {
        if (!meetingTypes) {
          return null;
        }
        return meetingTypes.sort((mt1, mt2) => {
          return mt1.name.localeCompare(mt2.name, undefined, { numeric: true });
        });
      }),
    );
    this.personalEventTypeMeetings$ = this.meetingSchedulerStoreService.loadPersonalCalendar();
    this.teamEventTypeMeetings$ = this.meetingSchedulerStoreService.loadGroupMeetingTypes({ returnCache: true });

    this.teamEventTypes$ = this.teamEventTypeMeetings$.pipe(
      map((teamMeetingTypes) => meetingTypeMapToArray(teamMeetingTypes)),
    );

    this.allMeetingTypes$ = combineLatest([this.personalEventTypes$, this.teamEventTypes$]).pipe(
      map(([personalMeetings, teamMeetings]) => {
        return sortAndJoinMeetingsTypes(personalMeetings, teamMeetings);
      }),
      shareReplay(1),
    );

    const loadMeetingFilters$ = this.setupLoadMeetingFilters();
    this.meetings$ = loadMeetingFilters$.pipe(
      switchMap((meetingTypeFilter) => this.loadMeetings(meetingTypeFilter)),
      shareReplay(1),
    );

    this.dataSource$ = combineLatest([this.meetings$, this.allMeetingTypes$]).pipe(
      map(([meetings, meetingTypes]) => {
        return meetings
          .filter((m) => m.id !== undefined)
          .map((m) => {
            const primaryAttendee = (m.attendees || []).find((a) => a.isPrimary) || { email: '', phoneNumber: '' };
            const additionalGuestEmails = (m.attendees || []).filter((a) => !a.isPrimary).map((a) => a.email);
            const meetingType = meetingTypes.find((type) => type.id === m.eventTypeId);
            const hostNames: string[] =
              meetingType?.meetingType === TeamEventMeetingType.MULTI_HOST && Array.isArray(m?.hostDetails)
                ? m.hostDetails.map((user) => user.name)
                : [];

            return {
              meetingId: m.id,
              title: meetingType?.name || '',
              joinMeeting: m.joinMeetingUrl.replace('https', 'http') || '',
              date: { start: m.start, end: m.end },
              guest: m.attendees.length > 0 ? m.attendees[0].firstName + ' ' + m.attendees[0].lastName : '',
              contact: primaryAttendee,
              additionalGuestEmails,
              comments: m.description || '',
              location: m.location,
              created: m.created,
              formAnswers: m.formAnswers.formAnswers,
              attachments: m.attachments,
              calendarId: m.calendarId,
              eventTypeId: m.eventTypeId,
              meetingType: m.meetingType,
              hostNames,
            };
          });
      }),
      map((meetings) => {
        return this.selectedTab === 'past' ? meetings.reverse() : meetings;
      }),
      shareReplay(1),
    );

    // Skipping the first emission because it's the initial load
    this.teamEventTypes$.pipe(skip(1), take(1)).subscribe((_val) => {
      this.initialLoadComplete = true;
      this.hideLoader$.next(true);
    });

    this.subscription = this.meetings$.subscribe((_meetings) => {
      if (this.initialLoadComplete) {
        this.hideLoader$.next(true);
      }
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  openMeetingLink(url: string): void {
    this.analyticsService.trackEvent(POSTHOG_KEYS.MEETING_JOINED, POSTHOG_CATEGORIES.USER, POSTHOG_ACTIONS.CLICK);
    const w = openSecureNewTab(url);
    if (w && w.focus) {
      w.focus();
    }
  }

  openGoogleMaps(address: string) {
    const url = GOOGLE_MAP_URL_PREFIX + address;
    this.openMeetingLink(url);
  }

  isSameDate(newDate: string, index: number): boolean {
    // Avioding multiple calls that cause previousDate have same value as newDate
    if (index === 0) {
      this.previousDate = newDate;
      return false;
    }

    if (this.previousDate === newDate) {
      return true;
    } else {
      this.previousDate = newDate;
      return false;
    }
  }

  filterByMeetingTypeIds(meetingTypeIds: string[]): void {
    this.selectedEventTypeFilter = meetingTypeIds[0];
    this.eventTypeFilter$$.next(meetingTypeIds);
  }

  stopPropagation(e: Event): void {
    // used to prevent event propagation for anchor tag
    e.stopPropagation();
  }

  changedMeetingListTab(index: number): void {
    this.selectedTab = index === 1 ? 'past' : 'upcoming';

    if (index === 1) return;
    const timeSpan = { start: this.now, end: this.getTenYearsLater() };
    this.meetingTimeSpanFilter$$.next(timeSpan);
  }

  setDateRange(range?: [Date, Date]): void {
    if (this.selectedTab === 'past' && range) {
      // va-time-range-selector emits Dates or Moments
      // (Date|Moment)->Moment->Date handles both
      // va-time-range-selector also doesn't care about times, just dates
      const start = moment(range[0]);
      let end = moment(range[1]);
      const now = moment();
      const endsToday = end.isSame(now, 'day');
      if (endsToday) {
        end.add(now.hour(), 'hour');
        end.add(now.minute(), 'minute');
        end.add(now.second(), 'second');
      } else {
        end = end.endOf('day');
      }
      this.meetingTimeSpanFilter$$.next({
        start: start.toDate(),
        end: end.toDate(),
      });
    }
    if (this.timeRangeKeySelected === 'custom' && range) {
      this.timeRangeOptions.forEach((option) => {
        if (option.key === 'custom') {
          option.dateRange = range;
        }
      });
    }
  }

  setTimeRangeKey(key: string): void {
    this.timeRangeKeySelected = key;
  }

  private getTenYearsLater(): Date {
    const tenYearsLater = new Date();
    tenYearsLater.setFullYear(tenYearsLater.getFullYear() + 10);
    return tenYearsLater;
  }

  private setupLoadMeetingFilters(): Observable<LoadMeetingFilters> {
    const meetingTypeFilter$ = this.eventTypeFilter$$.pipe(startWith([]), distinctUntilChanged());
    const timeSpanFilter$ = this.meetingTimeSpanFilter$$.pipe(
      distinctUntilChanged((a, b) => {
        const aStr = a.start.toISOString() + '-' + a.end.toISOString();
        const bStr = b.start.toISOString() + '-' + b.end.toISOString();
        return aStr === bStr;
      }),
    );
    return combineLatest([meetingTypeFilter$, timeSpanFilter$]).pipe(
      map(([meetingTypeIds, timeSpan]) => {
        return {
          meetingTypeIds,
          timeSpan,
        };
      }),
    );
  }

  private loadMeetings(filters: LoadMeetingFilters): Observable<Meeting[]> {
    const { meetingTypeIds, timeSpan } = filters;

    // Show loader before API call
    this.hideLoader$.next(false);

    return this.meetingSchedulerStoreService
      .loadPersonalMeetings({
        filters: {
          timeSpan: timeSpan,
          meetingTypeIds: meetingTypeIds && meetingTypeIds.length > 0 ? meetingTypeIds : [],
        },
        timeZone: { id: moment.tz.guess() },
        pageSize: 1000,
      })
      .pipe(
        tap(() => {
          // Hide loader after API call completes successfully, but only if initial load is complete
          if (this.initialLoadComplete) {
            this.hideLoader$.next(true);
          }
        }),
        // Use catchError to handle errors and still hide loader
        catchError((error) => {
          if (this.initialLoadComplete) {
            this.hideLoader$.next(true);
          }
          throw error;
        }),
      );
  }
}

function meetingTypeMapToArray(meetingTypeMap: { [key: string]: MeetingType[] }): MeetingType[] {
  return Object.values(meetingTypeMap || {}).reduce((allTeamMeetingTypes, meetingTypes) => {
    return [...allTeamMeetingTypes, ...meetingTypes];
  }, []);
}

function sortAndJoinMeetingsTypes(mts1: MeetingType[], mts2: MeetingType[]): MeetingType[] {
  return [...sortMeetingTypes(mts1), ...sortMeetingTypes(mts2)].map((mt) => ({
    ...mt,
    displayName: isGroupMeetingType(mt) ? `${mt.calendarName}: ${mt.name}` : mt.name,
  }));
}

function sortMeetingTypes(meetingTypes: MeetingType[]): MeetingType[] {
  if (!meetingTypes) {
    return [];
  }
  return meetingTypes.sort((mt1, mt2) => {
    const mt1Name = isGroupMeetingType(mt1) ? `${mt1.calendarName} ${mt1.name}` : mt1.name;
    const mt2Name = isGroupMeetingType(mt2) ? `${mt2.calendarName} ${mt2.name}` : mt2.name;
    return mt1Name.localeCompare(mt2Name, undefined, { numeric: true, sensitivity: 'base' });
  });
}
