import { Injectable } from '@angular/core';
import { RxState } from '@rx-angular/state';
import { CSVHeaderMappingInterface } from '@vendasta/crm';
import { Observable, of } from 'rxjs';

export type Step = 'fileUploadCompleted' | 'fieldMappingCompleted';
export type PermissionType = 'contactPermissions' | 'companyPermissions';
export type CsvRow = { [key: string]: CsvRowValue };
export interface CsvRowValue {
  value: string;
  isDuplicateHeaderName: boolean;
}

@Injectable()
export class BulkImportStepperService {
  csvFilename$: Observable<string> = this.state.select('csvFilename');
  csvRows$: Observable<string> = this.state.select('csvRows');
  csvIsRowEstimate$: Observable<boolean> = this.state.select('csvIsRowEstimate');
  csvFirstRow$: Observable<CsvRow> = this.state.select('csvFirstRow');
  csvMappings$: Observable<CSVHeaderMappingInterface[]> = this.state.select('csvHeaderMappings');
  fileUploadCompleted$: Observable<boolean> = this.state.select('fileUploadCompleted');
  fieldMappingCompleted$: Observable<boolean> = this.state.select('fieldMappingCompleted');
  contactPermissions$: Observable<boolean> = this.state.select('contactPermissions');
  companyPermissions$: Observable<boolean> = this.state.select('companyPermissions');

  constructor(private readonly state: RxState<ComponentState>) {
    this.reset();
  }

  reset(): void {
    const initial: ComponentState = {
      csvFilename: '',
      csvRows: '0',
      csvIsRowEstimate: false,
      csvFirstRow: {},
      csvHeaderMappings: [],
      fileUploadCompleted: false,
      fieldMappingCompleted: false,
      contactPermissions: false,
      companyPermissions: false,
    };
    this.state.set(initial);
  }

  isStepComplete(stepKey: Step): boolean {
    return this.state.get(stepKey);
  }

  completeStep(stepKey: Step, complete: boolean) {
    this.state.set({ [stepKey]: complete });
  }

  hasPermission$(permissionType: PermissionType): Observable<boolean> {
    return of(this.state.get(permissionType));
  }

  setPermission(permissionType: PermissionType, complete: boolean) {
    this.state.set({ [permissionType]: complete });
  }

  setCSVFilename(csvFilename: string) {
    this.state.set({ csvFilename: csvFilename });
  }

  setCSVRows(csvRows: string, isEstimated: boolean): void {
    this.state.set({ csvRows: csvRows, csvIsRowEstimate: isEstimated });
  }

  setCSVFirstRow(csvFirstRow: CsvRow): void {
    this.state.set({ csvFirstRow });
  }

  setCSVHeaderMappings(csvHeaderMappings: CSVHeaderMappingInterface[]): void {
    this.state.set({ csvHeaderMappings });
  }
}

interface ComponentState {
  csvFilename: string;
  csvRows: string;
  csvIsRowEstimate: boolean;
  csvFirstRow: CsvRow;
  csvHeaderMappings: CSVHeaderMappingInterface[];
  fileUploadCompleted: boolean;
  fieldMappingCompleted: boolean;
  contactPermissions: boolean;
  companyPermissions: boolean;
}
