import { CommonModule } from '@angular/common';
import { Component, effect, EventEmitter, Inject, Input, input, OnInit, Output } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import {
  CrmDependencies,
  CrmInjectionToken,
  FieldSchemaInterface,
  ObjectType,
  TranslationModule,
} from '@galaxy/crm/static';
import { BulkImportStepperService } from '../bulk-import-stepper.service';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { FieldMappingService, FieldSchemaGroup } from './field-mapping.service';
import { combineLatest, map, Observable, of, startWith, switchMap, withLatestFrom } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogRef } from '@angular/material/dialog';
import { take } from 'rxjs/operators';
import { toObservable } from '@angular/core/rxjs-interop';
import { MatInput } from '@angular/material/input';

const DoNotImportOption = '';

@Component({
  selector: 'crm-field-mapping-row',
  templateUrl: 'field-mapping-row.component.html',
  providers: [
    {
      provide: MatDialogRef,
      useValue: {},
    },
  ],
  styleUrls: ['field-mapping-row.component.scss'],
  imports: [
    CommonModule,
    TranslationModule,
    FormsModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
    GalaxyFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatInput,
  ],
})
export class CrmFieldMappingRowComponent implements OnInit {
  @Input() contactFieldMappingGroups: FieldSchemaGroup[] = [];
  @Input() companyFieldMappingGroups: FieldSchemaGroup[] = [];
  @Input() header = '';
  importType = input<ObjectType>();
  private readonly importType$ = toObservable(this.importType);

  @Output() initialImportType = new EventEmitter<ObjectType>();

  DoNotImportOption = DoNotImportOption;
  previousSelection = DoNotImportOption;
  searchFieldControl = new UntypedFormControl(DoNotImportOption);

  fieldSchemaGroups: FieldSchemaGroup[] = [];
  filteredSchemaGroups$!: Observable<FieldSchemaGroup[]>;
  initialMappingsSet = false;

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private readonly stepperService: BulkImportStepperService,
    private readonly mappingService: FieldMappingService,
  ) {
    effect(() => {
      const importType = this.importType();
      if (importType) {
        this.setFieldSchemaGroups(importType as ObjectType);
        if (!this.initialMappingsSet) {
          this.initialMappingsSet = true;
        } else {
          this.removeMapping();
        }
      }
    });
  }

  ngOnInit() {
    const searchControl$: Observable<string> = this.searchFieldControl.valueChanges.pipe(startWith(DoNotImportOption));

    this.filteredSchemaGroups$ = combineLatest([searchControl$, this.importType$]).pipe(
      switchMap(([_, importType]) => {
        if (!importType) {
          return of([]);
        }
        return this.mappingService.availableFieldGroups$(importType);
      }),
      withLatestFrom(searchControl$),
      map(([groups, searchValue]) => {
        if (searchValue === DoNotImportOption) {
          return groups;
        }
        const filterValue = searchValue.toLowerCase();
        return groups.map((group) => ({
          ...group,
          schemaOptions: group.schemaOptions.filter(({ fieldName }) => fieldName.toLowerCase().includes(filterValue)),
        }));
      }),
    );

    this.stepperService.csvMappings$
      .pipe(
        map((mappings) => {
          return mappings.filter(({ headerName }) => headerName === this.header);
        }),
        take(1),
      )
      .subscribe((result) => {
        if (result.length > 0) {
          const objectType = result[0]?.crmObjectType || '';
          this.setInitialImportType(objectType as ObjectType);
          const fieldId = result[0]?.fieldId || '';
          this.setControlWithFieldName(fieldId);
        }
      });
  }

  private findField(value: string): FieldSchemaInterface | undefined {
    const fieldSchemaGroups = this.fieldSchemaGroups || [];
    const allSchemas = [];
    for (const { schemaOptions } of fieldSchemaGroups) {
      allSchemas.push(...schemaOptions);
    }
    return allSchemas.find(({ fieldId }) => fieldId === value);
  }

  private findFieldName(value: string): string {
    return this.findField(value)?.fieldName || '';
  }

  private setControlWithFieldName(fieldId: string) {
    if (!fieldId) return;
    const foundFieldName = this.findFieldName(fieldId);
    if (foundFieldName) {
      this.previousSelection = foundFieldName;
      this.searchFieldControl.setValue(this.previousSelection);
    }
  }

  onSelectionChange(selectedId: string): void {
    const importType = this.importType();
    if (!selectedId || !importType) return;
    this.mappingService.addMapping(this.header, selectedId, importType);
    this.setControlWithFieldName(selectedId);
  }

  revertInvalidSelection(): void {
    this.searchFieldControl.setValue(this.previousSelection);
  }

  removeMapping(): void {
    this.mappingService.removeMapping(this.header);
    this.searchFieldControl.setValue(DoNotImportOption);
    this.previousSelection = DoNotImportOption;
  }

  setInitialImportType(objectType: ObjectType): void {
    this.setFieldSchemaGroups(objectType);
    this.initialImportType.emit(objectType);
  }

  setFieldSchemaGroups(objectType: ObjectType): void {
    switch (objectType) {
      case 'Contact':
        this.fieldSchemaGroups = this.contactFieldMappingGroups;
        break;
      case 'Company':
        this.fieldSchemaGroups = this.companyFieldMappingGroups;
        break;
    }
  }
}
