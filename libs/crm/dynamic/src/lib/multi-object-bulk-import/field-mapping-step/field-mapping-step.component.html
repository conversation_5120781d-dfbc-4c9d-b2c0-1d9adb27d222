<p [innerHTML]="instructionsText$ | async"></p>
@for (error of mappingsErrors$ | async; track error) {
  <p class="error-message">
    {{ error }}
  </p>
}
<div class="table-container">
  <table mat-table class="mat-elevation-z0" [dataSource]="dataSource$ | async">
    <ng-container matColumnDef="header">
      <th mat-header-cell *matHeaderCellDef>{{ 'BULK_IMPORT.MAP_FIELDS_STEP.COLUMN_HEADER' | translate }}</th>
      <td mat-cell *matCellDef="let element">
        {{ element.header }}
        <br />
        @if (element.isDuplicateHeaderName) {
          <span class="duplicate-header-message">
            <div>
              {{ 'BULK_IMPORT.MAP_FIELDS_STEP.DUPLICATE_HEADER_NAME' | translate }}
            </div>
            <mat-icon [glxyTooltip]="'BULK_IMPORT.MAP_FIELDS_STEP.DUPLICATE_HEADER_NAME_DESCRIPTION' | translate">
              info_outline
            </mat-icon>
          </span>
        }
      </td>
    </ng-container>

    <ng-container matColumnDef="firstRow">
      <th mat-header-cell *matHeaderCellDef>{{ 'BULK_IMPORT.MAP_FIELDS_STEP.PREVIEW' | translate }}</th>
      <td mat-cell *matCellDef="let element">{{ element.firstRow }}</td>
    </ng-container>

    <ng-container matColumnDef="importAs">
      <th mat-header-cell *matHeaderCellDef>{{ 'BULK_IMPORT.MAP_FIELDS_STEP.IMPORT_AS' | translate }}</th>
      <td mat-cell *matCellDef="let element; let i = index">
        @if ((fieldMappingGroupsLoading$ | async) === false) {
          <ng-container>
            <glxy-form-field size="medium" class="mat-select-import-as">
              <mat-select
                placeholder="Object type"
                [value]="importTypeValues[i]"
                (valueChange)="updateImportTypeValues(i, $event)"
              >
                @if (hasPermission$('contactPermissions') | async) {
                  <mat-option value="Contact">Contact</mat-option>
                }
                @if (hasPermission$('companyPermissions') | async) {
                  <mat-option value="Company">Company</mat-option>
                }
              </mat-select>
            </glxy-form-field>
          </ng-container>
        }
      </td>
    </ng-container>

    <ng-container matColumnDef="mapTo">
      <th mat-header-cell *matHeaderCellDef>{{ 'BULK_IMPORT.MAP_FIELDS_STEP.MAP_TO' | translate }}</th>
      <td mat-cell *matCellDef="let element; let i = index">
        @if ((fieldMappingGroupsLoading$ | async) === false) {
          <crm-field-mapping-row
            [importType]="importTypeValues[i]"
            [header]="element.header"
            [contactFieldMappingGroups]="
              (hasPermission$('contactPermissions') | async) ? (contactFieldMappingGroups$ | async) : []
            "
            [companyFieldMappingGroups]="
              (hasPermission$('companyPermissions') | async) ? (companyFieldMappingGroups$ | async) : []
            "
            (initialImportType)="updateImportTypeValues(i, $event)"
          ></crm-field-mapping-row>
        }
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>
</div>
