import { CommonModule } from '@angular/common';
import { Component, Inject, Input, signal, ViewChild } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MatStepper, MatStepperModule } from '@angular/material/stepper';
import { Router, RouterModule } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { RxState } from '@rx-angular/state';
import {
  CRMImporterApiService,
  DuplicateHandlingType,
  PagedRequestOptionsInterface,
  ListImportRecordsRequestInterface,
} from '@vendasta/crm';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { combineLatest, delay, of, retry, switchMap, take, withLatestFrom } from 'rxjs';
import { TranslationModule, PAGE_ROUTES } from '@galaxy/crm/static';
import { BulkImportStepperService, Step } from './bulk-import-stepper.service';
import { CrmFieldMappingStepComponent } from './field-mapping-step/field-mapping-step.component';
import { FieldMappingService } from './field-mapping-step/field-mapping.service';
import { CrmFileUploadStepComponent } from './file-upload-step/file-upload-step.component';
import { CrmDependencies, CrmInjectionToken } from '@galaxy/crm/static';
import { toSignal } from '@angular/core/rxjs-interop';
import { catchError, map, tap } from 'rxjs/operators';
import { ButtonLoadingIndicatorComponent } from '@vendasta/galaxy/button-loading-indicator';

const DELAY_LIST_RECORD_CALL_MS = 2000;
const RETRY_LIST_RECORD_CALL_AMOUNT = 5;

@Component({
  selector: 'crm-bulk-import-stepper',
  templateUrl: 'bulk-import-stepper.component.html',
  providers: [BulkImportStepperService, FieldMappingService, RxState],
  styleUrls: ['bulk-import-stepper.component.scss'],
  imports: [
    CommonModule,
    TranslationModule,
    RouterModule,
    MatStepperModule,
    MatButtonModule,
    CrmFileUploadStepComponent,
    CrmFieldMappingStepComponent,
    RouterModule,
    MatCheckboxModule,
    MatIconModule,
    GalaxyTooltipModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    ButtonLoadingIndicatorComponent,
  ],
})
export class CrmBulkImportStepperComponent {
  @ViewChild('stepper', { static: true }) pageStepper!: MatStepper;
  @Input({ required: true }) previousPageUrl!: string;
  @Input({ required: true }) previousPageObjectType!: string;

  private routePrefix$ = this.config.routePrefix$;

  protected readonly csvRows$ = this.stepperService.csvRows$;
  private readonly csvEstimatedTotal$ = this.stepperService.csvIsRowEstimate$;
  protected readonly csvFilename$ = this.stepperService.csvFilename$;
  private readonly csvMappings$ = this.stepperService.csvMappings$;
  protected readonly fileUploadCompleted$ = this.stepperService.fileUploadCompleted$;
  protected readonly fieldMappingCompleted$ = this.stepperService.fieldMappingCompleted$;

  protected readonly fieldMappingsErrors = signal('');
  protected readonly updateDuplicateIsChecked = signal(true);
  protected readonly loadingImportFromFile = signal(false);

  private readonly hasImportRecordFeatureFlag = toSignal(this.config.hasImportRecordFeatureFlag$ || of(false));

  protected readonly estimatedText$ = this.csvEstimatedTotal$.pipe(
    switchMap((isEstimate) => {
      if (isEstimate) {
        return this.translate.stream('BULK_IMPORT.REVIEW_IMPORT_STEP.ESTIMATED_TOTAL');
      }
      return of('');
    }),
  );

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private readonly stepperService: BulkImportStepperService,
    private readonly importer: CRMImporterApiService,
    private readonly snack: SnackbarService,
    private readonly translate: TranslateService,
    private readonly router: Router,
  ) {
    this.config.services?.accessService
      ?.canAccessCrmObjectType$?.('Contact')
      .pipe(take(1))
      .subscribe((canAccess) => {
        this.stepperService.setPermission('contactPermissions', canAccess);
      });
    this.config.services?.accessService
      ?.canAccessCrmObjectType$?.('Company')
      .pipe(take(1))
      .subscribe((canAccess) => {
        this.stepperService.setPermission('companyPermissions', canAccess);
      });
  }

  nextStep(stepKey: Step): void {
    if (this.stepperService.isStepComplete(stepKey)) {
      this.pageStepper?.next();
    }
  }

  startImport(): void {
    combineLatest([this.config.namespace$, this.csvFilename$, this.csvMappings$])
      .pipe(
        take(1),
        tap(() => this.loadingImportFromFile.set(true)),
        switchMap(([namespace, filename, mappings]) => {
          return this.importer.multiObjectTypeImportFromFile({
            namespace,
            filename: filename,
            mappings: mappings,
            duplicateHandlingType: this.updateDuplicateIsChecked()
              ? DuplicateHandlingType.DUPLICATE_HANDLING_TYPE_UPDATE
              : DuplicateHandlingType.DUPLICATE_HANDLING_TYPE_SKIP,
          });
        }),
        map((response) => response.importId),
        delay(DELAY_LIST_RECORD_CALL_MS),
        withLatestFrom(this.config.namespace$),
        switchMap(([importId, namespace]) => {
          return this.importer
            .listImportRecords({
              namespace: namespace,
              pagingOptions: {
                cursor: '',
                pageSize: 5,
              } as PagedRequestOptionsInterface,
            } as ListImportRecordsRequestInterface)
            .pipe(
              map((response) => {
                if (!response.importRecord.some((record) => record.importId == importId)) {
                  throw new Error('Import record not found');
                }
              }),
              retry({ count: RETRY_LIST_RECORD_CALL_AMOUNT, delay: DELAY_LIST_RECORD_CALL_MS }),
              catchError((_) => {
                return of(null);
              }),
            );
        }),
        withLatestFrom(this.csvFilename$),
        tap(() => this.loadingImportFromFile.set(false)),
      )
      .subscribe({
        next: ([, filename]) => {
          this.snack.openSuccessSnack('BULK_IMPORT.IMPORT_SUCCESS', {
            interpolateTranslateParams: { filename: filename },
          });
          this.backToList();
        },
        error: (error) => {
          console.error(error);
          this.snack.openErrorSnack('BULK_IMPORT.IMPORT_ERROR');
        },
      });
  }

  private backToList(): void {
    this.routePrefix$.pipe(take(1)).subscribe({
      next: (routePrefix) => {
        const url = this.hasImportRecordFeatureFlag()
          ? `${routePrefix}/${PAGE_ROUTES.IMPORTER.ROOT}`
          : `${routePrefix}/${this.previousPageUrl}`;
        this.router.navigateByUrl(url, {
          state: { previousPageObjectType: this.previousPageObjectType, previousPageSource: 'bulk-import-stepper' },
        });
      },
    });
  }

  updateFieldMappingsErrors(fieldMappingsErrors: string[]) {
    this.fieldMappingsErrors.set(fieldMappingsErrors.join('. '));
  }
}
