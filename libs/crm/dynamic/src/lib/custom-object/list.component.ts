import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ObjectType, PAGE_ROUTES, ListObjectsPageComponent, CrmObjectInjectionToken } from '@galaxy/crm/static';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { of } from 'rxjs';
import { map } from 'rxjs/operators';

@Component({
  // requires selector to prevent ID collision
  selector: 'crm-list-custom-object',
  template: `
    <crm-list-objects-page
      [objectType]="objectType"
      [createObjectRoute]="createObjectRoute | async"
      [presetFilters]="presetFilters$ | async"
    >
    </crm-list-objects-page>
  `,
  imports: [ListObjectsPageComponent, CommonModule, MatButtonModule, MatIconModule, TranslateModule],
})
export class ListCustomObjectsPageComponent {
  private readonly crmObjectDependencies = inject(CrmObjectInjectionToken);

  PAGE_ROUTES = PAGE_ROUTES;
  objectType: ObjectType = 'CustomObject';
  createObjectRoute = (this.crmObjectDependencies.defaultPageUrl$ || of('')).pipe(
    map((defaultPageUrl) => `${defaultPageUrl}/${PAGE_ROUTES.CUSTOM_OBJECT.SUBROUTES.CREATE}`),
  );
  protected readonly presetFilters$ = this.crmObjectDependencies.presetFilters$ ?? of([]);
}
