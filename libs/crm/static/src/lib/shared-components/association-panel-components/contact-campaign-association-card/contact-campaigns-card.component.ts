import { Component, Inject, inject, input, On<PERSON><PERSON>roy, Signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslationModule } from '../../../i18n/translation-module';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatInputModule } from '@angular/material/input';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';

import { MatCardModule } from '@angular/material/card';
import { CrmDependencies, CrmInjectionToken, ObjectType } from '../../../tokens-and-interfaces';
import {
  CampaignScheduleStatus,
  CampaignService,
  RecipientCampaignService,
  RecipientCampaignStruct,
} from '@vendasta/campaigns';
import { filter, map, switchMap, take, tap } from 'rxjs/operators';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { Router, RouterLink } from '@angular/router';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { catchError, Observable, of, Subscription } from 'rxjs';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { TranslateService } from '@ngx-translate/core';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { RecipientCampaignStatusComponent } from './recipient-campaign-status/recipient-campaign-status.component';

export interface CampaignCardData {
  id: string;
  recipientCampaignId: string;
  title: string;
  link: string;
  recipientCampaignStatus: CampaignScheduleStatus;
  partnerID: string;
}
@Component({
  selector: 'crm-campaigns-card',
  imports: [
    CommonModule,
    TranslationModule,
    MatExpansionModule,
    MatInputModule,
    GalaxyFormFieldModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatIconModule,
    MatCardModule,
    GalaxyAvatarModule,
    MatMenuModule,
    MatTooltipModule,
    GalaxyBadgeModule,
    RouterLink,
    RecipientCampaignStatusComponent,
  ],
  templateUrl: './contact-campaigns-card.component.html',
  styleUrls: ['./contact-campaigns-card.component.scss', '../../../shared-styles/association-panel-shared.scss'],
})
export class ContactCampaignsCardComponent implements OnDestroy {
  id = input.required<string>();
  name = input<string>('');
  objectType = input<ObjectType>('Contact');

  protected readonly canAddToCampaign = !!this.config.contact?.services?.campaignService;

  protected readonly confirmationModal = inject(OpenConfirmationModalService);
  protected campaigns$: Observable<CampaignCardData[]>;
  protected readonly recipientCampaigns$: Observable<RecipientCampaignStruct[]>;
  private readonly useBusinessCenterCampaignLink: Signal<boolean | undefined>;
  protected readonly canViewAllCampaigns$: Observable<boolean | undefined>;
  private subscriptions: Subscription[] = [];

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private readonly campaignService: CampaignService,
    private readonly recipientCampaignService: RecipientCampaignService,
    private readonly router: Router,
    private readonly snackbarService: SnackbarService,
    private translate: TranslateService,
    private postHogService: ProductAnalyticsService,
  ) {
    this.recipientCampaigns$ = toObservable(this.id).pipe(
      switchMap((id) => this.recipientCampaignService.list(id ?? '')),
    );
    this.canViewAllCampaigns$ = this.config.services?.accessService?.canViewAllCampaigns$ || of(false);
    this.campaigns$ = this.listRecipientCampaigns();
    this.useBusinessCenterCampaignLink = toSignal(
      config.parentNamespace$.pipe(
        map((parentNamespace) => {
          return parentNamespace !== '';
        }),
      ),
    );
  }

  protected addCampaigns() {
    if (this.canAddToCampaign) {
      this.config.contact?.services?.campaignService?.navigateToSendCampaign([this.id()]);
    }
  }

  private listRecipientCampaigns(): Observable<CampaignCardData[]> {
    let recipientCampaigns: RecipientCampaignStruct[] = [];
    const recipientCampaignIds$ = this.recipientCampaigns$.pipe(
      tap((data) => {
        recipientCampaigns = data;
      }),
      map((recipientCampaigns = []) => recipientCampaigns.map((rc: RecipientCampaignStruct) => rc.campaignId)),
    );
    return recipientCampaignIds$.pipe(
      switchMap((campaignIds) => {
        if (!campaignIds || campaignIds.length === 0) {
          return of([]);
        }
        return this.campaignService.getMulti(campaignIds).pipe(
          switchMap((campaigns) => {
            return of(
              campaigns.map((campaign) => {
                const campaignLink = this.getCampaignLink(campaign.campaignId, campaign.partnerId);
                const recipientCampaign =
                  recipientCampaigns.find((rc) => rc.campaignId === campaign.campaignId) || null;
                return {
                  id: campaign.campaignId,
                  recipientCampaignId: recipientCampaign?.recipientCampaignId,
                  title: campaign.name,
                  link: campaignLink,
                  recipientCampaignStatus: recipientCampaign?.status,
                  partnerID: campaign.partnerId,
                } as CampaignCardData;
              }),
            );
          }),
        );
      }),
    );
  }

  private getCampaignLink(campaignId: string, namespace: string): string {
    if (this.useBusinessCenterCampaignLink()) {
      const id = namespace.split('/')[1];
      return `/account/location/${id}/campaigns/${campaignId}/details`;
    } else {
      return `/marketing/campaign/details/${campaignId}`;
    }
  }

  protected viewAll() {
    if (this.useBusinessCenterCampaignLink()) {
      this.config.namespace$.pipe(take(1)).subscribe((namespace) => {
        this.router.navigate([`/account/location/${namespace}/campaigns/list`]);
      });
    } else {
      this.router.navigate(['/marketing/campaigns/all']);
    }
  }

  protected canShowMenu(status: CampaignScheduleStatus): boolean {
    const statuses = [
      CampaignScheduleStatus.CAMPAIGN_SCHEDULE_STATUS_ACTIVE,
      CampaignScheduleStatus.CAMPAIGN_SCHEDULE_STATUS_STOPPED,
      CampaignScheduleStatus.CAMPAIGN_SCHEDULE_STATUS_WAITING_ON_RATE_LIMIT,
    ];
    return statuses.includes(status);
  }

  protected pauseCampaign(campaign: CampaignCardData) {
    const modalTitle = this.translate.instant('ASSOCIATIONS.CAMPAIGNS.MENU.PAUSE.CONFIRMATION.TITLE', {
      title: campaign.title,
      recipient: this.name(),
    });
    const message = this.translate.instant('ASSOCIATIONS.CAMPAIGNS.MENU.PAUSE.CONFIRMATION.MESSAGE');
    const confirmText = this.translate.instant('ASSOCIATIONS.CAMPAIGNS.MENU.PAUSE.CONFIRMATION.CONFIRM');
    this.subscriptions.push(
      this.openConfirmationModal(modalTitle, message, confirmText)
        .pipe(
          filter((result) => result),
          switchMap(() =>
            this.recipientCampaignService.pauseRecipientCampaign(campaign.recipientCampaignId).pipe(
              tap(() => {
                this.campaigns$ = this.listRecipientCampaigns();
                this.snackbarService.openSuccessSnack(
                  this.translate.instant('ASSOCIATIONS.CAMPAIGNS.MENU.PAUSE.SUCCESS'),
                );
              }),
              catchError((err) => {
                console.error(err);
                this.snackbarService.openErrorSnack(
                  this.translate.instant('ASSOCIATIONS.CAMPAIGNS.MENU.PAUSE.FAILURE'),
                );
                return of(null);
              }),
            ),
          ),
        )
        .subscribe(() => {
          this.postHogService.trackEvent('user-clicked-crm-pause-campaign', 'pause-campaign-workflow', 'click', 0, {
            pid: campaign.partnerID,
            recipientCampaignID: campaign.recipientCampaignId,
            contactID: this.id(),
          });
        }),
    );
  }

  protected resumeCampaign(campaign: CampaignCardData) {
    const modalTitle = this.translate.instant('ASSOCIATIONS.CAMPAIGNS.MENU.RESUME.CONFIRMATION.TITLE', {
      title: campaign.title,
      recipient: this.name(),
    });
    const message = this.translate.instant('ASSOCIATIONS.CAMPAIGNS.MENU.RESUME.CONFIRMATION.MESSAGE');
    const confirmText = this.translate.instant('ASSOCIATIONS.CAMPAIGNS.MENU.RESUME.CONFIRMATION.CONFIRM');
    this.subscriptions.push(
      this.openConfirmationModal(modalTitle, message, confirmText)
        .pipe(
          filter((result) => result),
          switchMap(() =>
            this.recipientCampaignService.resumeRecipientCampaign(campaign.recipientCampaignId).pipe(
              tap(() => {
                this.campaigns$ = this.listRecipientCampaigns();
                this.snackbarService.openSuccessSnack(
                  this.translate.instant('ASSOCIATIONS.CAMPAIGNS.MENU.RESUME.SUCCESS'),
                );
              }),
              catchError((err) => {
                console.error(err);
                this.snackbarService.openErrorSnack(
                  this.translate.instant('ASSOCIATIONS.CAMPAIGNS.MENU.RESUME.FAILURE'),
                );
                return of(null);
              }),
            ),
          ),
        )
        .subscribe(() => {
          this.postHogService.trackEvent('user-clicked-crm-resume-campaign', 'resume-campaign-workflow', 'click', 1, {
            pid: campaign.partnerID,
            recipientCampaignID: campaign.recipientCampaignId,
            contactID: this.id(),
          });
        }),
    );
  }

  protected openConfirmationModal(title: string, message: string, confirmButtonText: string): Observable<boolean> {
    return this.confirmationModal.openModal({
      title: title,
      message: message,
      confirmButtonText: confirmButtonText,
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  protected readonly CampaignScheduleStatus = CampaignScheduleStatus;
}
