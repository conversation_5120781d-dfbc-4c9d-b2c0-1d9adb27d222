import { CommonModule } from '@angular/common';
import { Component, computed, Input, SecurityContext, signal, Signal, WritableSignal } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { RxState } from '@rx-angular/state';
import { ActivityInterface } from '@vendasta/crm';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { DateFormat } from '@vendasta/galaxy/utility/date-utils';
import { MatTooltipModule } from '@angular/material/tooltip';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';

import { DomSanitizer } from '@angular/platform-browser';
import { MatIcon } from '@angular/material/icon';
import { MatDialog } from '@angular/material/dialog';
import { AVATAR_WIDTH } from '../../../../../constants';

import { ActivityCardComponent } from './activity-card/activity-card.component';
import { TranslationModule } from '../../../../../i18n/translation-module';
import { ObjectType } from '../../../../../tokens-and-interfaces';
import {
  ActivityAssociationDisplayInfo,
  ActivityAssociationService,
  ActivityIcons,
  ActivityTypes,
  convertMillisecondsToHoursAndMinutes,
  MeetingStatus,
  MeetingSummaryModalComponent,
} from '../../../activities';
import { ActivityExtended } from '../../../timeline/timeline.service';
import { ActivityAssigneeService, CrmFieldService, StandardIds, SystemFieldIds } from '../../../../../shared-services';
import { RouterLink } from '@angular/router';

export interface ActivityCardInformation {
  type: 'CardInfo';
  meetingBody: string;
  meetingStatus: string;
  meetingOutcome: string;
  meetingLocation: string;
  meetingDuration: string;
  date: Date | null;
  duration: number;
  meetingSummaryTitle: string;
  meetingSummaryBody: string;
  meetingSummarySentiment: number;
  meetingSalesScore: number;
  meetingKeyTakeAways: string[];
  meetingActionItems: string[];
  meetingTranscriptUrl: string;
  meetingRecordingUrl: string;
  processedTranscriptID: string;
  ownerId: string;
  activity: ActivityInterface;
}

@Component({
  selector: 'crm-activity-meeting',
  templateUrl: './meeting.component.html',
  styleUrls: ['./meeting.component.scss'],
  providers: [RxState],
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    MatCardModule,
    GalaxyAvatarModule,
    GalaxyPipesModule,
    ActivityCardComponent,
    MatIcon,
    RouterLink,
    MatTooltipModule,
    GalaxyTooltipModule,
  ],
})
export class CrmActivityMeetingComponent {
  readonly AVATAR_WIDTH = AVATAR_WIDTH;
  readonly ActivityIcons = ActivityIcons;
  readonly ActivityTypes = ActivityTypes;
  dateFormat = DateFormat.mediumDateShortTime;
  private _cardInfo: WritableSignal<Partial<ActivityCardInformation>> = signal({});
  private _associatedObjectId: WritableSignal<string> = signal('');
  private _objectType: WritableSignal<ObjectType> = signal('Contact');
  companies: Signal<ActivityAssociationDisplayInfo[]> = this.associationService.companies;
  private isClosed: boolean;
  readonly primaryAssociation = this.associationService.primaryAssociation;
  readonly routePrefix = this.associationService.routePrefix;
  readonly showPrimaryAssociation = computed(() => !this.associatedObjectId);

  @Input() set objectType(objectType: ObjectType) {
    this._objectType.set(objectType);
  }

  get objectType() {
    return this._objectType();
  }

  @Input() set associatedObjectId(crmObjectId: string) {
    this._associatedObjectId.set(crmObjectId);
  }

  get associatedObjectId() {
    return this._associatedObjectId();
  }

  private _showAvatar = true;
  @Input() set showAvatar(showAvatar: boolean) {
    this._showAvatar = showAvatar;
  }

  get showAvatar() {
    return this._showAvatar;
  }

  @Input({ required: true }) set cardInfo(data: ActivityExtended | Partial<ActivityCardInformation>) {
    if (this.checkIfActivityCardInformation(data)) {
      this._cardInfo.set(data as ActivityCardInformation);
    } else {
      this.associationService.activity = data as ActivityExtended;
      this._cardInfo.set(this.convertActivityToCardInfo(data as ActivityExtended));
    }
  }

  get cardInfo(): Partial<ActivityCardInformation> {
    return this._cardInfo();
  }

  private checkIfActivityCardInformation(data: any): data is ActivityCardInformation {
    return 'type' in data && data.type === 'CardInfo';
  }

  private convertActivityToCardInfo(data: ActivityExtended): Partial<ActivityCardInformation> {
    const duration = this.getIntegerFieldData(data, StandardIds.ActivityMeetingDuration);
    return {
      meetingBody:
        this.sanitizer.sanitize(SecurityContext.HTML, this.getStringFieldData(data, StandardIds.ActivityMeetingBody)) ||
        '',
      meetingStatus: this.getStringFieldData(data, StandardIds.ActivityMeetingStatus),
      meetingOutcome: this.getStringFieldData(data, StandardIds.ActivityMeetingOutcome),
      meetingLocation: this.getStringFieldData(data, StandardIds.ActivityMeetingLocation),
      date: this.fieldService.getFieldValueFromCrmObject(data, SystemFieldIds.ActivityEffectiveDate)?.dateValue ?? null,
      meetingDuration: convertMillisecondsToHoursAndMinutes(duration),
      duration: duration,
      meetingSummaryTitle: this.getStringFieldData(data, StandardIds.ActivityMeetingSummaryTitle),
      meetingKeyTakeAways: this.getStringValuesFieldData(data, StandardIds.ActivityMeetingKeyTakeaways),
      meetingActionItems: this.getStringValuesFieldData(data, StandardIds.ActivityMeetingActionItems),
      meetingSummaryBody: this.getStringFieldData(data, StandardIds.ActivityMeetingSummaryBody),
      meetingSummarySentiment: this.getIntegerFieldData(data, StandardIds.ActivityMeetingSentiment),
      meetingSalesScore: this.getIntegerFieldData(data, StandardIds.ActivityMeetingSalesScore),
      meetingTranscriptUrl: this.getStringFieldData(data, StandardIds.ActivityMeetingTranscriptUrl),
      meetingRecordingUrl: this.getStringFieldData(data, StandardIds.ActivityMeetingRecordingUrl),
      processedTranscriptID: this.getStringFieldData(data, StandardIds.ActivityMeetingAnalysisProcessedTranscriptID),
      ownerId: this.getStringFieldData(data, SystemFieldIds.ActivityOwnerID),
      activity: data,
    };
  }

  headerTitle$ = computed(() => {
    return this.translateService.stream('ACTIVITY.MEETING.TITLE');
  });

  statusTitle$ = computed(() => {
    const cardInfo = this.cardInfo;
    if (!cardInfo || !cardInfo.meetingStatus) {
      return this.translateService.stream('ACTIVITY.MEETING.STATUS_HEADER.UNKNOWN_STATUS');
    }

    const statusConfigs: { [key: string]: { i18nKey: string } } = {
      [MeetingStatus.Scheduled.toLowerCase()]: {
        i18nKey: 'ACTIVITY.MEETING.SCHEDULED',
      },
      [MeetingStatus.Rescheduled.toLowerCase()]: {
        i18nKey: 'ACTIVITY.MEETING.RESCHEDULED',
      },
      [MeetingStatus.Cancelled.toLowerCase()]: {
        i18nKey: 'ACTIVITY.MEETING.CANCELLED',
      },
      [MeetingStatus.Completed.toLowerCase()]: {
        i18nKey: 'ACTIVITY.MEETING.COMPLETED',
      },
      [MeetingStatus.NoShow.toLowerCase()]: {
        i18nKey: 'ACTIVITY.MEETING.NO_SHOW',
      },
    };

    const statusConfig = statusConfigs[cardInfo.meetingStatus.toLowerCase()];
    if (!statusConfig) {
      return this.translateService.stream('ACTIVITY.MEETING.STATUS_HEADER.UNKNOWN_STATUS');
    }
    return this.translateService.stream(statusConfig.i18nKey);
  });

  cardTitle = computed(() => {
    const { duration } = this.cardInfo;
    const minutes = Math.floor(duration / 60000);
    const hours = Math.floor(minutes / 60);
    if (hours > 0) {
      return `${hours}-hour ${minutes % 60}-minute meeting`;
    }
    return `${minutes}-minute meeting`;
  });

  constructor(
    private readonly fieldService: CrmFieldService,
    private readonly translateService: TranslateService,
    private readonly sanitizer: DomSanitizer,
    private readonly dialog: MatDialog,
    private readonly associationService: ActivityAssociationService,
    private readonly userService: ActivityAssigneeService,
  ) {}

  private getStringFieldData(activity: ActivityInterface, fieldId: string): string {
    return this.fieldService.getFieldValueFromCrmObject(activity, fieldId)?.stringValue ?? '';
  }

  private getStringValuesFieldData(activity: ActivityInterface, fieldId: string): string[] {
    return this.fieldService.getFieldValueFromCrmObject(activity, fieldId)?.stringValues?.values ?? [];
  }

  private getIntegerFieldData(activity: ActivityInterface, fieldId: string): number {
    return this.fieldService.getFieldValueFromCrmObject(activity, fieldId)?.integerValue ?? 0;
  }

  openSummaryModal(): void {
    const cardDetails = this.cardInfo;
    const companyList = this.companies();

    const dialogRef = this.dialog.open(MeetingSummaryModalComponent, {
      width: '950px',
      data: {
        meetingTitle: cardDetails.meetingSummaryTitle,
        meetingDate: cardDetails.date,
        duration: cardDetails.meetingDuration,
        sentimentScore: `${cardDetails.meetingSummarySentiment} / 10`,
        salesScore: `${cardDetails.meetingSalesScore} / 10`,
        summary: cardDetails.meetingSummaryBody,
        keyTakeaways: cardDetails.meetingKeyTakeAways,
        actionItems: cardDetails.meetingActionItems,
        transcriptUrl: cardDetails.meetingTranscriptUrl,
        recordingUrl: cardDetails.meetingRecordingUrl,
        companies: companyList,
        processedTranscriptID: cardDetails.processedTranscriptID,
        ownerID: cardDetails.ownerId,
        activity: cardDetails.activity,
      },
    });

    dialogRef.afterClosed().subscribe(() => {
      this.isClosed = true;
    });
  }
}
