<div *ngIf="cardInfo" class="activity-card-meeting">
  <crm-activity-card
    [activity]="cardInfo.activity"
    [icon]="ActivityIcons[ActivityTypes.Meeting]"
    [disableCollapse]="!!(cardInfo.meetingSummaryBody || cardInfo.meetingSummaryTitle)"
  >
    <!-- Activity title -->
    <span activity-title>
      <div>
        <strong>{{ 'ACTIVITY.MEETING.TITLE' | translate }}</strong>
        @if (statusTitle$() | async; as status) {
          @if (status !== 'ACTIVITY.MEETING.STATUS_HEADER.UNKNOWN_STATUS') {
            - {{ status }}
            @if (showPrimaryAssociation()) {
              @if (primaryAssociation(); as primaryAssociation) {
                with
                <a [routerLink]="routePrefix() + '/' + primaryAssociation.linkUrl">{{ primaryAssociation.name }}</a>
              }
            }
          }
        }
      </div>
    </span>

    <!-- summary -->
    <div activity-preview>
      @if (cardInfo.meetingSummaryBody || cardInfo.meetingSummaryTitle) {
        <div *ngIf="cardInfo.meetingSummaryTitle" class="meeting-summary-title">
          <div class="summary-title-container">
            <div
              [innerHTML]="cardInfo.meetingSummaryTitle"
              class="summary-title clickable-title"
              (click)="openSummaryModal()"
            ></div>
            <mat-icon class="open-summary-icon clickable-title" (click)="openSummaryModal()">open_in_new</mat-icon>
          </div>
        </div>

        <div class="summarized-by-ai" *ngIf="cardInfo.meetingSummaryBody">
          <div class="summary-by-ai">
            <mat-icon class="ai-icon" svgIcon="galaxy-ai-icon" />
            <div>{{ 'ACTIVITY.MEETING.SUMMARY_BY_AI' | translate }}</div>
          </div>
          <div class="meeting-summary-body">
            <span [innerHTML]="cardInfo.meetingSummaryBody"></span>
          </div>
          <a data-action="view-summary-link-clicked" class="view-summary-link" (click)="openSummaryModal()">
            {{ 'ACTIVITY.MEETING.MEETING_ANALYSIS.VIEW_DETAILS' | translate }}
          </a>
        </div>
      } @else if (cardInfo.meetingBody) {
        <div class="meeting-body" [innerHTML]="cardInfo.meetingBody"></div>
      }
      <!-- Additional content -->
      <div activity-content>
        @if (!cardInfo.meetingSummaryBody && !cardInfo.meetingSummaryTitle) {
          <div class="row meeting-details">
            @if (cardInfo.date) {
              <ng-container
                [ngTemplateOutlet]="activityData"
                [ngTemplateOutletContext]="{ label: 'ACTIVITY.DATE', value: cardInfo.date | date: dateFormat }"
              ></ng-container>
            }
            @if (cardInfo.meetingSalesScore) {
              <ng-container
                [ngTemplateOutlet]="activityData"
                [ngTemplateOutletContext]="{
                  label: 'ACTIVITY.MEETING.SALES_SCORE',
                  value: cardInfo.meetingSalesScore + '/10',
                }"
              ></ng-container>
            }
            @if (cardInfo.meetingStatus && !cardInfo.meetingSummarySentiment) {
              <ng-container
                [ngTemplateOutlet]="activityData"
                [ngTemplateOutletContext]="{ label: 'ACTIVITY.STATUS', value: cardInfo.meetingStatus }"
              ></ng-container>
            }
            @if (cardInfo.meetingOutcome) {
              <ng-container
                [ngTemplateOutlet]="activityData"
                [ngTemplateOutletContext]="{ label: 'ACTIVITY.OUTCOME', value: cardInfo.meetingOutcome }"
              ></ng-container>
            }
            @if (cardInfo.meetingDuration) {
              <ng-container
                [ngTemplateOutlet]="activityData"
                [ngTemplateOutletContext]="{ label: 'ACTIVITY.DURATION', value: cardInfo.meetingDuration }"
              ></ng-container>
            }
            @if (cardInfo.meetingLocation) {
              <ng-container
                [ngTemplateOutlet]="activityData"
                [ngTemplateOutletContext]="{ label: 'ACTIVITY.LOCATION', value: cardInfo.meetingLocation }"
              ></ng-container>
            }
            @if (cardInfo.meetingSummarySentiment) {
              <ng-container
                [ngTemplateOutlet]="activityData"
                [ngTemplateOutletContext]="{
                  label: 'ACTIVITY.MEETING.SENTIMENT_SCORE',
                  value: cardInfo.meetingSummarySentiment + '/10',
                }"
              ></ng-container>
            }
          </div>
        }
      </div>
    </div>
  </crm-activity-card>
</div>

<ng-template #activityData let-label="label" let-value="value">
  <div class="col col-xs-12 col-md-3">
    <div class="activity-data-label">
      {{ label | translate }}
    </div>
    <div class="activity-data-value">
      <ng-container *ngIf="value">
        {{ value }}
      </ng-container>
    </div>
  </div>
</ng-template>
