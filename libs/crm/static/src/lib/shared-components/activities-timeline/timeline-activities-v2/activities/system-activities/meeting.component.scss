@use 'design-tokens' as dt;

$avatar-width: 36px;

:host {
  display: block;
  position: relative;
}

.mat-icon {
  width: unset;
  height: unset;
  line-height: 1;
}

.title {
  font-size: dt.$font-preset-4-size;
  overflow-wrap: anywhere;
}

.clickable-title {
  cursor: pointer;
  color: dt.$glxy-blue-700;

  &:hover {
    text-decoration: underline;
  }
}

.loading-header-text {
  height: 18px;
  width: 225px;
  display: block;
}

.mat-card-title {
  padding: 16px 16px 0;
  font-weight: bold;
}

.meeting-title,
.summary-title {
  font-size: dt.$font-preset-4-size;
  overflow-wrap: anywhere;
  margin-bottom: 8px;
}

.summary-title-container {
  display: flex;
  align-items: center;
  gap: dt.$spacing-2;
}

.open-summary-icon {
  font-size: dt.$font-preset-4-size;
  height: calc(dt.$spacing-4 - dt.$spacing-1);
}

.summary-title {
  font-weight: bold;

  &.clickable-title {
    cursor: pointer;
    color: dt.$glxy-blue-700;

    &:hover {
      text-decoration: underline;
    }
  }
}

.subtitle {
  font-size: dt.$font-preset-5-size;
  padding-bottom: 10px;
}

.effective-date {
  font-size: dt.$font-preset-5-size;
  color: dt.$tertiary-text-color;
  margin: 0;
  padding-bottom: dt.$spacing-1;
}

.bolded {
  font-weight: bold;
}

.meeting-body {
  margin: 0;
  overflow-wrap: anywhere;
  white-space: pre-line;
  display: block;
  padding-bottom: 10px;
}

mat-card-content:empty {
  padding-bottom: 0;
}

.activity-data-label {
  display: block;
  @include dt.text-preset-5;
  color: dt.$secondary-font-color;
}

.activity-data-value {
  display: block;
  margin-bottom: dt.$spacing-2;

  &:empty:before {
    content: '—';
    color: dt.$tertiary-font-color;
  }
}

.meeting-details {
  margin-top: 6px;
}

.summary-by-ai {
  display: flex;
  align-items: center;
  color: dt.$grey;
  font-size: dt.$font-preset-5-size;
  margin-bottom: dt.$spacing-1;
}

.summary-by-ai mat-icon {
  width: 16px;
  height: auto;
}

.summarized-by-ai {
  background-color: #943cff0d;
  padding: 12px;
  border-radius: 4px;
}

.meeting-summary-body {
  display: block;
  padding-bottom: 0;

  .info-icon {
    display: inline-flex;
    align-items: center;
    font-size: dt.$font-preset-2-size;
    color: dt.$grey;
    margin-left: dt.$spacing-2;
    cursor: help;
    vertical-align: middle;
  }
}

.view-summary-link {
  font-size: dt.$font-preset-5-size;
  color: #943cff;
}

.tooltip-text {
  color: var(--dt-color-text-secondary);
  font-size: var(--dt-font-size-2);
  margin-left: var(--dt-spacing-2);
  cursor: pointer;
}
