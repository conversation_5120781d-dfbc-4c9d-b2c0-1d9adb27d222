@use 'design-tokens' as dt;

$avatar-width: 36px;

:host {
  display: block;
  position: relative;
}

.title {
  font-size: dt.$font-preset-4-size;
  overflow-wrap: anywhere;
}

.loading-header-text {
  height: 18px;
  width: 225px;
  display: block;
}

.mat-card-title {
  padding: dt.$spacing-3 dt.$spacing-3 0;
  font-weight: bold;
}

.call-title {
  font-size: dt.$font-preset-4-size;
  overflow-wrap: anywhere;
  margin-bottom: dt.$spacing-2;
}

.subtitle {
  font-size: dt.$font-preset-5-size;
  padding-bottom: 10px;
}

.effective-date {
  font-size: dt.$font-preset-5-size;
  color: dt.$tertiary-text-color;
  margin: 0;
  padding-bottom: dt.$spacing-1;
}

.bolded {
  font-weight: bold;
}

.call-body {
  margin: 0;
  overflow-wrap: anywhere;
  white-space: pre-line;
  display: block;
  padding-bottom: 10px;
}

mat-card-content:empty {
  padding-bottom: 0;
}

.activity-data-label {
  display: block;
  @include dt.text-preset-5;
  color: dt.$secondary-font-color;
}

.activity-data-value {
  display: block;
  margin-bottom: dt.$spacing-3;

  &:empty:before {
    content: '—';
    color: dt.$tertiary-font-color;
  }
}

.call-details {
  margin-top: 6px;
}

.activity-data-call-recording {
  display: block;
  margin-bottom: dt.$spacing-3;
  margin-top: dt.$spacing-2;
}
