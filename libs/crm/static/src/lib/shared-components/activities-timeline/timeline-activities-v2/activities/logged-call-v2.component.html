@if (cardInfo(); as cardInfo) {
  <crm-activity-card [activity]="activity()" [icon]="icon()" [crmObjectIdentifier]="associatedObject()">
    <span activity-title>
      @if (cardInfo.title; as title) {
        <span>
          <strong>{{ title.typeWithVerb | translate }}</strong>
          {{ title.preposition | translate }}
        </span>
        @if (fullName()) {
          <span> {{ fullName() }}</span>
        }
      } @else {
        {{ 'ACTIVITY.UNKNOWN_TITLE' | translate }}
      }
      @if (showPrimaryAssociation()) {
        @if (primaryAssociation(); as primaryAssociation) {
          {{ 'OBJECT_TYPES.FOR_LABEL' | translate }}
          <a [routerLink]="primaryAssociationLink()">{{ primaryAssociation.name }}</a>
        }
      }
    </span>

    <div activity-preview>
      <crm-activity-rich-text [richText]="cardInfo.body"></crm-activity-rich-text>
      @if (callRecordingSource$ | async; as callUrl) {
        <audio class="activity-data-call-recording" preload="metadata" controls src="{{ callUrl }}">
          {{ 'ACTIVITY.AUDIO_PLAYBACK_ERROR' | translate }}
        </audio>
      }
      <div class="row">
        <ng-container
          [ngTemplateOutlet]="activityData"
          [ngTemplateOutletContext]="{ label: 'ACTIVITY.STATUS', value: cardInfo.status }"
        ></ng-container>
        <ng-container
          [ngTemplateOutlet]="activityData"
          [ngTemplateOutletContext]="{ label: 'ACTIVITY.OUTCOME', value: cardInfo.outcome }"
        ></ng-container>
      </div>
    </div>
  </crm-activity-card>
}

<ng-template #activityData let-label="label" let-value="value">
  <div class="col col-xs-12 col-md-4">
    <div class="activity-data-label">
      {{ label | translate }}
    </div>
    <div class="activity-data-value">
      <!-- ng-container necessary so angular interpolation will add empty -->
      @if (value) {
        <ng-container>
          {{ value }}
        </ng-container>
      }
    </div>
  </div>
</ng-template>
