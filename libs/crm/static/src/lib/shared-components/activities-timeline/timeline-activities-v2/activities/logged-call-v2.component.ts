import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, computed, model, OnInit, Signal } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ActivityInterface } from '@vendasta/crm';
import { Observable, of } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { CrmFieldService, StandardIds } from '../../../../shared-services/crm-services/field.service';
import { TranslationModule } from '../../../../i18n/translation-module';
import { ActivityRichTextComponent } from '../../activities/activity-rich-text.component';
import { User } from '@vendasta/iamv2';
import { ActivityAssociationService, ActivityIcons, ActivityTypes, CrmObjectIdentifier } from '../../activities';
import { ActivityCardComponent } from './system-activities/activity-card/activity-card.component';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'crm-activity-logged-call-v2',
  templateUrl: './logged-call-v2.component.html',
  styleUrls: ['./logged-call-v2.component.scss'],
  providers: [],
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    ActivityRichTextComponent,
    ActivityCardComponent,
    GalaxyPipesModule,
    RouterLink,
  ],
})
export class CrmActivityLoggedCallV2Component implements OnInit {
  private unknownNamePlaceholder = this.translate.instant('CONTACT.ACTIVITIES.UNKNOWN_NAME');
  activity = model<ActivityInterface>({});
  ownerUser = model<User | undefined>(undefined);
  associatedObject = model<CrmObjectIdentifier | undefined>(undefined);

  icon = computed(() => ActivityIcons[ActivityTypes.Call]);

  fullName = computed(() => {
    const user = this.ownerUser() as User;
    return `${user?.firstName || ''} ${user?.lastName || ''}`.trim() || this.unknownNamePlaceholder;
  });

  showPrimaryAssociation = computed(() => !this.associatedObject()?.crmObjectId);
  primaryAssociation = this.associationService.primaryAssociation;
  routePrefix = this.associationService.routePrefix;
  primaryAssociationLink = computed(() => {
    if (!this.primaryAssociation()) return '';
    const linkUrl = this.primaryAssociation()?.linkUrl;
    return `${this.routePrefix()}/${linkUrl}`;
  });

  callRecordingSource$: Observable<any>;

  cardInfo: Signal<ActivityCardInformation> = computed(() => {
    return {
      title: this.getTitleByDirection(),
      body: this.getStringFieldData(this.activity(), StandardIds.ActivityCallBody),
      outcome: this.getStringFieldData(this.activity(), StandardIds.ActivityCallOutcome),
      status: this.getStringFieldData(this.activity(), StandardIds.ActivityCallStatus),
      callRecordingUrl: this.getStringFieldData(this.activity(), StandardIds.ActivityCallRecordingUrl),
    };
  });

  private getStringFieldData(activity: ActivityInterface, fieldId: string): string {
    return this.fieldService.getFieldValueFromCrmObject(activity, fieldId)?.stringValue ?? '';
  }

  private getTitleByDirection() {
    const baseKey = 'ACTIVITY.LOGGED_ACTIVITY.HEADER_ADDITIONAL_TEXT.CALL';
    const callDirection = this.getStringFieldData(this.activity(), StandardIds.ActivityCallDirection);
    const typeWithVerb = `${baseKey}.${callDirection.toUpperCase()}`;
    const preposition = 'ACTIVITY.LOGGED_ACTIVITY.HEADER_ADDITIONAL_TEXT.BY';
    return {
      typeWithVerb,
      preposition,
    };
  }

  constructor(
    private readonly http: HttpClient,
    private readonly fieldService: CrmFieldService,
    private readonly associationService: ActivityAssociationService,
    private readonly translate: TranslateService,
  ) {
    this.callRecordingSource$ = toObservable(this.cardInfo).pipe(
      switchMap((cardInfo) => {
        if (!cardInfo.callRecordingUrl) {
          return of(null);
        }
        return this.http.get(cardInfo.callRecordingUrl, {
          responseType: 'text',
          withCredentials: true,
        });
      }),
    );
  }

  ngOnInit(): void {
    this.associationService.activity = this.activity();
  }
}

interface ActivityHeaderParts {
  typeWithVerb: string;
  preposition: string;
}

interface ActivityCardInformation {
  title: ActivityHeaderParts;
  body: string;
  outcome: string;
  status: string;
  callRecordingUrl?: string;
}
