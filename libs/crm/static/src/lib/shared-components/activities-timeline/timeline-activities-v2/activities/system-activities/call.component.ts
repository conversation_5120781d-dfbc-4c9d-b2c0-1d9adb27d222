import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, computed, effect, inject, input, Signal } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';
import { ActivityInterface, GetMultiCrmObjectResponseInterface } from '@vendasta/crm';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { DateFormat } from '@vendasta/galaxy/utility/date-utils';
import { combineLatest, firstValueFrom, Observable, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { AVATAR_WIDTH } from '../../../../../constants';
import { TranslationModule } from '../../../../../i18n/translation-module';
import {
  CrmFieldService,
  CrmObjectService,
  StandardExternalIds,
  StandardIds,
  SystemFieldIds,
} from '../../../../../shared-services';
import { CrmInjectionToken, ObjectType } from '../../../../../tokens-and-interfaces';
import { ActivityExtended } from '../../../timeline/timeline.service';
import { GalaxyChatModule } from '@vendasta/galaxy/chat';
import { CallTranscriptModalComponent } from '../../../../../shared-components/activities-timeline/activities/call-transcript-modal.component';
import { MatDialog } from '@angular/material/dialog';
import { parsePhoneNumberFromString } from 'libphonenumber-js';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { ActivityCardComponent } from './activity-card/activity-card.component';
import { ActivityAssociationService, ActivityIcons, ActivityTypes } from '../../../activities';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'crm-activity-call',
  templateUrl: './call.component.html',
  styleUrls: ['./call.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    MatCardModule,
    GalaxyAvatarModule,
    GalaxyPipesModule,
    GalaxyChatModule,
    ActivityCardComponent,
    RouterLink,
  ],
  providers: [ActivityAssociationService],
})
export class CrmActivityCallComponent {
  readonly AVATAR_WIDTH = AVATAR_WIDTH;
  dateFormat = DateFormat.mediumDateShortTime;
  readonly ActivityIcons = ActivityIcons;
  readonly ActivityTypes = ActivityTypes;
  callRecordingSource$: Observable<any>;
  readonly activity = input.required<ActivityExtended>({});
  readonly associatedObjectId = input('');
  private readonly associatedObjectId$ = toObservable(this.associatedObjectId);
  readonly objectType = input<ObjectType>('Contact');
  private readonly objectType$ = toObservable(this.objectType);
  readonly showAvatar = input(true);

  associatedCrmObject$ = toObservable(this.activity).pipe(switchMap(() => this.getAssociatedObjectName()));
  associatedCrmObject = toSignal(this.associatedCrmObject$);

  private readonly config = inject(CrmInjectionToken);

  private readonly associationService = inject(ActivityAssociationService);
  readonly primaryAssociation = this.associationService.primaryAssociation;
  readonly routePrefix = this.associationService.routePrefix;

  readonly showPrimaryAssociation = computed(() => !this.associatedObjectId());

  private readonly breakpointObserver = inject(BreakpointObserver);
  private readonly isExtraSmall = this.breakpointObserver.observe(Breakpoints.XSmall);

  private getAssociatedObjectName(): Observable<string> {
    return combineLatest([this.objectType$, this.associatedObjectId$]).pipe(
      switchMap(([objectType, associatedObjectId]) => {
        if (objectType === 'Contact') {
          return this.crmObjectService.getMultiObject('Contact', [associatedObjectId]).pipe(
            map((data: GetMultiCrmObjectResponseInterface) => {
              const crmObject = data?.crmObjects[0];
              const firstName =
                this.fieldService.getFieldValueFromCrmObject(crmObject, StandardExternalIds.FirstName)?.stringValue ||
                '';
              const lastName =
                this.fieldService.getFieldValueFromCrmObject(crmObject, StandardExternalIds.LastName)?.stringValue ||
                '';
              return [firstName, lastName].filter(Boolean).join(' ');
            }),
          );
        }
        if (objectType === 'Company') {
          return this.crmObjectService.getMultiObject('Company', [associatedObjectId]).pipe(
            map((data: GetMultiCrmObjectResponseInterface) => {
              const crmObject = data?.crmObjects[0];
              const companyName =
                this.fieldService.getFieldValueFromCrmObject(crmObject, StandardIds.CompanyName)?.stringValue || '';
              return `${companyName}`;
            }),
          );
        }
        return of('');
      }),
    );
  }

  headerText = computed(() => {
    const { direction } = this.cardInfo();
    let directionText = '';
    if (direction === 'Inbound') {
      directionText = `Call from ${this.associatedCrmObject()}`;
    } else if (direction === 'Outbound') {
      directionText = `Call to ${this.associatedCrmObject()}`;
    }
    return directionText;
  });

  cardSubtitle = computed(() => {
    const { status, duration } = this.cardInfo();
    const minutes = Math.floor(duration / 60000);
    const seconds = ((duration % 60000) / 1000).toFixed(0);
    if (status === 'Connected') {
      return `${minutes} minutes, ${seconds} seconds call`;
    }
    return `Call - ${status}`;
  });

  ownerName = computed(() => {
    const owner = this.activity()?.ownerUser;
    if (owner) {
      return `${owner?.firstName || ''} ${owner?.lastName || ''}`.trim();
    }
    if (!this.activity().ownerId) {
      const sourceName = this.getStringFieldData(this.activity(), StandardIds.ActivitySourceName);
      if (sourceName) {
        return sourceName;
      }
    }
    return 'Unknown';
  });

  cardInfo: Signal<ActivityCardInformation> = computed(() => {
    const cardInfo: ActivityCardInformation = {
      body: this.getStringFieldData(this.activity(), StandardIds.ActivityCallBody),
      status: this.getStringFieldData(this.activity(), StandardIds.ActivityCallStatus),
      outcome: this.getStringFieldData(this.activity(), StandardIds.ActivityCallOutcome),
      direction: this.getStringFieldData(this.activity(), StandardIds.ActivityCallDirection),
      date:
        this.fieldService.getFieldValueFromCrmObject(this.activity(), SystemFieldIds.ActivityEffectiveDate)
          ?.dateValue ?? null,
      to: this.formatPhoneNumber(this.getStringFieldData(this.activity(), StandardIds.ActivityCallToNumber)),
      from: this.formatPhoneNumber(this.getStringFieldData(this.activity(), StandardIds.ActivityCallFromNumber)),
      duration:
        this.fieldService.getFieldValueFromCrmObject(this.activity(), StandardIds.ActivityCallDuration)?.integerValue ??
        0,
      transcriptJSON: this.getStringFieldData(this.activity(), StandardIds.ActivityCallTranscript),
    };
    const callRecordingUrl = this.getStringFieldData(this.activity(), StandardIds.ActivityCallRecordingUrl);
    if (callRecordingUrl) {
      cardInfo.callRecordingUrl = callRecordingUrl;
    }
    cardInfo.callRecordId = this.getStringFieldData(this.activity(), StandardIds.ActivityCallRecordId);

    return cardInfo;
  });

  private getStringFieldData(activity: ActivityInterface, fieldId: string): string {
    return this.fieldService.getFieldValueFromCrmObject(activity, fieldId)?.stringValue ?? '';
  }

  private formatPhoneNumber(phoneNumber: string): string {
    const parsedNumber = parsePhoneNumberFromString(phoneNumber, {
      defaultCountry: 'US',
    });
    return parsedNumber?.formatInternational() || phoneNumber;
  }

  async openTranscriptModal() {
    let fromName = this.ownerName();
    let toName = this.associatedCrmObject() || '';
    if (this.cardInfo().direction === 'Inbound') {
      fromName = this.associatedCrmObject() || '';
      toName = this.ownerName();
    }

    const [namespace, parentNamespace] = await firstValueFrom(
      combineLatest([this.config.namespace$, this.config.parentNamespace$]),
    );
    const partnerId = parentNamespace ? parentNamespace : namespace;
    const accountGroupId = namespace;
    const dialogRef = this.dialog.open(CallTranscriptModalComponent, {
      width: '950px',
      maxWidth: '100vw',
      maxHeight: '100vh',
      data: {
        partnerId,
        accountGroupId,
        callRecordId: this.cardInfo().callRecordId,
        transcriptJSON: this.cardInfo().transcriptJSON,
        toName: toName || this.cardInfo().to,
        fromName: fromName || this.cardInfo().from,
      },
    });

    const smallDialogSubscription = this.isExtraSmall.subscribe((size) => {
      if (size.matches) {
        dialogRef.updateSize('100vw', '100vh');
      } else {
        dialogRef.updateSize('950px', '80%');
      }
    });
    dialogRef.afterClosed().subscribe(() => {
      smallDialogSubscription.unsubscribe();
    });
  }

  constructor(
    private readonly fieldService: CrmFieldService,
    private readonly http: HttpClient,
    private readonly crmObjectService: CrmObjectService,
    private readonly dialog: MatDialog,
  ) {
    this.callRecordingSource$ = toObservable(this.cardInfo).pipe(
      switchMap((cardInfo) => {
        if (!cardInfo.callRecordingUrl) {
          return of(null);
        }
        // If the recordingURL contains apigateway.co call it with auth to get the signed temporary URL
        if (cardInfo.callRecordingUrl.includes('apigateway.co')) {
          return this.http.get(cardInfo.callRecordingUrl, {
            responseType: 'text',
            withCredentials: true,
          });
        }
        return of(cardInfo.callRecordingUrl);
      }),
    );
    effect(() => {
      this.associationService.activity = this.activity();
    });
  }
}

interface ActivityCardInformation {
  body: string;
  status: string;
  outcome?: string;
  direction: string;
  callRecordId?: string;
  callRecordingUrl?: string;
  date?: Date | null;
  to: string;
  from: string;
  duration: number;
  transcriptJSON: string;
}
