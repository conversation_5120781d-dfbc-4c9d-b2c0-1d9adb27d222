<div *ngIf="cardInfo() as cardInfo" class="activity-card-call">
  <crm-activity-card
    [activity]="activity()"
    [icon]="ActivityIcons[ActivityTypes.Call]"
    [showPrimaryAssociation]="false"
  >
    <!-- Activity title -->
    <span activity-title>
      <div>
        <strong>{{ 'ACTIVITY.CALL.TITLE' | translate }}</strong>
        @if (cardInfo.status) {
          - {{ cardInfo.status }}
          @if (showPrimaryAssociation()) {
            @if (primaryAssociation(); as primaryAssociation) {
              with <a [routerLink]="routePrefix() + '/' + primaryAssociation.linkUrl">{{ primaryAssociation.name }}</a>
            }
          }
        }
      </div>
    </span>

    <!-- Activity subtitle -->
    <span activity-subtitle>
      @if (cardInfo.duration) {
        {{ cardSubtitle() }}
      }
    </span>

    <!-- Always visible content -->
    <div activity-preview>
      @if (cardInfo.body) {
        <div class="activity-body">
          <span class="call-body" [innerHTML]="cardInfo.body | iTrustThisHtml"></span>
        </div>
      }

      <!-- Additional content -->
      <div activity-content>
        <ng-container *ngIf="callRecordingSource$ | async as callUrl">
          <audio class="activity-data-call-recording" preload="metadata" controls src="{{ callUrl }}">
            {{ 'ACTIVITY.AUDIO_PLAYBACK_ERROR' | translate }}
          </audio>
        </ng-container>

        @if (cardInfo.transcriptJSON) {
          <a data-action="view-transcript-clicked" (click)="openTranscriptModal()">
            {{ 'ACTIVITY.CALL.VIEW_TRANSCRIPT' | translate }}
          </a>
        }

        <div class="row">
          <ng-container
            [ngTemplateOutlet]="activityData"
            [ngTemplateOutletContext]="{ label: 'ACTIVITY.STATUS', value: cardInfo.status }"
          ></ng-container>
          <ng-container
            [ngTemplateOutlet]="activityData"
            [ngTemplateOutletContext]="{ label: 'ACTIVITY.OUTCOME', value: cardInfo.outcome }"
          ></ng-container>
        </div>
      </div>
    </div>
  </crm-activity-card>
</div>

<ng-template #activityData let-label="label" let-value="value">
  <div class="col col-xs-12 col-md-4">
    <div class="activity-data-label">
      {{ label | translate }}
    </div>
    <div class="activity-data-value">
      @if (value) {
        <ng-container>
          {{ value }}
        </ng-container>
      }
    </div>
  </div>
</ng-template>
