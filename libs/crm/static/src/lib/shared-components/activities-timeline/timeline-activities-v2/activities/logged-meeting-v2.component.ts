import { CommonModule } from '@angular/common';
import { Component, computed, model, OnInit, Signal } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TranslationModule } from '../../../../i18n/translation-module';
import { CrmFieldService, StandardIds, SystemFieldIds } from '../../../../shared-services/crm-services/field.service';
import { ActivityInterface } from '@vendasta/crm';
import { intervalToDuration } from 'date-fns';
import { ActivityRichTextComponent } from '../../activities/activity-rich-text.component';
import { ActivityAssociationService, ActivityIcons, ActivityTypes, CrmObjectIdentifier } from '../../activities';
import { User } from '@vendasta/iamv2';
import { ActivityCardComponent } from './system-activities/activity-card/activity-card.component';
import { RouterLink } from '@angular/router';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { DateFormat } from '@vendasta/galaxy/utility/date-utils';

@Component({
  selector: 'crm-activity-logged-meeting-v2',
  templateUrl: './logged-meeting-v2.component.html',
  styleUrls: ['./logged-meeting-v2.component.scss'],
  providers: [],
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    ActivityRichTextComponent,
    ActivityCardComponent,
    RouterLink,
    GalaxyPipesModule,
  ],
})
export class CrmActivityLoggedMeetingV2Component implements OnInit {
  private unknownNamePlaceholder = this.translate.instant('CONTACT.ACTIVITIES.UNKNOWN_NAME');
  activity = model<ActivityInterface>({});
  ownerUser = model<User | undefined>(undefined);
  associatedObject = model<CrmObjectIdentifier | undefined>(undefined);
  dateFormat = DateFormat.mediumDateShortTime;

  icon = computed(() => ActivityIcons[ActivityTypes.Meeting]);

  fullName = computed(() => {
    const user = this.ownerUser() as User;
    return `${user?.firstName || ''} ${user?.lastName || ''}`.trim() || this.unknownNamePlaceholder;
  });

  showPrimaryAssociation = computed(() => !this.associatedObject()?.crmObjectId);
  primaryAssociation = this.associationService.primaryAssociation;
  routePrefix = this.associationService.routePrefix;
  primaryAssociationLink = computed(() => {
    if (!this.primaryAssociation()) return '';
    const linkUrl = this.primaryAssociation()?.linkUrl;
    return `${this.routePrefix()}/${linkUrl}`;
  });

  cardInfo: Signal<ActivityCardInformation> = computed(() => {
    const duration = this.getIntegerFieldData(this.activity(), StandardIds.ActivityMeetingDuration);
    return {
      title: this.getTitle(),
      body: this.getStringFieldData(this.activity(), StandardIds.ActivityMeetingBody),
      status: this.getStringFieldData(this.activity(), StandardIds.ActivityMeetingStatus),
      outcome: this.getStringFieldData(this.activity(), StandardIds.ActivityMeetingOutcome),
      duration: this.convertMillisecondsToHoursAndMinutes(duration),
      date: this.getDateFieldData(this.activity(), SystemFieldIds.ActivityEffectiveDate),
    };
  });

  private getStringFieldData(activity: ActivityInterface, fieldId: string): string {
    return this.fieldService.getFieldValueFromCrmObject(activity, fieldId)?.stringValue ?? '';
  }

  private getIntegerFieldData(activity: ActivityInterface, fieldId: string): number {
    return this.fieldService.getFieldValueFromCrmObject(activity, fieldId)?.integerValue ?? 0;
  }

  private getDateFieldData(activity: ActivityInterface, fieldId: string): Date | null {
    return this.fieldService.getFieldValueFromCrmObject(activity, fieldId)?.dateValue ?? null;
  }

  private convertMillisecondsToHoursAndMinutes(duration: number): string {
    if (duration === 0) return '';
    const convertedDuration = intervalToDuration({ start: 0, end: duration });
    if (convertedDuration.hours && convertedDuration.hours > 0) {
      return `${convertedDuration.hours}h ${convertedDuration.minutes}m`;
    }
    return `${convertedDuration.minutes}m`;
  }

  private getTitle() {
    const typeWithVerb = 'ACTIVITY.LOGGED_ACTIVITY.HEADER_ADDITIONAL_TEXT.MEETING';
    const preposition = 'ACTIVITY.LOGGED_ACTIVITY.HEADER_ADDITIONAL_TEXT.BY';
    return {
      typeWithVerb,
      preposition,
    };
  }

  ngOnInit(): void {
    this.associationService.activity = this.activity();
  }

  constructor(
    private readonly fieldService: CrmFieldService,
    private readonly associationService: ActivityAssociationService,
    private readonly translate: TranslateService,
  ) {}
}

interface ActivityHeaderParts {
  typeWithVerb: string;
  preposition: string;
}

interface ActivityCardInformation {
  title: ActivityHeaderParts;
  body: string;
  outcome: string;
  status: string;
  duration: string;
  date: Date | null;
}
