<div class="header">
  <div class="header-left">
    <h2 mat-dialog-title class="title">{{ 'INBOX.EVENTS.PHONE.TRANSCRIPT' | translate }}</h2>

    @if ((showVoiceConfig$ | async) && (callRecordHasVoiceSettings$ | async)) {
      @let callRecord = callRecord$ | async;
      @let openaiRealtimeConfig = callRecord?.metadata?.voiceConfig?.openaiRealtimeConfig;
      <mat-icon [glxyPopover]="popover" (click)="popover.open()" class="click-target">info_outline </mat-icon>

      <glxy-popover
        #popover
        [(isOpen)]="show"
        [maxWidth]="'400px'"
        [maxHeight]="'200px'"
        [hasBackdrop]="true"
        [showBackdrop]="false"
        [closeOnBackdropClick]="true"
        [closeOnEscapeKey]="true"
        [hasArrow]="false"
        [keepOnScreen]="true"
        [padding]="'small'"
        [highContrast]="false"
        [positions]="[PopoverPositions.Left]"
        [mobileFullScreen]="false"
        [showMobileClose]="true"
      >
        {{ 'INBOX.EVENTS.PHONE.VOICE_AI_SETTINGS_LABEL' | translate }}
        @if (openaiRealtimeConfig?.voice) {
          <div>
            {{ ('INBOX.EVENTS.PHONE.VOICE_LABEL' | translate) + ': ' + openaiRealtimeConfig?.voice }}
          </div>
        }
        @if (openaiRealtimeConfig?.turnDetection?.threshold) {
          <div>
            {{
              ('INBOX.EVENTS.PHONE.TURN_DETECTION_THRESHOLD_LABEL' | translate) +
                ': ' +
                openaiRealtimeConfig.turnDetection.threshold
            }}
          </div>
        }
        @if (openaiRealtimeConfig?.turnDetection?.prefixPadding) {
          <div>
            {{
              ('INBOX.EVENTS.PHONE.TURN_DETECTION_PREFIX_PADDING_LABEL' | translate) +
                ': ' +
                openaiRealtimeConfig.turnDetection.prefixPadding
            }}
          </div>
        }
        @if (openaiRealtimeConfig?.turnDetection?.silenceDuration) {
          <div>
            {{
              ('INBOX.EVENTS.PHONE.TURN_DETECTION_SILENCE_DURATION_LABEL' | translate) +
                ': ' +
                openaiRealtimeConfig.turnDetection.silenceDuration
            }}
          </div>
        }
      </glxy-popover>
    }
  </div>
  <div class="header-right">
    <button mat-icon-button (click)="closeDialog()">
      <mat-icon>close</mat-icon>
    </button>
  </div>
</div>
<mat-dialog-content class="dialog-content">
  <glxy-chat-container [chatId]="''" [animationDuration]="200">
    <div class="chat-bubbles">
      @let assistantProfilePicUrl = (assistantProfilePic$ | async) ?? undefined;
      @let assistantLoading = assistantLoading$ | async;
      @for (messageGroup of transcript; track messageGroup) {
        <glxy-chat-message-group
          [type]="messageGroup.name === 'To' ? 'sent' : 'received'"
          [showProfilePic]="messageGroup.name === 'To' ? !assistantLoading : true"
          [profilePicUrl]="messageGroup.name === 'To' ? assistantProfilePicUrl : undefined"
          [messageFrom]="messageGroup.name === 'To' ? data.toName : data.fromName"
          [profileSVGIcon]="messageGroup.name === 'To' && !assistantLoading ? OPENAI_BOT_SVG_ICON : undefined"
          [profileBackgroundColor]="
            messageGroup.name === 'To' && !assistantLoading ? OPENAI_BOT_BACKGROUND_COLOR : undefined
          "
        >
          @for (message of messageGroup.messages; track message) {
            <glxy-chat-message [messageTime]="message.timestamp | date: 'h:mm:ss a'" [messageText]="message.content" />
          }
        </glxy-chat-message-group>
      }
    </div>
  </glxy-chat-container>
</mat-dialog-content>
