import { Component, computed, ElementRef, inject, Inject, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogActions, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule, DatePipe } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { ActivityAssociationDisplayInfo, ActivityAssociationService } from './activity-association.service';
import { take } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { TranslationModule } from '../../../i18n/translation-module';
import { FeedbackFormModalComponent } from './feedback-form-modal.component';
import { EvaluationFeedback, EvaluationReason, MeetingAnalysisApiService } from '@vendasta/meeting-analysis';
import { intervalToDuration } from 'date-fns';
import { CrmActivityAssociationPopoverComponent } from './association-popover.component';
import { PrimaryAccountGroupIDForCrmObject } from '../../../tokens-and-interfaces';
import { of } from 'rxjs';
import { ActivityInterface } from '@vendasta/crm';
import { RouterLink } from '@angular/router';
import { Clipboard } from '@angular/cdk/clipboard';

@Component({
  selector: 'crm-meeting-summary-modal',
  templateUrl: './meeting-summary-modal.component.html',
  styleUrls: ['./meeting-summary-modal.component.scss'],
  imports: [
    MatDialogModule,
    TranslateModule,
    TranslationModule,
    MatCardModule,
    MatIconModule,
    DatePipe,
    MatDialogActions,
    MatButtonModule,
    CommonModule,
    CrmActivityAssociationPopoverComponent,
    RouterLink,
  ],
  providers: [
    ActivityAssociationService,
    {
      provide: PrimaryAccountGroupIDForCrmObject,
      useFactory: () => {
        // Used for opportunity associations, which we don't need at the moment
        return of('');
      },
    },
  ],
})
export class MeetingSummaryModalComponent {
  @ViewChild('downloadAnchor', { static: true })
  private downloadAnchor: ElementRef;

  private readonly associationService = inject(ActivityAssociationService);
  externalRoutePrefix = computed(() => {
    const crmInternalRoutePrefix = this.associationService.routePrefix();
    return crmInternalRoutePrefix.replace('/crm', '');
  });

  constructor(
    private clipboard: Clipboard,
    public dialogRef: MatDialogRef<MeetingSummaryModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: MeetingSummaryData,
    private http: HttpClient,
    private snackbarService: SnackbarService,
    private readonly dialog: MatDialog,
    private meetingAnalysisApiService: MeetingAnalysisApiService,
    private readonly activityAssociationService: ActivityAssociationService,
  ) {
    this.activityAssociationService.activity = this.data.activity;
  }

  title = this.data.meetingTitle;
  summary = this.data.summary;
  keyTakeaways = this.data.keyTakeaways;
  actionItems = this.data.actionItems;
  duration = this.data.duration;
  meetingDate = this.data.meetingDate;
  recordingUrl = this.data.recordingUrl;
  transcriptUrl = this.data.transcriptUrl;
  sentimentScore = this.data.sentimentScore;
  salesScore = this.data.salesScore;
  companies = this.data.companies;
  processedTranscriptID = this.data.processedTranscriptID;

  copyToClipboard(copyItem: string | string[]): void {
    const text = Array.isArray(copyItem) ? (copyItem.length ? '• ' + copyItem.join('\n• ') : '') : (copyItem ?? '');

    this.clipboard.copy(text);

    this.snackbarService.openSuccessSnack('COMMON.SNACKBAR_MESSAGE.COPY_SUCCESS');
  }
  hasRecording() {
    return !!this.recordingUrl;
  }

  viewRecording() {
    if (this.recordingUrl) {
      window.open(this.recordingUrl, '_blank');
    }
  }

  downloadTranscript() {
    this.http
      .get(this.transcriptUrl || '', {
        responseType: 'blob',
        withCredentials: true,
      })
      .pipe(take(1))
      .subscribe({
        next: (result) => {
          this.snackbarService.openSuccessSnack('ACTIVITY.MEETING.MEETING_ANALYSIS.DOWNLOAD_WILL_START_SHORTLY');
          this.downloadFile(result, 'transcript.txt');
        },
        error: (error) => {
          console.error(error);
          this.snackbarService.openErrorSnack(error?.message);
        },
      });
  }

  downloadFile(file: Blob, filename: string): void {
    // Based off this article: https://www.illucit.com/en/angular/angular-5-httpclient-file-download-with-authentication/
    const url = window.URL.createObjectURL(
      new Blob([file], {
        type: 'text/plain',
      }),
    );
    const link = this.downloadAnchor.nativeElement;
    link.href = url;
    link.download = filename;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  thumbsUp() {
    this.submitEvaluation(EvaluationFeedback.EVALUATION_FEEDBACK_POSITIVE, [], '');
  }

  thumbsDown() {
    const dialogRef = this.dialog.open(FeedbackFormModalComponent, {
      width: '600px',
      data: { processedTranscriptID: this.processedTranscriptID },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.submitEvaluation(result.feedbackType, result.feedbackReasons, result.comment);
      }
    });
  }

  submitEvaluation(feedbackType: EvaluationFeedback, feedbackReasons: EvaluationReason[], comment: string) {
    const request = {
      conversationId: this.processedTranscriptID,
      feedback: feedbackType,
      comment: comment,
      reasons: feedbackReasons,
    };

    this.meetingAnalysisApiService
      .createEvaluation(request)
      .pipe(take(1))
      .subscribe({
        next: () => {
          this.snackbarService.openSuccessSnack('ACTIVITY.MEETING.MEETING_ANALYSIS.FEEDBACK.SUCCESS');
        },
        error: () => {
          this.snackbarService.openErrorSnack('ACTIVITY.MEETING.MEETING_ANALYSIS.FEEDBACK.ERROR');
        },
      });
  }

  closeDialog() {
    this.dialogRef.close();
  }
}

export interface MeetingSummaryData {
  meetingTitle: string;
  summary: string;
  keyTakeaways: string[];
  actionItems: string[];
  duration: string;
  meetingDate: Date;
  sentimentScore: string;
  salesScore: string;
  companies: ActivityAssociationDisplayInfo[];
  recordingUrl?: string;
  transcriptUrl?: string;
  processedTranscriptID: string;
  activity: ActivityInterface;
}

export function convertMillisecondsToHoursAndMinutes(duration: number): string {
  if (duration === 0) return '';
  const convertedDuration = intervalToDuration({ start: 0, end: duration });
  if (convertedDuration.hours !== undefined && convertedDuration.hours > 0) {
    return `${convertedDuration.hours}h ${convertedDuration.minutes}m`;
  }
  return `${convertedDuration.minutes}m`;
}
