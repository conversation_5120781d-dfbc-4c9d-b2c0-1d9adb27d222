import { Injectable } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Environment, EnvironmentService } from '@galaxy/core';
import { CrmObjectInterface, FieldValueInterface } from '@vendasta/crm';

export const StandardExternalIds = {
  FirstName: 'standard__first_name',
  LastName: 'standard__last_name',
  PhoneNumber: 'standard__phone_number',
  Email: 'standard__email',
  Tags: 'standard__tags',
  ActivityRecordChangeType: 'standard__record_change_type',
  ContactCompanyName: 'standard__contact_company_name',
  ChangeRecordChangeType: 'standard__change_record_change_type',
};

export const NonStandardExternalIDs = {
  NetPromoterScore: 'reputation_management_net_promoter_score_details',
  LastEmailRequest: 'reputation_management__review_request_email_sent_time',
  LastSmsRequest: 'reputation_management__review_request_sms_status',
};

// these are not external_ids
// system fields do not have a UUID generated for them but are instead set from a predefined set of values
export const SystemFieldIds = {
  ActivityCreated: 'system__activity_created',
  ActivityUpdated: 'system__activity_updated',
  ActivityEffectiveDate: 'system__activity_effective',
  ActivityID: 'system__activity_id',
  ActivityType: 'system__activity_activity_type',
  ActivityOwnerID: 'system__activity_owner_id',

  ContactCreated: 'system__contact_created',
  ContactUpdated: 'system__contact_updated',
  ContactID: 'system__contact_id',
  ContactExternalID: 'system__contact_external_id',
  ContactOwnerID: 'system__contact_owner_id',
  ContactGroupID: 'system__contact_group_id',
  ContactLastActivityDate: 'system__contact_last_activity_date',

  CompanyCreated: 'system__company_created',
  CompanyUpdated: 'system__company_updated',
  CompanyID: 'system__company_id',
  CompanyExternalID: 'system__company_external_id',
  CompanyOwnerID: 'system__company_owner_id',
  CompanyGroupID: 'system__company_group_id',
  CompanyLastActivityDate: 'system__company_last_activity_date',

  OpportunityCreated: 'system__opportunity_created',
  OpportunityUpdated: 'system__opportunity_updated',
  OpportunityID: 'system__opportunity_id',
  OpportunityExternalID: 'system__opportunity_external_id',
  OpportunityOwnerID: 'system__opportunity_owner_id',
  OpportunityLastActivityDate: 'system__opportunity_last_activity_date',

  CustomObjectCreated: 'system__customobject_created',
  CustomObjectUpdated: 'system__customobject_updated',
  CustomObjectID: 'system__customobject_id',
  CustomObjectExternalID: 'system__customobject_external_id',
  CustomObjectOwnerID: 'system__customobject_owner_id',
  CustomObjectTypeID: 'system__customobject_object_type',
};

export const ContactCustomColumns = {
  FullName: 'fullName',
  FullAddress: 'fullAddress',
  PrimaryCompanyName: 'primaryCompanyName',
  Location: 'location',
};

export const CompanyCustomColumns = {
  FullAddress: 'fullAddress',
  ParentCompanyName: 'parentCompanyName',
};

export const TaskCustomColumns = {
  AssignedTo: 'assignedTo',
  AssociatedCompany: 'associatedCompany',
  AssociatedContact: 'associatedContact',
  Location: 'location',
};

export const OpportunityCustomColumns = {
  PrimaryAssociation: 'associatedContact',
};

export const StandardIds = {
  ActivityManuallyLogged: 'FieldID-836fb592-2def-4607-8ff6-58a9fd915034',
  ActivityNoteBody: 'FieldID-88134dbb-5bec-4a96-ac83-6896eacf6ead',
  ActivityNoteTitle: 'FieldID-47fbed3b-824e-4d94-959e-ea5accbd10a3',
  ActivityEmailID: 'FieldID-d77cce25-5891-4697-b005-6a1ad57f8625',
  ActivityEmailType: 'FieldID-6fbd0e7d-0af4-4f0e-8816-01aa2c4433d6',
  ActivityEmailDirection: 'FieldID-56236ac1-44a9-4cde-8020-5ff41881923f',
  ActivityEmailStatus: 'FieldID-5a15fea4-cc20-498c-a55b-97938775f00e',
  ActivityEmailStatusReason: 'FieldID-13574049-33c6-40b1-8cdd-7288f2e813b0',
  ActivityEmailBody: 'FieldID-be2d2528-0126-4914-ab53-ffc1ffb03a08',
  ActivityEmailSubject: 'FieldID-6d18abbb-bb6f-4c75-98e2-2333fb6315d3',
  ActivityEmailHeaders: 'FieldID-07775d45-b2b8-44fa-9418-f40bb296eea2',
  ActivityEmailDate: 'FieldID-85b69e6e-64de-45ee-b865-c44144e37d60',
  ActivityEmailCampaignName: 'FieldID-fd209910-fbb9-433a-8e6d-677515d3f992',
  ActivityCampaignID: 'FieldID-96b1b1b8-6534-4b2c-a974-5c45cf05e556',
  ActivityCallDirection: 'FieldID-cfc68004-2622-4c2d-a370-ef7f03b35d4b',
  ActivityCallOutcome: 'FieldID-74f24db8-bd19-4f42-84a0-00791769469e',
  ActivityCallDuration: 'FieldID-22498095-16f0-44cd-ae44-d2abfce09663',
  ActivityCallFromNumber: 'FieldID-80835376-c944-4f60-8dec-8ffdf779f2a1',
  ActivityCallToNumber: 'FieldID-9643b618-e11f-4e03-80a2-6e01384faca9',
  ActivityCallRecordingURL: 'FieldID-c4888a86-f334-452f-a6a5-c23ecffdd2e0',
  ActivityCallBody: 'FieldID-3bfa5b22-d263-4cf1-bf57-70fe644d95a6',
  ActivityCallStatus: 'FieldID-a56c7fe2-a1b7-4a90-9142-67e10e87d303',
  ActivityCallDate: 'FieldID-d3125adc-2ad3-4227-9c39-291a536d28fe',
  ActivityCallRecordingUrl: 'FieldID-c4888a86-f334-452f-a6a5-c23ecffdd2e0',
  ActivityCallRecordId: 'FieldID-b337ff45-d938-4c5d-9ca2-70abb8d1e5fe',
  ActivityCallTranscript: 'FieldID-878fc317-6983-441d-bea1-2b8ca8b8e7de',
  ActivityMeetingBody: 'FieldID-e6661bd3-df23-4859-b073-8ab14a46ce8d',
  ActivityMeetingInternalNotes: 'FieldID-82d7e564-4fec-4cab-809e-8abfeb8cd961',
  ActivityMeetingURL: 'FieldID-7eb03173-ba5b-4ec7-b4c7-3e32257871d3',
  ActivityMeetingLocation: 'FieldID-4e388221-1b84-46e5-88c1-70bcc8df63d1',
  ActivityMeetingStartTime: 'FieldID-019f1d17-8663-4a82-8895-eb9e94320d1e',
  ActivityMeetingEndTime: 'FieldID-296520b8-6289-4924-a3b7-5c7bb085c3e2',
  ActivityMeetingOutcome: 'FieldID-98fe825a-82b3-4f4b-ae5f-0fcc08369668',
  ActivityMeetingStatus: 'FieldID-a7b66d53-12e3-4f22-a42c-6f0ccc26ab56',
  ActivityMeetingDuration: 'FieldID-f1118e0f-bbcc-4ae9-b0a6-d95c1db37777',
  ActivityMeetingSentiment: 'FieldID-7db820f4-2ba9-40f2-b173-d3d490f9ffca',
  ActivityMeetingSalesScore: 'FieldID-64e359bc-e18d-4363-bccd-eb8d491ad3bf',
  ActivityMeetingSummaryTitle: 'FieldID-971c636e-6322-4a08-b143-d48b8858307b',
  ActivityMeetingSummaryBody: 'FieldID-4605f29f-76af-4784-a09f-2495cf2e325a',
  ActivityMeetingKeyTakeaways: 'FieldID-313e65ae-933c-402e-bd7a-75ce518a3bb6',
  ActivityMeetingActionItems: 'FieldID-4d8c7a31-cabb-4aac-a2c2-4e73e341590b',
  ActivityMeetingAnalysisProcessedTranscriptID: 'FieldID-3ec4ac0b-bd79-4645-b9ea-119689ef6246',
  ActivityMeetingLastAnalyzedDate: 'FieldID-280edfa8-3b78-47ff-b270-a268decffff6',
  ActivityMeetingTranscriptUrl: 'FieldID-489ae5a5-bde1-4fe1-bb89-a251ffff99c8',
  ActivityMeetingRecordingUrl: 'FieldID-4d87d5a5-9165-46b2-af81-6e8a323c9a32',
  ActivityName: 'FieldID-be62ace6-9cd8-4899-b283-82ea9c2facb1',
  ActivitySourceName: 'FieldID-508a87c7-8c3e-4664-a3dc-6626fafb3fce',
  ActivityRecordSourceDrillDown1: 'FieldID-bd927830-22b3-4771-9ac3-124bef0c1a05',
  ActivityRecordSourceDrillDown2: 'FieldID-22f1dbd6-f1e2-4c33-b064-383885c303a1',
  ActivityTaskBody: 'FieldID-5cbae7eb-51a6-4de5-ace2-99f41d2869af',
  ActivityTaskDueDate: 'FieldID-aa757187-94a4-422f-88b1-2b4b7d236f85',
  ActivityTaskPriority: 'FieldID-5ee74662-8a88-40df-b2bd-e1899cf7533b',
  ActivityTaskStatus: 'FieldID-b4d2e328-5792-4226-baad-163509af6cd9',
  ActivityTaskType: 'FieldID-79ba5085-0c91-4d5e-bb40-8c4302817408',
  ActivityTags: 'FieldID-244bd78f-8022-4bc6-a746-daf1427f0d6f',
  ActivityCommunicationChannel: 'FieldID-e684b889-06e9-4f17-901a-974e423ac44e',
  ActivityCommunicationOutcome: 'FieldID-4574fcba-ae33-4f40-ad33-dfb5c12d5f7b',
  ActivityCommunicationDirection: 'FieldID-fb96f396-b75e-4eb2-8411-c1399f734745',
  ActivityCommunicationFromId: 'FieldID-180dc101-2ba3-414d-9f1f-62cd62248d62',
  ActivityCommunicationToId: 'FieldID-d1045c66-f262-4631-b5cc-efea42b6f04d',
  ActivityCommunicationBody: 'FieldID-326742c0-d5ac-480c-b73e-0c1e6085f14a',
  ActivityCommunicationDate: 'FieldID-1ab5f7a8-4517-4992-a31b-2af9d2735870',
  ActivityCommunicationType: 'FieldID-e02e253d-33ff-4656-a1d1-e43e6d01b17b',
  ActivityCommunicationStatus: 'FieldID-886ff621-e859-435a-bfc4-b4b6e8f171eb',
  ActivityUrl: 'FieldID-e1b74b4f-9f6c-4fb5-a251-4621b89a14ed',
  ActivityUrlName: 'FieldID-26c17f01-6ca8-4a3c-8bb2-ddf3ba4a5677',
  ActivityOpportunityChangeID: 'FieldID-f004651a-1d60-4e0a-8ee5-8b6615834f71',
  ActivityOpportunityChangePipelineStage: 'FieldID-6c06383b-ce77-40e4-8b62-c45b3b8fc262',
  ActivityOpportunityChangeLostReason: 'FieldID-799f1ae7-f934-4b7b-b114-79600c17244f',
  ActivityOpportunityChangeNotes: 'FieldID-47338101-7d63-484b-82b8-b728e8abc623',
  ActivityOpportunityChangeUpdatedDate: 'FieldID-1ee17946-c5f5-447d-b301-b4fd836aa41e',
  ActivityOpportunityChangeSalespersonID: 'FieldID-02489de8-fc56-4175-bc4b-a9b16d9e12eb',
  CompanyName: 'FieldID-b2a5d86e-f9ae-11ed-be56-0242ac120002',
  CompanyPrimaryAddressLine1: 'FieldID-36785fbc-9c40-4938-92e3-b262d40ef6bb',
  CompanyPrimaryAddressLine2: 'FieldID-ac4c2ea1-f8d1-4bdd-8a42-a1ad4127eef6',
  CompanyPrimaryAddressCity: 'FieldID-33b7b0a0-82ed-471b-baa1-87ab958f90d2',
  CompanyPrimaryAddressState: 'FieldID-184fe9c2-b3a8-47c5-85dc-7c3a285c17ac',
  CompanyPrimaryAddressPostalCode: 'FieldID-b0e75bc5-59f7-4c54-98a8-510b9ec982ec',
  CompanyPrimaryAddressCountry: 'FieldID-daa0c91f-8a0e-4c90-be9c-e53a73789edc',
  CompanyPrimaryAddressGooglePlaceID: 'FieldID-ecba7574-ed05-4e89-98f2-d49a2c307a69',
  CompanyPrimaryAddressLatitudeLongitude: 'FieldID-017480ce-49d4-4453-8622-c1e2633eae73',
  CompanyWebsite: 'FieldID-f25628fb-eeb3-4d99-8737-44b403758837',
  CompanyPhoneNumber: 'FieldID-fc7ff4ae-b753-4650-9961-42b07bcc6ac6',
  CompanyTags: 'FieldID-b74e203d-bab3-4c78-83d0-ac96f44d1061',
  CompanyLifecycleStage: 'FieldID-865f4984-2fa2-462c-82d0-f78ad1477fb0',
  CompanyTimezone: 'FieldID-3ebce03e-260b-4573-93b0-85f5582ade3d',
  CompanyCategoryIDs: 'FieldID-3cf8b8e0-bc71-4593-a545-d32707445fea',
  CompanyParentCompanyID: 'FieldID-92bb3816-a000-4075-a56d-51458f99d7ab',
  CompanyGoogleReviewScore: 'FieldID-c1fd51d0-3971-4ffd-addb-9afb08ab1d8d',
  CompanyLeadScore: 'FieldID-512d33e1-6682-4848-9b0e-5190f8e7c2a3',
  CompanyLeadQuality: 'FieldID-5a082a52-9b65-4769-a750-261ef54fa51f',
  CompanyGBPClaimed: 'FieldID-63d04854-7d4a-4e2e-9358-0c349a51f66c', // from lead prospector
  CompanyLastEngagementDate: 'FieldID-f63f74aa-e77a-4990-88b4-03095c4bcec2',
  CompanyReviewGrade: 'FieldID-17dc90f1-2f14-4ddd-9ec6-d79af0f0b937',
  CompanySocialGrade: 'FieldID-42eed722-ea35-4aa6-a577-fb0898108cbe',
  CompanyWebsiteGrade: 'FieldID-97309ba6-4bc6-4931-9ea6-c3f9ef4cb57d',
  CompanyListingGrade: 'FieldID-22894f6c-7e61-4ea3-aaeb-f2aef4204581',
  CompanySEOGrade: 'FieldID-59cff5d2-00a3-4ba3-910c-9ffdbdc63266',
  ContactPrimaryCompanyID: 'FieldID-e4d82132-ed77-4a2f-8625-1163a04efba1',
  ContactPrimaryAddressLine1: 'FieldID-59dc9adf-6401-4af4-91cb-666ce81acc37',
  ContactPrimaryAddressLine2: 'FieldID-623fb8a0-c224-4952-af4c-a4e1e3bd5b70',
  ContactPrimaryAddressCity: 'FieldID-4fe28279-6e83-4e7c-b310-9474dcd5034d',
  ContactPrimaryAddressState: 'FieldID-a215813c-8225-4c21-b646-01b370eefd47',
  ContactPrimaryAddressPostalCode: 'FieldID-3ba73c58-3fa9-4fbf-b28b-8709837685bc',
  ContactPrimaryAddressCountry: 'FieldID-aa50a455-c00b-4d6b-98f7-f833332b1b5c',
  CompanyPrimarySalespersonID: 'FieldID-1d95f7ec-48f5-407e-ae13-22bf13420a7f',
  CompanySourceName: 'FieldID-2cf065dc-7627-4247-97d9-77b594ecebe4',
  ContactPrimarySalespersonID: 'FieldID-46d1bf91-111a-44d6-a029-9f6722e51fc9',
  CompanyAdditionalSalespersonIDs: 'FieldID-1f3217bc-0712-41b9-aa79-9923b18ebae8',
  ContactAdditionalSalespersonIDs: 'FieldID-3febf407-1d0d-4e06-a962-8774560a2dbc',
  CompanyAdditionalTeamMemberIDs: 'FieldID-7565e426-cfa2-41b4-aa85-5351aa106cc2',
  ContactAdditionalTeamMemberIDs: 'FieldID-716159d5-06b1-4ca1-8b6d-11124fe1a6ea',
  CompanyRecordSourceDrill1: 'FieldID-5e852887-d57f-4186-9140-020981ed5e4f',
  CompanyOriginalSource: 'FieldID-62bebfb2-bd7b-4121-ad37-9625a4900f56',
  CompanyOriginalSourceDrill1: 'FieldID-c8f9ec5f-22b0-4f6e-bac3-feecb2ed2c5b',
  ContactLifecycleStage: 'FieldID-5a74c326-94d8-45ab-90ab-fa319d4812e7',
  ContactJobTitle: 'FieldID-7dba3c94-e95a-49c4-aeb2-765621e4bb87',
  CompanyLinkedInURL: 'FieldID-ac372fb8-3066-447f-9c0b-b7fc693390b0',
  CompanyFacebookURL: 'FieldID-b71abcf6-9091-4be9-a63d-14a6151124e5',
  CompanyPinterestURL: 'FieldID-9011434d-7e16-429b-8d80-db72e6ffa0df',
  CompanyTikTokURL: 'FieldID-6d056a77-c30f-43b7-acb6-ffb60c20a6f9',
  CompanyXURL: 'FieldID-d46270a6-dab3-4ff9-b617-920659297469',
  CompanyInstagramURL: 'FieldID-60e53809-3833-4977-a563-77538c19c2c4',
  ContactLinkedInURL: 'FieldID-b7ef3c20-810d-4a82-80ab-177a95dd7d6e',
  ContactSourceName: 'FieldID-d80132b7-4fbe-45dd-92d3-a313286567ac',
  RecordChangedByUserID: 'FieldID-e7c112b0-15da-4400-8ad7-94150856bdbd',
  RecordChangeFieldIDsList: 'FieldID-7714b53d-f29f-43ac-a038-91daaf1bdcc8',
  RecordChangeReferenceFieldID: 'FieldID-b12f7278-2aee-465e-a37d-75fd0d248724',
  CompanyNextTaskDueDate: 'FieldID-bd887f68-c188-4124-9378-0f0c5e08e067',
  ContactNextTaskDueDate: 'FieldID-4b174b2a-74bc-4dc6-aa78-c77e27e27fce',
  ContactRecordSourceDrill1: 'FieldID-9c480157-7dd2-478e-b296-06a00124af2e',
  ContactOriginalSource: 'FieldID-78e702fa-84c1-46c3-84d9-c59b9f83db88',
  ContactOriginalSourceDrill1: 'FieldID-4cd02441-ace4-4d64-befe-d07a880758da',
  ContactLastEngagementDate: 'FieldID-9d3544b9-b53e-4477-a4c1-7655b582a47e',
  ContactLeadScore: 'FieldID-bb231018-4fe9-4744-b729-95e32f3692da',
  ContactLeadQuality: 'FieldID-232e19aa-d5b4-4746-9f70-de01bb6101e4',
  CompanyLastCampaignEmailOpenDate: 'FieldID-c511effb-b0da-40c9-b35e-481f53b67c1b',
  CompanyLastCampaignEmailClickedDate: 'FieldID-86093a23-ca44-46e0-a0c1-1b1ca0f7de2e',
  ContactLastCampaignEmailOpenDate: 'FieldID-e16d7514-834b-4e39-9df5-36155e736424',
  ContactLastCampaignEmailClickedDate: 'FieldID-abcfcf20-f632-433e-9eee-36aabd9da6ba',
  ContactMarketingEmailConsentStatus: 'FieldID-7e76c322-bf7c-47ed-817a-dc4e2d60402f',
  ContactMarketingEmailConsentSource: 'FieldID-b23bf8e2-b543-4788-8082-4cef2722d435',
  ContactMarketingEmailConsentTimestamp: 'FieldID-95ce7369-6556-45b7-b2ea-2efe17afa360',
  ContactSMSConsentStatus: 'FieldID-7c8eda3b-b1b9-44b1-8f88-adff9dbc61fb',
  ContactSMSConsentSource: 'FieldID-6b8b9930-6b8e-40d2-ace6-11372797870d',
  ContactSMSConsentTimestamp: 'FieldID-265dfa1e-4cd8-4c79-9e06-d87a298e404e',
  CustomObjectName: 'FieldID-b147e280-2cc0-48f2-9fb1-35be050385c0',

  OpportunityName: 'FieldID-4c23e7e6-b699-4a66-84ed-b177604e6f61',
  OpportunityAmount: 'FieldID-b4309e38-73cc-4bc1-85e3-3d7de7b824fd',
  OpportunityCurrency: 'FieldID-625e5c04-ab9a-4b0c-81ff-383e24225b10',
  OpportunityExpectedCloseDate: 'FieldID-0ad290fd-52d5-43ce-8cf7-cb02476aff12',
  OpportunityActualCloseDate: 'FieldID-462247c9-6378-42cd-81c1-7a75c829a994',
  OpportunityTags: 'FieldID-3b84bfa4-f1f6-4c4e-a8a3-f28c4d3a2367',
  OpportunityPipelineID: 'FieldID-572b4034-8628-401f-bcf8-3a1f4f3514b7',
  OpportunityProbability: 'FieldID-0c14d2b6-e636-4191-a77e-dd7dc56ff7d3',
  OpportunityStatus: 'FieldID-99be2e4e-13d3-4e21-aeb2-778d92bf0705',
  OpportunityCalculatedStageID: 'FieldID-a2eea10a-e888-41c9-97e2-679be260b21d',
  OpportunitySourceName: 'FieldID-63efc1c8-c83a-46e2-a6b1-5db6dee85ce2',
  OpportunityClosedLostReason: 'FieldID-874eaec2-ceef-4b03-b4b8-a7a206561120',
  OpportunityClosedLostReasonDescription: 'FieldID-dfd465e3-f0f0-48f0-91c4-7be58e73c9cf',
};

export const PlatformExtensionFieldIds = {
  AccountGroupID: 'FieldID-12c064c5-3260-478e-952d-04a3a7e83b15',
  LatestSnapshotID: 'FieldID-60e8f540-6ad5-4e04-a08d-ac6fd1a331e7',
  UserID: 'FieldID-2ccc0de0-89be-499a-968d-b6d1d59b77a1',
  GoogleBusinessProfileClaimed: 'FieldID-4bd53e5d-b489-4271-89f9-d51bf7efe2a1', // from Snapshot
};

// these are not external_ids, not system fields, and have the same value across all environments
const fieldIds: string[] = [
  StandardIds.ActivityManuallyLogged,
  StandardIds.ActivityNoteBody,
  StandardIds.ActivityNoteTitle,
  StandardIds.ActivityEmailType,
  StandardIds.ActivityEmailDirection,
  StandardIds.ActivityEmailStatus,
  StandardIds.ActivityEmailBody,
  StandardIds.ActivityEmailSubject,
  StandardIds.ActivityEmailHeaders,
  StandardIds.ActivityEmailDate,
  StandardIds.ActivityCampaignID,
  StandardIds.ActivityCallDirection,
  StandardIds.ActivityCallOutcome,
  StandardIds.ActivityCallDuration,
  StandardIds.ActivityCallFromNumber,
  StandardIds.ActivityCallToNumber,
  StandardIds.ActivityCallRecordingURL,
  StandardIds.ActivityCallRecordId,
  StandardIds.ActivityCallBody,
  StandardIds.ActivityCallStatus,
  StandardIds.ActivityCallDate,
  StandardIds.ActivityCallRecordingUrl,
  StandardIds.ActivityMeetingBody,
  StandardIds.ActivityMeetingInternalNotes,
  StandardIds.ActivityMeetingURL,
  StandardIds.ActivityMeetingLocation,
  StandardIds.ActivityMeetingStartTime,
  StandardIds.ActivityMeetingEndTime,
  StandardIds.ActivityMeetingOutcome,
  StandardIds.ActivityMeetingStatus,
  StandardIds.ActivityMeetingDuration,
  StandardIds.ActivityName,
  StandardIds.ActivitySourceName,
  StandardIds.ActivityRecordSourceDrillDown1,
  StandardIds.ActivityRecordSourceDrillDown2,
  StandardIds.ActivityTaskBody,
  StandardIds.ActivityTaskDueDate,
  StandardIds.ActivityTaskPriority,
  StandardIds.ActivityTaskStatus,
  StandardIds.ActivityTaskType,
  StandardIds.ActivityTags,
  StandardIds.ActivityCommunicationChannel,
  StandardIds.ActivityCommunicationOutcome,
  StandardIds.ActivityCommunicationBody,
  StandardIds.ActivityCommunicationDate,
  StandardIds.ActivityCommunicationFromId,
  StandardIds.ActivityCommunicationToId,
  StandardIds.ActivityUrl,
  StandardIds.ActivityUrlName,
  StandardIds.ActivityOpportunityChangeID,
  StandardIds.ActivityOpportunityChangePipelineStage,
  StandardIds.ActivityOpportunityChangeLostReason,
  StandardIds.ActivityOpportunityChangeNotes,
  StandardIds.ActivityOpportunityChangeUpdatedDate,
  StandardIds.ActivityOpportunityChangeSalespersonID,
  StandardIds.CompanyName,
  StandardIds.CompanyParentCompanyID,
  StandardIds.CompanyNextTaskDueDate,
  StandardIds.CompanyPrimaryAddressLine1,
  StandardIds.CompanyPrimaryAddressLine2,
  StandardIds.CompanyPrimaryAddressCity,
  StandardIds.CompanyPrimaryAddressState,
  StandardIds.CompanyPrimaryAddressPostalCode,
  StandardIds.CompanyPrimaryAddressCountry,
  StandardIds.CompanyPrimaryAddressLatitudeLongitude,
  StandardIds.CompanyPrimaryAddressGooglePlaceID,
  StandardIds.CompanyGBPClaimed,
  StandardIds.CompanyGoogleReviewScore,
  StandardIds.CompanyWebsite,
  StandardIds.CompanyPhoneNumber,
  StandardIds.CompanyPrimarySalespersonID,
  StandardIds.CompanyAdditionalSalespersonIDs,
  StandardIds.CompanyLeadScore,
  StandardIds.CompanyLastEngagementDate,
  StandardIds.ContactPrimaryCompanyID,
  StandardIds.ContactPrimaryAddressLine1,
  StandardIds.ContactPrimaryAddressLine2,
  StandardIds.ContactPrimaryAddressCity,
  StandardIds.ContactPrimaryAddressState,
  StandardIds.ContactPrimaryAddressPostalCode,
  StandardIds.ContactPrimaryAddressCountry,
  StandardIds.ContactPrimarySalespersonID,
  StandardIds.ContactAdditionalSalespersonIDs,
  StandardIds.ContactJobTitle,
  StandardIds.ContactLinkedInURL,
  StandardIds.RecordChangedByUserID,
  StandardIds.RecordChangeFieldIDsList,
  StandardIds.RecordChangeReferenceFieldID,
  StandardIds.ContactNextTaskDueDate,
  StandardIds.ContactLastEngagementDate,
  StandardIds.CompanyAdditionalTeamMemberIDs,
  StandardIds.ContactAdditionalTeamMemberIDs,
  StandardIds.CustomObjectName,
];

const renderableReadonlyFields: string[] = [
  SystemFieldIds.CompanyOwnerID,
  SystemFieldIds.ContactOwnerID,
  StandardIds.OpportunityStatus,
];

const demoFieldIds: { [name: string]: string } = {
  [StandardExternalIds.FirstName]: 'FieldID-3a287668-9112-4e10-864d-ae553fe2c1dc',
  [StandardExternalIds.LastName]: 'FieldID-b9d359dd-c415-4c31-a241-8e66af668f2c',
  [StandardExternalIds.PhoneNumber]: 'FieldID-da943860-ae6a-49f9-9210-56935d764ecb',
  [StandardExternalIds.Email]: 'FieldID-f2b5c6ff-a141-412e-89c7-269b7546cb63',
  [StandardExternalIds.Tags]: 'FieldID-e458cc1b-2873-4026-9eae-d0fe01c2385d',
  [StandardExternalIds.ActivityRecordChangeType]: 'FieldID-fb7ca0a7-e1a9-43a9-8ead-dd6ea20dda11',
  [StandardExternalIds.ContactCompanyName]: 'FieldID-7451a249-08ea-4fcc-8e2e-b2770ceaf636',
  [StandardExternalIds.ChangeRecordChangeType]: 'FieldID-e7c112b0-15da-4400-8ad7-94150856bdbd',
  [StandardIds.CompanyPrimarySalespersonID]: 'FieldID-1d95f7ec-48f5-407e-ae13-22bf13420a7f',
  [StandardIds.CompanyLifecycleStage]: 'FieldID-865f4984-2fa2-462c-82d0-f78ad1477fb0',
  [NonStandardExternalIDs.NetPromoterScore]: 'FieldID-0d582902-d711-4e31-ad86-01c673eca76a',
  [NonStandardExternalIDs.LastEmailRequest]: 'FieldID-dd423310-864a-453a-9f37-f98dd8617964',
  [NonStandardExternalIDs.LastSmsRequest]: 'FieldID-5153b6ea-e46b-4886-a086-96162372cf00',
};

const prodFieldIds: { [name: string]: string } = {
  [StandardExternalIds.FirstName]: 'FieldID-211d398a-d905-43f8-a8e2-c1c952d4f5cc',
  [StandardExternalIds.LastName]: 'FieldID-a1c64750-0a8e-457b-8575-bcada69db4a1',
  [StandardExternalIds.PhoneNumber]: 'FieldID-1bcd68e1-2234-4a0c-9c70-21d9a6ff0ce8',
  [StandardExternalIds.Email]: 'FieldID-d2fea894-1e23-4bf6-b488-2cd7a8359dde',
  [StandardExternalIds.Tags]: 'FieldID-a7b68b66-6820-4a44-9717-f8f4e1d399ce',
  [StandardExternalIds.ActivityRecordChangeType]: 'FieldID-fb7ca0a7-e1a9-43a9-8ead-dd6ea20dda11',
  [StandardExternalIds.ContactCompanyName]: 'FieldID-7451a249-08ea-4fcc-8e2e-b2770ceaf636',
  [StandardExternalIds.ChangeRecordChangeType]: 'FieldID-e7c112b0-15da-4400-8ad7-94150856bdbd',
  [StandardIds.CompanyPrimarySalespersonID]: 'FieldID-1d95f7ec-48f5-407e-ae13-22bf13420a7f',
  [StandardIds.CompanyLifecycleStage]: 'FieldID-865f4984-2fa2-462c-82d0-f78ad1477fb0',
  [NonStandardExternalIDs.NetPromoterScore]: 'FieldID-d92c9787-2a4c-41cf-a094-6ac0bf3507b9',
  [NonStandardExternalIDs.LastEmailRequest]: 'FieldID-2eb31e35-0c7f-401c-9921-271e8d552911',
  [NonStandardExternalIDs.LastSmsRequest]: 'FieldID-d7f307b1-b53f-4c6f-9e7d-87d882cca929',
};

@Injectable({ providedIn: 'root' })
export class CrmFieldService {
  constructor(private readonly environmentService: EnvironmentService) {}

  // identifier can be a system field ID or an external ID
  getFieldId(identifier: string): string {
    if (Object.values(SystemFieldIds).includes(identifier)) {
      return identifier;
    }

    if (Object.values(PlatformExtensionFieldIds).includes(identifier)) {
      return identifier;
    }

    if (fieldIds.includes(identifier)) {
      return identifier;
    }

    if (Object.values(StandardIds).includes(identifier)) {
      return identifier;
    }

    const environment = this.environmentService.getEnvironment();
    switch (environment) {
      case Environment.DEMO:
        for (const [key, value] of Object.entries(demoFieldIds)) {
          if (value === identifier || key === identifier) {
            return value;
          }
        }
        throw new Error(
          `Unknown identifier (${identifier}) specified for ${this.environmentService.getEnvironmentString()} environment`,
        );
      case Environment.PROD:
        for (const [key, value] of Object.entries(prodFieldIds)) {
          if (value === identifier || key === identifier) {
            return value;
          }
        }
        throw new Error(
          `Unknown identifier (${identifier}) specified for ${this.environmentService.getEnvironmentString()} environment`,
        );
      default:
        throw new Error(
          `Unknown environment (${this.environmentService.getEnvironmentString()}) specified when getting field ID`,
        );
    }
  }

  // identifier can be a system field ID or an external ID
  getFieldValueFromCrmObject(crmObject: CrmObjectInterface, identifier: string): FieldValueInterface | null {
    if (!crmObject.fields) {
      return null;
    }

    const fieldId = this.getFieldId(identifier);
    const fieldValue = crmObject.fields.find((field) => field.fieldId === fieldId);
    if (!fieldValue) {
      return null;
    }
    return fieldValue;
  }

  setFieldValueOnCrmObject(crmObject: CrmObjectInterface, identifier: string, stringValue: string): CrmObjectInterface {
    if (!crmObject.fields) {
      crmObject.fields = [];
    }

    const fieldId = this.getFieldId(identifier);
    const fieldValue = crmObject.fields.find((field) => field.fieldId === fieldId);
    const value: FieldValueInterface = {
      fieldId,
      stringValue,
    };
    if (!fieldValue) {
      crmObject.fields.push(value);
    } else {
      fieldValue.stringValue = value.stringValue;
    }
    return crmObject;
  }

  getFieldIdFromFormField(field: string): string {
    switch (field) {
      case 'noteBody':
        return StandardIds.ActivityNoteBody;
      case 'loggedNoteTags':
        return StandardIds.ActivityTags;
      case 'loggedEmailDescription':
        return StandardIds.ActivityEmailBody;
      case 'loggedEmailDirection':
        return StandardIds.ActivityEmailDirection;
      case 'loggedEmailDate':
        return StandardIds.ActivityEmailDate;
      case 'loggedCallNote':
        return StandardIds.ActivityCallBody;
      case 'loggedCallDirection':
        return StandardIds.ActivityCallDirection;
      case 'loggedCallDate':
        return StandardIds.ActivityCallDate;
      case 'loggedCallStatus':
        return StandardIds.ActivityCallStatus;
      case 'loggedCallOutcome':
        return StandardIds.ActivityCallOutcome;
      case 'loggedMeetingNote':
        return StandardIds.ActivityMeetingBody;
      case 'loggedMeetingDate':
        return StandardIds.ActivityMeetingStartTime;
      case 'loggedMeetingStatus':
        return StandardIds.ActivityMeetingStatus;
      case 'loggedMeetingOutcome':
        return StandardIds.ActivityMeetingOutcome;
      case 'loggedMeetingDuration':
        return StandardIds.ActivityMeetingDuration;
      case 'taskName':
        return StandardIds.ActivityName;
      case 'taskInstructions':
        return StandardIds.ActivityTaskBody;
      case 'taskDueDate':
        return StandardIds.ActivityTaskDueDate;
      case 'taskPriority':
        return StandardIds.ActivityTaskPriority;
      case 'taskType':
        return StandardIds.ActivityTaskType;
      case 'loggedCommunicationBody':
        return StandardIds.ActivityCommunicationBody;
      case 'loggedCommunicationChannel':
        return StandardIds.ActivityCommunicationChannel;
      case 'loggedCommunicationOutcome':
        return StandardIds.ActivityCommunicationOutcome;
      case 'loggedCommunicationDate':
        return StandardIds.ActivityCommunicationDate;
      default:
        return '';
    }
  }

  getFieldMaskFromFormFields(fields: string[]): string[] {
    const fieldIds: string[] = [];
    fields.forEach((field) => {
      const id = this.getFieldIdFromFormField(field);
      if (id) {
        fieldIds.push(`fields.${id}`);
      }
    });
    return fieldIds;
  }

  getFieldMaskFromFormGroup(formGroup?: FormGroup): string[] {
    if (!formGroup) return [];
    const dirtyFields = [];
    for (const field in formGroup.controls) {
      const control = formGroup.get(field);
      if (control?.dirty) {
        dirtyFields.push(field);
      }
    }
    return this.getFieldMaskFromFormFields(dirtyFields);
  }

  shouldRenderReadonlyField(identifier: string): boolean {
    return renderableReadonlyFields.includes(identifier);
  }
}
