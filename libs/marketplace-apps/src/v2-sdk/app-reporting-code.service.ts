import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  AppReportingCodeOverridesApiService,
  GetAppReportingCodeOverrideRequest,
  UpsertAppReportingCodeOverrideRequest,
  AppReportingCodeOverrideInterface,
  AppReportingCodeOverrideType,
} from '@vendasta/marketplace-apps';

@Injectable({ providedIn: 'root' })
export class AppReportingCodeService {
  constructor(private apiService: AppReportingCodeOverridesApiService) {}

  public getAppReportingCode(appId: string): Observable<AppReportingCodeOverrideInterface | null> {
    const request = new GetAppReportingCodeOverrideRequest({ appId });

    return this.apiService.get(request).pipe(map((response) => response?.appReportingCodeOverride || null));
  }

  public upsertAppReportingCode(
    appId: string,
    reportingCodeType: AppReportingCodeOverrideType,
    updatedByUserId: string,
  ): Observable<AppReportingCodeOverrideInterface> {
    const request = new UpsertAppReportingCodeOverrideRequest({
      appId,
      reportingCodeType,
      updatedByUserId,
    });

    return this.apiService.upsert(request).pipe(map((response) => response.appReportingCodeOverride));
  }
}
