<atlas-modal>
  <atlas-modal-header>Privileged access required</atlas-modal-header>
  <atlas-modal-content class="content">
    <glxy-alert type="warning">
      <strong>You are about to access sensitive customer data</strong>
      &mdash; please enter your reason for access and proceed with caution
    </glxy-alert>
    <p class="text">You will receive the following access</p>
    @let entitlement = entitlement$ | async;
    @if (entitlement) {
      <mat-card>
        <mat-card-header>
          <mat-card-title>{{ entitlement.name }}</mat-card-title>
        </mat-card-header>
        <mat-card-content>{{ entitlement.description }}</mat-card-content>
      </mat-card>
    } @else {
      <glxy-loading-spinner></glxy-loading-spinner>
    }
    <glxy-form-field class="form">
      <glxy-label>Reason for access</glxy-label>
      <textarea
        matInput
        placeholder="Provide a reason for requiring access (optional)"
        [formControl]="reason"
      ></textarea>
    </glxy-form-field>
  </atlas-modal-content>
  <atlas-modal-footer>
    <button mat-flat-button color="primary" (click)="createGrant()">Get access</button>
  </atlas-modal-footer>
</atlas-modal>
