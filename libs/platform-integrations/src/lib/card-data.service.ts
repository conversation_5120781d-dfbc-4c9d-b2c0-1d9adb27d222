import { HttpClient, HttpResponse } from '@angular/common/http';
import { effect, Inject, inject, Injectable, signal } from '@angular/core';
import {
  ConnectionMethods,
  ConnectionResponse,
  ConnectionsApiService,
  ConnectionStatus,
  CreateConnectionRequest,
  CreateConnectionResponse,
  CustomFields,
  DeleteConnectionRequest,
  FieldMask,
  GetAuthorizationCodeRedirectURLRequest,
  GetConnectionRequest,
  GetConnectionResponse,
  GetIntegrationMarketingRequest,
  IntegrationMarketingApiService,
  IntegrationMarketingResponse,
  ListConnectionRequest,
  ListIntegrationMarketingRequest,
  OAuthApiService,
  RevokeAccessTokenAndDeleteRequest,
  SupportedContexts,
  UpdateConnectionRequest,
  UpdateConnectionRequestUpdateFields,
  Category as IntegrationCategory,
  CategoryApiService,
  ListCategoryRequest,
} from '@vendasta/platform-integrations';
import { EMPTY, filter, map, mergeMap, Observable, of, shareReplay, switchMap, throwError } from 'rxjs';

import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { IAMService, User, UserIdentifier } from '@vendasta/iamv2';
import { catchError, take } from 'rxjs/operators';
import { CustomField, IntegrationType, VAPIResponse } from './model/connection-integration-detail';
import {
  App,
  AppSettings,
  Branding,
  EditionChange,
  EditionUpgradeAction,
  GetAppSettingsRequest,
  GetMultiAppRequest,
  GetMultiAppResponse,
  PartnerApiService,
} from '@vendasta/marketplace-apps';
import {
  PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_ORIGIN_INJECTION_TOKEN,
  PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$,
} from '@galaxy/platform-integrations/shared';
import { FeatureFlagService } from './feature-flag.service';
import { toSignal } from '@angular/core/rxjs-interop';
import { Environment, EnvironmentService } from '@galaxy/core';
import { WhitelabelApiService } from '@vendasta/partner';
import { UtilService } from './common/util.service';
import { GA4PropertiesApiService, DeletePropertyRequest, Category } from '@vendasta/google-analytics';
import { QuickBooksService } from '@vendasta/quickbooks';
import { IntegrationCard } from './model/Integration-connections-detail';
import {
  MeetingSource,
  MeetingSourceListResponse,
  MeetingSourceOrigin,
  MeetingSourceQuery,
  MeetingsService,
} from '@vendasta/meetings';
import { InstagramApiService } from '@vendasta/instagram';
import { AccountsService } from '@vendasta/accounts/legacy';

@Injectable({
  providedIn: 'root',
})
export class CardDataService {
  private readonly integrationService = inject(IntegrationMarketingApiService);
  private readonly connectionService = inject(ConnectionsApiService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly authService = inject(OAuthApiService);
  private readonly whitelabelService = inject(WhitelabelApiService);
  private readonly marketplaceAppsService = inject(PartnerApiService);
  private readonly featureFlagService = inject(FeatureFlagService);
  private readonly iamService = inject(IAMService);
  private readonly environmentService = inject(EnvironmentService);
  private readonly ga = inject(GA4PropertiesApiService);
  private readonly http = inject(HttpClient);
  private readonly quickBooksService = inject(QuickBooksService);
  private readonly meetingsService = inject(MeetingsService);
  private readonly instagramService = inject(InstagramApiService);
  private readonly partnerApiService = inject(PartnerApiService);
  private readonly accountsService = inject(AccountsService);
  private readonly categoryApiService = inject(CategoryApiService);

  protected readonly partnerID = toSignal(this.partnerId$, { initialValue: '' });
  protected readonly marketID = toSignal(this.marketId$, { initialValue: '' });
  protected readonly namespace = toSignal(this.namespace$, { initialValue: '' });
  readonly isRMActive = signal(false);

  constructor(
    @Inject(PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$) public readonly namespace$: Observable<string>,
    @Inject(PLATFORM_INTEGRATIONS_ORIGIN_INJECTION_TOKEN) public readonly origin: MeetingSourceOrigin,
    @Inject(PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$)
    public readonly context: SupportedContexts,
    private readonly utilService: UtilService,
    @Inject(PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$)
    public readonly partnerId$: Observable<string>,
    @Inject(PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$)
    public readonly marketId$: Observable<string>,
  ) {
    effect(() => {
      if (this.context !== SupportedContexts.PI_CONTEXT_SMB) {
        this.isRMActive.set(false);
        return;
      }

      const accountGroupId = this.namespace();
      const partnerId = this.partnerID();

      this.accountsService.list(accountGroupId, partnerId).subscribe((res) => {
        const accounts = (res.accounts ?? []).filter((a) => !a.trial && !a.deactivation);
        const isActive = accounts.some((a) => a.productId === 'RM' && a.editionId === this.getEditionId());
        this.isRMActive.set(isActive);
      });
    });
  }

  getIntegrationsList(displayNameFilter?: string, categoryKeys?: string[]): Observable<IntegrationMarketingResponse[]> {
    return this.integrationService
      .list(
        new ListIntegrationMarketingRequest({
          pagingOptions: {
            pageSize: 500,
          },
          supportedContexts: this.context,
          filters: {
            displayName: displayNameFilter,
            categoryKeys: categoryKeys && categoryKeys.length > 0 ? categoryKeys : [],
          },
        }),
      )
      .pipe(
        mergeMap((resp) => {
          if (!resp?.integrations || resp.integrations.length === 0) {
            return of([]);
          }
          // Extract integration type from integrations
          const featureFlagIds = resp.integrations.map(
            (integration) => 'show_' + integration.integrationType.toLowerCase() + '_integration',
          );
          // Check feature flags for all extracted IDs in a single call and
          // determine whether card need to be displayed or not
          return this.featureFlagService
            .checkFeatureFlag(this.partnerID()!, '', ...featureFlagIds)
            .pipe(map((flags) => resp.integrations.filter((_, index) => flags[index])));
        }),
        catchError((err) => {
          if (err.status !== 403) {
            this.snackbarService.openErrorSnack('INTEGRATION_CARD.INTEGRATION_LIST_ERROR');
          }
          return throwError(err);
        }),
        shareReplay(),
      );
  }

  getIntegration(integrationId: string, namespace: string): Observable<IntegrationMarketingResponse> {
    return this.integrationService
      .get(
        new GetIntegrationMarketingRequest({
          integrationId: integrationId,
          namespace: namespace,
        }),
      )
      .pipe(
        map((resp) => resp),
        shareReplay(),
        catchError((err) => {
          this.snackbarService.openErrorSnack('INTEGRATION_CARD.INTEGRATION_ERROR');
          return throwError(err);
        }),
      );
  }

  getConnection(connectionId: string, namespace: string): Observable<GetConnectionResponse> {
    return this.connectionService
      .get(
        new GetConnectionRequest({
          connectionId: connectionId,
          namespace: namespace,
          filters: {
            context: this.context,
          },
        }),
      )
      .pipe(
        map((resp) => resp),
        catchError((err) => {
          this.snackbarService.openErrorSnack('INTEGRATION_CARD.GET_CONNECTION_ERROR');
          return throwError(err);
        }),
      );
  }

  getIntegrationId(connectionId: string, namespace: string): Observable<string> {
    return this.connectionService
      .get(
        new GetConnectionRequest({
          connectionId: connectionId,
          namespace: namespace,
          filters: {
            context: this.context,
          },
        }),
      )
      .pipe(
        map((resp) => resp.integrationId),
        catchError((err) => {
          this.snackbarService.openErrorSnack('INTEGRATION_CARD.GET_CONNECTION_ERROR');
          return throwError(err);
        }),
      );
  }

  deleteGA4Connection(partnerId: string, accountGroupId: string): Observable<any> {
    return this.ga.deleteProperty(
      new DeletePropertyRequest({
        partnerId: partnerId,
        businessId: accountGroupId,
        category: Category.CATEGORY_WEBSITE,
        subcategory: 'CATEGORY_WEBSITE',
      }),
    );
  }

  disconnectInstagramConnection(connectionId: string, partnerId: string, accountGroupId: string) {
    return this.instagramService
      .listUsers({
        partnerId: partnerId,
        businessId: accountGroupId,
      })
      .pipe(
        switchMap((response) => {
          // connectionId format(Instagram-<NAMSPACE>-<INSTAGRAM_USER_ID>) eg: Instagram-AG-FF32LBZP2V-*****************
          const connectionIDParts = connectionId.split('-');
          // instagramUserId will be the last part of the connectionIDParts list
          const instagramUserID = connectionIDParts[connectionIDParts.length - 1];
          if (response?.users?.length) {
            for (const user of response.users) {
              if (user.instagramUserId === instagramUserID) {
                return this.instagramService.deleteUser({
                  partnerId: partnerId,
                  businessId: accountGroupId,
                  userId: user.userId,
                });
              }
            }
          }
          return EMPTY;
        }),
      );
  }

  listMeetConnection(): Observable<MeetingSourceListResponse> {
    return this.namespace$.pipe(
      take(1),
      switchMap((namespace) => {
        return this.meetingsService.list(
          [
            new MeetingSourceQuery({
              source: MeetingSource.MEETING_SOURCE_ZOOM,
              connectNextUrl: window.location.href,
              disconnectNextUrl: window.location.href,
            }),
            new MeetingSourceQuery({
              source: MeetingSource.MEETING_SOURCE_GOOGLE_MEET,
              connectNextUrl: window.location.href,
              disconnectNextUrl: window.location.href,
            }),
            new MeetingSourceQuery({
              source: MeetingSource.MEETING_SOURCE_MICROSOFT,
              connectNextUrl: window.location.href,
              disconnectNextUrl: window.location.href,
            }),
          ],
          namespace,
          this.origin,
        );
      }),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
  }

  deleteSocialConnections(deleteUrl: string): Observable<VAPIResponse> {
    return this.http.post<VAPIResponse>(deleteUrl, {});
  }

  deleteQuickBookPersonalConnection(namespace: string, userID: string): Observable<any> {
    return this.quickBooksService.revokeAccessToken(namespace, userID);
  }

  getConnectionsList(namespace: string, integrationID?: string, cursor?: string): Observable<ConnectionResponse[]> {
    return this.connectionService.list(this.getConnectionListRequest(namespace, integrationID, cursor)).pipe(
      mergeMap((resp) => {
        if (resp.pagingMetadata && resp.pagingMetadata.hasMore) {
          // If there's a next cursor, recursively call getConnectionsList
          return this.getConnectionsList(namespace, integrationID, resp.pagingMetadata.nextCursor).pipe(
            map((nextPageConnections) => resp.connections.concat(nextPageConnections)),
          );
        } else {
          return of(resp.connections);
        }
      }),
      shareReplay(),
      catchError((err) => {
        this.snackbarService.openErrorSnack('INTEGRATION_CARD.GET_CONNECTIONS_LIST_ERROR');
        return throwError(err);
      }),
    );
  }

  getConnectionListRequest(namespace: string, integrationID?: string, cursor?: string) {
    const pageSize = 25;
    let requestParams: ListConnectionRequest;

    if (cursor) {
      requestParams = new ListConnectionRequest({
        namespace: namespace,
        filters: {
          context: this.context,
          integrationId: integrationID,
        },
        pagingOptions: {
          pageSize,
          cursor,
        },
      });
    } else {
      requestParams = new ListConnectionRequest({
        namespace: namespace,
        filters: {
          context: this.context,
          integrationId: integrationID,
        },
        pagingOptions: {
          pageSize,
        },
      });
    }
    return requestParams;
  }

  getRMAppSettings(partnerID: string, marketID: string): Observable<AppSettings> {
    return this.marketplaceAppsService
      .getAppSettings(
        new GetAppSettingsRequest({
          partnerId: partnerID,
          marketId: marketID,
          appId: 'RM',
          disableFallback: true,
        }),
      )
      .pipe(
        map((resp) => resp.appSettings),
        catchError((error) => {
          console.error('Failed to fetch RM app settings:.. setting defaults', error);
          const defaultAppSettings = new AppSettings({
            partnerId: partnerID,
            marketId: marketID,
            appId: 'RM',
            editionChange: new EditionChange({
              editionUpgradeAction: EditionUpgradeAction.EDITION_UPGRADE_ACTION_CONTACT_SALES,
            }),
            branding: new Branding({
              iconUrl: 'https://storage.googleapis.com/galaxy-libs-public-images/reputation_management_logo.png',
              name: 'Available on Reputation Management Premium',
            }),
          });
          return of(defaultAppSettings);
        }),
      );
  }

  handleConnection(cards: IntegrationCard[]) {
    if (!cards || cards.length !== 1) return;
    const card = cards[0];
    if (card?.integration?.connectionMethod == ConnectionMethods.OAUTH2) {
      this.performSSO(
        String(card.namespace),
        card.integration.integrationId,
        String(card?.connection?.connectionId),
        this.utilService.getCurrentURL() + '?src=sso',
      ).subscribe();
    }
  }

  createConnection(
    namespace: string,
    integrationId: string,
    syncData: CustomField[],
    context: SupportedContexts,
  ): Observable<CreateConnectionResponse> {
    return this.connectionService.create(
      new CreateConnectionRequest({
        namespace: namespace,
        integrationId: integrationId,
        customFields: syncData,
        context: context,
      }),
    );
  }

  deleteConnectionAndRevokeAccessToken(namespace: string, connectionId: string): Observable<HttpResponse<null>> {
    return this.authService.revokeAccessTokenAndDelete(
      new RevokeAccessTokenAndDeleteRequest({
        connectionId: connectionId,
        namespace: namespace,
      }),
    );
  }

  deleteConnection(namespace: string, connectionId: string): Observable<HttpResponse<null>> {
    return this.connectionService.delete(
      new DeleteConnectionRequest({
        connectionId: connectionId,
        namespace: namespace,
      }),
    );
  }

  updateConnection(
    connectionId: string,
    namespace: string,
    customFieldUpdates: { label: string; value: string }[],
    connectionStatusUpdate?: ConnectionStatus, // Leave connectionStatus as unspecified if you don't want to change the connection status
  ): Observable<HttpResponse<null>> {
    const customFields = customFieldUpdates.map(
      (update) =>
        new CustomFields({
          label: update.label,
          value: update.value,
        }),
    );

    let fieldMask = new FieldMask({
      paths: ['customFields', 'status'],
    });
    let updateFields = new UpdateConnectionRequestUpdateFields({
      status: connectionStatusUpdate,
      customFields: customFields,
    });
    if (!connectionStatusUpdate) {
      fieldMask = new FieldMask({
        paths: ['customFields'],
      });
      updateFields = new UpdateConnectionRequestUpdateFields({
        customFields: customFields,
      });
    }

    return this.connectionService.update(
      new UpdateConnectionRequest({
        connectionId: connectionId,
        namespace: namespace,
        fieldMask: fieldMask,
        updateFields: updateFields,
      }),
    );
  }

  //Get Edition ID based on Environment
  getEditionId(): string {
    switch (this.environmentService.getEnvironment()) {
      case Environment.DEMO:
        return 'EDITION-BFXF8W8Q';
      case Environment.PROD:
        return 'EDITION-JFRPLQPN';
    }
    //default returning demo Edition ID
    return 'EDITION-BFXF8W8Q';
  }

  performSSO(namespace: string, integrationId: string, connectionId: string, baseURL: string): Observable<void> {
    return this.authService
      .getAuthorizationCodeRedirectUrl(
        new GetAuthorizationCodeRedirectURLRequest({
          namespace: namespace,
          integrationId: integrationId,
          connectionId: connectionId,
          baseUrl: baseURL,
        }),
      )
      .pipe(
        filter((response) => !!response.url),
        switchMap((response): Observable<void> => {
          // Redirect user to external authentication server
          window.location.href = response.url;
          return of(undefined);
        }),
        catchError(() => {
          this.snackbarService.openErrorSnack('INTEGRATION_CARD.PERFORM_SSO_ERROR');
          return EMPTY;
        }),
      );
  }

  getUserById(userId: string): Observable<User> {
    return this.iamService.getUser(
      new UserIdentifier({
        userId: userId,
      }),
    );
  }

  getMultiByUserId(userIds: string[]): Observable<User[]> {
    const userIdentifiers = userIds?.map((userId) => new UserIdentifier({ userId: userId }));
    return this.iamService.getMultiUsers(userIdentifiers);
  }

  getPlatformName(partnerID: string, marketID: string): Observable<string> {
    return this.whitelabelService
      .getBranding({
        partnerId: partnerID,
        marketId: marketID,
      })
      .pipe(
        map((resp) => resp.branding.apps['VBC'].name),
        shareReplay(1),
        catchError((err) => {
          this.snackbarService.openErrorSnack('error in getting businessName');
          return throwError(err);
        }),
      );
  }

  getProductDetailsByProductIds(productIds: string[]): Observable<App[]> {
    const appKeys = productIds.map((id) => ({
      appId: id,
    }));

    return this.partnerApiService
      .getMultiApp(
        new GetMultiAppRequest({
          appKeys: appKeys,
          partnerId: this.partnerID(),
          marketId: this.marketID(),
          includeNotEnabled: true,
        }),
      )
      .pipe(
        map((resp: GetMultiAppResponse) => {
          return resp?.apps || [];
        }),
      );
  }

  getIntegrationByIntegrationType(
    integrationType: IntegrationType,
  ): Observable<IntegrationMarketingResponse | undefined> {
    return this.getIntegrationsList().pipe(
      map((integrationList) => integrationList.find((integration) => integration.integrationType === integrationType)),
    );
  }

  // This will only work for integrations with a single connection per namespace. It will return a connection if
  // the connection is in the preconnected, connected, or disconnected state.
  getExistingConnectionByIntegrationType(
    integrationType: string,
    namespace: string,
  ): Observable<ConnectionResponse | undefined> {
    const integration$ = this.getIntegrationsList().pipe(
      map((integrationList) => integrationList.find((integration) => integration.integrationType === integrationType)),
      switchMap((integration) =>
        integration
          ? of(integration)
          : throwError(() => new Error(`Integration not found for type: ${integrationType}`)),
      ),
    );
    return integration$.pipe(
      switchMap((integration) => this.getConnectionsList(namespace, integration.integrationId)),
      map((connectionList) => {
        const connections = connectionList?.filter(
          (connect) =>
            connect.status === ConnectionStatus.CONNECTED ||
            connect.status === ConnectionStatus.DISCONNECTED ||
            connect.status === ConnectionStatus.PRECONNECTED,
        );
        return connections?.length ? connections[0] : undefined;
      }),
    );
  }

  // This function only works for Apple connections in the preconnected state. If used in other cases it will always
  // return true.
  // This is a special case since we only want to display these settings if we have a valid access token for Apple.
  // We can check this by looking for the connection information in the custom fields of the connection.
  // Long term we should have an additional state to handle this, then we can check the connection state.
  applePreconnectionHasToken(
    integrationType: string,
    connectionStatus: ConnectionStatus,
    customFields: CustomFields[],
  ): boolean {
    if (
      connectionStatus !== ConnectionStatus.PRECONNECTED ||
      integrationType !== IntegrationType.APPLEBUSINESSCONNECT
    ) {
      return true;
    }
    if (!customFields) {
      return false;
    }
    return customFields?.some((field) => field.label === 'companyID') || false;
  }

  getCategoriesList(categoryKeys?: string[]): Observable<IntegrationCategory[]> {
    return this.categoryApiService
      .listCategory(
        new ListCategoryRequest({
          pagingOptions: {
            pageSize: 500,
          },
          filters: {
            categoryKeys: categoryKeys,
          },
        }),
      )
      .pipe(
        map((resp) => resp?.categories || []),
        catchError((err) => {
          if (err.status !== 403) {
            this.snackbarService.openErrorSnack('CATEGORY.CATEGORY_LIST_ERROR');
          }
          return throwError(() => err);
        }),
        shareReplay(),
      );
  }
}
