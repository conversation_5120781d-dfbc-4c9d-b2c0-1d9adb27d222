import { CommonModule, DOCUMENT } from '@angular/common';
import { Component, DestroyRef, Inject, inject, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  ConnectionMethods,
  ConnectionStatus,
  FieldConfig,
  FormConfig,
  GetConnectionResponse,
  IntegrationMarketingResponse,
  SupportedContexts,
} from '@vendasta/platform-integrations';
import { BehaviorSubject, combineLatest, EMPTY, filter, map, Observable, of, switchMap, take, tap } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { CardDataService } from '../card-data.service';
import { IntegrationConnectionDetail } from '../model/Integration-connections-detail';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatCardModule } from '@angular/material/card';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { ConnectionIntegrationDetail, IntegrationType, RmAppDetail } from '../model/connection-integration-detail';
import { GalaxyCheckboxModule } from '@vendasta/galaxy/checkbox';
import { MatCheckboxModule } from '@angular/material/checkbox';
import {
  PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$,
  PlatformIntegrationsI18nModule,
} from '@galaxy/platform-integrations/shared';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { DialogBoxComponent } from '../unlock-dialogue-box/unlock-dialogue-box.component';
import { AccountDetails, AccountDetailsComponent } from './account-details/account-details.component';
import { AppSettings } from '@vendasta/marketplace-apps';
import { ResourcesComponent } from './resources/resources.component';
import { SyncSettingsComponent } from './sync-settings/sync-settings.component';
import { MatRadioModule } from '@angular/material/radio';
import { IntegrationUtilService } from '../common/integration-util.service';
import { HttpResponse } from '@angular/common/http';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { LegacyAuthService } from '../legacy-auth.service';
import { CookieService } from 'ngx-cookie-service';
import { InstructionsComponent } from './instructions/instructions.component';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { UserAction } from '../const';

@Component({
  selector: 'platform-integration-config-page',
  imports: [
    CommonModule,
    MatButtonModule,
    MatChipsModule,
    MatDividerModule,
    MatIconModule,
    GalaxyBadgeModule,
    TranslateModule,
    GalaxyPageModule,
    MatSlideToggleModule,
    MatCardModule,
    GalaxyCheckboxModule,
    ReactiveFormsModule,
    FormsModule,
    MatCheckboxModule,
    MatDialogModule,
    AccountDetailsComponent,
    ResourcesComponent,
    MatRadioModule,
    GalaxyAlertModule,
    PlatformIntegrationsI18nModule,
    SyncSettingsComponent,
    InstructionsComponent,
    GalaxyLoadingSpinnerModule,
  ],
  templateUrl: './config-page.component.html',
  styleUrls: ['./config-page.component.scss'],
})
export class ConfigPageComponent implements OnInit {
  syncSettingsForm!: FormGroup;
  private formBuilder = inject(FormBuilder);
  private readonly router = inject(Router);
  private readonly cardService = inject(CardDataService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly translate = inject(TranslateService);
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly confirmationModal = inject(OpenConfirmationModalService);
  private readonly dialog = inject(MatDialog);
  private readonly integrationUtilService = inject(IntegrationUtilService);
  protected readonly connectionId$ = this.activatedRoute.paramMap.pipe(map((params) => params.get('connectionId')));
  protected readonly connectionMethods = ConnectionMethods;
  protected readonly connectionStatus = ConnectionStatus;
  private readonly legacyAuthService = inject(LegacyAuthService);
  private readonly cookieService = inject(CookieService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly refreshTrigger$ = new BehaviorSubject<void>(undefined);
  protected displaySettings = true;

  protected readonly partnerId = toSignal(this.partnerId$, { initialValue: '' });
  protected readonly namespace = toSignal(this.namespace$, { initialValue: '' });

  constructor(
    @Inject(PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$) public readonly namespace$: Observable<string>,
    @Inject(PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$)
    public readonly partnerId$: Observable<string>,
    @Inject(PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$) public readonly marketId$: Observable<string>,
    @Inject(DOCUMENT) private document: Document,
    @Inject(PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$)
    public readonly context: SupportedContexts,
  ) {}

  ngOnInit(): void {
    this.activatedRoute.queryParams
      .pipe(
        map((param) => {
          const isSSO = param['src'] ? param['src'] : '';
          const errorCode = param['error'] ? param['error'] : '';
          if (errorCode) {
            const errorDescription = param['error_description']
              ? param['error_description']
              : 'INTEGRATION_CARD.CONNECTION_ERROR_SSO';
            this.snackbarService.openErrorSnack(errorDescription);
          } else if (isSSO === 'sso') {
            // TODO: change this so that if the connection is still in pre-connect state it tells you that you need
            // to do <steps> to complete the connection
            this.snackbarService.openSuccessSnack('INTEGRATION_CARD.CREATE_CONNECTION_SUCCESS');
          }
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  protected readonly connection$ = this.refreshTrigger$.pipe(
    switchMap(() =>
      this.connectionId$.pipe(
        filter((connectionId) => !!connectionId),
        switchMap((connectionId) => this.cardService.getConnection(connectionId, this.namespace())),
      ),
    ),
  );

  private readonly integrationId$ = this.connectionId$.pipe(
    filter((connectionId) => !!connectionId),
    switchMap((connectionId) => this.cardService.getIntegrationId(connectionId, this.namespace())),
  );

  protected readonly integration$ = this.integrationId$.pipe(
    filter((integrationId) => !!integrationId),
    switchMap((integrationId) =>
      integrationId
        ? this.cardService.getIntegration(integrationId, this.namespace())
        : of({} as IntegrationMarketingResponse),
    ),
  );

  configPreconnectMessage(cardDetail: ConnectionIntegrationDetail): string {
    switch (cardDetail?.integration?.connectionMethod) {
      case this.connectionMethods.OAUTH2:
        return this.translate.instant('INTEGRATION_CARD.PRECONNECT_SSO_BASED_WARNING_PROBLEM', {
          title: cardDetail.integration.displayName,
        });
      default:
        return this.translate.instant('INTEGRATION_CARD.PRECONNECT_WARNING_PROBLEM', {
          title: cardDetail.integration.displayName,
        });
    }
  }

  protected integrationType$: Observable<string> = this.integration$.pipe(
    take(1),
    switchMap((integration: any) => {
      return of(integration.integrationType);
    }),
  );

  protected readonly getAppSettings$: Observable<AppSettings> = combineLatest([this.partnerId$, this.marketId$]).pipe(
    switchMap(([partnerID, marketID]) => {
      return this.cardService.getRMAppSettings(partnerID, marketID);
    }),
  );

  protected readonly getRMAppDetailsConfigPage$ = combineLatest([this.getAppSettings$]).pipe(
    map(([rmAppSettings]) => {
      const res = {
        hasRMPremiumActive: false,
        upgradeSetting: rmAppSettings,
      } as RmAppDetail;
      if (this.cardService.isRMActive()) {
        res.hasRMPremiumActive = true;
        return res;
      }
      return res;
    }),
  );

  protected readonly cardDetail$ = combineLatest([this.connection$, this.integration$]).pipe(
    map(([connection, integration]) => {
      if (integration && connection) {
        this.displaySettings = this.cardService.applePreconnectionHasToken(
          integration.integrationType,
          connection.status,
          connection.customFields,
        );
        if (this.displaySettings) {
          this.initializeFormGrop(connection, integration);
        }
        return {
          connection: connection,
          integration: integration,
        };
      }
      return {};
    }),
  );

  private readonly getBusinessName$: Observable<string> = combineLatest([this.partnerId$, this.marketId$]).pipe(
    switchMap(([partnerID, marketID]) => {
      return this.cardService.getPlatformName(partnerID, marketID);
    }),
  );

  private readonly getBusinessName = toSignal(this.getBusinessName$);
  protected readonly pageData$ = combineLatest([this.cardDetail$, this.namespace$, this.connectionId$]).pipe(
    map(([cardDetail, namespace, connectionId]) => {
      let platformName = 'the platform';
      const businessName = this.getBusinessName();
      if (this.context === SupportedContexts.PI_CONTEXT_SMB && businessName) {
        platformName = businessName;
      }

      if (cardDetail?.integration?.configPageFields) {
        cardDetail.integration.configPageFields.forEach((formConfig: FormConfig) => {
          if (formConfig.fields) {
            formConfig.fields.forEach((field: FieldConfig) => {
              if (field.hintText) {
                field.hintText = field.hintText
                  .replace('{{agid}}', namespace || '')
                  .replaceAll('{{platformName}}', platformName);
              }
            });
          }
        });
      }
      return {
        cardDetail: cardDetail,
        namespace: namespace,
        connectionId: connectionId,
      };
    }),
  );

  protected accountDetails$: Observable<AccountDetails> = this.pageData$.pipe(
    take(1),
    map((data) => {
      const integration: any = data.cardDetail.integration;
      const connection: any = data.cardDetail.connection;
      return {
        integration,
        connection,
      };
    }),
    switchMap((data) => {
      if (data.connection.userId) {
        return this.cardService.getUserById(data.connection.userId).pipe(
          map((user) => ({
            user: user.firstName || user.lastName ? user : null,
            integration: data.integration,
            connection: data.connection,
          })),
        );
      } else {
        return of({
          user: null,
          integration: data.integration,
          connection: data.connection,
        });
      }
    }),
    map((data) => {
      const accountDetails: AccountDetails = {
        accountName: data.connection.label,
        connectionStatus: this.statusConversion(data.connection.status),
      };
      if (data.user) {
        accountDetails.requestedBy = `${data.user.firstName} ${data.user.lastName}`;
      }
      if (data.connection.statusLastUpdated) {
        accountDetails.statusLastUpdated = new Date(data.connection.statusLastUpdated);
      }
      if (data.connection.dataLastReceived) {
        accountDetails.dataLastReceived = new Date(data.connection.dataLastReceived);
      }

      if (data.integration.integrationType == IntegrationType.APPLEBUSINESSCONNECT) {
        const profileField = data.connection.customFields?.find((field) => field.label === 'placeCardUrl');
        accountDetails.profileURL = profileField ? profileField.value : null;
      } else {
        const profileField = data.connection.customFields?.find((field) => field.label === 'profileUrl');
        accountDetails.profileURL = profileField ? profileField.value : null;
      }
      if (
        data.integration.integrationType == IntegrationType.MICROSOFTTEAMS ||
        data.integration.integrationType == IntegrationType.ZOOMMEET ||
        data.integration.integrationType == IntegrationType.GOOGLEMEET
      ) {
        const connectionLabel = data.connection.customFields?.find((field) => field.label === 'ConnectionLabel');
        accountDetails.connectionLabel = connectionLabel?.value || null;
      }

      const profileVerified = data.connection.customFields?.find((field) => field.label === 'isLocationVerified');
      accountDetails.isLocationVerified = profileVerified ? profileVerified.value : null;

      return accountDetails;
    }),
  );

  refreshConnection() {
    this.refreshTrigger$.next();
  }

  statusConversion(s: ConnectionStatus) {
    switch (s) {
      case ConnectionStatus.CONNECTED:
        return 'CONNECTED';
      case ConnectionStatus.DISCONNECTED:
        return 'DISCONNECTED';
      case ConnectionStatus.PRECONNECTED:
        return 'PRECONNECTED';
    }
    return '';
  }

  openWarnModal(card: { cardDetail: IntegrationConnectionDetail; namespace: string; connectionId: string }) {
    let warningMesssage;
    let dialogBoxtitle = this.translate.instant('INTEGRATION_CARD.TITLE', {
      title: card?.cardDetail?.integration?.displayName,
    });
    let confirmButtonMsg = this.translate.instant('INTEGRATION_CARD.DISCONNECT');
    let cancelBtnMsg = this.translate.instant('INTEGRATION_CARD.CANCEL');

    switch (card.cardDetail.integration?.integrationType) {
      case IntegrationType.FACEBOOK:
        warningMesssage = this.translate.instant('INTEGRATION_CARD.FACEBOOK_DISCONNECT_WARNING_MESSAGE');
        break;
      case IntegrationType.GOOGLEMYBUSINESS:
        warningMesssage = this.translate.instant('INTEGRATION_CARD.GOOGLE_BUSINESS_PROFILE_DISCONNECT_WARNING_MESSAGE');
        break;
      default:
        warningMesssage = this.translate.instant('INTEGRATION_CARD.MESSAGE');
    }

    if (card.cardDetail.connection?.status === ConnectionStatus.DISCONNECTED) {
      warningMesssage = this.translate.instant('INTEGRATION_CARD.CANCEL_CONNECTION_MSG');
      dialogBoxtitle = this.translate.instant('INTEGRATION_CARD.CANCEL_CONNECTION_TITLE', {
        title: card?.cardDetail?.integration?.displayName,
      });
      confirmButtonMsg = this.translate.instant('INTEGRATION_CARD.CANCEL_CONNECTION');
      cancelBtnMsg = this.translate.instant('INTEGRATION_CARD.KEEP_CONNECTION');
    }

    this.confirmationModal
      .openModal({
        type: 'warn',
        title: dialogBoxtitle,
        message: warningMesssage,
        confirmButtonText: confirmButtonMsg,
        cancelButtonText: cancelBtnMsg,
      })
      .pipe(take(1))
      .subscribe((userDidAction) => {
        if (userDidAction) {
          const integrationType = card.cardDetail.integration?.integrationType;
          const connectionStatus = card.cardDetail.connection?.status;
          const cookieKey = `dismissedInstruction_${integrationType}_${connectionStatus}`;
          if (this.cookieService.get(cookieKey)) {
            this.cookieService.delete(cookieKey, '/');
          }
          this.disconnectConnection(card);
        }
      });
  }

  //Used to get path of URL to make it generic for Both PC and BusinessApp
  // Example, if URL is https://vendasta-university-dot-vbc-demo.appspot.com/account/location/AG-JP5VN52N7P/settings/integrations/config/705947aa-21bc-4bfb-9484-bdbb85219b9b
  // we extract only account/location/AG-JP5VN52N7P/settings/integrations , which used to redirect.
  getCurrentPath(): string {
    let path = window.location.pathname.slice(1);
    const index = path.indexOf('config');
    path = path.substring(0, index - 1);
    return path;
  }

  navigateBack(): string {
    const path = this.getCurrentPath() + `?tab=${UserAction.MANAGE}`;
    return path;
  }

  protected disconnectConnection(card: {
    cardDetail: IntegrationConnectionDetail;
    namespace: string;
    connectionId: string;
  }) {
    if (!card) {
      console.warn('No card provided; cannot disconnect');
      return;
    }

    const connectionMethod = card.cardDetail.integration.connectionMethod;
    if (connectionMethod === ConnectionMethods.LEGACY_OAUTH) {
      this.disconnectLegacy(card);
      return EMPTY;
    }
    const integrationType = card.cardDetail.integration.integrationType;
    let deleteConnection$: Observable<HttpResponse<null>>;
    // Special case since we revoke the token, then delete connection for QBO
    if (
      integrationType.toLowerCase() === IntegrationType.QUICKBOOKS.toLowerCase() &&
      card.cardDetail.connection.status === ConnectionStatus.CONNECTED
    ) {
      deleteConnection$ = this.cardService.deleteConnectionAndRevokeAccessToken(card.namespace, card.connectionId);
    } else {
      deleteConnection$ = this.cardService.deleteConnection(card.namespace, card.connectionId);
    }

    deleteConnection$
      .pipe(
        tap(() => {
          this.snackbarService.openSuccessSnack('INTEGRATION_CARD.DELETE_SUCCESS_MESSAGE', {
            interpolateTranslateParams: { title: card.cardDetail.integration.displayName },
          });
          this.navigateToManageTab();
          return EMPTY;
        }),
        catchError(() => {
          this.snackbarService.openErrorSnack('INTEGRATION_CARD.DELETE_ERROR_MESSAGE');
          return EMPTY;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  protected disconnectLegacy(card: {
    cardDetail: IntegrationConnectionDetail;
    namespace: string;
    connectionId: string;
  }) {
    const integrationType = card.cardDetail.integration.integrationType;
    let disconnectURL = '';
    switch (integrationType) {
      case IntegrationType.GOOGLEANALYTICS:
        this.cardService
          .deleteGA4Connection(this.partnerId(), card.namespace)
          .pipe(
            tap(() => {
              this.navigateToManageTab();
            }),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe();
        break;
      case IntegrationType.GOOGLEMYBUSINESS:
      case IntegrationType.FACEBOOK:
      case IntegrationType.GOOGLESEARCHCONSOLE:
        card.cardDetail.connection.customFields.forEach((customField) => {
          if (customField.label === 'disconnectUrl') {
            disconnectURL = customField.value;
          }
        });
        this.cardService
          .deleteSocialConnections(disconnectURL)
          .pipe(
            tap(() => {
              this.navigateToManageTab();
            }),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe();
        break;
      case IntegrationType.GOOGLEMEET:
      case IntegrationType.ZOOMMEET:
      case IntegrationType.MICROSOFTTEAMS:
        card.cardDetail.connection.customFields.forEach((customField) => {
          if (customField.label === 'disconnectUrl') {
            disconnectURL = customField.value;
          }
        });
        window.location.href = disconnectURL;
        break;
      case IntegrationType.QUICKBOOKSONLINEPERSONAL:
        // set the cookie TTL for 15 seconds.
        this.cookieService.set('qbopersonal-disconnect', card.cardDetail?.connection?.connectionId, 15 / 86400, '/');
        this.cardService
          .deleteQuickBookPersonalConnection(card.namespace, card.cardDetail?.connection?.userId)
          .pipe(
            tap(() => {
              this.navigateToManageTab();
            }),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe();

        // Optionally clear the cookie manually after 15 seconds (as a backup)
        setTimeout(() => {
          this.clearDisconnectCookie('qbopersonal-disconnect');
        }, 15000);
        break;
      case IntegrationType.INSTAGRAM:
        // set the cookie TTL for 10 seconds.
        this.cookieService.set(
          'instagram-disconnect',
          String(card.cardDetail?.connection?.connectionId),
          10 / 86400,
          '/',
        );
        // Deleting the cookie after 10 seconds.
        setTimeout(() => {
          this.clearDisconnectCookie('instagram-disconnect');
        }, 10000);
        this.cardService
          .disconnectInstagramConnection(card.connectionId, this.partnerId() || '', this.namespace() || '')
          .pipe(
            tap(() => {
              this.snackbarService.openSuccessSnack('INTEGRATION_CARD.DELETE_SUCCESS_MESSAGE', {
                interpolateTranslateParams: { title: IntegrationType.INSTAGRAM },
              });
              this.navigateToManageTab();
            }),
            catchError(() => {
              this.snackbarService.openErrorSnack('INTEGRATION_CARD.DELETE_ERROR_MESSAGE');
              return EMPTY;
            }),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe();
        break;
    }
  }

  // Clear the disconnect cookie
  private clearDisconnectCookie(name: string) {
    this.cookieService.delete(name, '/');
  }

  initializeFormGrop(connection: GetConnectionResponse, integration: IntegrationMarketingResponse) {
    this.syncSettingsForm = this.formBuilder.group({});
    const syncSettingIds = this.integrationUtilService.getFieldIdsFromConfig(
      integration?.configPageFields?.[0]?.fields ?? [],
    ); //TODO:PHNX assuming sync setting is first index for now
    connection.customFields?.forEach((con) => {
      if (syncSettingIds.includes(con.label)) {
        const control = new FormControl(this.stringToBooleanConversion(con.value));
        this.syncSettingsForm.addControl(con.label, control);
      }
    });
  }

  stringToBooleanConversion(value: string): boolean {
    if (value === 'true') {
      return true;
    }
    return false;
  }

  getCurrentURL(): string {
    let url = window.location.href;
    const configIndex = url.indexOf('config');
    url = url.substring(0, configIndex - 1);
    return url;
  }

  navigateToManageTab() {
    const path = this.getCurrentPath();
    this.router.navigate([path], {
      queryParams: { tab: UserAction.MANAGE },
    });
  }

  reconnectConnection(card: { cardDetail: IntegrationConnectionDetail; namespace: string; connectionId: string }) {
    if (card.cardDetail.integration.connectionMethod == ConnectionMethods.OAUTH2) {
      return this.cardService
        .performSSO(
          card.namespace,
          card.cardDetail.integration.integrationId,
          card.connectionId,
          this.getCurrentURL() + '?src=sso',
        )
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe();
    } else if (card.cardDetail.integration.connectionMethod == ConnectionMethods.LEGACY_OAUTH) {
      return this.legacyAuthService.HandleLegacyIntegration({
        cardDetail: card.cardDetail,
        namespace: card.namespace,
      });
    }

    return null;
  }

  handleButtonClick(rmAppDetails: RmAppDetail): void {
    this.dialog.open(DialogBoxComponent, {
      width: '463px',
      data: {
        rmAppDetails: rmAppDetails.hasRMPremiumActive,
        rmAppSettings: rmAppDetails.upgradeSetting,
      },
    });
  }

  navigateToAction(ctaUrl: string) {
    if (ctaUrl) {
      this.document.location.href = ctaUrl;
    }
  }
}
