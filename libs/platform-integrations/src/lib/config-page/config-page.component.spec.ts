import { TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { of } from 'rxjs';
import { CardDataService } from '../card-data.service';
import { ConfigPageComponent } from './config-page.component';
import {
  PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$,
} from '@galaxy/platform-integrations/shared';
import { LegacyAuthService } from '../legacy-auth.service';

describe('ConfigPageComponent', () => {
  let component: ConfigPageComponent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        ConfigPageComponent,
        {
          provide: ActivatedRoute,
          useValue: { paramMap: of({ agid: 'groot', integrationId: 'groot123' }) },
        },
        {
          provide: OpenConfirmationModalService,
          useValue: {
            openModal: jest.fn(),
          },
        },
        {
          provide: PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$,
          useValue: of('test_namespace'),
        },
        {
          provide: PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$,
          useValue: of('test_partnerID'),
        },
        {
          provide: PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$,
          useValue: of('test_partnerID'),
        },
        {
          provide: PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$,
          useValue: of('test_businessApp'),
        },
        {
          provide: LegacyAuthService,
          useValue: {
            HandleLegacyIntegration: jest.fn(),
          },
        },
        {
          provide: CardDataService,
          useValue: {
            getIntegrationsList: jest.fn(),
            getConnectionsList: jest.fn(),
            createConnection: jest.fn(),
            performSSO: jest.fn(),
            getRMAppSettings: jest.fn(),
            getRMAppDetail: jest.fn(),
          },
        },
        {
          provide: SnackbarService,
          useValue: {
            openErrorSnack: jest.fn(),
            openSuccessSnack: jest.fn(),
          },
        },
        {
          provide: TranslateService,
          useValue: {},
        },
      ],
    });
    component = TestBed.inject(ConfigPageComponent);
    TestBed.inject(ActivatedRoute);
    TestBed.inject(CardDataService);
    TestBed.inject(SnackbarService);
    TestBed.inject(TranslateService);
    TestBed.inject(OpenConfirmationModalService);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
