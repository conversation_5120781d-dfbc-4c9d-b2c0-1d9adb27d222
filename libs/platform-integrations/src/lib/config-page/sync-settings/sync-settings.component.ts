import {
  Component,
  Inject,
  inject,
  Input,
  Output,
  OnInit,
  EventEmitter,
  DestroyRef,
  ChangeDetectorRef,
} from '@angular/core';
import { IntegrationType, RmAppDetail } from '../../model';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import {
  PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$,
  PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$,
} from '@galaxy/platform-integrations/shared';
import { MatSlideToggle, MatSlideToggleModule } from '@angular/material/slide-toggle';
import { IntegrationConnectionDetail } from '../../model/Integration-connections-detail';
import { combineLatest, EMPTY, map, Observable, switchMap, take, tap } from 'rxjs';
import { catchError, combineLatestWith } from 'rxjs/operators';
import { CardDataService } from '../../card-data.service';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { DialogBoxComponent } from '../../unlock-dialogue-box/unlock-dialogue-box.component';
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { AppSettings } from '@vendasta/marketplace-apps/lib/_internal/objects/app-settings';
import { MatRadioModule } from '@angular/material/radio';
import {
  ActionType,
  ConnectionStatus,
  CustomFields,
  FieldConfig,
  FieldType,
  FormConfig,
  GetConnectionResponse,
  IntegrationMarketingResponse,
  SupportedContexts,
} from '@vendasta/platform-integrations';
import { IntegrationUtilService } from '../../common/integration-util.service';
import { MatButtonModule } from '@angular/material/button';
import { GalaxyInputModule } from '@vendasta/galaxy/input';
import { MatSelectModule } from '@angular/material/select';
import { RecordService } from '@vendasta/google-search-console';
import { PasswordFieldComponent } from '../instructions/fields/password/password.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { GalaxyTagsModule } from '@vendasta/galaxy/tags';
import { LabelFieldComponent } from '../instructions/fields/label/label.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AppleLocation, AutoCompleteComponent } from './fields/auto-complete/auto-complete.component';
import { AppleAutoCompleteService } from './fields/auto-complete/auto-complete.service';
import { FeatureFlagService } from '@vendasta/businesses';

const invalidPartnerCodeError = 'Value passed in the Partner code is not a valid integration name ';
const TWO_WAY_SYNC_FEATURE_FLAG = 'enable_qbo_two_way_data_sync';

@Component({
  selector: 'platform-integration-sync-settings',
  imports: [
    TranslateModule,
    MatCardModule,
    MatCheckboxModule,
    MatCardModule,
    MatSlideToggleModule,
    TranslateModule,
    ReactiveFormsModule,
    FormsModule,
    MatCheckboxModule,
    CommonModule,
    MatIconModule,
    MatRadioModule,
    MatDividerModule,
    MatInputModule,
    MatFormFieldModule,
    GalaxyFormFieldModule,
    MatButtonModule,
    MatAutocompleteModule,
    PasswordFieldComponent,
    GalaxyTagsModule,
    LabelFieldComponent,
    AutoCompleteComponent,
    GalaxyInputModule,
    MatSelectModule,
  ],
  templateUrl: './sync-settings.component.html',
  styleUrl: './sync-settings.component.scss',
})
export class SyncSettingsComponent implements OnInit {
  syncSettingsForm!: FormGroup;
  isEdited = false;
  isSaved = false;
  syncSettings!: FieldConfig[];
  private formBuilder = inject(FormBuilder);
  private featureFlagService = inject(FeatureFlagService);
  appleAutocompleteFormControlId = '';
  appleAutocompleteFormControl: FormControl = new FormControl([]);
  @Input() integrationType!: string;
  @Input() pageData!: { cardDetail: IntegrationConnectionDetail; namespace: string; connectionId: string };
  @Output() locationSaved = new EventEmitter<void>();
  configPageFields: FormConfig[] = [];
  private readonly snackbarService = inject(SnackbarService);
  private readonly cardService = inject(CardDataService);
  private readonly confirmationModal = inject(OpenConfirmationModalService);
  private readonly translate = inject(TranslateService);
  private readonly dialog = inject(MatDialog);
  protected readonly fieldType = FieldType;
  protected readonly integrationTypeEnums = IntegrationType;
  protected readonly actionType = ActionType;
  private readonly integrationUtilService = inject(IntegrationUtilService);
  private readonly recordService = inject(RecordService);
  private readonly destroyRef = inject(DestroyRef);
  protected readonly ConnectionStatus = ConnectionStatus;
  private readonly autoCompleteService = inject(AppleAutoCompleteService);
  constructor(
    @Inject(PLATFORM_INTEGRATIONS_NAMESPACE_INJECTION_TOKEN$) public readonly namespace$: Observable<string>,
    @Inject(PLATFORM_INTEGRATIONS_PARTNER_ID_INJECTION_TOKEN$)
    public readonly partnerId$: Observable<string>,
    @Inject(PLATFORM_INTEGRATIONS_MARKET_ID_INJECTION_TOKEN$) public readonly marketId$: Observable<string>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    @Inject(PLATFORM_INTEGRATIONS_CONTEXT_INJECTION_TOKEN$)
    public readonly context: SupportedContexts,
    private cdr: ChangeDetectorRef,
  ) {}

  stringToBooleanConversion(value: string): boolean {
    if (value === 'true') {
      return true;
    }
    return false;
  }

  ngOnInit(): void {
    this.configPageFields = this.pageData.cardDetail.integration?.configPageFields ?? [];
    this.syncSettings = this.getSyncSettings(this.configPageFields.flatMap((config) => config.fields));
    this.initializeFormGroup(
      this.pageData.cardDetail?.connection,
      this.pageData.cardDetail?.integration,
      this.syncSettings,
    );

    this.syncSettings.forEach((field) => {
      if (this.isInputField(field.fieldType)) {
        const customField = this.pageData.cardDetail?.connection?.customFields?.find((cf) => cf.label === field.id);
        if (customField && customField?.value) {
          this.syncSettingsForm.controls[field.id].disable();
        }
      }
    });
    const atLeastOneEditableFieldFilled = this.syncSettings
      .filter((field) => this.isInputField(field.fieldType) && !field.readonly)
      .some((field) => {
        const customField = this.pageData.cardDetail?.connection?.customFields?.find((cf) => cf.label === field.id);
        return customField?.value && customField?.value !== '[]';
      });

    this.isSaved = atLeastOneEditableFieldFilled;
  }

  updateSelectSetting(fieldName: string, fieldId: string, selectedValue: any) {
    const updates = [{ label: fieldId, value: selectedValue }];

    this.updateConnectionSetting(
      this.pageData,
      updates,
      ConnectionStatus.UNSPECIFIED_STATUS,
      { key: 'CONNECTION_SYNC_SETTINGS.UPDATE_SETTINGS', params: { label: fieldName } },
      { key: 'CONNECTION_SYNC_SETTINGS.UPDATE_SYNC_ID_SETTINGS_ERROR', params: { label: fieldName } },
    );
  }

  private reloadPage() {
    window.location.reload();
  }

  parseValue(value: any, fieldType: FieldType): any {
    if (fieldType === FieldType.FIELD_TYPE_CHIPLIST) {
      return value.split(',');
    }
    return value;
  }

  initializeFormGroup(
    connection: GetConnectionResponse | undefined,
    integration: IntegrationMarketingResponse | undefined,
    syncSettings: FieldConfig[],
  ) {
    this.syncSettingsForm = this.formBuilder.group({});
    const syncSettingIds = this.integrationUtilService.getFieldIdsFromConfig(syncSettings);
    const syncSettingsMap = this.integrationUtilService.getFieldConfigAsMap(syncSettings);
    const customFieldsMap: Map<string, CustomFields> = new Map(
      connection?.customFields?.map((item) => [item.label, item]),
    );
    const pageConfigFields = new Map<string, FieldConfig>();

    syncSettingIds.forEach((syncSettingId) => {
      integration?.configPageFields.forEach((configPage) => {
        configPage.fields.forEach((field) => {
          if (syncSettingId === field.id) {
            pageConfigFields.set(field.id, field);
          }
        });
      });
    });

    syncSettingIds.forEach((id) => {
      const pageConfigFieldInfo = pageConfigFields.get(id);
      const customFieldInfo = customFieldsMap.get(id);
      const syncSettingFieldInfo = syncSettingsMap.get(id);

      if (syncSettingFieldInfo) {
        switch (this.integrationType) {
          case IntegrationType.GOOGLESEARCHCONSOLE:
            this.syncSettingsForm.addControl(id, new FormControl([]));
            this.getGSCKeywords()
              .pipe(takeUntilDestroyed(this.destroyRef))
              .subscribe((keywords) => {
                this.syncSettingsForm.get(id)?.setValue(keywords);
                if (keywords !== undefined && keywords.length > 0) {
                  this.syncSettingsForm.get(id)?.disable();
                  this.isSaved = true;
                }
              });
            break;
          case IntegrationType.APPLEBUSINESSCONNECT:
            this.appleAutocompleteFormControlId = id;
            this.syncSettingsForm.addControl(id, this.appleAutocompleteFormControl);
            break;
          default:
            if (pageConfigFieldInfo) {
              switch (syncSettingFieldInfo.fieldType) {
                case FieldType.FIELD_TYPE_TOGGLE:
                  this.syncSettingsForm.addControl(
                    pageConfigFieldInfo.id,
                    new FormControl(this.stringToBooleanConversion(customFieldInfo?.value ?? 'false')),
                  );
                  break;
                case FieldType.FIELD_TYPE_PASSWORD:
                  this.syncSettingsForm.addControl(pageConfigFieldInfo.id, new FormControl(customFieldInfo?.value));
                  break;
                case FieldType.FIELD_TYPE_TEXT:
                  this.syncSettingsForm.addControl(pageConfigFieldInfo.id, new FormControl(customFieldInfo?.value));
                  break;
                case FieldType.FIELD_TYPE_SELECT:
                  this.syncSettingsForm.addControl(pageConfigFieldInfo.id, new FormControl(customFieldInfo?.value));
                  break;
                case FieldType.FIELD_TYPE_CHIPLIST: {
                  this.syncSettingsForm.addControl(id, new FormControl([]));
                  const customField = this.pageData.cardDetail?.connection?.customFields?.find((cf) => cf.label === id);
                  if (customField?.value) {
                    this.syncSettingsForm
                      .get(id)
                      ?.setValue(this.parseValue(customField.value, syncSettingFieldInfo.fieldType));
                  }
                  break;
                }
              }
            }
        }
        if (syncSettingFieldInfo.dependents) {
          syncSettingFieldInfo.dependents.forEach((dep) => {
            const dependentCustomField = customFieldsMap.get(dep.id);
            if (dependentCustomField) {
              const depControl = new FormControl(
                {
                  value: this.stringToBooleanConversion(dependentCustomField.value),
                  disabled: !syncSettingFieldInfo.defaultValue,
                },
                dep.required ? Validators.required : null,
              );
              this.syncSettingsForm.addControl(dependentCustomField.label, depControl);
              if (customFieldsMap.get(syncSettingFieldInfo.id)?.value === 'true') {
                depControl.enable();
              } else {
                depControl.disable();
              }
            }
          });
        }
      }
    });
  }

  updateToggleSetting(
    setting: FieldConfig,
    card: { cardDetail: IntegrationConnectionDetail; namespace: string; connectionId: string },
    event: MatSlideToggle,
  ) {
    const config = this.configPageFields?.[0]?.fields ?? []; //TODO:PHNX assuming sync setting is first for now
    if (config?.some((field) => field.dependents?.length > 0)) {
      setting.defaultValue = this.syncSettingsForm.controls[setting.id].value;
      config.forEach((field) => {
        if (field.dependents?.length > 0 && field.id === setting.id) {
          field.dependents.forEach((depField) => {
            if (setting.defaultValue === true) {
              this.syncSettingsForm.controls[depField.id].enable();
            } else {
              this.syncSettingsForm.controls[depField.id].disable();
            }
          });
        }
      });
    }
    if (this.syncSettingsForm.controls[setting?.id]?.value === false) {
      this.openSyncSettingWarnModal(setting, card);
    } else {
      const updates = [{ label: setting.id, value: event.checked.toString() }];
      this.updateConnectionSetting(
        card,
        updates,
        ConnectionStatus.UNSPECIFIED_STATUS,
        { key: 'CONNECTION_SYNC_SETTINGS.UPDATE_SETTINGS', params: { label: setting.label } },
        { key: 'CONNECTION_SYNC_SETTINGS.UPDATE_SYNC_ID_SETTINGS_ERROR', params: { label: setting.label } },
      );
    }
  }

  openSyncSettingWarnModal(
    setting: FieldConfig,
    card: { cardDetail: IntegrationConnectionDetail; namespace: string; connectionId: string },
  ) {
    this.confirmationModal
      .openModal({
        type: 'warn',
        title: this.translate.instant('INTEGRATION_CARD.SYNC_SETTING_TITLE', {
          title: setting.label,
        }),
        message: this.translate.instant('INTEGRATION_CARD.SYNC_SETTING_MESSAGE', {
          title: setting.label,
        }),
        confirmButtonText: this.translate.instant('INTEGRATION_CARD.TURN_OFF'),
        cancelButtonText: this.translate.instant('INTEGRATION_CARD.CANCEL'),
      })
      .pipe(take(1))
      .subscribe((userDidAction) => {
        if (userDidAction) {
          const updates = [{ label: setting.id, value: 'false' }];
          this.updateConnectionSetting(
            card,
            updates,
            ConnectionStatus.UNSPECIFIED_STATUS,
            { key: 'CONNECTION_SYNC_SETTINGS.UPDATE_SETTINGS', params: { label: setting.label } },
            { key: 'CONNECTION_SYNC_SETTINGS.UPDATE_SYNC_ID_SETTINGS_ERROR', params: { label: setting.label } },
          );
        } else {
          this.syncSettingsForm.controls[setting.id].setValue(true);
          const config = this.configPageFields?.[0]?.fields ?? []; //TODO:PHNX assuming sync setting is first for now
          if (config?.some((field) => field.dependents?.length > 0)) {
            setting.defaultValue = true;
          }
        }
      });
  }

  getSyncSettings(configPageFields: FieldConfig[]): FieldConfig[] {
    return configPageFields
      ?.filter((setting) => {
        return !(setting.id === 'reviewRequestData' && this.context === SupportedContexts.PI_CONTEXT_PARTNER);
      })
      .map((field) => {
        if (field.id === 'TwoWayDataSync') {
          combineLatest([this.partnerId$, this.marketId$])
            .pipe(
              switchMap(([partnerId, marketId]) =>
                this.featureFlagService.checkFeatureFlag(partnerId, marketId, TWO_WAY_SYNC_FEATURE_FLAG),
              ),
              takeUntilDestroyed(this.destroyRef),
            )
            .subscribe((isEnabled) => {
              field.hidden = !isEnabled;
            });
        }
        return field;
      });
  }

  handleButtonClick(rmAppDetails: RmAppDetail): void {
    this.dialog.open(DialogBoxComponent, {
      width: '463px',
      data: {
        namespace: rmAppDetails.namespace,
        partnerId: rmAppDetails.partnerId,
        rmAppSettings: rmAppDetails.upgradeSetting,
        integrationId: this.data['integrationID'],
      },
    });
  }

  isInputField(fieldType: FieldType): boolean {
    if (
      fieldType === FieldType.FIELD_TYPE_TEXT ||
      fieldType === FieldType.FIELD_TYPE_PASSWORD ||
      fieldType === FieldType.FIELD_TYPE_CHIPLIST
    ) {
      return true;
    }
    return false;
  }

  protected readonly getAppSettings$: Observable<AppSettings> = combineLatest([this.partnerId$, this.marketId$]).pipe(
    switchMap(([partnerID, marketID]) => {
      return this.cardService.getRMAppSettings(partnerID, marketID);
    }),
  );

  protected readonly getRMAppDetailsConfigPage$ = combineLatest([this.getAppSettings$]).pipe(
    map(([rmAppSettings]) => {
      const res = {
        hasRMPremiumActive: false,
        upgradeSetting: rmAppSettings,
      } as RmAppDetail;
      if (this.cardService.isRMActive()) {
        res.hasRMPremiumActive = true;
        return res;
      }
      return res;
    }),
  );

  updateRadioButton(
    setting: FieldConfig,
    card: { cardDetail: IntegrationConnectionDetail; namespace: string; connectionId: string },
  ) {
    const updates = [{ label: setting.id, value: this.syncSettingsForm.controls[setting.id].value.toString() }];
    const selectedOption = setting.options?.find((option) => option?.value === true);
    this.updateConnectionSetting(
      card,
      updates,
      ConnectionStatus.UNSPECIFIED_STATUS,
      { key: 'CONNECTION_SYNC_SETTINGS.UPDATE_SETTINGS', params: { label: selectedOption?.label } },
      { key: 'CONNECTION_SYNC_SETTINGS.UPDATE_SYNC_ID_SETTINGS_ERROR', params: { label: selectedOption?.label } },
    );
  }

  onSave(
    card: { cardDetail: IntegrationConnectionDetail; namespace: string; connectionId: string },
    fields: FieldConfig[],
  ) {
    const configPageFields = card.cardDetail?.integration?.configPageFields;
    const integrationType = card.cardDetail.integration?.integrationType;
    const title = configPageFields?.map((field) => field?.title).filter(Boolean)?.[0];

    if (!this.syncSettingsForm.valid) {
      this.snackbarService.openErrorSnack('CONNECTION_SYNC_SETTINGS.REQUIRED_FIELD_ERROR');
      return;
    }

    // Leave connectionStatus as unspecified if you don't want to change the connection status
    let connectionStatus = ConnectionStatus.UNSPECIFIED_STATUS;
    const updates = fields.flatMap((field) => {
      const control = this.syncSettingsForm.controls[field.id];
      control?.disable();
      if (field.id === this.appleAutocompleteFormControlId) {
        const l: AppleLocation = control?.getRawValue();
        if (!l) {
          return [];
        }

        connectionStatus = ConnectionStatus.CONNECTED;
        return [
          { label: 'businessId', value: l.brandId }, // Leaving brands as business in custom fields for now
          { label: 'locationId', value: l.locationId },
          { label: 'state', value: l.state },
          { label: 'placeCardUrl', value: l.placeCardUrl || '' },
          { label: 'locationName', value: l.name },
          { label: 'partnersLocationId', value: l.partnersLocationId },
          { label: 'partnersLocationVersion', value: l.partnersLocationVersion || '' },
        ];
      }
      let value = control?.getRawValue();
      // Handle different field types
      if (field.fieldType === FieldType.FIELD_TYPE_CHIPLIST) {
        value = Array.isArray(value) ? value.join(',') : (value || '').toString();
      }
      return [{ label: field.id, value: value?.toString() || '' }];
    });

    // Append locationId with data_connectionId:data_apiKey for "protractor" integration alone for now
    // TODO - Create a locationID in config and hide it. Just update over here
    if (integrationType === IntegrationType.PROTRACTOR) {
      const connectionId = updates.find((f) => f.label === 'data_connectionId')?.value;
      const apiKey = updates.find((f) => f.label === 'data_apiKey')?.value;
      if (connectionId && apiKey) {
        const locationIdEntry = updates.find((f) => f.label === 'locationID');
        const locationValue = `${connectionId}:${apiKey}`;
        if (locationIdEntry) {
          locationIdEntry.value = locationValue;
        } else {
          updates.push({ label: 'locationID', value: locationValue });
        }
      }
    }

    const successMessageKey =
      integrationType === IntegrationType.APPLEBUSINESSCONNECT
        ? 'CONNECTION_SYNC_SETTINGS.SINGLE_VALUE_SAVE_SUCCESS'
        : 'CONNECTION_SYNC_SETTINGS.MULTIPLE_VALUES_SAVE_SUCCESS';

    const errorMessageKey = 'CONNECTION_SYNC_SETTINGS.UPDATE_SETTINGS_ERROR';
    this.isSaved = true;
    this.isEdited = false;

    this.updateConnectionSetting(
      card,
      updates,
      connectionStatus,
      { key: successMessageKey, params: { title } },
      { key: errorMessageKey },
    );
  }

  onEdit(fields: FieldConfig[]) {
    fields.forEach((field) => {
      this.syncSettingsForm.controls[field.id]?.enable();
    });
    this.isSaved = false;
    this.isEdited = true;
  }

  onCancel(
    card: { cardDetail: IntegrationConnectionDetail; namespace: string; connectionId: string },
    fields: FieldConfig[],
  ) {
    const syncSettingIds = this.integrationUtilService.getFieldIdsFromConfig(this.syncSettings);

    this.cardService
      .getConnection(card.connectionId, card.namespace)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((updatedConnection) => {
        const updatedCustomFields = updatedConnection?.customFields;
        if (Array.isArray(updatedCustomFields)) {
          updatedCustomFields.forEach((con) => {
            if (syncSettingIds.includes(con.label)) {
              fields.forEach((field) => {
                if (
                  (field.fieldType === FieldType.FIELD_TYPE_TEXT ||
                    field.fieldType === FieldType.FIELD_TYPE_CHIPLIST) &&
                  field.id === con.label
                ) {
                  if (card.cardDetail.integration?.integrationType === IntegrationType.APPLEBUSINESSCONNECT) {
                    this.syncSettingsForm.controls[field.id].setValue(
                      this.autoCompleteService.getLocationFromCustomFields(updatedCustomFields),
                    );
                  } else {
                    this.syncSettingsForm.controls[field.id].setValue(this.parseValue(con.value, field.fieldType));
                  }
                  this.syncSettingsForm.controls[field.id].disable();
                }
              });
            }
          });
        }
        this.isSaved = true;
        this.isEdited = false;
      });
  }

  updateConnectionSetting(
    card: { cardDetail: IntegrationConnectionDetail; namespace: string; connectionId: string },
    updates: Array<{ label: string; value: string }>,
    connectionStatus: ConnectionStatus = ConnectionStatus.UNSPECIFIED_STATUS,
    successMessageDetails?: { key: string; params?: any },
    errorMessageDetails?: { key: string; params?: any },
  ) {
    const configPageFields = card.cardDetail?.integration?.configPageFields;

    switch (this.integrationType) {
      case IntegrationType.GOOGLESEARCHCONSOLE: {
        const setting = configPageFields
          ?.find((field) => field.fields?.find((f) => f.id === updates[0].label))
          ?.fields?.find((f) => f.id === updates[0].label);
        if (setting) {
          this.setGSCKeywords(this.syncSettingsForm.get(setting.id)?.getRawValue() || [], setting)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe();
        }
        break;
      }
      default:
        this.cardService
          .updateConnection(card.connectionId, card.namespace, updates, connectionStatus)
          .pipe(
            tap(() => {
              if (successMessageDetails) {
                this.snackbarService.openSuccessSnack(
                  this.translate.instant(successMessageDetails.key, successMessageDetails?.params),
                );
              }

              // Special case: Emit event on success for Apple Integration
              if (card.cardDetail.integration?.integrationType === IntegrationType.APPLEBUSINESSCONNECT) {
                this.locationSaved.emit();
              }
            }),
            catchError((error) => {
              if (error?.error?.message === invalidPartnerCodeError) {
                this.snackbarService.openErrorSnack('CONNECTION_SYNC_SETTINGS.INVALID_PARTNER_CODE_ERROR');
              } else if (errorMessageDetails) {
                this.snackbarService.openErrorSnack(
                  this.translate.instant(errorMessageDetails.key, errorMessageDetails?.params),
                );
              } else {
                this.snackbarService.openErrorSnack('CONNECTION_SYNC_SETTINGS.UPDATE_SYNC_SETTING_ERROR');
              }
              return EMPTY;
            }),
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe();
    }
  }

  // getGSCKeywords method will fetch the list of keyword for the namespace from google search console
  getGSCKeywords(): Observable<string[]> {
    return this.partnerId$.pipe(
      combineLatestWith(this.namespace$),
      switchMap(([partnerId, namespace]) => {
        return this.recordService.getKeywords(partnerId, namespace);
      }),
    );
  }

  // getGSCKeywords method will set the list of keyword for the namespace in google search console
  setGSCKeywords(keywords: string[], setting: FieldConfig): Observable<boolean> {
    return this.partnerId$.pipe(
      combineLatestWith(this.namespace$),
      switchMap(([partnerId, namespace]) => {
        return this.recordService.setKeywords(partnerId, namespace, keywords);
      }),
      tap((updated) => {
        if (updated) {
          this.snackbarService.openSuccessSnack(
            this.translate.instant('CONNECTION_SYNC_SETTINGS.UPDATE_SETTINGS', { label: setting.label }),
          );
        } else {
          this.translate.instant('CONNECTION_SYNC_SETTINGS.UPDATE_SYNC_ID_SETTINGS_ERROR', {
            label: setting.label,
          });
        }
      }),
      catchError((_err) => {
        this.snackbarService.openErrorSnack('CONNECTION_SYNC_SETTINGS.UPDATE_SYNC_SETTING_ERROR');
        return EMPTY;
      }),
    );
  }
}
