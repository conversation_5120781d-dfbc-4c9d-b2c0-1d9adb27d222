<div class="sync-settings" [formGroup]="syncSettingsForm">
  @for (config of configPageFields; track $index) {
    <ng-container *ngIf="config.title; else noTitleBlock">
      <mat-card appearance="outlined" class="mat-card-margin">
        <mat-card-content class="chips-field">
          <div class="mat-card-content-title title">{{ config.title | translate }}</div>
          <ng-container *ngTemplateOutlet="fieldBlock; context: { fields: config.fields }"></ng-container>
        </mat-card-content>
      </mat-card>
    </ng-container>
    <ng-template #noTitleBlock>
      <ng-container *ngFor="let field of config.fields">
        <mat-card *ngIf="!field.hidden" appearance="outlined" class="mat-card-margin">
          <mat-card-content class="chips-field">
            <ng-container *ngTemplateOutlet="fieldBlock; context: { fields: [field] }"></ng-container>
          </mat-card-content>
        </mat-card>
      </ng-container>
    </ng-template>
    <ng-template #fieldBlock let-fields="fields">
      <ng-container *ngFor="let field of fields">
        @switch (field.fieldType) {
          @case (fieldType.FIELD_TYPE_PASSWORD) {
            <div *ngIf="!field.hidden" class="mat-card-content">
              <div class="mat-card-content-column">
                <div class="mat-card-content-title title">
                  {{ field.label | translate }}
                </div>
              </div>
              <div class="sub-title">
                {{ field.hintText | translate }}
              </div>
              <div>
                <platform-integration-passwordfield
                  [formControlName]="field.id"
                  [disabled]="pageData.cardDetail.connection?.status === ConnectionStatus.CONNECTED || field.readonly"
                  [placeholder]="field.placeHolder"
                  [required]="field.required"
                ></platform-integration-passwordfield>
              </div>
            </div>
          }
          @case (fieldType.FIELD_TYPE_CHIPLIST) {
            <div class="mat-card-content">
              <div class="mat-card-content-column">
                <div class="mat-card-content-title title">
                  {{ field.label | translate }}
                </div>
              </div>
              <div class="sub-title">
                {{ field.hintText | translate }}
              </div>
              <div class="mat-chiplist">
                <glxy-form-field>
                  <glxy-tags-input
                    [maxTags]="20"
                    [placeholder]="field.placeHolder"
                    [formControlName]="field.id"
                    [required]="field.required"
                  ></glxy-tags-input>
                </glxy-form-field>
              </div>
            </div>
          }
          @case (fieldType.FIELD_TYPE_LABEL) {
            <platform-integration-labelfield
              [field]="field"
              [connection]="pageData.cardDetail.connection"
            ></platform-integration-labelfield>
          }
          @case (fieldType.FIELD_TYPE_SELECT) {
            <div class="mat-card-content">
              <div class="mat-card-content-column">
                <div class="mat-card-content-title title">
                  {{ field.label | translate }}
                </div>
              </div>
              <div class="sub-title">
                {{ field.hintText | translate }}
              </div>
              <div class="select-input">
                <mat-form-field class="full-width">
                  <mat-select
                    [formControl]="syncSettingsForm.get(field.id)"
                    (selectionChange)="updateSelectSetting(field.label, field.id, $event.value)"
                  >
                    @for (option of field?.options; track option.value) {
                      <mat-option [value]="option.value">
                        {{ option.label }}
                      </mat-option>
                    }
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
          }
          @default {
            <div class="mat-card-content">
              <div class="mat-card-content-column">
                <div class="mat-card-content-title title">
                  {{ field.label }}
                  <ng-container *ngIf="getRMAppDetailsConfigPage$ | async as rmAppDetails; else checkTemplate">
                    <ng-container
                      *ngIf="!rmAppDetails.hasRMPremiumActive && field.id === 'reviewRequestData'; else toggleTemplate"
                    >
                      <mat-icon class="lock-icon">lock</mat-icon>
                      <button color="primary" mat-stroked-button (click)="handleButtonClick(rmAppDetails)">
                        <b>{{ 'CONNECTION_SYNC_SETTINGS.UNLOCK_PREMIUM_SERVICE' | translate }}</b>
                      </button>
                    </ng-container>
                  </ng-container>
                  <ng-template #toggleTemplate>
                    <mat-slide-toggle
                      *ngIf="!isInputField(field.fieldType)"
                      [formControlName]="field.id"
                      (change)="updateToggleSetting(field, pageData, $event)"
                    ></mat-slide-toggle>
                  </ng-template>
                  <ng-template #checkTemplate>
                    <mat-icon class="lock-icon" *ngIf="field.id === 'reviewRequestData'">lock</mat-icon>
                    <ng-container *ngIf="getAppSettings$ | async as rmAppDetails">
                      <ng-container *ngIf="field.id === 'reviewRequestData'; else toggleTemplate">
                        <button color="primary" mat-stroked-button (click)="handleButtonClick(rmAppDetails)">
                          <b>{{ 'CONNECTION_SYNC_SETTINGS.UNLOCK_PREMIUM_SERVICE' | translate }}</b>
                        </button>
                      </ng-container>
                    </ng-container>
                    <ng-template #toggleTemplate>
                      <mat-slide-toggle
                        *ngIf="!isInputField(field.fieldType)"
                        [formControlName]="field.id"
                        (change)="updateToggleSetting(field, data, $event)"
                      ></mat-slide-toggle>
                    </ng-template>
                  </ng-template>
                </div>
              </div>
              <div class="sub-title">
                {{ field.hintText }}
              </div>
              <div class="display-row" *ngIf="field.fieldType === fieldType.FIELD_TYPE_TEXT">
                <div
                  class="apple-display-row"
                  *ngIf="integrationType === integrationTypeEnums.APPLEBUSINESSCONNECT; else defaultText"
                >
                  <div class="custom-input">
                    <platform-integration-auto-complete-field
                      [theFormControl]="appleAutocompleteFormControl"
                      [fieldConfig]="field"
                      [pageData]="pageData"
                    ></platform-integration-auto-complete-field>
                  </div>
                </div>
                <ng-template #defaultText>
                  <div class="custom-input">
                    <glxy-form-field *ngIf="isInputField(field.fieldType)">
                      <input
                        [type]="field.fieldType"
                        [placeholder]="field.placeHolder"
                        [formControlName]="field.id"
                        [value]="field?.defaultValue ? field.defaultValue : ''"
                        [required]="field.required"
                        matInput
                      />
                    </glxy-form-field>
                  </div>
                </ng-template>
              </div>
              <div *ngFor="let dep of field.dependents || []">
                <label [for]="dep.id">{{ dep.label }}</label>
                <div [ngSwitch]="dep.fieldType">
                  <mat-radio-group
                    *ngSwitchCase="fieldType.FIELD_TYPE_RADIO"
                    [formControlName]="dep.id"
                    (change)="updateRadioButton(dep, pageData)"
                  >
                    <div class="radio-button-column" *ngFor="let option of dep?.options">
                      <mat-radio-button [value]="option.value">{{ option.label }}</mat-radio-button>
                    </div>
                  </mat-radio-group>
                </div>
              </div>
            </div>
          }
        }
      </ng-container>
      <div>
        @for (action of config.actions; track $index) {
          @switch (action.actionType) {
            @case (actionType.ACTION_TYPE_SAVE) {
              <button
                class="button-with-space"
                *ngIf="(!isEdited && !isSaved) || (!isSaved && isEdited)"
                (click)="onSave(pageData, config.fields)"
                color="primary"
                mat-flat-button
              >
                <b>{{ 'CONNECTION_SYNC_SETTINGS.SAVE' | translate }}</b>
              </button>
              <button *ngIf="isSaved && !isEdited" (click)="onEdit(config.fields)" color="primary" mat-stroked-button>
                <b>{{ 'CONNECTION_SYNC_SETTINGS.EDIT' | translate }}</b>
              </button>
              <button *ngIf="!isSaved && isEdited" (click)="onCancel(pageData, config.fields)" mat-stroked-button>
                <b>{{ 'CONNECTION_SYNC_SETTINGS.CANCEL' | translate }}</b>
              </button>
            }
          }
        }
      </div>
    </ng-template>
  }
</div>
