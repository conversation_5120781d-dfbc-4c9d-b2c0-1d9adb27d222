<div>
  <ng-container *ngIf="getRMAppDetailsMarketingPage$ | async as rmAppDetails">
    <h2 mat-dialog-title class="model-title">{{ preconnectFormFields.title }}</h2>
    <mat-dialog-content>
      <div class="content-title">
        {{ preconnectFormFields.description }}
      </div>
      <div class="sync-settings" [formGroup]="syncSettingsForm">
        <div *ngFor="let setting of getSyncSettings()">
          <mat-card *ngIf="!setting.hidden" appearance="outlined" class="mat-card-margin">
            <mat-card-content>
              <div class="mat-card-content">
                <mat-checkbox [formControlName]="setting.id"></mat-checkbox>
                <div class="mat-card-content-column">
                  <div class="title">
                    <div
                      [ngClass]="{
                        'disabled-title': setting.id === 'reviewRequestData' && !rmAppDetails.hasRMPremiumActive,
                      }"
                      class="title-row"
                    >
                      {{ setting.label }}
                      <mat-icon
                        class="lock-icon"
                        *ngIf="setting.id === 'reviewRequestData' && !rmAppDetails.hasRMPremiumActive"
                        >lock</mat-icon
                      >
                    </div>
                  </div>
                  <div class="sub-title">
                    {{ setting.hintText }}
                  </div>

                  <!-- Display dependent fields -->
                  <div *ngFor="let dep of setting.dependents || []">
                    <label [for]="dep.id">{{ dep.label }}</label>
                    <div [ngSwitch]="dep.fieldType">
                      <mat-radio-group *ngSwitchCase="fieldType.FIELD_TYPE_RADIO" [formControlName]="dep.id">
                        <div class="radio-button-column">
                          <mat-radio-button *ngFor="let option of dep?.options" [value]="option.value">
                            {{ option.label }}
                          </mat-radio-button>
                        </div>
                      </mat-radio-group>
                    </div>
                  </div>
                </div>
                <ng-container *ngIf="!rmAppDetails.hasRMPremiumActive && setting.id === 'reviewRequestData'">
                  <button
                    class="unlockButton"
                    color="primary"
                    mat-stroked-button
                    (click)="handleButtonClick(rmAppDetails)"
                    mat-dialog-close
                  >
                    <span>
                      <b>{{ 'CONNECTION_SYNC_SETTINGS.UNLOCK_PREMIUM_SERVICE' | translate }}</b>
                    </span>
                  </button>
                </ng-container>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </mat-dialog-content>
    <mat-dialog-actions>
      <platform-integration-action-type [actions]="preconnectFormFields.actions" (actionClicked)="handleAction($event)">
      </platform-integration-action-type>
    </mat-dialog-actions>
  </ng-container>
</div>
