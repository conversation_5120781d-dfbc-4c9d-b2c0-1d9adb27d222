<glxy-table-container
  [dataSource]="tableDataSource"
  [columns]="columns"
  [pageSizeOptions]="pageSizeOptions"
  [pageSize]="defaultPageSize"
  [showFooter]="(payoutListDataSource.loading$() | async) || shouldShowPayouts"
>
  <table mat-table>
    <tr mat-header-row *matHeaderRowDef="[]"></tr>

    <tr mat-row *matRowDef="let row; columns: []" [hidden]="!shouldShowPayouts" (click)="onPayoutSelected(row.id)"></tr>

    <tr
      mat-footer-row
      *matFooterRowDef="[PayoutColumns.MANAGE_IN_STRIPE]"
      [hidden]="(payoutListDataSource.loading$() | async) || shouldShowPayouts"
    ></tr>

    <!-- Amount Column -->
    <ng-container matColumnDef="{{ PayoutColumns.AMOUNT }}">
      <th mat-header-cell *matHeaderCellDef>{{ 'PAYOUTS_PAGE.COLUMNS.AMOUNT' | translate }}</th>
      <td mat-cell *matCellDef="let row">
        <span>{{ row.amount / 100 | glxyCurrency: row.currencyCode }}</span>
      </td>
    </ng-container>

    <!-- Status Column -->
    <ng-container matColumnDef="{{ PayoutColumns.STATUS }}">
      <th mat-header-cell *matHeaderCellDef>{{ 'PAYOUTS_PAGE.COLUMNS.STATUS' | translate }}</th>
      <td mat-cell *matCellDef="let row">
        <smb-invoicing-payout-status-badge
          [status]="row.status"
          [failureMessage]="row.failureMessage"
        ></smb-invoicing-payout-status-badge>
      </td>
    </ng-container>

    <!-- Arrival Date Column -->
    <ng-container matColumnDef="{{ PayoutColumns.ARRIVAL_DATE }}">
      <th mat-header-cell *matHeaderCellDef>{{ 'PAYOUTS_PAGE.COLUMNS.DATE' | translate }}</th>
      <td mat-cell *matCellDef="let row">
        {{ row.arrivalDate | glxyDate }}
      </td>
    </ng-container>

    <!-- Bank Account Column -->
    <ng-container matColumnDef="{{ PayoutColumns.BANK }}">
      <th mat-header-cell *matHeaderCellDef>{{ 'PAYOUTS_PAGE.COLUMNS.BANK' | translate }}</th>
      <td mat-cell *matCellDef="let row">
        <span *ngIf="row.lastFourDigits"> {{ row.bankName }} ****** {{ row.lastFourDigits }} </span>
        <i *ngIf="!row.lastFourDigits" [matTooltip]="'BANK_ACCOUNT.REMOVED_TOOLTIP' | translate">
          {{ 'BANK_ACCOUNT.REMOVED' | translate }}
        </i>
      </td>
    </ng-container>

    <ng-container matColumnDef="{{ PayoutColumns.MANAGE_IN_STRIPE }}">
      <td mat-footer-cell *matFooterCellDef [attr.colspan]="columns.length" class="to-stripe-table">
        {{ 'PAYOUTS_PAGE.DEFER_TO_STRIPE.TABLE' | translate }}
      </td>
    </ng-container>
  </table>
</glxy-table-container>
