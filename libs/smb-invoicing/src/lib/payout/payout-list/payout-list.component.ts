import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MerchantService, RetailPayout } from '@galaxy/billing';
import { Observable } from 'rxjs';
import { PayoutListDataSource } from './payout-list-datasource';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { GalaxyColumnDef, GalaxyDataSource, GalaxyTableModule, MODULE_IMPORTS } from '@vendasta/galaxy/table';
import { MatTooltip } from '@angular/material/tooltip';
import { VaMaterialTableModule } from '@vendasta/uikit';
import { PayoutStatusBadgeComponent } from '../payout-status-badge/payout-status-badge.component';

export enum PayoutColumns {
  ARRIVAL_DATE = 'arrivalDate',
  STATUS = 'status',
  BANK = 'bankAcct',
  AMOUNT = 'amount',
  MANAGE_IN_STRIPE = 'manageInStripe',
}

@Component({
  selector: 'smb-invoicing-payout-list',
  templateUrl: './payout-list.component.html',
  styleUrls: ['./payout-list.component.scss'],
  standalone: true,
  imports: [
    TranslateModule,
    GalaxyPipesModule,
    MODULE_IMPORTS,
    MatTooltip,
    VaMaterialTableModule,
    PayoutStatusBadgeComponent,
    GalaxyTableModule,
  ],
})
export class PayoutListComponent implements OnInit {
  PayoutColumns = PayoutColumns;
  pageSizeOptions = [25, 50, 100];
  defaultPageSize = 50;

  columns: GalaxyColumnDef[] = [
    {
      id: PayoutColumns.ARRIVAL_DATE,
    },
    {
      id: PayoutColumns.STATUS,
    },
    {
      id: PayoutColumns.BANK,
    },
    {
      id: PayoutColumns.AMOUNT,
    },
  ];

  payoutListDataSource: PayoutListDataSource;
  tableDataSource: GalaxyDataSource<RetailPayout>;

  @Input() shouldShowPayouts: boolean;
  @Input() merchantId$: Observable<string>;
  @Output() payoutSelected: EventEmitter<string> = new EventEmitter();

  constructor(private merchantService: MerchantService) {}

  ngOnInit() {
    this.payoutListDataSource = new PayoutListDataSource(this.merchantService, this.merchantId$);
    this.tableDataSource = new GalaxyDataSource<RetailPayout>(this.payoutListDataSource);
  }

  onPayoutSelected(payoutId: string) {
    this.payoutSelected.emit(payoutId);
  }
}
