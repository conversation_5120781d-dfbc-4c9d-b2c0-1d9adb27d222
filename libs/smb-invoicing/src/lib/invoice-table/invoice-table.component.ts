import { AsyncPipe, DatePipe, formatDate } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  signal,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatPaginator, MatPaginatorIntl } from '@angular/material/paginator';
import {
  CollectionMethodAPI,
  Invoice,
  InvoiceDateField,
  InvoiceOrigin,
  InvoiceOriginAPI,
  InvoicePDFType,
  InvoiceService,
  InvoiceStatus,
  LastPaymentStatus,
  ListInvoicesDateFilter,
  ListInvoicesFilter,
} from '@galaxy/billing';
import { PopoverPositions } from '@vendasta/galaxy/popover';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import {
  CheckboxFilterControl,
  DateFilterControl,
  FilterControl,
  FilterGroup,
  FilterModule,
  FilterService,
  SearchFilterControl,
} from '@vendasta/va-filter2';
import { BehaviorSubject, combineLatest, EMPTY, firstValueFrom, Observable, of, ReplaySubject } from 'rxjs';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  map,
  skip,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
  withLatestFrom,
} from 'rxjs/operators';
import { ChangeInvoiceStatusDialogComponent } from '../change-invoice-status-dialog/change-invoice-status-dialog.component';
import { ListInvoicesDataSource } from './invoice-table.data-source';
import { FeatureFlagService } from '@galaxy/partner';
import { RECURRING_INVOICES_FEATURE_FLAG_ID } from '../constants';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { MatTab, MatTabGroup } from '@angular/material/tabs';
import { VaMaterialTableModule } from '@vendasta/uikit';
import { MatIcon } from '@angular/material/icon';
import {
  MatCell,
  MatCellDef,
  MatColumnDef,
  MatHeaderCell,
  MatHeaderCellDef,
  MatHeaderRow,
  MatHeaderRowDef,
  MatRow,
  MatRowDef,
  MatTable,
} from '@angular/material/table';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import {
  InvoiceCollectionMethodPipe,
  PadInvoiceNumberPipe,
  PaymentStatusBadgePipe,
  PricePipe,
  StatusBadgePipe,
} from '../shared/pipes';
import { MatMenu, MatMenuItem, MatMenuTrigger } from '@angular/material/menu';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { MatButton, MatIconButton } from '@angular/material/button';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { INDETERMINATE_MAX, MatPaginatorOfManyDisplayed, Filter2TableI18nModule } from '@vendasta/va-filter2-table';

interface InvoiceColumn {
  id: string;
  name: string;
}

interface Status {
  name: string;
  status: InvoiceStatus | null;
  pastDue: boolean;
}

const DESIRED_COLUMN_ORDER = [
  'total',
  'invoice_status',
  'number',
  'payment_status',
  'customer',
  'due',
  'issued',
  'collection',
  'actions',
];

@Component({
  selector: 'smb-invoicing-invoice-table',
  templateUrl: './invoice-table.component.html',
  styleUrls: ['./invoice-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [FilterService, [{ provide: MatPaginatorIntl, useClass: MatPaginatorOfManyDisplayed }]],
  standalone: true,
  imports: [
    FilterModule,
    MatTabGroup,
    MatTab,
    VaMaterialTableModule,
    MatIcon,
    TranslateModule,
    AsyncPipe,
    MatTable,
    MatColumnDef,
    MatHeaderCell,
    MatCell,
    GalaxyBadgeModule,
    GalaxyTooltipModule,
    PadInvoiceNumberPipe,
    MatMenuTrigger,
    GalaxyPipesModule,
    MatMenu,
    MatMenuItem,
    DatePipe,
    GalaxyButtonLoadingIndicatorModule,
    MatIconButton,
    MatProgressSpinner,
    MatHeaderRow,
    MatRow,
    MatPaginator,
    MatButton,
    PricePipe,
    PaymentStatusBadgePipe,
    InvoiceCollectionMethodPipe,
    StatusBadgePipe,
    MatHeaderRowDef,
    MatRowDef,
    MatHeaderCellDef,
    MatCellDef,
    Filter2TableI18nModule,
  ],
})
export class InvoiceTableComponent implements OnInit, OnDestroy, OnChanges {
  @ViewChild('invoicePaginator', { static: true }) paginator: MatPaginator;
  @ViewChild('downloadAnchor') private downloadAnchor: ElementRef;
  @ViewChild('statusTabGroup') statusTabGroup;
  // The additional columns to be displayed alongside the static columns of the table. If not provided, only the static columns will be displayed.
  @Input() additionalColumns: string[];
  @Input() isAdmin = false; // Controls what actions are displayed on the table
  @Input() partnerId: string;

  @Input() filters: ListInvoicesFilter;
  @Input() dataAction = 'search-invoice';

  @Output() invoiceSelected = new EventEmitter<string>();
  @Output() customerSelected = new EventEmitter<string>();
  @Output() invoiceSettingSelected = new EventEmitter<string>();

  confirmationModal = inject(OpenConfirmationModalService);

  public length$: Observable<number>;
  public displayedColumns: string[];
  public displayedColumnsNames: string[];
  public dataSource: ListInvoicesDataSource;
  public filterGroup: FilterGroup;
  public statusFormControl: UntypedFormControl = new UntypedFormControl('');
  protected readonly InvoiceStatus = InvoiceStatus; // so we can use the enum in the template
  protected readonly PaymentStatus = LastPaymentStatus; // so we can use the enum in the template
  CollectionMethod = CollectionMethodAPI; // so we can use the enum in the template
  todayMidnight = new Date().setHours(0, 0, 0, 0); // set to the start of the calendar day

  // The tabs that appear above the invoice table which provide quick filtering by invoice status
  public tabs: Status[] = [];
  public statuses: Status[] = [];

  // Columns that are always displayed on the table
  staticColumns: InvoiceColumn[] = [];
  // All columns that can be displayed on the table
  allColumns: InvoiceColumn[] = [];

  private destroyed$: ReplaySubject<boolean> = new ReplaySubject(1);
  private filters$: Observable<ListInvoicesFilter>;
  private invoiceFilters$: Observable<ListInvoicesFilter>;
  private inputFilters$$: ReplaySubject<ListInvoicesFilter> = new ReplaySubject<ListInvoicesFilter>(1);
  private inputFilters$: Observable<ListInvoicesFilter> = this.inputFilters$$.asObservable();
  private tabFilter$$: BehaviorSubject<Status[]> = new BehaviorSubject<Status[]>([this.tabs[0]]);
  private tabFilter$ = this.tabFilter$$.asObservable();

  duplicating$$ = new BehaviorSubject<boolean>(false);
  InvoicePDFType = InvoicePDFType;

  PopoverPositions = PopoverPositions;

  loadingPaymentLink = signal<{ [invoiceId: string]: boolean }>({});

  constructor(
    private translate: TranslateService,
    private invoiceService: InvoiceService,
    private analyticsService: ProductAnalyticsService,
    private snackbarService: SnackbarService,
    public dialog: MatDialog,
    private featureFlagService: FeatureFlagService,
    private router: Router,
    private translateService: TranslateService,
  ) {
    this.dataSource = new ListInvoicesDataSource(this.invoiceService);
    this.length$ = this.dataSource.storedInvoices$.pipe(
      skip(1),
      withLatestFrom(this.dataSource.hasMore$),
      map(([items, hasMore]) => (hasMore ? INDETERMINATE_MAX : items.length)),
    );
  }

  ngOnInit(): void {
    this.staticColumns = [
      {
        id: 'total',
        name: this.translateService.instant('INVOICE.COLUMNS.TOTAL'),
      },
      {
        id: 'invoice_status',
        name: this.translateService.instant('INVOICE.COLUMNS.INVOICE_STATUS'),
      },
      {
        id: 'number',
        name: this.translateService.instant('INVOICE.COLUMNS.NUMBER'),
      },
      {
        id: 'payment_status',
        name: this.translateService.instant('INVOICE.COLUMNS.PAYMENT_STATUS'),
      },
      {
        id: 'due',
        name: this.translateService.instant('INVOICE.COLUMNS.DUE'),
      },
      {
        id: 'issued',
        name: this.translateService.instant('INVOICE.COLUMNS.ISSUED'),
      },
      {
        id: 'actions',
        name: this.translateService.instant('INVOICE.COLUMNS.ACTIONS'),
      },
    ];

    this.allColumns = [
      ...this.staticColumns,
      {
        id: 'collection',
        name: this.translateService.instant('INVOICE.COLUMNS.COLLECTION_METHOD'),
      },
      {
        id: 'customer',
        name: this.translateService.instant('INVOICE.COLUMNS.CUSTOMER'),
      },
    ];

    this.tabs = [
      { name: this.translateService.instant('INVOICE.TABLE.FILTERS.ALL_INVOICES'), status: null, pastDue: false },
      {
        name: this.translateService.instant('INVOICE.TABLE.FILTERS.DRAFT'),
        status: InvoiceStatus.DRAFT,
        pastDue: false,
      },
      { name: this.translateService.instant('INVOICE.TABLE.FILTERS.DUE'), status: InvoiceStatus.DUE, pastDue: false },
      {
        name: this.translateService.instant('INVOICE.TABLE.FILTERS.PAST_DUE'),
        status: InvoiceStatus.DUE,
        pastDue: true,
      },
      { name: this.translateService.instant('INVOICE.TABLE.FILTERS.PAID'), status: InvoiceStatus.PAID, pastDue: false },
    ];
    this.statuses = [
      { name: this.translateService.instant('INVOICE.TABLE.FILTERS.DUE'), status: InvoiceStatus.DUE, pastDue: false },
      {
        name: this.translateService.instant('INVOICE.TABLE.FILTERS.PAST_DUE'),
        status: InvoiceStatus.DUE,
        pastDue: true,
      },
      { name: this.translateService.instant('INVOICE.TABLE.FILTERS.PAID'), status: InvoiceStatus.PAID, pastDue: false },
      { name: this.translateService.instant('INVOICE.TABLE.FILTERS.VOID'), status: InvoiceStatus.VOID, pastDue: false },
      {
        name: this.translateService.instant('INVOICE.TABLE.FILTERS.DRAFT'),
        status: InvoiceStatus.DRAFT,
        pastDue: false,
      },
    ];

    this.initFilterGroup();
    this.initTabsControl();
    this.initInvoiceFilters();

    const columnsToDisplay = this.getColumnsToDisplay(this.additionalColumns);
    this.displayedColumns = columnsToDisplay.map((c: InvoiceColumn) => c.id);
    this.displayedColumnsNames = columnsToDisplay.map((c: InvoiceColumn) => c.name);

    this.filters$ = combineLatest([this.tabFilter$, this.inputFilters$, this.invoiceFilters$]).pipe(
      map(([tabFilter, inputFilters, invoiceFilters]) => {
        const pastDue = !!tabFilter?.find((f) => !!f?.pastDue);
        const statuses = this.computeStatusesFromFilters(tabFilter, inputFilters, invoiceFilters);

        return {
          ...invoiceFilters,
          merchantId: inputFilters.merchantId,
          customerId: inputFilters.customerId,
          statuses: statuses,
          pastDue: pastDue || invoiceFilters?.pastDue,
          origins: invoiceFilters?.origins,
          collectionMethods: invoiceFilters?.collectionMethods,
        };
      }),
    );

    this.initPaginator();
    this.filters$.pipe(takeUntil(this.destroyed$)).subscribe((filters) => {
      this.paginator.firstPage();
      this.dataSource.reload(filters, this.paginator.pageSize, this.paginator.pageIndex);
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.inputFilters$$.next(changes.filters.currentValue);
  }

  ngOnDestroy(): void {
    this.destroyed$.next(true);
    this.destroyed$.complete();
  }

  initFilterGroup(): void {
    const issuedStartDateControl = new DateFilterControl('issuedStartDate', 'Issued start', null, {
      appliedValueMapper: (name, value) => ({
        name,
        value,
        label: this.translateService.instant('INVOICE.TABLE.FILTERS.ISSUED_START', {
          date: formatDate(value, 'mediumDate', 'en-US'),
        }),
      }),
    });

    const issuedEndDateControl = new DateFilterControl('issuedEndDate', 'Issued end', null, {
      appliedValueMapper: (name, value) => ({
        name,
        value,
        label: this.translateService.instant('INVOICE.TABLE.FILTERS.ISSUED_END', {
          date: formatDate(value, 'mediumDate', 'en-US'),
        }),
      }),
    });

    const createdStartDateControl = new DateFilterControl('createdStartDate', 'Created start', null, {
      appliedValueMapper: (name, value) => ({
        name,
        value,
        label: this.translateService.instant('INVOICE.TABLE.FILTERS.CREATED_START', {
          date: formatDate(value, 'mediumDate', 'en-US'),
        }),
      }),
    });

    const createdEndDateControl = new DateFilterControl('createdEndDate', 'Created end', null, {
      appliedValueMapper: (name, value) => ({
        name,
        value,
        label: this.translateService.instant('INVOICE.TABLE.FILTERS.CREATED_END', {
          date: formatDate(value, 'mediumDate', 'en-US'),
        }),
      }),
    });

    this.filterGroup = new FilterGroup('invoice-list')
      .addToolbarSection(
        new SearchFilterControl('number', this.translateService.instant('INVOICE.TABLE.FILTERS.INVOICE_NUMBER_HASH')),
      )
      .addSection(this.translateService.instant('INVOICE.TABLE.FILTERS.ISSUED_DATE'), [
        issuedStartDateControl,
        issuedEndDateControl,
      ])
      .addSection(this.translateService.instant('INVOICE.TABLE.FILTERS.CREATED_DATE'), [
        createdStartDateControl,
        createdEndDateControl,
      ])
      .prependSection(
        this.translateService.instant('INVOICE.TABLE.FILTERS.PAYMENT_STATUS'),
        this.buildLastPaymentStatusCheckboxControls(),
      )
      .prependSection(
        this.translateService.instant('INVOICE.TABLE.FILTERS.STATUS'),
        this.buildStatusCheckboxControls(),
      );

    const disableCreatedControls = () => {
      createdStartDateControl.disable({ emitEvent: false });
      createdEndDateControl.disable({ emitEvent: false });
    };

    const disableIssuedControls = () => {
      issuedStartDateControl.disable({ emitEvent: false });
      issuedEndDateControl.disable({ emitEvent: false });
    };

    const enableCreatedControls = () => {
      createdStartDateControl.enable({ emitEvent: false });
      createdEndDateControl.enable({ emitEvent: false });
    };

    const enableIssuedControls = () => {
      issuedStartDateControl.enable({ emitEvent: false });
      issuedEndDateControl.enable({ emitEvent: false });
    };

    combineLatest([
      issuedStartDateControl.valueChanges.pipe(startWith(issuedStartDateControl.value)),
      issuedEndDateControl.valueChanges.pipe(startWith(issuedEndDateControl.value)),
    ]).subscribe(([issuedStart, issuedEnd]) => {
      if (issuedStart || issuedEnd) {
        disableCreatedControls();
      } else {
        enableCreatedControls();
      }
    });

    combineLatest([
      createdStartDateControl.valueChanges.pipe(startWith(createdStartDateControl.value)),
      createdEndDateControl.valueChanges.pipe(startWith(createdEndDateControl.value)),
    ]).subscribe(([createdStart, createdEnd]) => {
      if (createdStart || createdEnd) {
        disableIssuedControls();
      } else {
        enableIssuedControls();
      }
    });

    if (this.isAdmin) {
      this.featureFlagService
        .batchGetStatus(this.partnerId, '', [RECURRING_INVOICES_FEATURE_FLAG_ID])
        .pipe(
          catchError(() => of({ grandfather_recurring_invoices: false })),
          map((resp) => resp[RECURRING_INVOICES_FEATURE_FLAG_ID]),
        )
        .subscribe((isOnRecurringInvoiceFeatureFlag) => {
          const sourceFilters = [
            new CheckboxFilterControl(
              'renewal',
              this.translateService.instant('INVOICE.TABLE.FILTERS.SUBSCRIPTION_RENEWALS'),
              false,
              {
                appliedValueMapper: (name) => ({
                  name,
                  label: this.translateService.instant('INVOICE.TABLE.FILTERS.SUBSCRIPTION_RENEWALS'),
                }),
              },
            ),
            new CheckboxFilterControl(
              'template',
              this.translateService.instant('INVOICE.TABLE.FILTERS.RECURRING_INVOICE'),
              false,
              {
                appliedValueMapper: (name) => ({
                  name,
                  label: this.translateService.instant('INVOICE.TABLE.FILTERS.RECURRING_INVOICE'),
                }),
              },
            ),
            new CheckboxFilterControl('manual', this.translateService.instant('INVOICE.TABLE.FILTERS.MANUAL'), false, {
              appliedValueMapper: (name) => ({
                name,
                label: this.translateService.instant('INVOICE.TABLE.FILTERS.MANUAL'),
              }),
            }),
          ];
          this.filterGroup
            .addSection(
              'Source',
              isOnRecurringInvoiceFeatureFlag ? sourceFilters : [sourceFilters[0], sourceFilters[2]],
            )
            .addSection(this.translateService.instant('INVOICE.TABLE.FILTERS.COLLECTION_METHOD'), [
              new CheckboxFilterControl(
                'chargeAutomatically',
                this.translateService.instant('INVOICE.TABLE.FILTERS.AUTOMATICALLY_CHARGE'),
                false,
                {
                  appliedValueMapper: (name) => ({
                    name,
                    label: this.translateService.instant('INVOICE.TABLE.FILTERS.AUTOMATICALLY_CHARGE'),
                  }),
                },
              ),
              new CheckboxFilterControl(
                'sendEmail',
                this.translateService.instant('INVOICE.TABLE.FILTERS.SEND_EMAIL'),
                false,
                {
                  appliedValueMapper: (name) => ({
                    name,
                    label: this.translateService.instant('INVOICE.TABLE.FILTERS.SEND_EMAIL'),
                  }),
                },
              ),
              new CheckboxFilterControl(
                'manualCollection',
                this.translateService.instant('INVOICE.TABLE.FILTERS.MANUALLY_COLLECT'),
                false,
                {
                  appliedValueMapper: (name) => ({
                    name,
                    label: this.translateService.instant('INVOICE.TABLE.FILTERS.MANUALLY_COLLECT'),
                  }),
                },
              ),
            ]);
        });
    }
  }

  buildStatusCheckboxControls(): FilterControl[] {
    const c = [
      new CheckboxFilterControl('due', this.translateService.instant('INVOICE.TABLE.FILTERS.DUE'), false, {
        appliedValueMapper: (name) => ({ name, label: this.translateService.instant('INVOICE.TABLE.FILTERS.DUE') }),
      }),
      new CheckboxFilterControl('pastDue', this.translateService.instant('INVOICE.TABLE.FILTERS.PAST_DUE'), false, {
        appliedValueMapper: (name) => ({
          name,
          label: this.translateService.instant('INVOICE.TABLE.FILTERS.PAST_DUE'),
        }),
      }),
      new CheckboxFilterControl('paid', this.translateService.instant('INVOICE.TABLE.FILTERS.PAID'), false, {
        appliedValueMapper: (name) => ({
          name,
          label: this.translateService.instant('INVOICE.TABLE.FILTERS.PAID'),
        }),
      }),
    ];
    if (this.isAdmin) {
      c.push(
        new CheckboxFilterControl('void', this.translateService.instant('INVOICE.TABLE.FILTERS.VOID'), false, {
          appliedValueMapper: (name) => ({ name, label: this.translateService.instant('INVOICE.TABLE.FILTERS.VOID') }),
        }),
        new CheckboxFilterControl('draft', this.translateService.instant('INVOICE.TABLE.FILTERS.DRAFT'), false, {
          appliedValueMapper: (name) => ({ name, label: this.translateService.instant('INVOICE.TABLE.FILTERS.DRAFT') }),
        }),
      );
    }
    return c;
  }

  buildLastPaymentStatusCheckboxControls(): FilterControl[] {
    const c = [
      new CheckboxFilterControl(
        'paymentStatusSuccess',
        this.translateService.instant('INVOICE.TABLE.FILTERS.SUCCESS'),
        false,
        {
          appliedValueMapper: (name) => ({
            name,
            label: this.translateService.instant('INVOICE.TABLE.FILTERS.SUCCESS'),
          }),
        },
      ),
      new CheckboxFilterControl(
        'paymentStatusPending',
        this.translateService.instant('INVOICE.TABLE.FILTERS.PENDING'),
        false,
        {
          appliedValueMapper: (name) => ({
            name,
            label: this.translateService.instant('INVOICE.TABLE.FILTERS.PENDING'),
          }),
        },
      ),
      new CheckboxFilterControl(
        'paymentStatusFailed',
        this.translateService.instant('INVOICE.TABLE.FILTERS.FAILED'),
        false,
        {
          appliedValueMapper: (name) => ({
            name,
            label: this.translateService.instant('INVOICE.TABLE.FILTERS.FAILED'),
          }),
        },
      ),
    ];
    return c;
  }

  computeStatusesFromFilters(
    tabFilter: Status[],
    inputFilter: ListInvoicesFilter,
    invoiceFilter: ListInvoicesFilter,
  ): InvoiceStatus[] {
    const statuses = tabFilter?.filter((f) => !!f?.status).map((f) => f.status);
    if (statuses.length <= 0) {
      return invoiceFilter.statuses.length > 0 ? invoiceFilter.statuses : inputFilter.statuses;
    }
    return statuses;
  }

  initTabsControl(): void {
    this.statusFormControl.valueChanges.pipe(takeUntil(this.destroyed$), debounceTime(300)).subscribe((statuses) => {
      this.tabFilter$$.next(statuses);
      this.statusTabGroup.selectedIndex === 0
        ? this.filterGroup.prependSection(
            this.translateService.instant('INVOICE.TABLE.FILTERS.STATUS'),
            this.buildStatusCheckboxControls(),
          )
        : this.filterGroup.removeSection(this.translateService.instant('INVOICE.TABLE.FILTERS.STATUS'));
    });
  }

  initInvoiceFilters(): void {
    this.invoiceFilters$ = this.filterGroup.valueChanges.pipe(
      startWith(this.filterGroup.value),
      debounceTime(300),
      distinctUntilChanged(),
      map(() => {
        const filterValues = this.filterGroup.value;
        const statuses = this.buildStatusFilterValues(
          filterValues.draft,
          filterValues.due,
          filterValues.paid,
          filterValues.void,
          filterValues.pastDue,
        );
        const pastDue = !!filterValues.pastDue;
        const origins = this.buildOriginsFilterValues(filterValues.manual, filterValues.renewal, filterValues.template);
        const collectionMethods = this.buildCollectionMethodsFilterValues(
          filterValues.manualCollection,
          filterValues.chargeAutomatically,
          filterValues.sendEmail,
        );
        const lastPaymentStatuses = this.buildLastPaymentStatusFilterValues(
          filterValues.paymentStatusSuccess,
          filterValues.paymentStatusPending,
          filterValues.paymentStatusFailed,
        );

        let dateFilter: ListInvoicesDateFilter | null = null;
        if (!!filterValues.issuedStartDate || !!filterValues.issuedEndDate) {
          dateFilter = createDateFilter(
            filterValues.issuedStartDate,
            filterValues.issuedEndDate,
            InvoiceDateField.ISSUED,
          );
        } else if (!!filterValues.createdStartDate || !!filterValues.createdEndDate) {
          dateFilter = createDateFilter(
            filterValues.createdStartDate,
            filterValues.createdEndDate,
            InvoiceDateField.CREATED,
          );
        }
        return <ListInvoicesFilter>{
          number: filterValues.number,
          statuses: statuses,
          pastDue: pastDue,
          dateFilter: dateFilter,
          origins: origins,
          collectionMethods: collectionMethods,
          lastPaymentStatuses: lastPaymentStatuses,
        };
      }),
    );
  }

  buildStatusFilterValues(
    filterDraft,
    filterDue,
    filterPaid,
    filterVoid,
    filterPastDue: FilterControl,
  ): InvoiceStatus[] {
    const statuses: InvoiceStatus[] = [];
    if (filterDraft) {
      statuses.push(InvoiceStatus.DRAFT);
    }
    if (filterDue) {
      statuses.push(InvoiceStatus.DUE);
    }
    if (filterPaid) {
      statuses.push(InvoiceStatus.PAID);
    }
    if (filterVoid) {
      statuses.push(InvoiceStatus.VOID);
    }
    if (filterPastDue) {
      // pastDue requires the DUE status
      statuses.push(InvoiceStatus.DUE);
    }
    return statuses;
  }

  buildLastPaymentStatusFilterValues(filterSuccess, filterPending, filterFailed: FilterControl): LastPaymentStatus[] {
    const lastPaymentStatuses: LastPaymentStatus[] = [];
    if (filterSuccess) {
      lastPaymentStatuses.push(LastPaymentStatus.SUCCESS);
    }
    if (filterPending) {
      lastPaymentStatuses.push(LastPaymentStatus.PENDING);
    }
    if (filterFailed) {
      lastPaymentStatuses.push(LastPaymentStatus.FAILED);
    }
    return lastPaymentStatuses;
  }

  buildOriginsFilterValues(filterManual, filterRenewal, filterTemplate: FilterControl): string[] {
    const origins: string[] = [];
    if (filterManual) {
      origins.push(InvoiceOriginAPI.MANUAL);
    }
    if (filterRenewal) {
      origins.push(InvoiceOriginAPI.RENEWAL);
    }
    if (filterTemplate) {
      origins.push(InvoiceOriginAPI.TEMPLATE);
    }
    return origins;
  }

  buildCollectionMethodsFilterValues(filterManual, filterAuto, filterEmail: FilterControl): string[] {
    const collectionMethods: string[] = [];
    if (filterManual) {
      collectionMethods.push(CollectionMethodAPI.MANUAL_COLLECTION);
    }
    if (filterAuto) {
      collectionMethods.push(CollectionMethodAPI.CHARGE_AUTOMATICALLY);
    }
    if (filterEmail) {
      collectionMethods.push(CollectionMethodAPI.SEND_EMAIL);
    }
    return collectionMethods;
  }

  initPaginator(): void {
    this.paginator.page.pipe(takeUntil(this.destroyed$), withLatestFrom(this.filters$)).subscribe(([, filters]) => {
      this.dataSource.updatePage(filters, this.paginator.pageSize, this.paginator.pageIndex);
    });
  }

  deleteInvoice(invoice: Invoice): void {
    this.filters$
      .pipe(
        take(1),
        switchMap((filters) => {
          return this.confirmationModal
            .openModal({
              type: 'warn',
              title: this.translate.instant('INVOICE.DELETE_INVOICE_MODAL.TITLE', { invoiceNumber: invoice.number }),
              message: this.translate.instant('INVOICE.DELETE_INVOICE_MODAL.SUBTITLE'),
              confirmButtonText: this.translate.instant('INVOICE.DELETE_INVOICE_MODAL.DELETE_INVOICE'),
              actionOnEnterKey: false,
            })
            .pipe(
              switchMap((action) => (action ? this.invoiceService.delete(filters.merchantId, invoice.id) : EMPTY)),
              tap((deleted) => {
                if (deleted) {
                  this.paginator.pageIndex = 0;
                  this.dataSource.reload(filters, this.paginator.pageSize, this.paginator.pageIndex);
                }
              }),
            );
        }),
      )
      .subscribe();
  }

  public changeInvoiceStatus(invoice: Invoice): void {
    this.filters$
      .pipe(
        switchMap((filters) => {
          return this.dialog
            .open(ChangeInvoiceStatusDialogComponent, {
              width: '650px',
              data: {
                merchantId: filters.merchantId,
                invoiceId: invoice.id,
                invoiceNumber: invoice.number,
              },
            } as MatDialogConfig)
            .afterClosed();
        }),
        withLatestFrom(this.filters$),
        take(1),
      )
      .subscribe(([isSuccess, filters]) => {
        if (isSuccess) {
          this.paginator.pageIndex = 0;
          this.dataSource.reload(filters, this.paginator.pageSize, this.paginator.pageIndex);
        }
      });
  }

  public consumeEvent(event: Event): void {
    event.stopPropagation();
  }

  public setFilter(index: number): void {
    this.statusFormControl.setValue([this.tabs[index]]);
  }

  private sortColumns(columns: InvoiceColumn[]): InvoiceColumn[] {
    return columns.sort((a, b) => DESIRED_COLUMN_ORDER.indexOf(a.id) - DESIRED_COLUMN_ORDER.indexOf(b.id));
  }

  private getColumnsToDisplay(columnIds: string[]): InvoiceColumn[] {
    if (!(columnIds?.length > 0)) {
      return this.sortColumns(this.staticColumns);
    }
    const additionalColumns = this.allColumns.filter((c) => columnIds.includes(c.id));
    const selectedColumns = [...this.staticColumns, ...additionalColumns];
    return this.sortColumns(selectedColumns);
  }

  public selectInvoice(invoice: Invoice): void {
    this.invoiceSelected.emit(invoice.id);
  }

  public selectCustomer(customerId: string): void {
    this.customerSelected.emit(customerId);
  }

  public generatePDF(documentType: InvoicePDFType, invoice: Invoice): void {
    this.filters$
      .pipe(
        take(1),
        switchMap((filters) => this.invoiceService.generatePDF(filters.merchantId, invoice.id, documentType)),
      )
      .subscribe((fileResponse) => {
        const filename = `${documentType}.pdf`;
        this.downloadFile(fileResponse, filename);
      });
  }

  public async copyShortenedLink(invoiceId: string): Promise<void> {
    this.loadingPaymentLink.update((state) => ({ ...state, [invoiceId]: true }));
    let trackingProps = {};

    try {
      const filters: ListInvoicesFilter = await firstValueFrom(this.filters$);
      trackingProps = { invoiceId: invoiceId, partnerId: filters.merchantId, accountGroupId: filters.customerId };
      const url: string = await firstValueFrom(this.invoiceService.generateShortLink(filters.merchantId, invoiceId));
      if (!document.hasFocus()) {
        window.focus();
      }
      return await navigator.clipboard.writeText(url).then(() => {
        this.analyticsService.trackEvent('billing', 'invoice-table', 'copy-payment-link-success', 0, trackingProps);
        this.snackbarService.openSuccessSnack(
          this.translateService.instant('INVOICE.TABLE.SNACK_BAR.PAYMENT_LINK_COPIED'),
        );
      });
    } catch (e) {
      this.analyticsService.trackEvent('billing', 'invoice-table', 'copy-payment-link-fail', 0, {
        ...trackingProps,
        error: e.message,
      });
      this.snackbarService.openErrorSnack(this.translateService.instant('INVOICE.TABLE.SNACK_BAR.FAILED_TO_GENERATE'));
    } finally {
      this.loadingPaymentLink.update((state) => ({ ...state, [invoiceId]: false }));
    }
  }

  public exportCSV(): void {
    this.filters$
      .pipe(
        take(1),
        switchMap((filters) => this.invoiceService.exportCSV(filters.merchantId, filters)),
      )
      .subscribe((fileResponse) => {
        this.downloadFile(fileResponse, 'invoices.csv');
      });
  }

  private downloadFile(file: Blob, filename: string): void {
    const url = window.URL.createObjectURL(file);
    const link = this.downloadAnchor.nativeElement;
    link.href = url;
    link.download = filename;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  invoiceSettingsClicked(customerId: string): void {
    this.invoiceSettingSelected.emit(customerId);
  }

  duplicateInvoice(invoice: Invoice): void {
    this.duplicating$$.next(true);
    this.filters$
      .pipe(
        switchMap((filters) => this.invoiceService.duplicate(filters.merchantId, invoice.id)),
        take(1),
      )
      .subscribe((invoiceID) => {
        this.invoiceSelected.emit(invoiceID);
        this.duplicating$$.next(false);
      });
  }

  displayIcon(origin: InvoiceOrigin): boolean {
    return this.isAdmin && (origin === InvoiceOrigin.RENEWAL || origin === InvoiceOrigin.TEMPLATE);
  }

  updateOriginTooltip(origin: InvoiceOrigin): string {
    switch (origin) {
      case InvoiceOrigin.RENEWAL:
        return this.translateService.instant('INVOICE.TABLE.TOOLTIP.INVOICE_FROM_SUBSCRIPTION');
      case InvoiceOrigin.TEMPLATE:
        return this.translateService.instant('INVOICE.TABLE.TOOLTIP.INVOICE_FROM_RECURRING');
      default:
        return '';
    }
  }

  navToCreateCreditNote(invoiceId: string): void {
    this.router.navigate(['billing', 'credit-note', 'create'], {
      queryParams: { invoiceId: invoiceId },
    });
  }
}

function createDateFilter(
  startDateValue: string,
  endDateValue: string,
  dateField: InvoiceDateField,
): ListInvoicesDateFilter {
  let startDate: Date | null = null;
  let endDate: Date | null = null;

  if (startDateValue) {
    startDate = new Date(startDateValue);
    startDate.setUTCHours(0, 0, 0, 0);
  }
  if (endDateValue) {
    endDate = new Date(endDateValue);
    endDate.setUTCHours(23, 59, 59, 999);
  }

  return {
    dateGte: startDate || null,
    dateLte: endDate || null,
    dateField,
  };
}
