@if (isAdmin) {
  <mat-tab-group class="tab-group" #statusTabGroup (selectedIndexChange)="setFilter($event)" disableRipple>
    @for (tab of tabs; track tab) {
      <mat-tab label="{{ tab.name }}"></mat-tab>
    }
  </mat-tab-group>
}
<va-filter2-model [filterGroup]="filterGroup" [isLoading]="dataSource.loading$ | async">
  <va-filter-toolbar-actions>
    <div class="table-toolbar-container">
      <div class="download-button">
        @if (isAdmin) {
          <button mat-stroked-button (click)="exportCSV()">
            <mat-icon>cloud_download</mat-icon>
            <label>{{ 'INVOICE.TABLE.EXPORT' | translate }}</label>
          </button>
        }
      </div>
    </div>
  </va-filter-toolbar-actions>
  <va-filter-content>
    <va-mat-table>
      <div class="table-wrapper">
        <div class="table-container">
          <table mat-table class="invoice-table" [dataSource]="dataSource" aria-label="Invoices">
            <!-- Total Column -->
            <ng-container matColumnDef="total">
              <th mat-header-cell *matHeaderCellDef>{{ 'INVOICE.TABLE.TOTAL' | translate }}</th>
              <td mat-cell *matCellDef="let row">
                <strong>
                  {{ row.total | price: row.currency : null : true : false }}
                </strong>
              </td>
            </ng-container>

            <!-- Currency Column -->
            <ng-container matColumnDef="currency">
              <th mat-header-cell *matHeaderCellDef>Total</th>
              <td mat-cell *matCellDef="let row">
                {{ row.total }}
              </td>
            </ng-container>

            <!-- Invoice Status Column -->
            <ng-container matColumnDef="invoice_status">
              <th mat-header-cell *matHeaderCellDef>{{ 'INVOICE.TABLE.INVOICE_STATUS' | translate }}</th>
              <td mat-cell *matCellDef="let row">
                @if (row | statusBadgePipe; as status) {
                  <div class="status-container">
                    <glxy-badge [color]="status.glxyBadgeColor">{{ status?.text }}</glxy-badge>
                    @if (
                      row.status === InvoiceStatus.DRAFT &&
                      row.collectionMethod !== CollectionMethod.MANUAL_COLLECTION &&
                      row.autoAdvance > todayMidnight
                    ) {
                      <mat-icon
                        class="scheduled-icon"
                        [highContrast]="false"
                        [tooltipPositions]="[PopoverPositions.Bottom]"
                        glxyTooltip="{{
                          'INVOICE.SCHEDULED_TO_SEND' | translate: { date: row.autoAdvance | date: 'mediumDate' }
                        }}"
                      >
                        schedule_send
                      </mat-icon>
                    }
                  </div>
                }
              </td>
            </ng-container>

            <!-- Number Column -->
            <ng-container matColumnDef="number">
              <th mat-header-cell *matHeaderCellDef>{{ 'INVOICE.TABLE.NUMBER' | translate }}</th>
              <td mat-cell *matCellDef="let row">
                <div class="number-column">
                  {{ row.number | padInvoiceNumber }}
                  @if (displayIcon(row.origin)) {
                    <mat-icon
                      [glxyTooltip]="updateOriginTooltip(row.origin)"
                      [highContrast]="false"
                      [tooltipPositions]="[PopoverPositions.Bottom]"
                      class="origin-icon"
                    >
                      loop
                    </mat-icon>
                  }
                </div>
              </td>
            </ng-container>

            <!-- Payment Status Column -->
            <ng-container matColumnDef="payment_status">
              <th mat-header-cell *matHeaderCellDef>{{ 'INVOICE.TABLE.PAYMENT_STATUS' | translate }}</th>
              <td mat-cell *matCellDef="let row">
                @if (row.lastPaymentStatus) {
                  <glxy-badge [color]="(row.lastPaymentStatus | paymentStatusBadge).color">
                    {{ (row.lastPaymentStatus | paymentStatusBadge).text | translate }}
                  </glxy-badge>
                }
              </td>
            </ng-container>

            <!-- Account Column -->
            <ng-container matColumnDef="customer">
              <th mat-header-cell *matHeaderCellDef>{{ 'INVOICE.TABLE.ACCOUNT' | translate }}</th>
              <td mat-cell *matCellDef="let row">
                @if (isAdmin) {
                  <div class="link-layout title">
                    <a [matMenuTriggerFor]="businessMenu" class="menu-layout" (click)="consumeEvent($event)">
                      {{ row.customerName || row.customerId }}
                      <mat-icon>arrow_drop_down</mat-icon>
                    </a>
                  </div>
                  <div class="subtitle">
                    {{
                      {
                        address: row.customerAddress.address,
                        city: row.customerAddress.city,
                        state: row.customerAddress.state,
                      } | glxyAddress
                    }}
                  </div>
                  <mat-menu #businessMenu="matMenu">
                    <button mat-menu-item (click)="selectCustomer(row.customerId)">
                      {{ 'INVOICE.TABLE.ACCOUNT_ACTIONS.VIEW_ACCOUNT' | translate }}
                    </button>
                    <button mat-menu-item (click)="invoiceSettingsClicked(row.customerId)">
                      {{ 'INVOICE.TABLE.ACCOUNT_ACTIONS.EDIT_SETTINGS' | translate }}
                    </button>
                  </mat-menu>
                } @else {
                  <div class="title">
                    {{ row.customerName || row.customerId }}
                  </div>
                  <div class="subtitle">
                    {{
                      {
                        address: row.customerAddress.address,
                        city: row.customerAddress.city,
                        state: row.customerAddress.state,
                      } | glxyAddress
                    }}
                  </div>
                }
              </td>
            </ng-container>

            <!-- Due Column -->
            <ng-container matColumnDef="due">
              <th mat-header-cell *matHeaderCellDef>{{ 'INVOICE.TABLE.DUE' | translate }}</th>
              <td mat-cell *matCellDef="let row">
                {{ row.due | date }}
              </td>
            </ng-container>

            <!-- Issued Column -->
            <ng-container matColumnDef="issued">
              <th mat-header-cell *matHeaderCellDef>{{ 'INVOICE.TABLE.ISSUED' | translate }}</th>
              <td mat-cell *matCellDef="let row">
                {{ row.issued | date }}
              </td>
            </ng-container>

            <!-- Collection Method Column -->
            <ng-container matColumnDef="collection">
              <th mat-header-cell *matHeaderCellDef>{{ 'INVOICE.TABLE.COLLECTION_METHOD' | translate }}</th>
              <td mat-cell *matCellDef="let row">
                {{ row.collectionMethod | collectionMethod }}
              </td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef></th>
              <td mat-cell *matCellDef="let row">
                <glxy-button-loading-indicator [isLoading]="loadingPaymentLink()[row.id]">
                  <button mat-icon-button [matMenuTriggerFor]="optionsMenu" (click)="consumeEvent($event)">
                    <mat-icon>more_vert</mat-icon>
                  </button>
                </glxy-button-loading-indicator>
                <mat-menu #optionsMenu>
                  @if (isAdmin) {
                    <div>
                      <button
                        mat-menu-item
                        (click)="consumeEvent($event); selectInvoice(row)"
                        [disabled]="row.status !== InvoiceStatus.DRAFT"
                      >
                        {{ 'INVOICE.TABLE.ACTIONS.EDIT' | translate }}
                      </button>
                    </div>
                  }
                  <button
                    [disabled]="row.status !== InvoiceStatus.PAID"
                    mat-menu-item
                    (click)="generatePDF(InvoicePDFType.RECEIPT, row)"
                  >
                    {{ 'INVOICE.TABLE.ACTIONS.DOWNLOAD_RECEIPT' | translate }}
                  </button>
                  <button mat-menu-item (click)="generatePDF(InvoicePDFType.INVOICE, row)">
                    {{ 'INVOICE.TABLE.ACTIONS.DOWNLOAD_INVOICE' | translate }}
                  </button>
                  @if (isAdmin && row.status === InvoiceStatus.DRAFT) {
                    <div>
                      <div class="flex-row">
                        <button mat-menu-item (click)="consumeEvent($event); deleteInvoice(row)">
                          {{ 'INVOICE.TABLE.ACTIONS.DELETE_DRAFT' | translate }}
                        </button>
                      </div>
                    </div>
                  }
                  @if (isAdmin && row.status === InvoiceStatus.DUE) {
                    <div>
                      <div class="flex-row">
                        <button mat-menu-item (click)="consumeEvent($event); changeInvoiceStatus(row)">
                          {{ 'INVOICE.TABLE.ACTIONS.CHANGE_STATUS' | translate }}
                        </button>
                      </div>
                    </div>
                  }
                  @if (isAdmin) {
                    <div>
                      @if ((duplicating$$ | async) === false) {
                        <div class="flex-row">
                          <button mat-menu-item (click)="consumeEvent($event); duplicateInvoice(row)">
                            {{ 'INVOICE.TABLE.ACTIONS.DUPLICATE' | translate }}
                          </button>
                        </div>
                      }
                      <div class="working">
                        @if (duplicating$$ | async) {
                          <mat-spinner [diameter]="30"></mat-spinner>
                        }
                      </div>
                    </div>
                  }
                  @if (row.status !== InvoiceStatus.DRAFT) {
                    <button mat-menu-item (click)="copyShortenedLink(row.id)">
                      {{ 'INVOICE.TABLE.ACTIONS.COPY_PAYMENT_LINK' | translate }}
                    </button>
                  }
                  @if (isAdmin && (row.status === InvoiceStatus.DUE || row.status === InvoiceStatus.PAID)) {
                    <button
                      mat-menu-item
                      (click)="navToCreateCreditNote(row.id)"
                      [disabled]="row.lastPaymentStatus === PaymentStatus.PENDING"
                    >
                      {{ 'BILLING.INVOICE_PAGE.ISSUE_CREDIT_NOTE' | translate }}
                    </button>
                  }
                </mat-menu>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr
              class="invoice-row"
              mat-row
              *matRowDef="let row; columns: displayedColumns"
              (click)="selectInvoice(row)"
            ></tr>
          </table>
        </div>
      </div>
      @if ((length$ | async) === 0 && (dataSource.loading$ | async) === false) {
        <ng-content select="[empty-state]"></ng-content>
      }
      <mat-paginator
        #invoicePaginator
        [length]="length$ | async"
        [pageIndex]="0"
        [pageSize]="25"
        [pageSizeOptions]="[25, 50, 100, 250]"
      ></mat-paginator>
    </va-mat-table>
  </va-filter-content>
</va-filter2-model>

<a #downloadAnchor [hidden]="true"></a>
