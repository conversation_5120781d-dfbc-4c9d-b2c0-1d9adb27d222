import { AppliedBundle, InvoiceItem, AppliedTaxRate } from '@galaxy/billing';
import { InvoiceComponent } from './invoice.component';

describe('InvoiceComponent', () => {
  let component: InvoiceComponent;

  beforeEach(() => {
    component = new InvoiceComponent();
  });

  describe('calculateBundleTotal', () => {
    let lineItems: InvoiceItem[];

    beforeEach(() => {
      lineItems = [
        { bundleId: 'SOL-123', bundleInstanceId: '123', subtotal: 1.23 } as InvoiceItem,
        { bundleId: 'SOL-123', bundleInstanceId: '123', subtotal: 12.34 } as InvoiceItem,
        { bundleId: 'SOL-123', bundleInstanceId: '123' } as InvoiceItem,
        { bundleId: 'SOL-123', bundleInstanceId: '123', subtotal: 0 } as InvoiceItem,

        { bundleId: 'SOL-123', bundleInstanceId: '456', subtotal: 3.21 } as InvoiceItem,
        { bundleId: 'SOL-123', bundleInstanceId: '456', subtotal: 43.21 } as InvoiceItem,
        { bundleId: 'SOL-123', bundleInstanceId: '456' } as InvoiceItem,
        { bundleId: 'SOL-123', bundleInstanceId: '456', subtotal: 0 } as InvoiceItem,

        { bundleId: 'SOL-123', subtotal: 123.45 } as InvoiceItem,
        { bundleId: 'SOL-123', subtotal: 987.65 } as InvoiceItem,
        { bundleId: 'SOL-123' } as InvoiceItem,
        { bundleId: 'SOL-123', subtotal: 0 } as InvoiceItem,

        { bundleId: 'SOL-456', subtotal: 234.56 } as InvoiceItem,
        { bundleId: 'SOL-456', subtotal: 345.67 } as InvoiceItem,
        { bundleId: 'SOL-456' } as InvoiceItem,
        { bundleId: 'SOL-456', subtotal: 0 } as InvoiceItem,
      ];
    });

    it('should return 0 if there are no matching items', () => {
      const bundle: AppliedBundle = { bundleId: 'SOL-123' } as AppliedBundle;
      lineItems = lineItems.slice(12);
      const result = component.calculateBundleTotal(bundle, lineItems);
      expect(result).toEqual(0);
    });

    it('should add zero if subtotal is undefined', () => {
      const bundle: AppliedBundle = { bundleId: 'SOL-123' } as AppliedBundle;
      lineItems = [{ bundleId: 'SOL-123' } as InvoiceItem, { bundleId: 'SOL-123', subtotal: 10 } as InvoiceItem];
      const result = component.calculateBundleTotal(bundle, lineItems);
      expect(result).toEqual(10);
    });

    it('should sum the items when both bundle ID and bundle instance ID present', () => {
      const bundle: AppliedBundle = { bundleId: 'SOL-123', bundleInstanceId: '123' } as AppliedBundle;
      const result = component.calculateBundleTotal(bundle, lineItems);
      expect(result).toEqual(13.57);
    });

    it('should sum the items when bundle ID present and bundle instance ID absent', () => {
      const bundle: AppliedBundle = { bundleId: 'SOL-123' } as AppliedBundle;
      const result = component.calculateBundleTotal(bundle, lineItems);
      expect(result).toEqual(1111.1);
    });
  });

  describe('bundleContainsItem', () => {
    it(`should return true when both the bundle and item have bundle instance IDs that match`, () => {
      const bundle = {
        bundleId: '123',
        bundleInstanceId: '123',
      } as AppliedBundle;
      const item = {
        bundleId: '123',
        bundleInstanceId: '123',
      } as InvoiceItem;
      expect(component.bundleContainsItem(bundle, item)).toBe(true);
    });
    it(`should return true when both the bundle and item have bundle IDs that match`, () => {
      const bundle = {
        bundleId: '123',
      } as AppliedBundle;
      const item = {
        bundleId: '123',
      } as InvoiceItem;
      expect(component.bundleContainsItem(bundle, item)).toBe(true);
    });
    it(`should return false when the bundle has a bundle instance ID but the item doesn't`, () => {
      const bundle = {
        bundleId: '123',
        bundleInstanceId: '123',
      } as AppliedBundle;
      const item = {
        bundleId: '123',
      } as InvoiceItem;
      expect(component.bundleContainsItem(bundle, item)).toBe(false);
    });
    it(`should return false when the bundle doesn't have a bundle instance ID but the item does`, () => {
      const bundle = {
        bundleId: '123',
      } as AppliedBundle;
      const item = {
        bundleId: '123',
        bundleInstanceId: '123',
      } as InvoiceItem;
      expect(component.bundleContainsItem(bundle, item)).toBe(false);
    });
    it(`should return false when the bundle and item bundle instance IDs don't match`, () => {
      const bundle = {
        bundleId: '123',
        bundleInstanceId: '123',
      } as AppliedBundle;
      const item = {
        bundleId: '123',
        bundleInstanceId: '456',
      } as InvoiceItem;
      expect(component.bundleContainsItem(bundle, item)).toBe(false);
    });
    it(`should return false when the bundle and item bundle IDs don't match`, () => {
      const bundle = {
        bundleId: '123',
      } as AppliedBundle;
      const item = {
        bundleId: '456',
      } as InvoiceItem;
      expect(component.bundleContainsItem(bundle, item)).toBe(false);
    });
    it(`should return false when there is no bundle`, () => {
      const item = {
        bundleId: '456',
      } as InvoiceItem;
      expect(component.bundleContainsItem(null, item)).toBe(false);
    });
    it(`should return false when there is no item`, () => {
      const bundle = {
        bundleId: '123',
      } as AppliedBundle;
      expect(component.bundleContainsItem(bundle, null)).toBe(false);
    });
  });

  describe('calculateBundleDiscount', () => {
    it('should calculate bundle discount for bundles with discount amount on items', () => {
      const bundle = {
        bundleItems: [
          {
            discountAmount: 75,
          },
          {
            discountAmount: 50,
          },
        ],
      };
      expect(component.calculateBundleDiscount(bundle)).toBe(125);
    });

    it('should calculate bundle discount from `appliedDiscountAmount` field', () => {
      const bundle = {
        appliedDiscountAmount: 200,
      };
      expect(component.calculateBundleDiscount(bundle)).toBe(200);
    });

    it('should calculate bundle discount from item amounts instead of `appliedDiscountAmount` field', () => {
      const bundle = {
        appliedDiscountAmount: 200,
        bundleItems: [
          {
            discountAmount: 75,
          },
          {
            discountAmount: 50,
          },
        ],
      };
      expect(component.calculateBundleDiscount(bundle)).toBe(125);
    });
  });

  describe('formatAppliedTaxesForDisplay', () => {
    it('should format a single tax rate', () => {
      const taxes = [{ percentage: 5 }] as AppliedTaxRate[];
      expect(component.formatAppliedTaxesForDisplay(taxes)).toBe('5%');
    });

    it('should format multiple tax rates', () => {
      const taxes = [{ percentage: 5 }, { percentage: 6 }] as AppliedTaxRate[];
      expect(component.formatAppliedTaxesForDisplay(taxes)).toBe('5% + 6%');
    });
  });
});
