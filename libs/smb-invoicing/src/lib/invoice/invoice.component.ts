import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { Observable } from 'rxjs';
import { take } from 'rxjs/operators';
import { InvoiceViewModel, retailSubscriptionGroupItems } from './invoice';
import {
  AppliedBundle,
  AppliedTaxRate,
  CreditNoteType,
  DiscountType,
  Invoice,
  InvoiceItem,
  InvoicePDFType,
  InvoiceStatus,
  LastPaymentStatus,
  PaymentIntentStatus,
} from '@galaxy/billing';
import { convertCentsToDollars } from '@vendasta/shared';

interface InvoiceItemWithDiscounts extends InvoiceItem {
  subtotalAfterDiscounts: number;
  isDiscounted: boolean;
}

interface InvoiceWithDiscounts extends Omit<Invoice, 'items'> {
  items: InvoiceItemWithDiscounts[];
  lineItemDiscountTotal: number;
  amountPaid: number;
}

@Component({
  selector: 'smb-invoicing-billing-invoice',
  templateUrl: './invoice.component.html',
  styleUrls: ['./invoice.component.scss'],
  standalone: false,
})
export class InvoiceComponent implements OnInit, OnChanges {
  InvoicePDFType = InvoicePDFType;
  InvoiceStatus = InvoiceStatus;
  PaymentIntentStatus = PaymentIntentStatus;
  LastPaymentStatus = LastPaymentStatus;
  CreditNoteType = CreditNoteType;

  invoice: InvoiceWithDiscounts;
  retailSubscriptionGroupInvoiceItems: retailSubscriptionGroupItems[];

  @ViewChild('downloadAnchor') private downloadAnchor: ElementRef;

  @Input() viewModel: InvoiceViewModel;
  @Input() stripePublicKey: string;
  @Input() merchantId?: string;
  @Input() generateInvoiceDocument?: (merchantID: string, invoiceID: string, documentType: string) => Observable<Blob>;
  @Output() processingPaymentEvent: EventEmitter<void> = new EventEmitter();

  ngOnInit(): void {
    if (this.viewModel?.isInvoiceInCents && this.viewModel.invoice) {
      this.viewModel.invoice = this.convertInvoiceValuesToDollars(this.viewModel.invoice);
    }

    this.retailSubscriptionGroupInvoiceItems = this.groupRetailSubscriptionGroups(this.viewModel?.invoice?.items);

    this.viewModel.invoice = this.calculateInvoiceDiscountAmounts(this.viewModel.invoice);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.viewModel) {
      this.viewModel.invoice = this.calculateInvoiceDiscountAmounts(this.viewModel.invoice);
    }
  }

  processingPayment(): void {
    this.processingPaymentEvent.emit(null);
  }

  downloadPDF(documentType: InvoicePDFType): void {
    this.generateInvoiceDocument(this.merchantId, this.viewModel.invoice.id, documentType)
      .pipe(take(1))
      .subscribe((file) => {
        const url = window.URL.createObjectURL(file);
        const link = this.downloadAnchor.nativeElement;
        link.href = url;
        link.download = `${documentType}.pdf`;
        link.click();
        window.URL.revokeObjectURL(url);
      });
  }

  formatAppliedTaxesForDisplay(taxes: AppliedTaxRate[]): string {
    return taxes.map((t) => String(t.percentage + '%')).join(' + ');
  }

  calculateBundleTotal(bundle: AppliedBundle, invoiceLineItems: InvoiceItem[]): number {
    if (!bundle || !invoiceLineItems) {
      return 0;
    }
    const bundleItems = invoiceLineItems.filter((item) => this.bundleContainsItem(bundle, item));
    return bundleItems.reduce((sum, item) => sum + (item.subtotal || 0), 0);
  }

  // TODO: (mlafreniere) update business-center-go to return SDK representation of an Invoice, then remove appliedDiscountAmount condition
  calculateBundleDiscount(bundle: any): number {
    return (
      (bundle?.bundleItems || []).reduce((sum, item) => sum + (item.discountAmount || 0), 0) ||
      bundle?.appliedDiscountAmount ||
      0
    );
  }

  bundleContainsItem(bundle: AppliedBundle, item: InvoiceItem): boolean {
    if (!bundle || !item) {
      return false;
    }

    if (!!bundle.bundleInstanceId && !!item.bundleInstanceId) {
      return bundle.bundleInstanceId === item.bundleInstanceId;
    }
    if (!bundle.bundleInstanceId && !item.bundleInstanceId) {
      return bundle.bundleId === item.bundleId;
    }
    return false;
  }

  private convertInvoiceValuesToDollars(invoice: Invoice): Invoice {
    return {
      ...invoice,
      total: convertCentsToDollars(invoice.total),
      amountOwing: convertCentsToDollars(invoice.amountOwing),
      amountPaid: convertCentsToDollars(invoice.amountPaid),
      discountTotal: convertCentsToDollars(invoice.discountTotal),
      subtotal: convertCentsToDollars(invoice.subtotal),
      appliedTaxes: (invoice.appliedTaxes || []).map((t) => {
        t.amount = convertCentsToDollars(t.amount);
        return t;
      }),
      appliedBundles: (invoice.appliedBundles || []).map((b) => {
        b.bundleItems = (b.bundleItems || []).map((bi) => {
          bi.discountAmount = convertCentsToDollars(bi.discountAmount);
          return bi;
        });
        return b;
      }),
      appliedCreditNotes: (invoice.appliedCreditNotes || []).map((cn) => {
        cn.amount = convertCentsToDollars(cn.amount);
        return cn;
      }),
      items: (invoice.items || []).map((i) => {
        i.amount = convertCentsToDollars(i.amount);
        i.subtotal = convertCentsToDollars(i.subtotal);
        i.total = convertCentsToDollars(i.total);
        i.appliedDiscounts = (i.appliedDiscounts || []).map((d) => {
          d.totalAmount = convertCentsToDollars(d.totalAmount);
          if (d.type === DiscountType.FIXED_AMOUNT || d.type === DiscountType.FIXED_AMOUNT_PER_UNIT) {
            d.value = convertCentsToDollars(d.value);
          }
          return d;
        });
        i.appliedTaxes = (i.appliedTaxes || []).map((t) => {
          t.amount = convertCentsToDollars(t.amount);
          return t;
        });
        return i;
      }),
    };
  }

  calculateInvoiceDiscountAmounts(invoice: Invoice): InvoiceWithDiscounts {
    let lineItemDiscountTotal = 0;
    const items = invoice.items.map((item) => {
      const discountAmount = item.appliedDiscounts.reduce((sum, d) => sum + (d.totalAmount || 0), 0);
      const itemSubtotalAfterDiscounts = Math.max(item.subtotal - discountAmount, 0);
      lineItemDiscountTotal += discountAmount;
      return {
        ...item,
        subtotalAfterDiscounts: itemSubtotalAfterDiscounts,
        isDiscounted: itemSubtotalAfterDiscounts < item.subtotal,
      };
    });

    return { ...invoice, items, lineItemDiscountTotal };
  }

  groupRetailSubscriptionGroups(invoiceItems: InvoiceItem[]): retailSubscriptionGroupItems[] {
    const retailSubscriptionGroupInvoiceItems: retailSubscriptionGroupItems[] = [];

    // Groups all items belonging to the same retail subscription group together
    const retailSubscriptionGroupItemMap: { [id: string]: retailSubscriptionGroupItems } = {};
    if (invoiceItems?.length > 0) {
      invoiceItems.forEach((item) => {
        const id = item?.retailSubscriptionGroup?.id;
        if (id) {
          if (retailSubscriptionGroupItemMap[id]) {
            retailSubscriptionGroupItemMap[id].items.push(item);
          } else {
            retailSubscriptionGroupItemMap[id] = {
              id: id,
              description: item?.retailSubscriptionGroup?.name,
              items: [item],
            };
          }
        }
      });
    }
    for (const id in retailSubscriptionGroupItemMap) {
      if (id) {
        const rsgItem = retailSubscriptionGroupItemMap[id];
        retailSubscriptionGroupInvoiceItems.push({
          id: id,
          description: rsgItem?.description,
          items: rsgItem?.items,
          subtotal: rsgItem?.items.reduce((accumulator, invoiceItem) => {
            return accumulator + invoiceItem.subtotal;
          }, 0),
        } as retailSubscriptionGroupItems);
      }
    }

    return retailSubscriptionGroupInvoiceItems;
  }
}
