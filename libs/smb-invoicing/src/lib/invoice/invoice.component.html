<div class="col container" *ngIf="viewModel as view">
  <mat-card appearance="outlined" class="content" [ngStyle]="{ 'border-color': view?.primaryColor }">
    <div class="col header">
      <img *ngIf="view?.partnerLogo" src="{{ view?.partnerLogo }}" height="53" alt="" />
      <div class="title">
        <p>
          {{ 'INVOICE.INVOICE_FROM' | translate }} {{ view?.partnerName }}
          <span *ngIf="view?.companyName">{{ 'INVOICE.BILLED_TO' | translate }} {{ view?.companyName }}</span>
          <span>{{ 'INVOICE.INVOICE' | translate }} #{{ view?.invoice?.number }}</span>
        </p>
      </div>
    </div>
    <mat-card-content class="col main-content">
      <div class="payment" [ngClass]="{ 'fixed-payment-height': view?.acceptPayment && view?.clientSecret }">
        @switch (view?.invoice?.status) {
          @case (InvoiceStatus.PAID) {
            <div class="col">
              <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
              </svg>
            </div>
            <div class="paid">{{ 'INVOICE.INVOICE_PAID' | translate }}</div>
            <div class="status">
              {{ view?.invoice?.amountPaid | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2' }}
              {{ 'INVOICE.ON' | translate }} {{ view?.invoice?.paid | date: 'longDate' }}
            </div>
            <div *ngIf="!!generateInvoiceDocument">
              <button class="download-button" mat-raised-button [matMenuTriggerFor]="menu">
                {{ 'INVOICE.DOWNLOAD_AS' | translate }} PDF
                <mat-icon iconPositionEnd>arrow_drop_down</mat-icon>
              </button>
              <mat-menu #menu="matMenu">
                <button class="download-button" mat-menu-item (click)="downloadPDF(InvoicePDFType.RECEIPT)">
                  {{ 'INVOICE.DOWNLOAD_RECEIPT' | translate }}
                </button>
                <button class="download-button" mat-menu-item (click)="downloadPDF(InvoicePDFType.INVOICE)">
                  {{ 'INVOICE.DOWNLOAD_INVOICE' | translate }}
                </button>
              </mat-menu>
            </div>
          }
          @case (InvoiceStatus.VOID) {
            <div class="voided">
              <div class="status">{{ 'INVOICE.VOIDED' | translate }}</div>
              <div class="date">
                {{ view?.invoice?.voided | date: 'longDate' }}
              </div>
            </div>
          }
          @default {
            <div class="status">
              {{ view?.invoice?.amountOwing | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2' }}
              {{ 'INVOICE.TABLE.DUE' | translate | lowercase }} {{ view?.invoice?.due | date: 'longDate' }}
            </div>
            @if (view?.invoice?.lastPaymentStatus === LastPaymentStatus.PENDING) {
              <ng-container [ngTemplateOutlet]="processingState"></ng-container>
            } @else {
              <smb-invoicing-billing-payment-form
                *ngIf="view?.acceptPayment && view?.clientSecret"
                [clientSecret]="view?.clientSecret || ''"
                [stripePublicKey]="stripePublicKey || ''"
                [stripeAccount]="view?.accountId || ''"
                [invoiceId]="view?.invoice?.id || ''"
                [merchantId]="merchantId || ''"
                [primaryColor]="view?.primaryColor || ''"
                [invoiceStatus]="view?.invoice?.status || 'DRAFT'"
                [lastErrorCode]="view?.paymentIntent?.lastErrorCode"
                [isPreview]="view?.isPreview"
                (processingPaymentEvent)="processingPayment()"
              ></smb-invoicing-billing-payment-form>
            }
          }
        }
      </div>

      <table>
        <thead>
          <tr>
            <th class="description">{{ 'INVOICE.DESCRIPTION' | translate }}</th>
            <th class="quantity">
              <span class="hide-on-small">{{ 'INVOICE.QUANTITY' | translate }}</span>
            </th>
            <th class="amount price-col">{{ 'INVOICE.PRICE' | translate }}</th>
            <th class="amount">{{ 'INVOICE.TAX' | translate }}</th>
            <th class="amount">{{ 'INVOICE.SUBTOTAL' | translate }}</th>
          </tr>
        </thead>

        <tbody
          *ngFor="let bundle of view.invoice.appliedBundles; index as i"
          [ngClass]="{ 'bundle-grouping': !bundle.hideBundleItems }"
        >
          <tr [ngClass]="{ 'bundle-header': !bundle.hideBundleItems }">
            <td [attr.colspan]="bundle.hideBundleItems ? 1 : 5" class="bundle-name">
              {{ bundle.name }}
            </td>
            <ng-container *ngIf="bundle.hideBundleItems">
              <td class="quantity">1</td>
              <td class="amount price-col">
                {{
                  calculateBundleTotal(bundle, view?.invoice?.items)
                    | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2'
                }}
              </td>
              <td class="amount">&nbsp;</td>
              <td class="amount">
                {{
                  calculateBundleTotal(bundle, view?.invoice?.items)
                    | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2'
                }}
              </td>
            </ng-container>
          </tr>
          <ng-container *ngIf="!bundle.hideBundleItems">
            <ng-container *ngFor="let row of view?.invoice?.items; index as i">
              <ng-container
                *ngIf="bundleContainsItem(bundle, row)"
                [ngTemplateOutlet]="TableLineItem"
                [ngTemplateOutletContext]="{ item: row }"
              ></ng-container>
            </ng-container>
            <tr>
              <td class="spacer" colspan="5">&nbsp;</td>
            </tr>
          </ng-container>
        </tbody>

        <tbody>
          @for (row of view?.invoice?.items; track row.id) {
            @if (!row.bundleId && !row.retailSubscriptionGroup) {
              <ng-container [ngTemplateOutlet]="TableLineItem" [ngTemplateOutletContext]="{ item: row }"></ng-container>
              <tr>
                <td colspan="5" class="item-divider">
                  <mat-divider></mat-divider>
                </td>
              </tr>
            }
          }
          @for (retailSubGroup of retailSubscriptionGroupInvoiceItems; track retailSubGroup.id) {
            <tr>
              <td colspan="4" class="description">{{ retailSubGroup.description }}</td>
              <td class="rsg-subtotal">
                {{ retailSubGroup.subtotal | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2' }}
              </td>
            </tr>
            <tr>
              <td colspan="5" class="item-divider">
                <mat-divider></mat-divider>
              </td>
            </tr>
            @for (item of retailSubGroup.items; track item.id) {
              <ng-container
                [ngTemplateOutlet]="TableLineItem"
                [ngTemplateOutletContext]="{ item: item }"
              ></ng-container>
              <tr>
                <td colspan="5" class="item-divider">
                  <mat-divider></mat-divider>
                </td>
              </tr>
            }
          }
        </tbody>

        <ng-template #TableLineItem let-item="item">
          <tr>
            <td [ngClass]="item?.retailSubscriptionGroup?.id ? 'rsg-description' : 'description'">
              {{ item.description }}
            </td>
            <td class="quantity">{{ item?.quantity }}</td>
            <td class="amount price-col">
              {{ item?.amount | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2' }}
            </td>
            @if (item?.appliedTaxes) {
              <td class="amount price-col">
                {{ formatAppliedTaxesForDisplay(item.appliedTaxes) }}
              </td>
            } @else {
              <td colspan="1"></td>
            }

            @if (!item?.retailSubscriptionGroup) {
              <td class="amount" [class.strike]="item.isDiscounted">
                {{ item.subtotal | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2' }}
              </td>
            } @else {
              <td colspan="1"></td>
            }
          </tr>
          <ng-container *ngIf="item.subtotal > 0">
            <ng-container *ngFor="let discount of item.appliedDiscounts">
              <tr>
                <td class="description discount">
                  {{ discount.description }}
                </td>
                <td colspan="1"></td>
                <td class="amount price-col">
                  -{{ discount.totalAmount | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2' }}
                </td>
                <td colspan="1"></td>
                <td class="amount">
                  {{ item.subtotalAfterDiscounts | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2' }}
                </td>
              </tr>
            </ng-container>
          </ng-container>
        </ng-template>

        <tfoot>
          <tr class="item-row-header">
            <td colspan="4" class="hide-on-small">{{ 'INVOICE.SUBTOTAL' | translate }}</td>
            <td colspan="3" class="show-on-small">{{ 'INVOICE.SUBTOTAL' | translate }}</td>
            <td class="amount">
              {{ view?.invoice?.subtotal | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2' }}
            </td>
          </tr>
          <ng-container *ngFor="let bundle of view?.invoice?.appliedBundles">
            <tr *ngIf="calculateBundleDiscount(bundle) as discountAmount">
              <td colspan="4" class="hide-on-small">{{ bundle.name }}&nbsp;{{ 'INVOICE.DISCOUNT' | translate }}</td>
              <td colspan="3" class="show-on-small">{{ bundle.name }}&nbsp;{{ 'INVOICE.DISCOUNT' | translate }}</td>
              <td class="amount">
                -{{ discountAmount | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2' }}
              </td>
            </tr>
          </ng-container>
          <ng-container
            *ngIf="
              (view?.invoice?.appliedBundles?.length > 0 && view?.invoice?.lineItemDiscountTotal > 0) ||
              view?.invoice?.discountTotal > 0
            "
          >
            <tr>
              <td colspan="4" class="hide-on-small">{{ 'INVOICE.DISCOUNTS' | translate }}</td>
              <td colspan="3" class="show-on-small">{{ 'INVOICE.DISCOUNTS' | translate }}</td>
              <td class="amount">
                <ng-container *ngIf="view?.invoice?.appliedBundles?.length > 0; else trueDiscountTotal">
                  <!-- Display the sum of line item discounts, calculated independently of bundle discount totals -->
                  -{{
                    view?.invoice?.lineItemDiscountTotal | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2'
                  }}
                </ng-container>
                <ng-template #trueDiscountTotal>
                  <!-- There are no bundle discounts, so we can accurately use the discount amount calculated on the backend. -->
                  -{{ view?.invoice?.discountTotal | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2' }}
                </ng-template>
              </td>
            </tr>
          </ng-container>
          <ng-container *ngFor="let row of view?.invoice?.appliedTaxes">
            <tr>
              <td colspan="4" class="hide-on-small">{{ row.name }}</td>
              <td colspan="3" class="show-on-small">{{ row.name }}</td>
              <td class="amount">
                {{ row.amount | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2' }}
              </td>
            </tr>
          </ng-container>
          <!-- Credits applied before the payment -->
          @for (credit of view?.invoice?.appliedCreditNotes; track credit.creditNoteId) {
            @if (credit.creditNoteType === CreditNoteType.PRE_PAYMENT) {
              <tr>
                <td colspan="4" class="hide-on-small">
                  {{ 'INVOICE.CREDITS_APPLIED' | translate }} ({{ credit.number }})
                </td>
                <td colspan="3" class="show-on-small">
                  {{ 'INVOICE.CREDITS_APPLIED' | translate }} ({{ credit.number }})
                </td>
                <td class="amount">
                  -{{ credit.amount | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2' }}
                </td>
              </tr>
            }
          }
          <tr>
            <td colspan="1"></td>
            <td colspan="4">
              <mat-divider></mat-divider>
            </td>
          </tr>
          <tr class="item-row-header">
            @if (view?.invoice?.status === InvoiceStatus.PAID) {
              <td colspan="4" class="hide-on-small">{{ 'INVOICE.AMOUNT_PAID' | translate }}</td>
              <td colspan="3" class="show-on-small">{{ 'INVOICE.AMOUNT_PAID' | translate }}</td>
              <td class="amount">
                {{ view?.invoice?.amountPaid | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2' }}
              </td>
            } @else {
              <td colspan="4" class="hide-on-small">{{ 'INVOICE.AMOUNT_DUE' | translate }}</td>
              <td colspan="3" class="show-on-small">{{ 'INVOICE.AMOUNT_DUE' | translate }}</td>
              <td class="amount">
                {{ view?.invoice?.amountOwing | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2' }}
              </td>
            }
          </tr>

          <!-- Credits applied after payment -->
          @for (credit of view?.invoice?.appliedCreditNotes; track credit.creditNoteId) {
            @if (credit.creditNoteType === CreditNoteType.POST_PAYMENT) {
              <tr>
                <td colspan="1"></td>
                <td colspan="4">
                  <mat-divider></mat-divider>
                </td>
              </tr>
              <tr class="item-row-header">
                <td colspan="4" class="hide-on-small">{{ 'INVOICE.REFUND' | translate }} ({{ credit.number }})</td>
                <td colspan="3" class="show-on-small">{{ 'INVOICE.REFUND' | translate }} ({{ credit.number }})</td>
                <td class="amount">
                  -{{ credit.amount | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2' }}
                </td>
              </tr>
              <tr>
                <td colspan="4" class="hide-on-small">{{ 'INVOICE.ADJUSTED_INVOICE_AMOUNT' | translate }}</td>
                <td colspan="3" class="show-on-small">{{ 'INVOICE.ADJUSTED_INVOICE_AMOUNT' | translate }}</td>
                <td class="amount">
                  {{
                    view?.invoice?.adjustedAmountAfterCreditRefund
                      | currency: view?.invoice?.currency : 'symbol-narrow' : '1.2-2'
                  }}
                </td>
              </tr>
            }
          }
        </tfoot>
      </table>

      <div class="memo-layout">{{ view?.invoice?.memo }}</div>
    </mat-card-content>
    <mat-card-footer>{{ 'INVOICE.QUESTIONS' | translate }} {{ view?.partnerName }}.</mat-card-footer>
  </mat-card>
  <div class="footer">
    {{ 'INVOICE.POWERED_BY' | translate }}
    <a href="https://stripe.com/" target="_blank">Stripe</a>
    <br />
    {{ view?.partnerName }} {{ 'INVOICE.PARTNERS_WITH' | translate }}
  </div>
</div>

<a id="download" #downloadAnchor [hidden]="true"></a>

<ng-template #processingState>
  <div class="processing">
    <div class="processing-icon">
      <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" preserveAspectRatio="none">
        <path d="M21 7L9 19l-5.5-5.5 1.41-1.41L9 16.17 19.59 5.59 21 7z" fill="#4caf50" />
      </svg>
    </div>
    <div class="processing-title">{{ 'INVOICE.INVOICE_PROCESSING' | translate }}</div>
    <div class="processing-details">
      {{ 'INVOICE.INVOICE_PROCESSING_DETAILS' | translate }}
    </div>
  </div>
</ng-template>
