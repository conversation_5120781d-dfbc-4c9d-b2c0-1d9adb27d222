@use 'design-tokens' as *;

* {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  box-sizing: border-box;
}

.mat-icon {
  font-family: 'Material Icons', sans-serif !important;
}

$curve: cubic-bezier(0.65, 0, 0.45, 1);

.container {
  padding: $spacing-4 $spacing-2;

  .mat-mdc-card {
    @extend .center;
    max-width: 600px;
    background: $card-background-color;
    border-top: 2px solid;
    padding: 0 0;

    .header {
      margin-top: $spacing-3;

      .img {
        height: 58px;
        max-width: 360px;
        margin: 0;
        padding: 0;
        border: none;
        display: inline-block;
      }
    }

    .title {
      color: $primary-text-color;
      font-size: 22px;
      padding: $spacing-3 $spacing-3 0;

      span {
        color: $secondary-text-color;
        font-size: 14px;
        display: block;
        margin: $spacing-3;
      }
    }
  }

  .mat-mdc-card-content {
    .fixed-payment-height {
      height: 100%;
      min-height: 180px;
    }

    .payment {
      @extend .center;
      margin-bottom: $spacing-3;
      font-size: 22px;
      background: #f7fafc;
      box-shadow: 0 1px 3px 0 #e6ebf1;
      padding: 20px $spacing-3;

      .paid {
        padding: $spacing-2;
        color: $success-text-color;
        font-size: 14px;
        font-weight: bold;
      }

      .status {
        padding: $spacing-3;
      }

      .voided {
        display: flex;
        flex-direction: column;
        height: 152px;
        justify-content: center;

        .date {
          font-size: $spacing-3;
        }
      }
    }
  }
}

table {
  table-layout: fixed;
  border-collapse: collapse;
  width: 100%;
  font-size: 14px;
  color: #5f6368;
}

th,
td {
  padding: $spacing-2 $spacing-1;

  &:first-child {
    padding-left: $spacing-3;
  }

  &:last-child {
    padding-right: $spacing-3;
  }
}

tfoot {
  text-align: right;

  td {
    padding-bottom: 0;
  }
}

.item-row-header {
  font-weight: bold;
}

.description {
  text-align: left;
  width: 40%;
}

.quantity {
  text-align: right;
  width: 10%;
}

.amount {
  text-align: right;
  width: 16.66667%;
}

.bundle-grouping {
  background-color: $secondary-background-color;
}

.bundle-header {
  background-color: $border-color;
  font-size: $spacing-3;
  font-weight: bold;
}

.bundle-name {
  text-align: left;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.spacer {
  background-color: #fff;
  line-height: 0;
}

.show-on-small {
  display: none;
}

@media screen and (max-width: 420px) {
  table {
    font-size: 12px;
    table-layout: auto;
  }
  .bundle-header {
    font-size: 14px;
  }
  .hide-on-small,
  .price-col {
    display: none;
  }
  .show-on-small {
    display: revert;
  }
}

.mat-mdc-card-footer {
  @extend .center;
  font-size: 14px;
  padding: $spacing-3;
}

.memo-layout {
  padding: 0 $spacing-3;
  text-align: left;
  white-space: pre-wrap;
}

.footer {
  @extend .center;
  margin-top: $spacing-3;
  color: #22292f;
  font-size: 13px;
}

.center {
  margin-right: auto;
  margin-left: auto;
  text-align: center;
  width: 100%;
}

.checkmark {
  width: $spacing-5;
  height: $spacing-5;
  border-radius: 50%;
  stroke-width: 2;
  stroke: $white;
  box-shadow: inset 0px 0px 0px $success-icon-color;
  animation:
    fill 0.4s ease-in-out 0.4s forwards,
    scale 0.3s ease-in-out 0.9s both;
}

.checkmark__circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 2;
  stroke-miterlimit: 10;
  stroke: $success-icon-color;
  fill: none;
  animation: stroke 0.6s $curve forwards;
}

.checkmark__check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  animation: stroke 0.3s $curve 0.8s forwards;
}

.item-divider {
  padding: 0 $spacing-3;
}

.strike {
  text-decoration: line-through;
}

td.discount {
  padding-left: $spacing-3 * 2;
}

@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes scale {
  0%,
  100% {
    transform: none;
  }
  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}

@keyframes fill {
  100% {
    box-shadow: inset 0px 0px 0px 30px $success-icon-color;
  }
}

.processing {
  display: flex;
  flex-direction: column;
  height: 152px;
  justify-content: center;
  align-items: center;
}

.processing-title {
  @include text-preset-3--bold;
}

.processing-details {
  @include text-preset-5;
}

.download-button {
  width: 200px;
}

.rsg-subtotal {
  text-align: right;
}

.rsg-description {
  text-align: left;
  padding-left: $spacing-5 !important;
}
