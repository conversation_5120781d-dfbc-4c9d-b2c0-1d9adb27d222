import { Invoice, PaymentIntentStatus, PaymentMethodType, RetailPaymentErrorCode, InvoiceItem } from '@galaxy/billing';

class PaymentIntent {
  paymentMethodType: PaymentMethodType;
  status: PaymentIntentStatus;
  lastErrorCode: RetailPaymentErrorCode;
}

export class InvoiceViewModel {
  isPreview: boolean;
  acceptPayment: boolean;
  partnerName: string;
  partnerLogo: string;
  primaryColor: string;
  companyName: string;
  invoice: Invoice;
  isInvoiceInCents: boolean;
  accountId: string;
  clientSecret?: string;
  paymentIntent: PaymentIntent;
}

export class retailSubscriptionGroupItems {
  id: string;
  description: string;
  items: InvoiceItem[];
  subtotal?: number;
}
