import { Injectable } from '@angular/core';
import {
  CommonFormData,
  CustomFieldsAnswer,
  CustomFieldsAnswers,
  IncludedCommonFormFieldsInterface,
  OrderFormFieldOptionsType,
  OrderFormInterface,
  OrderFormOptionsInterface,
  ProductInfoInterface,
  TextBoxFieldOptions,
} from '@vendasta/store';
import { User } from './interface';
import { combineLatest, Observable, of, zip } from 'rxjs';
import {
  GetMultiOrderFormsResponseOrderFormContainerInterface as MPOrderFormContainerInterface,
  MarketplaceAppsApiService,
} from '@galaxy/marketplace-apps/v1';
import { App } from '@galaxy/marketplace-apps';
import { SalesOrderService } from './sales-order.service';
import {
  CommonField,
  CustomField,
  Field,
  LineItem,
  LineItemInterface,
  Order,
  SalesOrdersService,
} from '@vendasta/sales-orders';
import { Account, AccountGroup } from '@galaxy/account-group';
import { Package } from '@vendasta/marketplace-packages';
import { map, publishReplay, refCount, switchMap } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';
import { AppType } from '@vendasta/marketplace-apps';

@Injectable()
export class SalesOrderFormService {
  private mpOrderForms$: Observable<MPOrderFormContainerInterface[]>;
  private appQuantityMap$: Observable<Map<string, number>>;
  private userOptions: User[];
  order$: Observable<Order>;

  constructor(
    private translateService: TranslateService,
    private appsApiService: MarketplaceAppsApiService,
    private salesOrderService: SalesOrderService,
    private salesOrderSdk: SalesOrdersService,
  ) {}

  initialize(businessId: string, orderId: string, lineItems: LineItemInterface[], userOptions: User[]): void {
    this.userOptions = userOptions;
    this.salesOrderService.initialize(businessId, orderId, lineItems);
    this.order$ = this.salesOrderService.order$;

    this.mpOrderForms$ = combineLatest([this.salesOrderService.apps$, this.salesOrderService.business$]).pipe(
      switchMap(([apps, business]) => {
        return this.getMarketplaceOrderForms(business.externalIdentifiers.partnerId, apps);
      }),
      publishReplay(1),
      refCount(),
    );

    this.appQuantityMap$ = zip(this.salesOrderService.packages$, this.salesOrderService.lineItems$).pipe(
      map(([packages, salesLineItems]) => {
        const quantityMap: Map<string, number> = new Map();
        const allPkgLineItems = packages.reduce((items, pkg) => {
          const pkgLineItem = salesLineItems.find((lineItem) => lineItem.packageId === pkg.packageId);
          const pkgCount = (pkgLineItem ? pkgLineItem.quantity : 1) || 1;
          return items.concat(this.salesOrderLineItemsFromPackage(pkg, pkgCount));
        }, []);
        salesLineItems = salesLineItems.filter((lineItem) => !lineItem.packageId);
        salesLineItems = salesLineItems.concat(allPkgLineItems);
        salesLineItems.forEach((item) => {
          const count = quantityMap.get(item.appKey.appId) || 0;
          quantityMap.set(item.appKey.appId, count + (item.quantity || 1));
        });
        return quantityMap;
      }),
      publishReplay(1),
      refCount(),
    );
  }

  private salesOrderLineItemsFromPackage(pkg: Package, pkgCount: number): LineItem[] {
    if (!pkg.lineItems || !pkg.lineItems.lineItems || !pkg.lineItems.lineItems.length) {
      return [];
    }
    return pkg.lineItems.lineItems.map(
      (lineItem) =>
        new LineItem({
          appKey: { appId: lineItem.id, editionId: lineItem.editionId },
          quantity: (lineItem.quantity || 1) * pkgCount,
        }),
    );
  }

  private getMarketplaceOrderForms(partnerId: string, apps: App[]): Observable<MPOrderFormContainerInterface[]> {
    if (apps?.length > 0) {
      const appIds: string[] = apps
        .filter((app) => !!app.key && app.activationInformation.orderFormEnabled)
        .map((app) => app.key.appId);
      if (appIds?.length > 0) {
        return this.appsApiService
          .getMultiOrderForms({ partnerId: partnerId, appIds: appIds })
          .pipe(map((resp) => resp.orderFormContainer || []));
      }
    }
    return of([]);
  }

  getOrderForms(options: OrderFormOptionsInterface): Observable<OrderFormInterface[]> {
    return combineLatest([
      zip(
        this.mpOrderForms$,
        this.appQuantityMap$,
        this.salesOrderService.uniqueApps$,
        this.salesOrderService.lineItems$,
      ),
      this.salesOrderService.business$,
    ]).pipe(
      map(([[orderForms, appQuantityMap, apps, lineItems], business]) => {
        return apps.reduce((forms, app) => {
          let commonFormRequiredFields: IncludedCommonFormFieldsInterface = {};
          let customFields: OrderFormFieldOptionsType[] = [];

          if (app.activationInformation.activationSpecificUrlEnabled) {
            customFields.unshift({
              id: 'custom-entry-url-' + app.key.appId,
              label: this.translateService.instant('FRONTEND.SALES_UI.ORDER_FORM.CUSTOM_ENTRY_URL'),
              type: 'text',
              required: options.showOfficeUseQuestions,
              hidden: !options.showOfficeUseQuestions,
              regexValidator: '^(http://|https://).*',
              regexErrorMessage: this.translateService.instant(
                'FRONTEND.SALES_UI.ORDER_FORM.CUSTOM_ENTRY_URL_REGEX_ERROR',
              ),
              textboxType: 'text',
            } as TextBoxFieldOptions);
          }

          const orderForm = orderForms.find((o) => o.appId === app.key.appId);

          if (!!orderForm || app.activationInformation.activationSpecificUrlEnabled) {
            if (
              !!orderForm &&
              orderForm.orderForm &&
              app.activationInformation.orderFormEnabled &&
              !this.isTrialUpgrade(app.key.appId, lineItems, business)
            ) {
              if (orderForm.orderForm.commonFormRequiredFields) {
                commonFormRequiredFields = orderForm.orderForm.commonFormRequiredFields;
              }
              if (orderForm.orderForm.orderForm) {
                customFields = customFields.concat(
                  orderForm.orderForm.orderForm.map(
                    (f) => ({ ...f, options: f.optionsWithLabels } as OrderFormFieldOptionsType),
                  ),
                );
              }
            }

            let appId = app.key.appId;
            let addonId = '';
            if (app.parentRequirements.enabled && app.appType === AppType.APP_TYPE_ADDON) {
              appId = app.parentRequirements.parentDetails.key.appId;
              addonId = app.key.appId;
            }

            const createdForm: OrderFormInterface = {
              appId: appId,
              addonId: addonId,
              customFields: customFields,
              commonFormRequiredFields: commonFormRequiredFields,
            };
            if (orderForm?.orderForm?.footerText && orderForm.orderForm.footerText.trim() !== '') {
              createdForm.footerText = orderForm.orderForm.footerText;
            }
            forms.push(createdForm);

            if (
              !!addonId &&
              app.activationInformation.orderFormEnabled &&
              app.activationInformation.multipleActivationsEnabled &&
              app.activationInformation.separateOrderForms
            ) {
              const count = appQuantityMap.get(app.key.appId) || 1;
              for (let i = 1; i < count; i++) {
                forms.push(createdForm);
              }
            } else if (
              !addonId &&
              app.activationInformation.orderFormEnabled &&
              app.activationInformation.multipleActivationsEnabled
            ) {
              const count = appQuantityMap.get(app.key.appId) || 1;
              for (let i = 1; i < count; i++) {
                forms.push(createdForm);
              }
            }
          }

          return forms;
        }, []);
      }),
    );
  }

  private isTrialUpgrade(appId: string, lineItems: LineItemInterface[], business: AccountGroup): boolean {
    return !this.isTrialLineItem(lineItems, appId) && business && this.businessHasActiveTrial(business.accounts, appId);
  }

  private isTrialLineItem(lineItems: LineItemInterface[], appId: string): boolean {
    if (lineItems) {
      const lineItem = lineItems.find((li) => li.appKey && li.appKey.appId === appId);
      return lineItem && lineItem.isTrial;
    }
    return false;
  }

  private businessHasActiveTrial(accounts: Account[], appId: string): boolean {
    if (accounts) {
      const account = accounts.find((a) => a.marketplaceAppId === appId);
      return account && account.isTrial;
    }
    return false;
  }

  private getNewCommonData(commonFormData: CommonFormData): Observable<CommonFormData> {
    return combineLatest([this.salesOrderService.business$, this.salesOrderService.salesperson$]).pipe(
      map(([business, salesperson]) => {
        const user = this.userOptions && this.userOptions.length > 0 ? this.userOptions[0] : null;
        let businessAddress = '';
        if (
          business &&
          business.napData &&
          (business.napData.address || business.napData.city || business.napData.state || business.napData.zip)
        ) {
          const addressItems = [
            business.napData.address,
            business.napData.city,
            business.napData.state,
            business.napData.zip,
          ];
          businessAddress = addressItems.filter((i) => !!i).join(', ');
        }
        return {
          user_options: this.userOptions,
          contact_email:
            !!commonFormData.contact_email && commonFormData.contact_email
              ? commonFormData.contact_email
              : user
              ? user.email
              : '',
          contact_name:
            !!commonFormData.contact_name && commonFormData.contact_name
              ? commonFormData.contact_name
              : user
              ? [user.firstName, user.lastName].filter((i) => !!i).join(' ')
              : '',
          contact_phone_number:
            !!commonFormData.contact_phone_number && commonFormData.contact_phone_number
              ? commonFormData.contact_phone_number
              : user
              ? user.phoneNumber
              : '',
          business_account_group_id:
            !!commonFormData.business_account_group_id && commonFormData.business_account_group_id
              ? commonFormData.business_account_group_id
              : business
              ? business.accountGroupId
              : '',
          business_name:
            !!commonFormData.business_name && commonFormData.business_name
              ? commonFormData.business_name
              : business && business.napData
              ? business.napData.companyName
              : '',
          business_address:
            !!commonFormData.business_address && commonFormData.business_address
              ? commonFormData.business_address
              : businessAddress,
          business_phone_number:
            !!commonFormData.business_phone_number && commonFormData.business_phone_number
              ? commonFormData.business_phone_number
              : business && business.napData && business.napData.workNumber && business.napData.workNumber.length > 0
              ? business.napData.workNumber[0]
              : '',
          business_website:
            !!commonFormData.business_website && commonFormData.business_website
              ? commonFormData.business_website
              : business && business.napData
              ? business.napData.website
              : '',
          salesperson_name:
            !!commonFormData.salesperson_name && commonFormData.salesperson_name
              ? commonFormData.salesperson_name
              : salesperson
              ? [salesperson.firstName, salesperson.lastName].filter((i) => !!i).join(' ')
              : '',
          salesperson_email:
            !!commonFormData.salesperson_email && commonFormData.salesperson_email
              ? commonFormData.salesperson_email
              : salesperson
              ? salesperson.email
              : '',
          salesperson_phone_number:
            !!commonFormData.salesperson_phone_number && commonFormData.salesperson_phone_number
              ? commonFormData.salesperson_phone_number
              : salesperson && salesperson.phoneNumber && salesperson.phoneNumber.length > 0
              ? salesperson.phoneNumber[0]
              : '',
          ...commonFormData,
        };
      }),
    );
  }

  getCommonData(): Observable<CommonFormData> {
    return this.salesOrderService.order$.pipe(
      switchMap((order) => {
        if (order) {
          const commonData: CommonFormData = {};
          (order.commonFields || []).map((cf) => {
            let fieldAnswer = cf.field.answer;
            try {
              fieldAnswer = JSON.parse(fieldAnswer);
            } catch (error) {
              // do nothing
            }
            const fieldId = cf.field.fieldId;
            commonData[fieldId] = fieldAnswer;
          });
          return this.getNewCommonData(commonData);
        }
        return this.getNewCommonData({});
      }),
    );
  }

  getProductInfo(): Observable<ProductInfoInterface[]> {
    return this.salesOrderService.uniqueApps$.pipe(
      map((apps) => {
        const productInfoMap: Map<string, ProductInfoInterface> = new Map();
        apps.map((app) => {
          let productInfo: ProductInfoInterface = {
            productId: 'temporary',
            addonInfo: [],
          };
          if (app.parentRequirements.enabled && app.appType === AppType.APP_TYPE_ADDON) {
            if (productInfoMap.has(app.parentRequirements.parentDetails.key.appId)) {
              productInfo = productInfoMap.get(app.parentRequirements.parentDetails.key.appId);
            }
            productInfo.name = app.parentRequirements.parentDetails.name;
            productInfo.tagline = app.parentRequirements.parentDetails.tagline;
            productInfo.icon = app.parentRequirements.parentDetails.iconUrl;
            productInfo.addonInfo.push({
              addonId: app.key.appId,
              name: app.sharedMarketingInformation.name,
            });
            productInfo.productId = app.parentRequirements.parentDetails.key.appId;
            productInfoMap.set(productInfo.productId, productInfo);
          } else {
            if (productInfoMap.has(app.key.appId)) {
              productInfo = productInfoMap.get(app.key.appId);
            }
            productInfo.name = app.sharedMarketingInformation.name;
            productInfo.tagline = app.sharedMarketingInformation.tagline;
            productInfo.icon = app.sharedMarketingInformation.iconUrl;
            productInfo.productId = app.key.appId;
            productInfoMap.set(productInfo.productId, productInfo);
          }
        });
        return Array.from(productInfoMap.values());
      }),
    );
  }

  getAnswers(): Observable<CustomFieldsAnswers[]> {
    return this.salesOrderService.order$.pipe(
      map((order) => {
        if (order) {
          const answers: CustomFieldsAnswers[] = [];
          if (order.customFields) {
            order.customFields.map((c) => {
              let fieldAnswers: CustomFieldsAnswer[] = [];
              if (c.fields) {
                fieldAnswers = c.fields.map((f) => ({ fieldId: f.fieldId, answer: f.answer }));
              }
              answers.push({
                productID: c.productId || (c.addonKey ? c.addonKey.appId : ''),
                addonKey: c.addonKey,
                customFieldsAnswers: fieldAnswers,
              });
            });
          }
          return answers;
        }
        return [];
      }),
    );
  }

  getExtraQuestions(): Observable<OrderFormFieldOptionsType[]> {
    return this.salesOrderService.orderConfig$.pipe(
      map((config) => {
        return !!config && !!config.extraFields
          ? config.extraFields.map((f) => ({ ...f, options: f.optionsWithLabels } as OrderFormFieldOptionsType))
          : [];
      }),
    );
  }

  getExtraAnswers(): Observable<CustomFieldsAnswer[]> {
    return this.salesOrderService.order$.pipe(
      map((order) => {
        if (order) {
          return order.extraFields || [];
        }
        return [];
      }),
    );
  }

  updateAnswers(commonFormData: CommonField[], customFormData: CustomField[], extraFields: Field[]): Observable<Order> {
    return this.salesOrderService.order$.pipe(
      switchMap((order) => {
        return this.salesOrderSdk.updateAnswers(
          order.orderId,
          order.businessId,
          customFormData,
          commonFormData,
          extraFields,
        );
      }),
    );
  }

  hasQuestions(orderFormOptions: OrderFormOptionsInterface): Observable<boolean> {
    return combineLatest([this.getExtraQuestions(), this.getOrderForms(orderFormOptions)]).pipe(
      map(([questions, forms]) => {
        const hasExtraQuestions = questions?.length > 0;

        const commonFormHasQuestions = (commonForm?: IncludedCommonFormFieldsInterface) => {
          return (
            commonForm &&
            Object.values(commonForm).some((field) => {
              return typeof field === 'boolean' && field;
            })
          );
        };

        const hasQuestions = forms.some((form) => {
          const hasCommonForm = commonFormHasQuestions(form.commonForm);
          const hasCommonFormRequiredFields = commonFormHasQuestions(form.commonFormRequiredFields);
          const hasCustomFields = form.customFields && form.customFields.length > 0;
          return hasCommonForm || hasCommonFormRequiredFields || hasCustomFields;
        });
        return hasQuestions || hasExtraQuestions;
      }),
    );
  }
}
