<mat-card appearance="outlined">
  <glxy-form-field [bottomSpacing]="'none'">
    <glxy-label>{{ 'CUSTOM_FORMS.CONFIGURE_FORM.EDIT_NAME' | translate }}</glxy-label>
    <input type="text" matInput [formControl]="form.controls.name" data-testid="form-name-config-input" />
  </glxy-form-field>

  <mat-divider class="divider"></mat-divider>

  <div class="title-section">
    <span class="title">{{ 'CUSTOM_FORMS.CONFIGURE_FORM.SUBMISSION_SETTINGS' | translate }}</span>
  </div>
  <glxy-form-field>
    <glxy-label>{{ 'CUSTOM_FORMS.CONFIGURE_FORM.URL_REDIRECT' | translate }}</glxy-label>
    <glxy-label-hint>{{ 'CUSTOM_FORMS.CONFIGURE_FORM.URL_REDIRECT_HINT' | translate }}</glxy-label-hint>
    <input type="text" matInput [formControl]="form.controls.redirectUrl" />
    <glxy-error *ngIf="form.controls.redirectUrl.errors">
      {{ 'COMMON.VALIDATORS.INVALID_URL' | translate }}
    </glxy-error>
  </glxy-form-field>
  <glxy-form-field bottomSpacing="none">
    <glxy-label>Inbox integration</glxy-label>
    <mat-checkbox [formControl]="form.controls.createInboxConversation">
      {{ 'CUSTOM_FORMS.CONFIGURE_FORM.INBOX_CREATE' | translate }}
      <extended>{{ 'CUSTOM_FORMS.CONFIGURE_FORM.INBOX_CREATE_DESCRIPTION' | translate }}</extended>
    </mat-checkbox>
  </glxy-form-field>

  <mat-divider class="divider"></mat-divider>

  <div class="title-section">
    <span class="title">{{ 'CUSTOM_FORMS.CONFIGURE_FORM.RECAPTCHA_SETTINGS' | translate }}</span>
    <glxy-label-hint>
      {{ 'CUSTOM_FORMS.CONFIGURE_FORM.RECAPTCHA_SETTINGS_HINT' | translate }}
      <a target="_blank" href="{{ recaptchaLink() }}">
        {{ 'CUSTOM_FORMS.CONFIGURE_FORM.RECAPTCHA_SETTINGS_HINT_LINK' | translate }}
        <mat-icon class="open-new-tab-icon">open_in_new</mat-icon>
      </a>
    </glxy-label-hint>
  </div>
  <glxy-form-field>
    <glxy-label>
      {{ 'CUSTOM_FORMS.CONFIGURE_FORM.RECAPTCHA_SITE_KEY' | translate }}
    </glxy-label>
    <input type="text" matInput [formControl]="form.controls.recaptchaSiteKey" data-testid="site-key-config-input" />
  </glxy-form-field>
  <glxy-form-field [bottomSpacing]="'none'">
    <glxy-label>
      {{ 'CUSTOM_FORMS.CONFIGURE_FORM.RECAPTCHA_SECRET_KEY' | translate }}
    </glxy-label>
    <input
      type="text"
      matInput
      [formControl]="form.controls.recaptchaSecretKey"
      (input)="onSecretInput($event.target.value)"
      data-testid="secret-key-config-input"
    />
  </glxy-form-field>
</mat-card>
