import { Inject, Injectable } from '@angular/core';
import { FileInfo, GalaxyUploaderService, UploadResponse } from '@vendasta/galaxy/uploader';
import { HttpClient, HttpParams } from '@angular/common/http';
import { map, Observable, ReplaySubject } from 'rxjs';
import { Environment, EnvironmentService } from '@galaxy/core';
import { SmsContextToken } from '../../../../token';
import { SmsContext } from '../../../../interface';
import { switchMap } from 'rxjs/operators';

interface SmsUploadResponse {
  data: {
    fileID: string;
  };
}

@Injectable({
  providedIn: 'root',
})
export class SmsDocumentUploaderService extends GalaxyUploaderService {
  fileID$$: ReplaySubject<string> = new ReplaySubject<string>(1);
  fileID$: Observable<string> = this.fileID$$.asObservable();

  constructor(
    httpClient: HttpClient,
    private readonly env: EnvironmentService,
    @Inject(SmsContextToken) private readonly context: SmsContext,
  ) {
    super(httpClient);
  }

  override buildRequest(fileInfo: FileInfo): Observable<UploadResponse> {
    this.uploadUrl = 'https://sms-demo.apigateway.co/regulatory/upload-file';
    if (this.env.getEnvironment() === Environment.PROD) {
      this.uploadUrl = 'https://sms-prod.apigateway.co/regulatory/upload-file';
    }
    return this.context.ownerId$.pipe(
      map((ownerID) => {
        const params = new HttpParams();
        params.set('ownerID', ownerID);
        params.set('ownerType', this.context.ownerType);
        return params;
      }),
      switchMap((params) => {
        const body = new FormData();

        body.append('file', fileInfo.file as Blob);

        const options = {
          withCredentials: true,
          params: params,
        };
        return this.http.post(this.uploadUrl, body, options) as Observable<SmsUploadResponse>;
      }),
      map((response) => {
        this.fileID$$.next(response.data.fileID);
        return {
          data: {
            // The super class expects this return type, but we don't have a URL on purpose
            url: '',
          },
        };
      }),
    );
  }

  override removeFile(fileInfo: FileInfo) {
    super.removeFile(fileInfo);
    this.fileID$$.next('');
  }
}
