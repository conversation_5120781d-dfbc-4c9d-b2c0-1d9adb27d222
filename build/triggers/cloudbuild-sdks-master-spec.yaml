triggers:
  - name: typescript-sdks-master-ai-assistants
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: ai-assistants
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-yext
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: yext
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-listing-syndication
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: listing-syndication
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-spambam
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: spambam
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-embeddings
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: embeddings
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-data-warehouse
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: data-warehouse
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-conversation
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: conversation
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-listing-products
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: listing-products
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-listing-sync-pro
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: listing-sync-pro
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-wsp-support-tools
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: wsp-support-tools
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-wsp-accounts
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: wsp-accounts
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-web-crawler
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: web-crawler
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-the-loop
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: the-loop
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-templates
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: templates
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-telephony
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: telephony
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-task-manager
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: task-manager
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-task
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: task
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-sso
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: sso
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-sre-reporting
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: sre-reporting
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-social-drafts
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: social-drafts
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-snapshot-widget
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: snapshot-widget
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-snapshot
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: snapshot
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-sales-opportunities
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: sales-opportunities
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-sales
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: sales
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-reputation
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: reputation
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-quickbooks
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: quickbooks
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-prospect
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: prospect
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-partner
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: partner
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-order-fulfillment
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: order-fulfillment
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-notifications
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: notifications
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-multi-location-reporting
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: multi-location-reporting
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-multi-location-analytics
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: multi-location-analytics
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-multi-location
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: multi-location
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-ms-office
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: ms-office
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-mission-control
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: mission-control
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-meetings
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: meetings
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-matchcraft
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: matchcraft
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-marketplace-apps
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: marketplace-apps
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-marketplace-api
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: marketplace-api
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-marketplace-analytics
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: marketplace-analytics
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-local-marketing-index
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: local-marketing-index
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-iam
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: IAM
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-v2-master-iam
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: IAM
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
    substitutions:
      _PATH_FOR_PACKAGE: 'v2'
      _VERSION_COMMAND_SUFFIX: ':iamv2'
  - name: typescript-sdks-master-help-center
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: help-center
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-google-my-business
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: google-my-business
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-google-analytics
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: google-analytics
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-godaddy
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: godaddy
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-g-suite
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: g-suite
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-facebook
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: facebook
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-executive-report
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: executive-report
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-event-broker
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: event-broker
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-email
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: email
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-email-builder
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: email-builder
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-domain
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: domain
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-developer-training
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: developer-training
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-developer-center
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: developer-center
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-contacts
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: contacts
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-business-center
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: business-center
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-bulk-actions
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: bulk-actions
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-billing
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: billing
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-automata
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: automata
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-atlas
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: atlas
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-advertising
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: advertising
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-accounts
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: accounts
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-sales-orders
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: sales-orders
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-address
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: address
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-customer-voice-service
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: customer-voice-service
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-wsp-wp-manager
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: wsp-wp-manager
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-wsp-monitor
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: wsp-monitor
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-shoppable-feed
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: shoppable-feed
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-lexicon
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: lexicon
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-url-shortener
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: url-shortener
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-url-social-posts
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: social-posts
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-url-instagram
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: instagram
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-url-composer
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: composer
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-url-media
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: media
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-url-account-group-media
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: account-group-media
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-account-group
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: account-group
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-campaigns
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: campaigns
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-constant-contact
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: constant-contact
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-nap
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: NAP
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-sms
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: sms
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-v2-master-sms
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: sms
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
    substitutions:
      _PATH_FOR_PACKAGE: 'v2'
      _VERSION_COMMAND_SUFFIX: ':smsv2'
  - name: typescript-sdks-master-google-search-console
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: google-search-console
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-wsp-logger
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: wsp-logger
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-category
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: category
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-forms
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: forms
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-support
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: support
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-crm
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: crm
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-platform-users
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: platform-users
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-crm-integrations
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: crm-integrations
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-api-gateway
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: api-gateway
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-platform-integrations
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: platform-integrations
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-library-management-system
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: library-management-system
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-meeting-analysis
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: meeting-analysis
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-voice
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: voice
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-listing-score
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: listing-score
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-configuration-management
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: configuration-management
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-master-vendasta-yesware-bridge
    description: Typescript SDKs - Master
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: vendasta-yesware-bridge
      push:
        branch: ^master$
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
