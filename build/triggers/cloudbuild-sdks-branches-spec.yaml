triggers:
  - name: typescript-sdks-branches-ai-assistants
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: ai-assistants
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-yext
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: yext
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-listing-syndication
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: listing-syndication
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-spambam
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: spambam
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-embeddings
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: embeddings
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-data-warehouse
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: data-warehouse
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-conversation
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: conversation
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-listing-products
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: listing-products
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-listing-sync-pro
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: listing-sync-pro
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-wsp-support-tools
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: wsp-support-tools
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-wsp-accounts
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: wsp-accounts
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-web-crawler
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: web-crawler
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-the-loop
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: the-loop
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-templates
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: templates
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-telephony
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: telephony
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-task-manager
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: task-manager
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-task
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: task
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-sso
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: sso
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-sre-reporting
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: sre-reporting
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-social-drafts
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: social-drafts
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-snapshot-widget
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: snapshot-widget
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-snapshot
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: snapshot
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-sales-opportunities
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: sales-opportunities
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-sales
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: sales
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-reputation
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: reputation
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-quickbooks
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: quickbooks
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-prospect
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: prospect
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-partner
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: partner
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-order-fulfillment
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: order-fulfillment
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-notifications
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: notifications
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-multi-location-reporting
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: multi-location-reporting
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-multi-location-analytics
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: multi-location-analytics
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-multi-location
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: multi-location
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-ms-office
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: ms-office
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-mission-control
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: mission-control
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-meetings
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: meetings
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-matchcraft
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: matchcraft
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-marketplace-apps
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: marketplace-apps
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-marketplace-api
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: marketplace-api
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-marketplace-analytics
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: marketplace-analytics
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-local-marketing-index
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: local-marketing-index
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-iam
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: IAM
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-v2-branches-iam
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: IAM
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
    substitutions:
      _PATH_FOR_PACKAGE: 'v2'
      _VERSION_COMMAND_SUFFIX: ':iamv2'
  - name: typescript-sdks-branches-help-center
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: help-center
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-google-my-business
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: google-my-business
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-google-analytics
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: google-analytics
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-godaddy
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: godaddy
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-g-suite
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: g-suite
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-facebook
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: facebook
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-executive-report
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: executive-report
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-event-broker
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: event-broker
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-email
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: email
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-email-builder
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: email-builder
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-domain
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: domain
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-developer-training
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: developer-training
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-developer-center
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: developer-center
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-contacts
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: contacts
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-business-center
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: business-center
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-bulk-actions
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: bulk-actions
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-billing
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: billing
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-automata
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: automata
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-atlas
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: atlas
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-advertising
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: advertising
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-accounts
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: accounts
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-sales-orders
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: sales-orders
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-address
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: address
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-customer-voice-service
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: customer-voice-service
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-wsp-wp-manager
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: wsp-wp-manager
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-wsp-monitor
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: wsp-monitor
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-shoppable-feed
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: shoppable-feed
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-lexicon
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: lexicon
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-url-shortener
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: url-shortener
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-url-social-posts
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: social-posts
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-url-instagram
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: instagram
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-url-composer
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: composer
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-url-media
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: media
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-url-account-group-media
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: account-group-media
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-account-group
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: account-group
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-campaigns
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: campaigns
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-constant-contact
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: constant-contact
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-nap
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: NAP
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-sms
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: sms
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-v2-branches-sms
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: sms
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
    substitutions:
      _PATH_FOR_PACKAGE: 'v2'
      _VERSION_COMMAND_SUFFIX: ':smsv2'
  - name: typescript-sdks-branches-google-search-console
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: google-search-console
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-wsp-logger
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: wsp-logger
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-category
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: category
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-forms
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: forms
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-support
    description: Typescript SDKs - Support
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: support
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-crm
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: crm
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-platform-users
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: platform-users
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-crm-integrations
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: crm-integrations
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-api-gateway
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: api-gateway
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-platform-integrations
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: platform-integrations
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-library-management-system
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: library-management-system
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-meeting-analysis
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: meeting-analysis
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-voice
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: voice
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-listing-score
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: listing-score
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-configuration-management
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: configuration-management
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
  - name: typescript-sdks-branches-vendasta-yesware-bridge
    description: Typescript SDKs - Branches
    disabled: false
    tags:
      - sdks
    github:
      owner: vendasta
      name: vendasta-yesware-bridge
      push:
        branch: '[^master$]'
        invertRegex: false
    includedFiles:
      - sdks/typescript/**
    ignoredFiles:
      - '**/*.go'
